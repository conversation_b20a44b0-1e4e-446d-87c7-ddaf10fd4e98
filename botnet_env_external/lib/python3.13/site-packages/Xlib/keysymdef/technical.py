XK_leftradical = 0x8a1
XK_topleftradical = 0x8a2
XK_horizconnector = 0x8a3
XK_topintegral = 0x8a4
XK_botintegral = 0x8a5
XK_vertconnector = 0x8a6
XK_topleftsqbracket = 0x8a7
XK_botleftsqbracket = 0x8a8
XK_toprightsqbracket = 0x8a9
XK_botrightsqbracket = 0x8aa
XK_topleftparens = 0x8ab
XK_botleftparens = 0x8ac
XK_toprightparens = 0x8ad
XK_botrightparens = 0x8ae
XK_leftmiddlecurlybrace = 0x8af
XK_rightmiddlecurlybrace = 0x8b0
XK_topleftsummation = 0x8b1
XK_botleftsummation = 0x8b2
XK_topvertsummationconnector = 0x8b3
XK_botvertsummationconnector = 0x8b4
XK_toprightsummation = 0x8b5
XK_botrightsummation = 0x8b6
XK_rightmiddlesummation = 0x8b7
XK_lessthanequal = 0x8bc
XK_notequal = 0x8bd
XK_greaterthanequal = 0x8be
XK_integral = 0x8bf
XK_therefore = 0x8c0
XK_variation = 0x8c1
XK_infinity = 0x8c2
XK_nabla = 0x8c5
XK_approximate = 0x8c8
XK_similarequal = 0x8c9
XK_ifonlyif = 0x8cd
XK_implies = 0x8ce
XK_identical = 0x8cf
XK_radical = 0x8d6
XK_includedin = 0x8da
XK_includes = 0x8db
XK_intersection = 0x8dc
XK_union = 0x8dd
XK_logicaland = 0x8de
XK_logicalor = 0x8df
XK_partialderivative = 0x8ef
XK_function = 0x8f6
XK_leftarrow = 0x8fb
XK_uparrow = 0x8fc
XK_rightarrow = 0x8fd
XK_downarrow = 0x8fe
