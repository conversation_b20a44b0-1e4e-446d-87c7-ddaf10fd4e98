# Automatically generated by evdev.genecodes_py

"""
This modules exposes the integer constants defined in ``linux/input.h`` and
``linux/input-event-codes.h``.

Exposed constants::

    KEY, ABS, REL, SW, MSC, LED, BTN, REP, SND, ID, EV,
    BUS, SYN, FF, FF_STATUS, INPUT_PROP

This module also provides reverse and forward mappings of the names and values
of the above mentioned constants::

    >>> evdev.ecodes.KEY_A
    30

    >>> evdev.ecodes.ecodes['KEY_A']
    30

    >>> evdev.ecodes.KEY[30]
    'KEY_A'

    >>> evdev.ecodes.REL[0]
    'REL_X'

    >>> evdev.ecodes.EV[evdev.ecodes.EV_KEY]
    'EV_KEY'

    >>> evdev.ecodes.bytype[evdev.ecodes.EV_REL][0]
    'REL_X'

Keep in mind that values in reverse mappings may point to one or more event
codes. For example::

    >>> evdev.ecodes.FF[80]
    ('FF_EFFECT_MIN', 'FF_RUMBLE')

    >>> evdev.ecodes.FF[81]
    'FF_PERIODIC'
"""

from typing import Final, Dict, Tuple, Union

ABS_BRAKE: Final[int] = 10
ABS_CNT: Final[int] = 64
ABS_DISTANCE: Final[int] = 25
ABS_GAS: Final[int] = 9
ABS_HAT0X: Final[int] = 16
ABS_HAT0Y: Final[int] = 17
ABS_HAT1X: Final[int] = 18
ABS_HAT1Y: Final[int] = 19
ABS_HAT2X: Final[int] = 20
ABS_HAT2Y: Final[int] = 21
ABS_HAT3X: Final[int] = 22
ABS_HAT3Y: Final[int] = 23
ABS_MAX: Final[int] = 63
ABS_MISC: Final[int] = 40
ABS_MT_BLOB_ID: Final[int] = 56
ABS_MT_DISTANCE: Final[int] = 59
ABS_MT_ORIENTATION: Final[int] = 52
ABS_MT_POSITION_X: Final[int] = 53
ABS_MT_POSITION_Y: Final[int] = 54
ABS_MT_PRESSURE: Final[int] = 58
ABS_MT_SLOT: Final[int] = 47
ABS_MT_TOOL_TYPE: Final[int] = 55
ABS_MT_TOOL_X: Final[int] = 60
ABS_MT_TOOL_Y: Final[int] = 61
ABS_MT_TOUCH_MAJOR: Final[int] = 48
ABS_MT_TOUCH_MINOR: Final[int] = 49
ABS_MT_TRACKING_ID: Final[int] = 57
ABS_MT_WIDTH_MAJOR: Final[int] = 50
ABS_MT_WIDTH_MINOR: Final[int] = 51
ABS_PRESSURE: Final[int] = 24
ABS_PROFILE: Final[int] = 33
ABS_RESERVED: Final[int] = 46
ABS_RUDDER: Final[int] = 7
ABS_RX: Final[int] = 3
ABS_RY: Final[int] = 4
ABS_RZ: Final[int] = 5
ABS_THROTTLE: Final[int] = 6
ABS_TILT_X: Final[int] = 26
ABS_TILT_Y: Final[int] = 27
ABS_TOOL_WIDTH: Final[int] = 28
ABS_VOLUME: Final[int] = 32
ABS_WHEEL: Final[int] = 8
ABS_X: Final[int] = 0
ABS_Y: Final[int] = 1
ABS_Z: Final[int] = 2
BTN_0: Final[int] = 256
BTN_1: Final[int] = 257
BTN_2: Final[int] = 258
BTN_3: Final[int] = 259
BTN_4: Final[int] = 260
BTN_5: Final[int] = 261
BTN_6: Final[int] = 262
BTN_7: Final[int] = 263
BTN_8: Final[int] = 264
BTN_9: Final[int] = 265
BTN_A: Final[int] = 304
BTN_B: Final[int] = 305
BTN_BACK: Final[int] = 278
BTN_BASE: Final[int] = 294
BTN_BASE2: Final[int] = 295
BTN_BASE3: Final[int] = 296
BTN_BASE4: Final[int] = 297
BTN_BASE5: Final[int] = 298
BTN_BASE6: Final[int] = 299
BTN_C: Final[int] = 306
BTN_DEAD: Final[int] = 303
BTN_DIGI: Final[int] = 320
BTN_DPAD_DOWN: Final[int] = 545
BTN_DPAD_LEFT: Final[int] = 546
BTN_DPAD_RIGHT: Final[int] = 547
BTN_DPAD_UP: Final[int] = 544
BTN_EAST: Final[int] = 305
BTN_EXTRA: Final[int] = 276
BTN_FORWARD: Final[int] = 277
BTN_GAMEPAD: Final[int] = 304
BTN_GEAR_DOWN: Final[int] = 336
BTN_GEAR_UP: Final[int] = 337
BTN_JOYSTICK: Final[int] = 288
BTN_LEFT: Final[int] = 272
BTN_MIDDLE: Final[int] = 274
BTN_MISC: Final[int] = 256
BTN_MODE: Final[int] = 316
BTN_MOUSE: Final[int] = 272
BTN_NORTH: Final[int] = 307
BTN_PINKIE: Final[int] = 293
BTN_RIGHT: Final[int] = 273
BTN_SELECT: Final[int] = 314
BTN_SIDE: Final[int] = 275
BTN_SOUTH: Final[int] = 304
BTN_START: Final[int] = 315
BTN_STYLUS: Final[int] = 331
BTN_STYLUS2: Final[int] = 332
BTN_STYLUS3: Final[int] = 329
BTN_TASK: Final[int] = 279
BTN_THUMB: Final[int] = 289
BTN_THUMB2: Final[int] = 290
BTN_THUMBL: Final[int] = 317
BTN_THUMBR: Final[int] = 318
BTN_TL: Final[int] = 310
BTN_TL2: Final[int] = 312
BTN_TOOL_AIRBRUSH: Final[int] = 324
BTN_TOOL_BRUSH: Final[int] = 322
BTN_TOOL_DOUBLETAP: Final[int] = 333
BTN_TOOL_FINGER: Final[int] = 325
BTN_TOOL_LENS: Final[int] = 327
BTN_TOOL_MOUSE: Final[int] = 326
BTN_TOOL_PEN: Final[int] = 320
BTN_TOOL_PENCIL: Final[int] = 323
BTN_TOOL_QUADTAP: Final[int] = 335
BTN_TOOL_QUINTTAP: Final[int] = 328
BTN_TOOL_RUBBER: Final[int] = 321
BTN_TOOL_TRIPLETAP: Final[int] = 334
BTN_TOP: Final[int] = 291
BTN_TOP2: Final[int] = 292
BTN_TOUCH: Final[int] = 330
BTN_TR: Final[int] = 311
BTN_TR2: Final[int] = 313
BTN_TRIGGER: Final[int] = 288
BTN_TRIGGER_HAPPY: Final[int] = 704
BTN_TRIGGER_HAPPY1: Final[int] = 704
BTN_TRIGGER_HAPPY10: Final[int] = 713
BTN_TRIGGER_HAPPY11: Final[int] = 714
BTN_TRIGGER_HAPPY12: Final[int] = 715
BTN_TRIGGER_HAPPY13: Final[int] = 716
BTN_TRIGGER_HAPPY14: Final[int] = 717
BTN_TRIGGER_HAPPY15: Final[int] = 718
BTN_TRIGGER_HAPPY16: Final[int] = 719
BTN_TRIGGER_HAPPY17: Final[int] = 720
BTN_TRIGGER_HAPPY18: Final[int] = 721
BTN_TRIGGER_HAPPY19: Final[int] = 722
BTN_TRIGGER_HAPPY2: Final[int] = 705
BTN_TRIGGER_HAPPY20: Final[int] = 723
BTN_TRIGGER_HAPPY21: Final[int] = 724
BTN_TRIGGER_HAPPY22: Final[int] = 725
BTN_TRIGGER_HAPPY23: Final[int] = 726
BTN_TRIGGER_HAPPY24: Final[int] = 727
BTN_TRIGGER_HAPPY25: Final[int] = 728
BTN_TRIGGER_HAPPY26: Final[int] = 729
BTN_TRIGGER_HAPPY27: Final[int] = 730
BTN_TRIGGER_HAPPY28: Final[int] = 731
BTN_TRIGGER_HAPPY29: Final[int] = 732
BTN_TRIGGER_HAPPY3: Final[int] = 706
BTN_TRIGGER_HAPPY30: Final[int] = 733
BTN_TRIGGER_HAPPY31: Final[int] = 734
BTN_TRIGGER_HAPPY32: Final[int] = 735
BTN_TRIGGER_HAPPY33: Final[int] = 736
BTN_TRIGGER_HAPPY34: Final[int] = 737
BTN_TRIGGER_HAPPY35: Final[int] = 738
BTN_TRIGGER_HAPPY36: Final[int] = 739
BTN_TRIGGER_HAPPY37: Final[int] = 740
BTN_TRIGGER_HAPPY38: Final[int] = 741
BTN_TRIGGER_HAPPY39: Final[int] = 742
BTN_TRIGGER_HAPPY4: Final[int] = 707
BTN_TRIGGER_HAPPY40: Final[int] = 743
BTN_TRIGGER_HAPPY5: Final[int] = 708
BTN_TRIGGER_HAPPY6: Final[int] = 709
BTN_TRIGGER_HAPPY7: Final[int] = 710
BTN_TRIGGER_HAPPY8: Final[int] = 711
BTN_TRIGGER_HAPPY9: Final[int] = 712
BTN_WEST: Final[int] = 308
BTN_WHEEL: Final[int] = 336
BTN_X: Final[int] = 307
BTN_Y: Final[int] = 308
BTN_Z: Final[int] = 309
BUS_ADB: Final[int] = 23
BUS_AMD_SFH: Final[int] = 32
BUS_AMIGA: Final[int] = 22
BUS_ATARI: Final[int] = 27
BUS_BLUETOOTH: Final[int] = 5
BUS_CEC: Final[int] = 30
BUS_GAMEPORT: Final[int] = 20
BUS_GSC: Final[int] = 26
BUS_HIL: Final[int] = 4
BUS_HOST: Final[int] = 25
BUS_I2C: Final[int] = 24
BUS_I8042: Final[int] = 17
BUS_INTEL_ISHTP: Final[int] = 31
BUS_ISA: Final[int] = 16
BUS_ISAPNP: Final[int] = 2
BUS_PARPORT: Final[int] = 21
BUS_PCI: Final[int] = 1
BUS_RMI: Final[int] = 29
BUS_RS232: Final[int] = 19
BUS_SPI: Final[int] = 28
BUS_USB: Final[int] = 3
BUS_VIRTUAL: Final[int] = 6
BUS_XTKBD: Final[int] = 18
EV_ABS: Final[int] = 3
EV_CNT: Final[int] = 32
EV_FF: Final[int] = 21
EV_FF_STATUS: Final[int] = 23
EV_KEY: Final[int] = 1
EV_LED: Final[int] = 17
EV_MAX: Final[int] = 31
EV_MSC: Final[int] = 4
EV_PWR: Final[int] = 22
EV_REL: Final[int] = 2
EV_REP: Final[int] = 20
EV_SND: Final[int] = 18
EV_SW: Final[int] = 5
EV_SYN: Final[int] = 0
EV_UINPUT: Final[int] = 257
EV_VERSION: Final[int] = 65537
FF_AUTOCENTER: Final[int] = 97
FF_CNT: Final[int] = 128
FF_CONSTANT: Final[int] = 82
FF_CUSTOM: Final[int] = 93
FF_DAMPER: Final[int] = 85
FF_EFFECT_MAX: Final[int] = 87
FF_EFFECT_MIN: Final[int] = 80
FF_FRICTION: Final[int] = 84
FF_GAIN: Final[int] = 96
FF_INERTIA: Final[int] = 86
FF_MAX: Final[int] = 127
FF_MAX_EFFECTS: Final[int] = 96
FF_PERIODIC: Final[int] = 81
FF_RAMP: Final[int] = 87
FF_RUMBLE: Final[int] = 80
FF_SAW_DOWN: Final[int] = 92
FF_SAW_UP: Final[int] = 91
FF_SINE: Final[int] = 90
FF_SPRING: Final[int] = 83
FF_SQUARE: Final[int] = 88
FF_STATUS_MAX: Final[int] = 1
FF_STATUS_PLAYING: Final[int] = 1
FF_STATUS_STOPPED: Final[int] = 0
FF_TRIANGLE: Final[int] = 89
FF_WAVEFORM_MAX: Final[int] = 93
FF_WAVEFORM_MIN: Final[int] = 88
ID_BUS: Final[int] = 0
ID_PRODUCT: Final[int] = 2
ID_VENDOR: Final[int] = 1
ID_VERSION: Final[int] = 3
INPUT_PROP_ACCELEROMETER: Final[int] = 6
INPUT_PROP_BUTTONPAD: Final[int] = 2
INPUT_PROP_CNT: Final[int] = 32
INPUT_PROP_DIRECT: Final[int] = 1
INPUT_PROP_MAX: Final[int] = 31
INPUT_PROP_POINTER: Final[int] = 0
INPUT_PROP_POINTING_STICK: Final[int] = 5
INPUT_PROP_SEMI_MT: Final[int] = 3
INPUT_PROP_TOPBUTTONPAD: Final[int] = 4
KEY_0: Final[int] = 11
KEY_1: Final[int] = 2
KEY_102ND: Final[int] = 86
KEY_10CHANNELSDOWN: Final[int] = 441
KEY_10CHANNELSUP: Final[int] = 440
KEY_2: Final[int] = 3
KEY_3: Final[int] = 4
KEY_3D_MODE: Final[int] = 623
KEY_4: Final[int] = 5
KEY_5: Final[int] = 6
KEY_6: Final[int] = 7
KEY_7: Final[int] = 8
KEY_8: Final[int] = 9
KEY_9: Final[int] = 10
KEY_A: Final[int] = 30
KEY_AB: Final[int] = 406
KEY_ACCESSIBILITY: Final[int] = 590
KEY_ADDRESSBOOK: Final[int] = 429
KEY_AGAIN: Final[int] = 129
KEY_ALL_APPLICATIONS: Final[int] = 204
KEY_ALS_TOGGLE: Final[int] = 560
KEY_ALTERASE: Final[int] = 222
KEY_ANGLE: Final[int] = 371
KEY_APOSTROPHE: Final[int] = 40
KEY_APPSELECT: Final[int] = 580
KEY_ARCHIVE: Final[int] = 361
KEY_ASPECT_RATIO: Final[int] = 375
KEY_ASSISTANT: Final[int] = 583
KEY_ATTENDANT_OFF: Final[int] = 540
KEY_ATTENDANT_ON: Final[int] = 539
KEY_ATTENDANT_TOGGLE: Final[int] = 541
KEY_AUDIO: Final[int] = 392
KEY_AUDIO_DESC: Final[int] = 622
KEY_AUTOPILOT_ENGAGE_TOGGLE: Final[int] = 637
KEY_AUX: Final[int] = 390
KEY_B: Final[int] = 48
KEY_BACK: Final[int] = 158
KEY_BACKSLASH: Final[int] = 43
KEY_BACKSPACE: Final[int] = 14
KEY_BASSBOOST: Final[int] = 209
KEY_BATTERY: Final[int] = 236
KEY_BLUE: Final[int] = 401
KEY_BLUETOOTH: Final[int] = 237
KEY_BOOKMARKS: Final[int] = 156
KEY_BREAK: Final[int] = 411
KEY_BRIGHTNESSDOWN: Final[int] = 224
KEY_BRIGHTNESSUP: Final[int] = 225
KEY_BRIGHTNESS_AUTO: Final[int] = 244
KEY_BRIGHTNESS_CYCLE: Final[int] = 243
KEY_BRIGHTNESS_MAX: Final[int] = 593
KEY_BRIGHTNESS_MENU: Final[int] = 649
KEY_BRIGHTNESS_MIN: Final[int] = 592
KEY_BRIGHTNESS_TOGGLE: Final[int] = 431
KEY_BRIGHTNESS_ZERO: Final[int] = 244
KEY_BRL_DOT1: Final[int] = 497
KEY_BRL_DOT10: Final[int] = 506
KEY_BRL_DOT2: Final[int] = 498
KEY_BRL_DOT3: Final[int] = 499
KEY_BRL_DOT4: Final[int] = 500
KEY_BRL_DOT5: Final[int] = 501
KEY_BRL_DOT6: Final[int] = 502
KEY_BRL_DOT7: Final[int] = 503
KEY_BRL_DOT8: Final[int] = 504
KEY_BRL_DOT9: Final[int] = 505
KEY_BUTTONCONFIG: Final[int] = 576
KEY_C: Final[int] = 46
KEY_CALC: Final[int] = 140
KEY_CALENDAR: Final[int] = 397
KEY_CAMERA: Final[int] = 212
KEY_CAMERA_ACCESS_DISABLE: Final[int] = 588
KEY_CAMERA_ACCESS_ENABLE: Final[int] = 587
KEY_CAMERA_ACCESS_TOGGLE: Final[int] = 589
KEY_CAMERA_DOWN: Final[int] = 536
KEY_CAMERA_FOCUS: Final[int] = 528
KEY_CAMERA_LEFT: Final[int] = 537
KEY_CAMERA_RIGHT: Final[int] = 538
KEY_CAMERA_UP: Final[int] = 535
KEY_CAMERA_ZOOMIN: Final[int] = 533
KEY_CAMERA_ZOOMOUT: Final[int] = 534
KEY_CANCEL: Final[int] = 223
KEY_CAPSLOCK: Final[int] = 58
KEY_CD: Final[int] = 383
KEY_CHANNEL: Final[int] = 363
KEY_CHANNELDOWN: Final[int] = 403
KEY_CHANNELUP: Final[int] = 402
KEY_CHAT: Final[int] = 216
KEY_CLEAR: Final[int] = 355
KEY_CLEARVU_SONAR: Final[int] = 646
KEY_CLOSE: Final[int] = 206
KEY_CLOSECD: Final[int] = 160
KEY_CNT: Final[int] = 768
KEY_COFFEE: Final[int] = 152
KEY_COMMA: Final[int] = 51
KEY_COMPOSE: Final[int] = 127
KEY_COMPUTER: Final[int] = 157
KEY_CONFIG: Final[int] = 171
KEY_CONNECT: Final[int] = 218
KEY_CONTEXT_MENU: Final[int] = 438
KEY_CONTROLPANEL: Final[int] = 579
KEY_COPY: Final[int] = 133
KEY_CUT: Final[int] = 137
KEY_CYCLEWINDOWS: Final[int] = 154
KEY_D: Final[int] = 32
KEY_DASHBOARD: Final[int] = 204
KEY_DATA: Final[int] = 631
KEY_DATABASE: Final[int] = 426
KEY_DELETE: Final[int] = 111
KEY_DELETEFILE: Final[int] = 146
KEY_DEL_EOL: Final[int] = 448
KEY_DEL_EOS: Final[int] = 449
KEY_DEL_LINE: Final[int] = 451
KEY_DICTATE: Final[int] = 586
KEY_DIGITS: Final[int] = 413
KEY_DIRECTION: Final[int] = 153
KEY_DIRECTORY: Final[int] = 394
KEY_DISPLAYTOGGLE: Final[int] = 431
KEY_DISPLAY_OFF: Final[int] = 245
KEY_DOCUMENTS: Final[int] = 235
KEY_DOLLAR: Final[int] = 434
KEY_DOT: Final[int] = 52
KEY_DOWN: Final[int] = 108
KEY_DO_NOT_DISTURB: Final[int] = 591
KEY_DUAL_RANGE_RADAR: Final[int] = 643
KEY_DVD: Final[int] = 389
KEY_E: Final[int] = 18
KEY_EDIT: Final[int] = 176
KEY_EDITOR: Final[int] = 422
KEY_EJECTCD: Final[int] = 161
KEY_EJECTCLOSECD: Final[int] = 162
KEY_EMAIL: Final[int] = 215
KEY_EMOJI_PICKER: Final[int] = 585
KEY_END: Final[int] = 107
KEY_ENTER: Final[int] = 28
KEY_EPG: Final[int] = 365
KEY_EQUAL: Final[int] = 13
KEY_ESC: Final[int] = 1
KEY_EURO: Final[int] = 435
KEY_EXIT: Final[int] = 174
KEY_F: Final[int] = 33
KEY_F1: Final[int] = 59
KEY_F10: Final[int] = 68
KEY_F11: Final[int] = 87
KEY_F12: Final[int] = 88
KEY_F13: Final[int] = 183
KEY_F14: Final[int] = 184
KEY_F15: Final[int] = 185
KEY_F16: Final[int] = 186
KEY_F17: Final[int] = 187
KEY_F18: Final[int] = 188
KEY_F19: Final[int] = 189
KEY_F2: Final[int] = 60
KEY_F20: Final[int] = 190
KEY_F21: Final[int] = 191
KEY_F22: Final[int] = 192
KEY_F23: Final[int] = 193
KEY_F24: Final[int] = 194
KEY_F3: Final[int] = 61
KEY_F4: Final[int] = 62
KEY_F5: Final[int] = 63
KEY_F6: Final[int] = 64
KEY_F7: Final[int] = 65
KEY_F8: Final[int] = 66
KEY_F9: Final[int] = 67
KEY_FASTFORWARD: Final[int] = 208
KEY_FASTREVERSE: Final[int] = 629
KEY_FAVORITES: Final[int] = 364
KEY_FILE: Final[int] = 144
KEY_FINANCE: Final[int] = 219
KEY_FIND: Final[int] = 136
KEY_FIRST: Final[int] = 404
KEY_FISHING_CHART: Final[int] = 641
KEY_FN: Final[int] = 464
KEY_FN_1: Final[int] = 478
KEY_FN_2: Final[int] = 479
KEY_FN_B: Final[int] = 484
KEY_FN_D: Final[int] = 480
KEY_FN_E: Final[int] = 481
KEY_FN_ESC: Final[int] = 465
KEY_FN_F: Final[int] = 482
KEY_FN_F1: Final[int] = 466
KEY_FN_F10: Final[int] = 475
KEY_FN_F11: Final[int] = 476
KEY_FN_F12: Final[int] = 477
KEY_FN_F2: Final[int] = 467
KEY_FN_F3: Final[int] = 468
KEY_FN_F4: Final[int] = 469
KEY_FN_F5: Final[int] = 470
KEY_FN_F6: Final[int] = 471
KEY_FN_F7: Final[int] = 472
KEY_FN_F8: Final[int] = 473
KEY_FN_F9: Final[int] = 474
KEY_FN_RIGHT_SHIFT: Final[int] = 485
KEY_FN_S: Final[int] = 483
KEY_FORWARD: Final[int] = 159
KEY_FORWARDMAIL: Final[int] = 233
KEY_FRAMEBACK: Final[int] = 436
KEY_FRAMEFORWARD: Final[int] = 437
KEY_FRONT: Final[int] = 132
KEY_FULL_SCREEN: Final[int] = 372
KEY_G: Final[int] = 34
KEY_GAMES: Final[int] = 417
KEY_GOTO: Final[int] = 354
KEY_GRAPHICSEDITOR: Final[int] = 424
KEY_GRAVE: Final[int] = 41
KEY_GREEN: Final[int] = 399
KEY_H: Final[int] = 35
KEY_HANGEUL: Final[int] = 122
KEY_HANGUEL: Final[int] = 122
KEY_HANGUP_PHONE: Final[int] = 446
KEY_HANJA: Final[int] = 123
KEY_HELP: Final[int] = 138
KEY_HENKAN: Final[int] = 92
KEY_HIRAGANA: Final[int] = 91
KEY_HOME: Final[int] = 102
KEY_HOMEPAGE: Final[int] = 172
KEY_HP: Final[int] = 211
KEY_I: Final[int] = 23
KEY_IMAGES: Final[int] = 442
KEY_INFO: Final[int] = 358
KEY_INSERT: Final[int] = 110
KEY_INS_LINE: Final[int] = 450
KEY_ISO: Final[int] = 170
KEY_J: Final[int] = 36
KEY_JOURNAL: Final[int] = 578
KEY_K: Final[int] = 37
KEY_KATAKANA: Final[int] = 90
KEY_KATAKANAHIRAGANA: Final[int] = 93
KEY_KBDILLUMDOWN: Final[int] = 229
KEY_KBDILLUMTOGGLE: Final[int] = 228
KEY_KBDILLUMUP: Final[int] = 230
KEY_KBDINPUTASSIST_ACCEPT: Final[int] = 612
KEY_KBDINPUTASSIST_CANCEL: Final[int] = 613
KEY_KBDINPUTASSIST_NEXT: Final[int] = 609
KEY_KBDINPUTASSIST_NEXTGROUP: Final[int] = 611
KEY_KBDINPUTASSIST_PREV: Final[int] = 608
KEY_KBDINPUTASSIST_PREVGROUP: Final[int] = 610
KEY_KBD_LAYOUT_NEXT: Final[int] = 584
KEY_KBD_LCD_MENU1: Final[int] = 696
KEY_KBD_LCD_MENU2: Final[int] = 697
KEY_KBD_LCD_MENU3: Final[int] = 698
KEY_KBD_LCD_MENU4: Final[int] = 699
KEY_KBD_LCD_MENU5: Final[int] = 700
KEY_KEYBOARD: Final[int] = 374
KEY_KP0: Final[int] = 82
KEY_KP1: Final[int] = 79
KEY_KP2: Final[int] = 80
KEY_KP3: Final[int] = 81
KEY_KP4: Final[int] = 75
KEY_KP5: Final[int] = 76
KEY_KP6: Final[int] = 77
KEY_KP7: Final[int] = 71
KEY_KP8: Final[int] = 72
KEY_KP9: Final[int] = 73
KEY_KPASTERISK: Final[int] = 55
KEY_KPCOMMA: Final[int] = 121
KEY_KPDOT: Final[int] = 83
KEY_KPENTER: Final[int] = 96
KEY_KPEQUAL: Final[int] = 117
KEY_KPJPCOMMA: Final[int] = 95
KEY_KPLEFTPAREN: Final[int] = 179
KEY_KPMINUS: Final[int] = 74
KEY_KPPLUS: Final[int] = 78
KEY_KPPLUSMINUS: Final[int] = 118
KEY_KPRIGHTPAREN: Final[int] = 180
KEY_KPSLASH: Final[int] = 98
KEY_L: Final[int] = 38
KEY_LANGUAGE: Final[int] = 368
KEY_LAST: Final[int] = 405
KEY_LEFT: Final[int] = 105
KEY_LEFTALT: Final[int] = 56
KEY_LEFTBRACE: Final[int] = 26
KEY_LEFTCTRL: Final[int] = 29
KEY_LEFTMETA: Final[int] = 125
KEY_LEFTSHIFT: Final[int] = 42
KEY_LEFT_DOWN: Final[int] = 617
KEY_LEFT_UP: Final[int] = 616
KEY_LIGHTS_TOGGLE: Final[int] = 542
KEY_LINEFEED: Final[int] = 101
KEY_LINK_PHONE: Final[int] = 447
KEY_LIST: Final[int] = 395
KEY_LOGOFF: Final[int] = 433
KEY_M: Final[int] = 50
KEY_MACRO: Final[int] = 112
KEY_MACRO1: Final[int] = 656
KEY_MACRO10: Final[int] = 665
KEY_MACRO11: Final[int] = 666
KEY_MACRO12: Final[int] = 667
KEY_MACRO13: Final[int] = 668
KEY_MACRO14: Final[int] = 669
KEY_MACRO15: Final[int] = 670
KEY_MACRO16: Final[int] = 671
KEY_MACRO17: Final[int] = 672
KEY_MACRO18: Final[int] = 673
KEY_MACRO19: Final[int] = 674
KEY_MACRO2: Final[int] = 657
KEY_MACRO20: Final[int] = 675
KEY_MACRO21: Final[int] = 676
KEY_MACRO22: Final[int] = 677
KEY_MACRO23: Final[int] = 678
KEY_MACRO24: Final[int] = 679
KEY_MACRO25: Final[int] = 680
KEY_MACRO26: Final[int] = 681
KEY_MACRO27: Final[int] = 682
KEY_MACRO28: Final[int] = 683
KEY_MACRO29: Final[int] = 684
KEY_MACRO3: Final[int] = 658
KEY_MACRO30: Final[int] = 685
KEY_MACRO4: Final[int] = 659
KEY_MACRO5: Final[int] = 660
KEY_MACRO6: Final[int] = 661
KEY_MACRO7: Final[int] = 662
KEY_MACRO8: Final[int] = 663
KEY_MACRO9: Final[int] = 664
KEY_MACRO_PRESET1: Final[int] = 691
KEY_MACRO_PRESET2: Final[int] = 692
KEY_MACRO_PRESET3: Final[int] = 693
KEY_MACRO_PRESET_CYCLE: Final[int] = 690
KEY_MACRO_RECORD_START: Final[int] = 688
KEY_MACRO_RECORD_STOP: Final[int] = 689
KEY_MAIL: Final[int] = 155
KEY_MARK_WAYPOINT: Final[int] = 638
KEY_MAX: Final[int] = 767
KEY_MEDIA: Final[int] = 226
KEY_MEDIA_REPEAT: Final[int] = 439
KEY_MEDIA_TOP_MENU: Final[int] = 619
KEY_MEMO: Final[int] = 396
KEY_MENU: Final[int] = 139
KEY_MESSENGER: Final[int] = 430
KEY_MHP: Final[int] = 367
KEY_MICMUTE: Final[int] = 248
KEY_MINUS: Final[int] = 12
KEY_MIN_INTERESTING: Final[int] = 113
KEY_MODE: Final[int] = 373
KEY_MOVE: Final[int] = 175
KEY_MP3: Final[int] = 391
KEY_MSDOS: Final[int] = 151
KEY_MUHENKAN: Final[int] = 94
KEY_MUTE: Final[int] = 113
KEY_N: Final[int] = 49
KEY_NAV_CHART: Final[int] = 640
KEY_NAV_INFO: Final[int] = 648
KEY_NEW: Final[int] = 181
KEY_NEWS: Final[int] = 427
KEY_NEXT: Final[int] = 407
KEY_NEXTSONG: Final[int] = 163
KEY_NEXT_ELEMENT: Final[int] = 635
KEY_NEXT_FAVORITE: Final[int] = 624
KEY_NOTIFICATION_CENTER: Final[int] = 444
KEY_NUMERIC_0: Final[int] = 512
KEY_NUMERIC_1: Final[int] = 513
KEY_NUMERIC_11: Final[int] = 620
KEY_NUMERIC_12: Final[int] = 621
KEY_NUMERIC_2: Final[int] = 514
KEY_NUMERIC_3: Final[int] = 515
KEY_NUMERIC_4: Final[int] = 516
KEY_NUMERIC_5: Final[int] = 517
KEY_NUMERIC_6: Final[int] = 518
KEY_NUMERIC_7: Final[int] = 519
KEY_NUMERIC_8: Final[int] = 520
KEY_NUMERIC_9: Final[int] = 521
KEY_NUMERIC_A: Final[int] = 524
KEY_NUMERIC_B: Final[int] = 525
KEY_NUMERIC_C: Final[int] = 526
KEY_NUMERIC_D: Final[int] = 527
KEY_NUMERIC_POUND: Final[int] = 523
KEY_NUMERIC_STAR: Final[int] = 522
KEY_NUMLOCK: Final[int] = 69
KEY_O: Final[int] = 24
KEY_OK: Final[int] = 352
KEY_ONSCREEN_KEYBOARD: Final[int] = 632
KEY_OPEN: Final[int] = 134
KEY_OPTION: Final[int] = 357
KEY_P: Final[int] = 25
KEY_PAGEDOWN: Final[int] = 109
KEY_PAGEUP: Final[int] = 104
KEY_PASTE: Final[int] = 135
KEY_PAUSE: Final[int] = 119
KEY_PAUSECD: Final[int] = 201
KEY_PAUSE_RECORD: Final[int] = 626
KEY_PC: Final[int] = 376
KEY_PHONE: Final[int] = 169
KEY_PICKUP_PHONE: Final[int] = 445
KEY_PLAY: Final[int] = 207
KEY_PLAYCD: Final[int] = 200
KEY_PLAYER: Final[int] = 387
KEY_PLAYPAUSE: Final[int] = 164
KEY_POWER: Final[int] = 116
KEY_POWER2: Final[int] = 356
KEY_PRESENTATION: Final[int] = 425
KEY_PREVIOUS: Final[int] = 412
KEY_PREVIOUSSONG: Final[int] = 165
KEY_PREVIOUS_ELEMENT: Final[int] = 636
KEY_PRINT: Final[int] = 210
KEY_PRIVACY_SCREEN_TOGGLE: Final[int] = 633
KEY_PROG1: Final[int] = 148
KEY_PROG2: Final[int] = 149
KEY_PROG3: Final[int] = 202
KEY_PROG4: Final[int] = 203
KEY_PROGRAM: Final[int] = 362
KEY_PROPS: Final[int] = 130
KEY_PVR: Final[int] = 366
KEY_Q: Final[int] = 16
KEY_QUESTION: Final[int] = 214
KEY_R: Final[int] = 19
KEY_RADAR_OVERLAY: Final[int] = 644
KEY_RADIO: Final[int] = 385
KEY_RECORD: Final[int] = 167
KEY_RED: Final[int] = 398
KEY_REDO: Final[int] = 182
KEY_REFRESH: Final[int] = 173
KEY_REFRESH_RATE_TOGGLE: Final[int] = 562
KEY_REPLY: Final[int] = 232
KEY_RESERVED: Final[int] = 0
KEY_RESTART: Final[int] = 408
KEY_REWIND: Final[int] = 168
KEY_RFKILL: Final[int] = 247
KEY_RIGHT: Final[int] = 106
KEY_RIGHTALT: Final[int] = 100
KEY_RIGHTBRACE: Final[int] = 27
KEY_RIGHTCTRL: Final[int] = 97
KEY_RIGHTMETA: Final[int] = 126
KEY_RIGHTSHIFT: Final[int] = 54
KEY_RIGHT_DOWN: Final[int] = 615
KEY_RIGHT_UP: Final[int] = 614
KEY_RO: Final[int] = 89
KEY_ROOT_MENU: Final[int] = 618
KEY_ROTATE_DISPLAY: Final[int] = 153
KEY_ROTATE_LOCK_TOGGLE: Final[int] = 561
KEY_S: Final[int] = 31
KEY_SAT: Final[int] = 381
KEY_SAT2: Final[int] = 382
KEY_SAVE: Final[int] = 234
KEY_SCALE: Final[int] = 120
KEY_SCREEN: Final[int] = 375
KEY_SCREENLOCK: Final[int] = 152
KEY_SCREENSAVER: Final[int] = 581
KEY_SCROLLDOWN: Final[int] = 178
KEY_SCROLLLOCK: Final[int] = 70
KEY_SCROLLUP: Final[int] = 177
KEY_SEARCH: Final[int] = 217
KEY_SELECT: Final[int] = 353
KEY_SELECTIVE_SCREENSHOT: Final[int] = 634
KEY_SEMICOLON: Final[int] = 39
KEY_SEND: Final[int] = 231
KEY_SENDFILE: Final[int] = 145
KEY_SETUP: Final[int] = 141
KEY_SHOP: Final[int] = 221
KEY_SHUFFLE: Final[int] = 410
KEY_SIDEVU_SONAR: Final[int] = 647
KEY_SINGLE_RANGE_RADAR: Final[int] = 642
KEY_SLASH: Final[int] = 53
KEY_SLEEP: Final[int] = 142
KEY_SLOW: Final[int] = 409
KEY_SLOWREVERSE: Final[int] = 630
KEY_SOS: Final[int] = 639
KEY_SOUND: Final[int] = 213
KEY_SPACE: Final[int] = 57
KEY_SPELLCHECK: Final[int] = 432
KEY_SPORT: Final[int] = 220
KEY_SPREADSHEET: Final[int] = 423
KEY_STOP: Final[int] = 128
KEY_STOPCD: Final[int] = 166
KEY_STOP_RECORD: Final[int] = 625
KEY_SUBTITLE: Final[int] = 370
KEY_SUSPEND: Final[int] = 205
KEY_SWITCHVIDEOMODE: Final[int] = 227
KEY_SYSRQ: Final[int] = 99
KEY_T: Final[int] = 20
KEY_TAB: Final[int] = 15
KEY_TAPE: Final[int] = 384
KEY_TASKMANAGER: Final[int] = 577
KEY_TEEN: Final[int] = 414
KEY_TEXT: Final[int] = 388
KEY_TIME: Final[int] = 359
KEY_TITLE: Final[int] = 369
KEY_TOUCHPAD_OFF: Final[int] = 532
KEY_TOUCHPAD_ON: Final[int] = 531
KEY_TOUCHPAD_TOGGLE: Final[int] = 530
KEY_TRADITIONAL_SONAR: Final[int] = 645
KEY_TUNER: Final[int] = 386
KEY_TV: Final[int] = 377
KEY_TV2: Final[int] = 378
KEY_TWEN: Final[int] = 415
KEY_U: Final[int] = 22
KEY_UNDO: Final[int] = 131
KEY_UNKNOWN: Final[int] = 240
KEY_UNMUTE: Final[int] = 628
KEY_UP: Final[int] = 103
KEY_UWB: Final[int] = 239
KEY_V: Final[int] = 47
KEY_VCR: Final[int] = 379
KEY_VCR2: Final[int] = 380
KEY_VENDOR: Final[int] = 360
KEY_VIDEO: Final[int] = 393
KEY_VIDEOPHONE: Final[int] = 416
KEY_VIDEO_NEXT: Final[int] = 241
KEY_VIDEO_PREV: Final[int] = 242
KEY_VOD: Final[int] = 627
KEY_VOICECOMMAND: Final[int] = 582
KEY_VOICEMAIL: Final[int] = 428
KEY_VOLUMEDOWN: Final[int] = 114
KEY_VOLUMEUP: Final[int] = 115
KEY_W: Final[int] = 17
KEY_WAKEUP: Final[int] = 143
KEY_WIMAX: Final[int] = 246
KEY_WLAN: Final[int] = 238
KEY_WORDPROCESSOR: Final[int] = 421
KEY_WPS_BUTTON: Final[int] = 529
KEY_WWAN: Final[int] = 246
KEY_WWW: Final[int] = 150
KEY_X: Final[int] = 45
KEY_XFER: Final[int] = 147
KEY_Y: Final[int] = 21
KEY_YELLOW: Final[int] = 400
KEY_YEN: Final[int] = 124
KEY_Z: Final[int] = 44
KEY_ZENKAKUHANKAKU: Final[int] = 85
KEY_ZOOM: Final[int] = 372
KEY_ZOOMIN: Final[int] = 418
KEY_ZOOMOUT: Final[int] = 419
KEY_ZOOMRESET: Final[int] = 420
LED_CAPSL: Final[int] = 1
LED_CHARGING: Final[int] = 10
LED_CNT: Final[int] = 16
LED_COMPOSE: Final[int] = 3
LED_KANA: Final[int] = 4
LED_MAIL: Final[int] = 9
LED_MAX: Final[int] = 15
LED_MISC: Final[int] = 8
LED_MUTE: Final[int] = 7
LED_NUML: Final[int] = 0
LED_SCROLLL: Final[int] = 2
LED_SLEEP: Final[int] = 5
LED_SUSPEND: Final[int] = 6
MSC_CNT: Final[int] = 8
MSC_GESTURE: Final[int] = 2
MSC_MAX: Final[int] = 7
MSC_PULSELED: Final[int] = 1
MSC_RAW: Final[int] = 3
MSC_SCAN: Final[int] = 4
MSC_SERIAL: Final[int] = 0
MSC_TIMESTAMP: Final[int] = 5
REL_CNT: Final[int] = 16
REL_DIAL: Final[int] = 7
REL_HWHEEL: Final[int] = 6
REL_HWHEEL_HI_RES: Final[int] = 12
REL_MAX: Final[int] = 15
REL_MISC: Final[int] = 9
REL_RESERVED: Final[int] = 10
REL_RX: Final[int] = 3
REL_RY: Final[int] = 4
REL_RZ: Final[int] = 5
REL_WHEEL: Final[int] = 8
REL_WHEEL_HI_RES: Final[int] = 11
REL_X: Final[int] = 0
REL_Y: Final[int] = 1
REL_Z: Final[int] = 2
REP_CNT: Final[int] = 2
REP_DELAY: Final[int] = 0
REP_MAX: Final[int] = 1
REP_PERIOD: Final[int] = 1
SND_BELL: Final[int] = 1
SND_CLICK: Final[int] = 0
SND_CNT: Final[int] = 8
SND_MAX: Final[int] = 7
SND_TONE: Final[int] = 2
SW_CAMERA_LENS_COVER: Final[int] = 9
SW_CNT: Final[int] = 17
SW_DOCK: Final[int] = 5
SW_FRONT_PROXIMITY: Final[int] = 11
SW_HEADPHONE_INSERT: Final[int] = 2
SW_JACK_PHYSICAL_INSERT: Final[int] = 7
SW_KEYPAD_SLIDE: Final[int] = 10
SW_LID: Final[int] = 0
SW_LINEIN_INSERT: Final[int] = 13
SW_LINEOUT_INSERT: Final[int] = 6
SW_MACHINE_COVER: Final[int] = 16
SW_MAX: Final[int] = 16
SW_MICROPHONE_INSERT: Final[int] = 4
SW_MUTE_DEVICE: Final[int] = 14
SW_PEN_INSERTED: Final[int] = 15
SW_RADIO: Final[int] = 3
SW_RFKILL_ALL: Final[int] = 3
SW_ROTATE_LOCK: Final[int] = 12
SW_TABLET_MODE: Final[int] = 1
SW_VIDEOOUT_INSERT: Final[int] = 8
SYN_CNT: Final[int] = 16
SYN_CONFIG: Final[int] = 1
SYN_DROPPED: Final[int] = 3
SYN_MAX: Final[int] = 15
SYN_MT_REPORT: Final[int] = 2
SYN_REPORT: Final[int] = 0
UI_FF_ERASE: Final[int] = 2
UI_FF_UPLOAD: Final[int] = 1

#: Mapping of names to values.
ecodes: Dict[str, int] = { 'ABS_BRAKE': 10,
  'ABS_CNT': 64,
  'ABS_DISTANCE': 25,
  'ABS_GAS': 9,
  'ABS_HAT0X': 16,
  'ABS_HAT0Y': 17,
  'ABS_HAT1X': 18,
  'ABS_HAT1Y': 19,
  'ABS_HAT2X': 20,
  'ABS_HAT2Y': 21,
  'ABS_HAT3X': 22,
  'ABS_HAT3Y': 23,
  'ABS_MAX': 63,
  'ABS_MISC': 40,
  'ABS_MT_BLOB_ID': 56,
  'ABS_MT_DISTANCE': 59,
  'ABS_MT_ORIENTATION': 52,
  'ABS_MT_POSITION_X': 53,
  'ABS_MT_POSITION_Y': 54,
  'ABS_MT_PRESSURE': 58,
  'ABS_MT_SLOT': 47,
  'ABS_MT_TOOL_TYPE': 55,
  'ABS_MT_TOOL_X': 60,
  'ABS_MT_TOOL_Y': 61,
  'ABS_MT_TOUCH_MAJOR': 48,
  'ABS_MT_TOUCH_MINOR': 49,
  'ABS_MT_TRACKING_ID': 57,
  'ABS_MT_WIDTH_MAJOR': 50,
  'ABS_MT_WIDTH_MINOR': 51,
  'ABS_PRESSURE': 24,
  'ABS_PROFILE': 33,
  'ABS_RESERVED': 46,
  'ABS_RUDDER': 7,
  'ABS_RX': 3,
  'ABS_RY': 4,
  'ABS_RZ': 5,
  'ABS_THROTTLE': 6,
  'ABS_TILT_X': 26,
  'ABS_TILT_Y': 27,
  'ABS_TOOL_WIDTH': 28,
  'ABS_VOLUME': 32,
  'ABS_WHEEL': 8,
  'ABS_X': 0,
  'ABS_Y': 1,
  'ABS_Z': 2,
  'BTN_0': 256,
  'BTN_1': 257,
  'BTN_2': 258,
  'BTN_3': 259,
  'BTN_4': 260,
  'BTN_5': 261,
  'BTN_6': 262,
  'BTN_7': 263,
  'BTN_8': 264,
  'BTN_9': 265,
  'BTN_A': 304,
  'BTN_B': 305,
  'BTN_BACK': 278,
  'BTN_BASE': 294,
  'BTN_BASE2': 295,
  'BTN_BASE3': 296,
  'BTN_BASE4': 297,
  'BTN_BASE5': 298,
  'BTN_BASE6': 299,
  'BTN_C': 306,
  'BTN_DEAD': 303,
  'BTN_DIGI': 320,
  'BTN_DPAD_DOWN': 545,
  'BTN_DPAD_LEFT': 546,
  'BTN_DPAD_RIGHT': 547,
  'BTN_DPAD_UP': 544,
  'BTN_EAST': 305,
  'BTN_EXTRA': 276,
  'BTN_FORWARD': 277,
  'BTN_GAMEPAD': 304,
  'BTN_GEAR_DOWN': 336,
  'BTN_GEAR_UP': 337,
  'BTN_JOYSTICK': 288,
  'BTN_LEFT': 272,
  'BTN_MIDDLE': 274,
  'BTN_MISC': 256,
  'BTN_MODE': 316,
  'BTN_MOUSE': 272,
  'BTN_NORTH': 307,
  'BTN_PINKIE': 293,
  'BTN_RIGHT': 273,
  'BTN_SELECT': 314,
  'BTN_SIDE': 275,
  'BTN_SOUTH': 304,
  'BTN_START': 315,
  'BTN_STYLUS': 331,
  'BTN_STYLUS2': 332,
  'BTN_STYLUS3': 329,
  'BTN_TASK': 279,
  'BTN_THUMB': 289,
  'BTN_THUMB2': 290,
  'BTN_THUMBL': 317,
  'BTN_THUMBR': 318,
  'BTN_TL': 310,
  'BTN_TL2': 312,
  'BTN_TOOL_AIRBRUSH': 324,
  'BTN_TOOL_BRUSH': 322,
  'BTN_TOOL_DOUBLETAP': 333,
  'BTN_TOOL_FINGER': 325,
  'BTN_TOOL_LENS': 327,
  'BTN_TOOL_MOUSE': 326,
  'BTN_TOOL_PEN': 320,
  'BTN_TOOL_PENCIL': 323,
  'BTN_TOOL_QUADTAP': 335,
  'BTN_TOOL_QUINTTAP': 328,
  'BTN_TOOL_RUBBER': 321,
  'BTN_TOOL_TRIPLETAP': 334,
  'BTN_TOP': 291,
  'BTN_TOP2': 292,
  'BTN_TOUCH': 330,
  'BTN_TR': 311,
  'BTN_TR2': 313,
  'BTN_TRIGGER': 288,
  'BTN_TRIGGER_HAPPY': 704,
  'BTN_TRIGGER_HAPPY1': 704,
  'BTN_TRIGGER_HAPPY10': 713,
  'BTN_TRIGGER_HAPPY11': 714,
  'BTN_TRIGGER_HAPPY12': 715,
  'BTN_TRIGGER_HAPPY13': 716,
  'BTN_TRIGGER_HAPPY14': 717,
  'BTN_TRIGGER_HAPPY15': 718,
  'BTN_TRIGGER_HAPPY16': 719,
  'BTN_TRIGGER_HAPPY17': 720,
  'BTN_TRIGGER_HAPPY18': 721,
  'BTN_TRIGGER_HAPPY19': 722,
  'BTN_TRIGGER_HAPPY2': 705,
  'BTN_TRIGGER_HAPPY20': 723,
  'BTN_TRIGGER_HAPPY21': 724,
  'BTN_TRIGGER_HAPPY22': 725,
  'BTN_TRIGGER_HAPPY23': 726,
  'BTN_TRIGGER_HAPPY24': 727,
  'BTN_TRIGGER_HAPPY25': 728,
  'BTN_TRIGGER_HAPPY26': 729,
  'BTN_TRIGGER_HAPPY27': 730,
  'BTN_TRIGGER_HAPPY28': 731,
  'BTN_TRIGGER_HAPPY29': 732,
  'BTN_TRIGGER_HAPPY3': 706,
  'BTN_TRIGGER_HAPPY30': 733,
  'BTN_TRIGGER_HAPPY31': 734,
  'BTN_TRIGGER_HAPPY32': 735,
  'BTN_TRIGGER_HAPPY33': 736,
  'BTN_TRIGGER_HAPPY34': 737,
  'BTN_TRIGGER_HAPPY35': 738,
  'BTN_TRIGGER_HAPPY36': 739,
  'BTN_TRIGGER_HAPPY37': 740,
  'BTN_TRIGGER_HAPPY38': 741,
  'BTN_TRIGGER_HAPPY39': 742,
  'BTN_TRIGGER_HAPPY4': 707,
  'BTN_TRIGGER_HAPPY40': 743,
  'BTN_TRIGGER_HAPPY5': 708,
  'BTN_TRIGGER_HAPPY6': 709,
  'BTN_TRIGGER_HAPPY7': 710,
  'BTN_TRIGGER_HAPPY8': 711,
  'BTN_TRIGGER_HAPPY9': 712,
  'BTN_WEST': 308,
  'BTN_WHEEL': 336,
  'BTN_X': 307,
  'BTN_Y': 308,
  'BTN_Z': 309,
  'BUS_ADB': 23,
  'BUS_AMD_SFH': 32,
  'BUS_AMIGA': 22,
  'BUS_ATARI': 27,
  'BUS_BLUETOOTH': 5,
  'BUS_CEC': 30,
  'BUS_GAMEPORT': 20,
  'BUS_GSC': 26,
  'BUS_HIL': 4,
  'BUS_HOST': 25,
  'BUS_I2C': 24,
  'BUS_I8042': 17,
  'BUS_INTEL_ISHTP': 31,
  'BUS_ISA': 16,
  'BUS_ISAPNP': 2,
  'BUS_PARPORT': 21,
  'BUS_PCI': 1,
  'BUS_RMI': 29,
  'BUS_RS232': 19,
  'BUS_SPI': 28,
  'BUS_USB': 3,
  'BUS_VIRTUAL': 6,
  'BUS_XTKBD': 18,
  'EV_ABS': 3,
  'EV_CNT': 32,
  'EV_FF': 21,
  'EV_FF_STATUS': 23,
  'EV_KEY': 1,
  'EV_LED': 17,
  'EV_MAX': 31,
  'EV_MSC': 4,
  'EV_PWR': 22,
  'EV_REL': 2,
  'EV_REP': 20,
  'EV_SND': 18,
  'EV_SW': 5,
  'EV_SYN': 0,
  'EV_UINPUT': 257,
  'EV_VERSION': 65537,
  'FF_AUTOCENTER': 97,
  'FF_CNT': 128,
  'FF_CONSTANT': 82,
  'FF_CUSTOM': 93,
  'FF_DAMPER': 85,
  'FF_EFFECT_MAX': 87,
  'FF_EFFECT_MIN': 80,
  'FF_FRICTION': 84,
  'FF_GAIN': 96,
  'FF_INERTIA': 86,
  'FF_MAX': 127,
  'FF_MAX_EFFECTS': 96,
  'FF_PERIODIC': 81,
  'FF_RAMP': 87,
  'FF_RUMBLE': 80,
  'FF_SAW_DOWN': 92,
  'FF_SAW_UP': 91,
  'FF_SINE': 90,
  'FF_SPRING': 83,
  'FF_SQUARE': 88,
  'FF_STATUS_MAX': 1,
  'FF_STATUS_PLAYING': 1,
  'FF_STATUS_STOPPED': 0,
  'FF_TRIANGLE': 89,
  'FF_WAVEFORM_MAX': 93,
  'FF_WAVEFORM_MIN': 88,
  'ID_BUS': 0,
  'ID_PRODUCT': 2,
  'ID_VENDOR': 1,
  'ID_VERSION': 3,
  'INPUT_PROP_ACCELEROMETER': 6,
  'INPUT_PROP_BUTTONPAD': 2,
  'INPUT_PROP_CNT': 32,
  'INPUT_PROP_DIRECT': 1,
  'INPUT_PROP_MAX': 31,
  'INPUT_PROP_POINTER': 0,
  'INPUT_PROP_POINTING_STICK': 5,
  'INPUT_PROP_SEMI_MT': 3,
  'INPUT_PROP_TOPBUTTONPAD': 4,
  'KEY_0': 11,
  'KEY_1': 2,
  'KEY_102ND': 86,
  'KEY_10CHANNELSDOWN': 441,
  'KEY_10CHANNELSUP': 440,
  'KEY_2': 3,
  'KEY_3': 4,
  'KEY_3D_MODE': 623,
  'KEY_4': 5,
  'KEY_5': 6,
  'KEY_6': 7,
  'KEY_7': 8,
  'KEY_8': 9,
  'KEY_9': 10,
  'KEY_A': 30,
  'KEY_AB': 406,
  'KEY_ACCESSIBILITY': 590,
  'KEY_ADDRESSBOOK': 429,
  'KEY_AGAIN': 129,
  'KEY_ALL_APPLICATIONS': 204,
  'KEY_ALS_TOGGLE': 560,
  'KEY_ALTERASE': 222,
  'KEY_ANGLE': 371,
  'KEY_APOSTROPHE': 40,
  'KEY_APPSELECT': 580,
  'KEY_ARCHIVE': 361,
  'KEY_ASPECT_RATIO': 375,
  'KEY_ASSISTANT': 583,
  'KEY_ATTENDANT_OFF': 540,
  'KEY_ATTENDANT_ON': 539,
  'KEY_ATTENDANT_TOGGLE': 541,
  'KEY_AUDIO': 392,
  'KEY_AUDIO_DESC': 622,
  'KEY_AUTOPILOT_ENGAGE_TOGGLE': 637,
  'KEY_AUX': 390,
  'KEY_B': 48,
  'KEY_BACK': 158,
  'KEY_BACKSLASH': 43,
  'KEY_BACKSPACE': 14,
  'KEY_BASSBOOST': 209,
  'KEY_BATTERY': 236,
  'KEY_BLUE': 401,
  'KEY_BLUETOOTH': 237,
  'KEY_BOOKMARKS': 156,
  'KEY_BREAK': 411,
  'KEY_BRIGHTNESSDOWN': 224,
  'KEY_BRIGHTNESSUP': 225,
  'KEY_BRIGHTNESS_AUTO': 244,
  'KEY_BRIGHTNESS_CYCLE': 243,
  'KEY_BRIGHTNESS_MAX': 593,
  'KEY_BRIGHTNESS_MENU': 649,
  'KEY_BRIGHTNESS_MIN': 592,
  'KEY_BRIGHTNESS_TOGGLE': 431,
  'KEY_BRIGHTNESS_ZERO': 244,
  'KEY_BRL_DOT1': 497,
  'KEY_BRL_DOT10': 506,
  'KEY_BRL_DOT2': 498,
  'KEY_BRL_DOT3': 499,
  'KEY_BRL_DOT4': 500,
  'KEY_BRL_DOT5': 501,
  'KEY_BRL_DOT6': 502,
  'KEY_BRL_DOT7': 503,
  'KEY_BRL_DOT8': 504,
  'KEY_BRL_DOT9': 505,
  'KEY_BUTTONCONFIG': 576,
  'KEY_C': 46,
  'KEY_CALC': 140,
  'KEY_CALENDAR': 397,
  'KEY_CAMERA': 212,
  'KEY_CAMERA_ACCESS_DISABLE': 588,
  'KEY_CAMERA_ACCESS_ENABLE': 587,
  'KEY_CAMERA_ACCESS_TOGGLE': 589,
  'KEY_CAMERA_DOWN': 536,
  'KEY_CAMERA_FOCUS': 528,
  'KEY_CAMERA_LEFT': 537,
  'KEY_CAMERA_RIGHT': 538,
  'KEY_CAMERA_UP': 535,
  'KEY_CAMERA_ZOOMIN': 533,
  'KEY_CAMERA_ZOOMOUT': 534,
  'KEY_CANCEL': 223,
  'KEY_CAPSLOCK': 58,
  'KEY_CD': 383,
  'KEY_CHANNEL': 363,
  'KEY_CHANNELDOWN': 403,
  'KEY_CHANNELUP': 402,
  'KEY_CHAT': 216,
  'KEY_CLEAR': 355,
  'KEY_CLEARVU_SONAR': 646,
  'KEY_CLOSE': 206,
  'KEY_CLOSECD': 160,
  'KEY_CNT': 768,
  'KEY_COFFEE': 152,
  'KEY_COMMA': 51,
  'KEY_COMPOSE': 127,
  'KEY_COMPUTER': 157,
  'KEY_CONFIG': 171,
  'KEY_CONNECT': 218,
  'KEY_CONTEXT_MENU': 438,
  'KEY_CONTROLPANEL': 579,
  'KEY_COPY': 133,
  'KEY_CUT': 137,
  'KEY_CYCLEWINDOWS': 154,
  'KEY_D': 32,
  'KEY_DASHBOARD': 204,
  'KEY_DATA': 631,
  'KEY_DATABASE': 426,
  'KEY_DELETE': 111,
  'KEY_DELETEFILE': 146,
  'KEY_DEL_EOL': 448,
  'KEY_DEL_EOS': 449,
  'KEY_DEL_LINE': 451,
  'KEY_DICTATE': 586,
  'KEY_DIGITS': 413,
  'KEY_DIRECTION': 153,
  'KEY_DIRECTORY': 394,
  'KEY_DISPLAYTOGGLE': 431,
  'KEY_DISPLAY_OFF': 245,
  'KEY_DOCUMENTS': 235,
  'KEY_DOLLAR': 434,
  'KEY_DOT': 52,
  'KEY_DOWN': 108,
  'KEY_DO_NOT_DISTURB': 591,
  'KEY_DUAL_RANGE_RADAR': 643,
  'KEY_DVD': 389,
  'KEY_E': 18,
  'KEY_EDIT': 176,
  'KEY_EDITOR': 422,
  'KEY_EJECTCD': 161,
  'KEY_EJECTCLOSECD': 162,
  'KEY_EMAIL': 215,
  'KEY_EMOJI_PICKER': 585,
  'KEY_END': 107,
  'KEY_ENTER': 28,
  'KEY_EPG': 365,
  'KEY_EQUAL': 13,
  'KEY_ESC': 1,
  'KEY_EURO': 435,
  'KEY_EXIT': 174,
  'KEY_F': 33,
  'KEY_F1': 59,
  'KEY_F10': 68,
  'KEY_F11': 87,
  'KEY_F12': 88,
  'KEY_F13': 183,
  'KEY_F14': 184,
  'KEY_F15': 185,
  'KEY_F16': 186,
  'KEY_F17': 187,
  'KEY_F18': 188,
  'KEY_F19': 189,
  'KEY_F2': 60,
  'KEY_F20': 190,
  'KEY_F21': 191,
  'KEY_F22': 192,
  'KEY_F23': 193,
  'KEY_F24': 194,
  'KEY_F3': 61,
  'KEY_F4': 62,
  'KEY_F5': 63,
  'KEY_F6': 64,
  'KEY_F7': 65,
  'KEY_F8': 66,
  'KEY_F9': 67,
  'KEY_FASTFORWARD': 208,
  'KEY_FASTREVERSE': 629,
  'KEY_FAVORITES': 364,
  'KEY_FILE': 144,
  'KEY_FINANCE': 219,
  'KEY_FIND': 136,
  'KEY_FIRST': 404,
  'KEY_FISHING_CHART': 641,
  'KEY_FN': 464,
  'KEY_FN_1': 478,
  'KEY_FN_2': 479,
  'KEY_FN_B': 484,
  'KEY_FN_D': 480,
  'KEY_FN_E': 481,
  'KEY_FN_ESC': 465,
  'KEY_FN_F': 482,
  'KEY_FN_F1': 466,
  'KEY_FN_F10': 475,
  'KEY_FN_F11': 476,
  'KEY_FN_F12': 477,
  'KEY_FN_F2': 467,
  'KEY_FN_F3': 468,
  'KEY_FN_F4': 469,
  'KEY_FN_F5': 470,
  'KEY_FN_F6': 471,
  'KEY_FN_F7': 472,
  'KEY_FN_F8': 473,
  'KEY_FN_F9': 474,
  'KEY_FN_RIGHT_SHIFT': 485,
  'KEY_FN_S': 483,
  'KEY_FORWARD': 159,
  'KEY_FORWARDMAIL': 233,
  'KEY_FRAMEBACK': 436,
  'KEY_FRAMEFORWARD': 437,
  'KEY_FRONT': 132,
  'KEY_FULL_SCREEN': 372,
  'KEY_G': 34,
  'KEY_GAMES': 417,
  'KEY_GOTO': 354,
  'KEY_GRAPHICSEDITOR': 424,
  'KEY_GRAVE': 41,
  'KEY_GREEN': 399,
  'KEY_H': 35,
  'KEY_HANGEUL': 122,
  'KEY_HANGUEL': 122,
  'KEY_HANGUP_PHONE': 446,
  'KEY_HANJA': 123,
  'KEY_HELP': 138,
  'KEY_HENKAN': 92,
  'KEY_HIRAGANA': 91,
  'KEY_HOME': 102,
  'KEY_HOMEPAGE': 172,
  'KEY_HP': 211,
  'KEY_I': 23,
  'KEY_IMAGES': 442,
  'KEY_INFO': 358,
  'KEY_INSERT': 110,
  'KEY_INS_LINE': 450,
  'KEY_ISO': 170,
  'KEY_J': 36,
  'KEY_JOURNAL': 578,
  'KEY_K': 37,
  'KEY_KATAKANA': 90,
  'KEY_KATAKANAHIRAGANA': 93,
  'KEY_KBDILLUMDOWN': 229,
  'KEY_KBDILLUMTOGGLE': 228,
  'KEY_KBDILLUMUP': 230,
  'KEY_KBDINPUTASSIST_ACCEPT': 612,
  'KEY_KBDINPUTASSIST_CANCEL': 613,
  'KEY_KBDINPUTASSIST_NEXT': 609,
  'KEY_KBDINPUTASSIST_NEXTGROUP': 611,
  'KEY_KBDINPUTASSIST_PREV': 608,
  'KEY_KBDINPUTASSIST_PREVGROUP': 610,
  'KEY_KBD_LAYOUT_NEXT': 584,
  'KEY_KBD_LCD_MENU1': 696,
  'KEY_KBD_LCD_MENU2': 697,
  'KEY_KBD_LCD_MENU3': 698,
  'KEY_KBD_LCD_MENU4': 699,
  'KEY_KBD_LCD_MENU5': 700,
  'KEY_KEYBOARD': 374,
  'KEY_KP0': 82,
  'KEY_KP1': 79,
  'KEY_KP2': 80,
  'KEY_KP3': 81,
  'KEY_KP4': 75,
  'KEY_KP5': 76,
  'KEY_KP6': 77,
  'KEY_KP7': 71,
  'KEY_KP8': 72,
  'KEY_KP9': 73,
  'KEY_KPASTERISK': 55,
  'KEY_KPCOMMA': 121,
  'KEY_KPDOT': 83,
  'KEY_KPENTER': 96,
  'KEY_KPEQUAL': 117,
  'KEY_KPJPCOMMA': 95,
  'KEY_KPLEFTPAREN': 179,
  'KEY_KPMINUS': 74,
  'KEY_KPPLUS': 78,
  'KEY_KPPLUSMINUS': 118,
  'KEY_KPRIGHTPAREN': 180,
  'KEY_KPSLASH': 98,
  'KEY_L': 38,
  'KEY_LANGUAGE': 368,
  'KEY_LAST': 405,
  'KEY_LEFT': 105,
  'KEY_LEFTALT': 56,
  'KEY_LEFTBRACE': 26,
  'KEY_LEFTCTRL': 29,
  'KEY_LEFTMETA': 125,
  'KEY_LEFTSHIFT': 42,
  'KEY_LEFT_DOWN': 617,
  'KEY_LEFT_UP': 616,
  'KEY_LIGHTS_TOGGLE': 542,
  'KEY_LINEFEED': 101,
  'KEY_LINK_PHONE': 447,
  'KEY_LIST': 395,
  'KEY_LOGOFF': 433,
  'KEY_M': 50,
  'KEY_MACRO': 112,
  'KEY_MACRO1': 656,
  'KEY_MACRO10': 665,
  'KEY_MACRO11': 666,
  'KEY_MACRO12': 667,
  'KEY_MACRO13': 668,
  'KEY_MACRO14': 669,
  'KEY_MACRO15': 670,
  'KEY_MACRO16': 671,
  'KEY_MACRO17': 672,
  'KEY_MACRO18': 673,
  'KEY_MACRO19': 674,
  'KEY_MACRO2': 657,
  'KEY_MACRO20': 675,
  'KEY_MACRO21': 676,
  'KEY_MACRO22': 677,
  'KEY_MACRO23': 678,
  'KEY_MACRO24': 679,
  'KEY_MACRO25': 680,
  'KEY_MACRO26': 681,
  'KEY_MACRO27': 682,
  'KEY_MACRO28': 683,
  'KEY_MACRO29': 684,
  'KEY_MACRO3': 658,
  'KEY_MACRO30': 685,
  'KEY_MACRO4': 659,
  'KEY_MACRO5': 660,
  'KEY_MACRO6': 661,
  'KEY_MACRO7': 662,
  'KEY_MACRO8': 663,
  'KEY_MACRO9': 664,
  'KEY_MACRO_PRESET1': 691,
  'KEY_MACRO_PRESET2': 692,
  'KEY_MACRO_PRESET3': 693,
  'KEY_MACRO_PRESET_CYCLE': 690,
  'KEY_MACRO_RECORD_START': 688,
  'KEY_MACRO_RECORD_STOP': 689,
  'KEY_MAIL': 155,
  'KEY_MARK_WAYPOINT': 638,
  'KEY_MAX': 767,
  'KEY_MEDIA': 226,
  'KEY_MEDIA_REPEAT': 439,
  'KEY_MEDIA_TOP_MENU': 619,
  'KEY_MEMO': 396,
  'KEY_MENU': 139,
  'KEY_MESSENGER': 430,
  'KEY_MHP': 367,
  'KEY_MICMUTE': 248,
  'KEY_MINUS': 12,
  'KEY_MIN_INTERESTING': 113,
  'KEY_MODE': 373,
  'KEY_MOVE': 175,
  'KEY_MP3': 391,
  'KEY_MSDOS': 151,
  'KEY_MUHENKAN': 94,
  'KEY_MUTE': 113,
  'KEY_N': 49,
  'KEY_NAV_CHART': 640,
  'KEY_NAV_INFO': 648,
  'KEY_NEW': 181,
  'KEY_NEWS': 427,
  'KEY_NEXT': 407,
  'KEY_NEXTSONG': 163,
  'KEY_NEXT_ELEMENT': 635,
  'KEY_NEXT_FAVORITE': 624,
  'KEY_NOTIFICATION_CENTER': 444,
  'KEY_NUMERIC_0': 512,
  'KEY_NUMERIC_1': 513,
  'KEY_NUMERIC_11': 620,
  'KEY_NUMERIC_12': 621,
  'KEY_NUMERIC_2': 514,
  'KEY_NUMERIC_3': 515,
  'KEY_NUMERIC_4': 516,
  'KEY_NUMERIC_5': 517,
  'KEY_NUMERIC_6': 518,
  'KEY_NUMERIC_7': 519,
  'KEY_NUMERIC_8': 520,
  'KEY_NUMERIC_9': 521,
  'KEY_NUMERIC_A': 524,
  'KEY_NUMERIC_B': 525,
  'KEY_NUMERIC_C': 526,
  'KEY_NUMERIC_D': 527,
  'KEY_NUMERIC_POUND': 523,
  'KEY_NUMERIC_STAR': 522,
  'KEY_NUMLOCK': 69,
  'KEY_O': 24,
  'KEY_OK': 352,
  'KEY_ONSCREEN_KEYBOARD': 632,
  'KEY_OPEN': 134,
  'KEY_OPTION': 357,
  'KEY_P': 25,
  'KEY_PAGEDOWN': 109,
  'KEY_PAGEUP': 104,
  'KEY_PASTE': 135,
  'KEY_PAUSE': 119,
  'KEY_PAUSECD': 201,
  'KEY_PAUSE_RECORD': 626,
  'KEY_PC': 376,
  'KEY_PHONE': 169,
  'KEY_PICKUP_PHONE': 445,
  'KEY_PLAY': 207,
  'KEY_PLAYCD': 200,
  'KEY_PLAYER': 387,
  'KEY_PLAYPAUSE': 164,
  'KEY_POWER': 116,
  'KEY_POWER2': 356,
  'KEY_PRESENTATION': 425,
  'KEY_PREVIOUS': 412,
  'KEY_PREVIOUSSONG': 165,
  'KEY_PREVIOUS_ELEMENT': 636,
  'KEY_PRINT': 210,
  'KEY_PRIVACY_SCREEN_TOGGLE': 633,
  'KEY_PROG1': 148,
  'KEY_PROG2': 149,
  'KEY_PROG3': 202,
  'KEY_PROG4': 203,
  'KEY_PROGRAM': 362,
  'KEY_PROPS': 130,
  'KEY_PVR': 366,
  'KEY_Q': 16,
  'KEY_QUESTION': 214,
  'KEY_R': 19,
  'KEY_RADAR_OVERLAY': 644,
  'KEY_RADIO': 385,
  'KEY_RECORD': 167,
  'KEY_RED': 398,
  'KEY_REDO': 182,
  'KEY_REFRESH': 173,
  'KEY_REFRESH_RATE_TOGGLE': 562,
  'KEY_REPLY': 232,
  'KEY_RESERVED': 0,
  'KEY_RESTART': 408,
  'KEY_REWIND': 168,
  'KEY_RFKILL': 247,
  'KEY_RIGHT': 106,
  'KEY_RIGHTALT': 100,
  'KEY_RIGHTBRACE': 27,
  'KEY_RIGHTCTRL': 97,
  'KEY_RIGHTMETA': 126,
  'KEY_RIGHTSHIFT': 54,
  'KEY_RIGHT_DOWN': 615,
  'KEY_RIGHT_UP': 614,
  'KEY_RO': 89,
  'KEY_ROOT_MENU': 618,
  'KEY_ROTATE_DISPLAY': 153,
  'KEY_ROTATE_LOCK_TOGGLE': 561,
  'KEY_S': 31,
  'KEY_SAT': 381,
  'KEY_SAT2': 382,
  'KEY_SAVE': 234,
  'KEY_SCALE': 120,
  'KEY_SCREEN': 375,
  'KEY_SCREENLOCK': 152,
  'KEY_SCREENSAVER': 581,
  'KEY_SCROLLDOWN': 178,
  'KEY_SCROLLLOCK': 70,
  'KEY_SCROLLUP': 177,
  'KEY_SEARCH': 217,
  'KEY_SELECT': 353,
  'KEY_SELECTIVE_SCREENSHOT': 634,
  'KEY_SEMICOLON': 39,
  'KEY_SEND': 231,
  'KEY_SENDFILE': 145,
  'KEY_SETUP': 141,
  'KEY_SHOP': 221,
  'KEY_SHUFFLE': 410,
  'KEY_SIDEVU_SONAR': 647,
  'KEY_SINGLE_RANGE_RADAR': 642,
  'KEY_SLASH': 53,
  'KEY_SLEEP': 142,
  'KEY_SLOW': 409,
  'KEY_SLOWREVERSE': 630,
  'KEY_SOS': 639,
  'KEY_SOUND': 213,
  'KEY_SPACE': 57,
  'KEY_SPELLCHECK': 432,
  'KEY_SPORT': 220,
  'KEY_SPREADSHEET': 423,
  'KEY_STOP': 128,
  'KEY_STOPCD': 166,
  'KEY_STOP_RECORD': 625,
  'KEY_SUBTITLE': 370,
  'KEY_SUSPEND': 205,
  'KEY_SWITCHVIDEOMODE': 227,
  'KEY_SYSRQ': 99,
  'KEY_T': 20,
  'KEY_TAB': 15,
  'KEY_TAPE': 384,
  'KEY_TASKMANAGER': 577,
  'KEY_TEEN': 414,
  'KEY_TEXT': 388,
  'KEY_TIME': 359,
  'KEY_TITLE': 369,
  'KEY_TOUCHPAD_OFF': 532,
  'KEY_TOUCHPAD_ON': 531,
  'KEY_TOUCHPAD_TOGGLE': 530,
  'KEY_TRADITIONAL_SONAR': 645,
  'KEY_TUNER': 386,
  'KEY_TV': 377,
  'KEY_TV2': 378,
  'KEY_TWEN': 415,
  'KEY_U': 22,
  'KEY_UNDO': 131,
  'KEY_UNKNOWN': 240,
  'KEY_UNMUTE': 628,
  'KEY_UP': 103,
  'KEY_UWB': 239,
  'KEY_V': 47,
  'KEY_VCR': 379,
  'KEY_VCR2': 380,
  'KEY_VENDOR': 360,
  'KEY_VIDEO': 393,
  'KEY_VIDEOPHONE': 416,
  'KEY_VIDEO_NEXT': 241,
  'KEY_VIDEO_PREV': 242,
  'KEY_VOD': 627,
  'KEY_VOICECOMMAND': 582,
  'KEY_VOICEMAIL': 428,
  'KEY_VOLUMEDOWN': 114,
  'KEY_VOLUMEUP': 115,
  'KEY_W': 17,
  'KEY_WAKEUP': 143,
  'KEY_WIMAX': 246,
  'KEY_WLAN': 238,
  'KEY_WORDPROCESSOR': 421,
  'KEY_WPS_BUTTON': 529,
  'KEY_WWAN': 246,
  'KEY_WWW': 150,
  'KEY_X': 45,
  'KEY_XFER': 147,
  'KEY_Y': 21,
  'KEY_YELLOW': 400,
  'KEY_YEN': 124,
  'KEY_Z': 44,
  'KEY_ZENKAKUHANKAKU': 85,
  'KEY_ZOOM': 372,
  'KEY_ZOOMIN': 418,
  'KEY_ZOOMOUT': 419,
  'KEY_ZOOMRESET': 420,
  'LED_CAPSL': 1,
  'LED_CHARGING': 10,
  'LED_CNT': 16,
  'LED_COMPOSE': 3,
  'LED_KANA': 4,
  'LED_MAIL': 9,
  'LED_MAX': 15,
  'LED_MISC': 8,
  'LED_MUTE': 7,
  'LED_NUML': 0,
  'LED_SCROLLL': 2,
  'LED_SLEEP': 5,
  'LED_SUSPEND': 6,
  'MSC_CNT': 8,
  'MSC_GESTURE': 2,
  'MSC_MAX': 7,
  'MSC_PULSELED': 1,
  'MSC_RAW': 3,
  'MSC_SCAN': 4,
  'MSC_SERIAL': 0,
  'MSC_TIMESTAMP': 5,
  'REL_CNT': 16,
  'REL_DIAL': 7,
  'REL_HWHEEL': 6,
  'REL_HWHEEL_HI_RES': 12,
  'REL_MAX': 15,
  'REL_MISC': 9,
  'REL_RESERVED': 10,
  'REL_RX': 3,
  'REL_RY': 4,
  'REL_RZ': 5,
  'REL_WHEEL': 8,
  'REL_WHEEL_HI_RES': 11,
  'REL_X': 0,
  'REL_Y': 1,
  'REL_Z': 2,
  'REP_CNT': 2,
  'REP_DELAY': 0,
  'REP_MAX': 1,
  'REP_PERIOD': 1,
  'SND_BELL': 1,
  'SND_CLICK': 0,
  'SND_CNT': 8,
  'SND_MAX': 7,
  'SND_TONE': 2,
  'SW_CAMERA_LENS_COVER': 9,
  'SW_CNT': 17,
  'SW_DOCK': 5,
  'SW_FRONT_PROXIMITY': 11,
  'SW_HEADPHONE_INSERT': 2,
  'SW_JACK_PHYSICAL_INSERT': 7,
  'SW_KEYPAD_SLIDE': 10,
  'SW_LID': 0,
  'SW_LINEIN_INSERT': 13,
  'SW_LINEOUT_INSERT': 6,
  'SW_MACHINE_COVER': 16,
  'SW_MAX': 16,
  'SW_MICROPHONE_INSERT': 4,
  'SW_MUTE_DEVICE': 14,
  'SW_PEN_INSERTED': 15,
  'SW_RADIO': 3,
  'SW_RFKILL_ALL': 3,
  'SW_ROTATE_LOCK': 12,
  'SW_TABLET_MODE': 1,
  'SW_VIDEOOUT_INSERT': 8,
  'SYN_CNT': 16,
  'SYN_CONFIG': 1,
  'SYN_DROPPED': 3,
  'SYN_MAX': 15,
  'SYN_MT_REPORT': 2,
  'SYN_REPORT': 0,
  'UI_FF_ERASE': 2,
  'UI_FF_UPLOAD': 1}

#: Mapping of event types to other value/name mappings.
bytype: Dict[int, Dict[int, Union[str, Tuple[str]]]] = { 0: {0: 'SYN_REPORT', 1: 'SYN_CONFIG', 2: 'SYN_MT_REPORT', 3: 'SYN_DROPPED', 15: 'SYN_MAX', 16: 'SYN_CNT'},
  1: { 0: 'KEY_RESERVED',
       1: 'KEY_ESC',
       2: 'KEY_1',
       3: 'KEY_2',
       4: 'KEY_3',
       5: 'KEY_4',
       6: 'KEY_5',
       7: 'KEY_6',
       8: 'KEY_7',
       9: 'KEY_8',
       10: 'KEY_9',
       11: 'KEY_0',
       12: 'KEY_MINUS',
       13: 'KEY_EQUAL',
       14: 'KEY_BACKSPACE',
       15: 'KEY_TAB',
       16: 'KEY_Q',
       17: 'KEY_W',
       18: 'KEY_E',
       19: 'KEY_R',
       20: 'KEY_T',
       21: 'KEY_Y',
       22: 'KEY_U',
       23: 'KEY_I',
       24: 'KEY_O',
       25: 'KEY_P',
       26: 'KEY_LEFTBRACE',
       27: 'KEY_RIGHTBRACE',
       28: 'KEY_ENTER',
       29: 'KEY_LEFTCTRL',
       30: 'KEY_A',
       31: 'KEY_S',
       32: 'KEY_D',
       33: 'KEY_F',
       34: 'KEY_G',
       35: 'KEY_H',
       36: 'KEY_J',
       37: 'KEY_K',
       38: 'KEY_L',
       39: 'KEY_SEMICOLON',
       40: 'KEY_APOSTROPHE',
       41: 'KEY_GRAVE',
       42: 'KEY_LEFTSHIFT',
       43: 'KEY_BACKSLASH',
       44: 'KEY_Z',
       45: 'KEY_X',
       46: 'KEY_C',
       47: 'KEY_V',
       48: 'KEY_B',
       49: 'KEY_N',
       50: 'KEY_M',
       51: 'KEY_COMMA',
       52: 'KEY_DOT',
       53: 'KEY_SLASH',
       54: 'KEY_RIGHTSHIFT',
       55: 'KEY_KPASTERISK',
       56: 'KEY_LEFTALT',
       57: 'KEY_SPACE',
       58: 'KEY_CAPSLOCK',
       59: 'KEY_F1',
       60: 'KEY_F2',
       61: 'KEY_F3',
       62: 'KEY_F4',
       63: 'KEY_F5',
       64: 'KEY_F6',
       65: 'KEY_F7',
       66: 'KEY_F8',
       67: 'KEY_F9',
       68: 'KEY_F10',
       69: 'KEY_NUMLOCK',
       70: 'KEY_SCROLLLOCK',
       71: 'KEY_KP7',
       72: 'KEY_KP8',
       73: 'KEY_KP9',
       74: 'KEY_KPMINUS',
       75: 'KEY_KP4',
       76: 'KEY_KP5',
       77: 'KEY_KP6',
       78: 'KEY_KPPLUS',
       79: 'KEY_KP1',
       80: 'KEY_KP2',
       81: 'KEY_KP3',
       82: 'KEY_KP0',
       83: 'KEY_KPDOT',
       85: 'KEY_ZENKAKUHANKAKU',
       86: 'KEY_102ND',
       87: 'KEY_F11',
       88: 'KEY_F12',
       89: 'KEY_RO',
       90: 'KEY_KATAKANA',
       91: 'KEY_HIRAGANA',
       92: 'KEY_HENKAN',
       93: 'KEY_KATAKANAHIRAGANA',
       94: 'KEY_MUHENKAN',
       95: 'KEY_KPJPCOMMA',
       96: 'KEY_KPENTER',
       97: 'KEY_RIGHTCTRL',
       98: 'KEY_KPSLASH',
       99: 'KEY_SYSRQ',
       100: 'KEY_RIGHTALT',
       101: 'KEY_LINEFEED',
       102: 'KEY_HOME',
       103: 'KEY_UP',
       104: 'KEY_PAGEUP',
       105: 'KEY_LEFT',
       106: 'KEY_RIGHT',
       107: 'KEY_END',
       108: 'KEY_DOWN',
       109: 'KEY_PAGEDOWN',
       110: 'KEY_INSERT',
       111: 'KEY_DELETE',
       112: 'KEY_MACRO',
       113: ('KEY_MIN_INTERESTING', 'KEY_MUTE'),
       114: 'KEY_VOLUMEDOWN',
       115: 'KEY_VOLUMEUP',
       116: 'KEY_POWER',
       117: 'KEY_KPEQUAL',
       118: 'KEY_KPPLUSMINUS',
       119: 'KEY_PAUSE',
       120: 'KEY_SCALE',
       121: 'KEY_KPCOMMA',
       122: ('KEY_HANGEUL', 'KEY_HANGUEL'),
       123: 'KEY_HANJA',
       124: 'KEY_YEN',
       125: 'KEY_LEFTMETA',
       126: 'KEY_RIGHTMETA',
       127: 'KEY_COMPOSE',
       128: 'KEY_STOP',
       129: 'KEY_AGAIN',
       130: 'KEY_PROPS',
       131: 'KEY_UNDO',
       132: 'KEY_FRONT',
       133: 'KEY_COPY',
       134: 'KEY_OPEN',
       135: 'KEY_PASTE',
       136: 'KEY_FIND',
       137: 'KEY_CUT',
       138: 'KEY_HELP',
       139: 'KEY_MENU',
       140: 'KEY_CALC',
       141: 'KEY_SETUP',
       142: 'KEY_SLEEP',
       143: 'KEY_WAKEUP',
       144: 'KEY_FILE',
       145: 'KEY_SENDFILE',
       146: 'KEY_DELETEFILE',
       147: 'KEY_XFER',
       148: 'KEY_PROG1',
       149: 'KEY_PROG2',
       150: 'KEY_WWW',
       151: 'KEY_MSDOS',
       152: ('KEY_COFFEE', 'KEY_SCREENLOCK'),
       153: ('KEY_DIRECTION', 'KEY_ROTATE_DISPLAY'),
       154: 'KEY_CYCLEWINDOWS',
       155: 'KEY_MAIL',
       156: 'KEY_BOOKMARKS',
       157: 'KEY_COMPUTER',
       158: 'KEY_BACK',
       159: 'KEY_FORWARD',
       160: 'KEY_CLOSECD',
       161: 'KEY_EJECTCD',
       162: 'KEY_EJECTCLOSECD',
       163: 'KEY_NEXTSONG',
       164: 'KEY_PLAYPAUSE',
       165: 'KEY_PREVIOUSSONG',
       166: 'KEY_STOPCD',
       167: 'KEY_RECORD',
       168: 'KEY_REWIND',
       169: 'KEY_PHONE',
       170: 'KEY_ISO',
       171: 'KEY_CONFIG',
       172: 'KEY_HOMEPAGE',
       173: 'KEY_REFRESH',
       174: 'KEY_EXIT',
       175: 'KEY_MOVE',
       176: 'KEY_EDIT',
       177: 'KEY_SCROLLUP',
       178: 'KEY_SCROLLDOWN',
       179: 'KEY_KPLEFTPAREN',
       180: 'KEY_KPRIGHTPAREN',
       181: 'KEY_NEW',
       182: 'KEY_REDO',
       183: 'KEY_F13',
       184: 'KEY_F14',
       185: 'KEY_F15',
       186: 'KEY_F16',
       187: 'KEY_F17',
       188: 'KEY_F18',
       189: 'KEY_F19',
       190: 'KEY_F20',
       191: 'KEY_F21',
       192: 'KEY_F22',
       193: 'KEY_F23',
       194: 'KEY_F24',
       200: 'KEY_PLAYCD',
       201: 'KEY_PAUSECD',
       202: 'KEY_PROG3',
       203: 'KEY_PROG4',
       204: ('KEY_ALL_APPLICATIONS', 'KEY_DASHBOARD'),
       205: 'KEY_SUSPEND',
       206: 'KEY_CLOSE',
       207: 'KEY_PLAY',
       208: 'KEY_FASTFORWARD',
       209: 'KEY_BASSBOOST',
       210: 'KEY_PRINT',
       211: 'KEY_HP',
       212: 'KEY_CAMERA',
       213: 'KEY_SOUND',
       214: 'KEY_QUESTION',
       215: 'KEY_EMAIL',
       216: 'KEY_CHAT',
       217: 'KEY_SEARCH',
       218: 'KEY_CONNECT',
       219: 'KEY_FINANCE',
       220: 'KEY_SPORT',
       221: 'KEY_SHOP',
       222: 'KEY_ALTERASE',
       223: 'KEY_CANCEL',
       224: 'KEY_BRIGHTNESSDOWN',
       225: 'KEY_BRIGHTNESSUP',
       226: 'KEY_MEDIA',
       227: 'KEY_SWITCHVIDEOMODE',
       228: 'KEY_KBDILLUMTOGGLE',
       229: 'KEY_KBDILLUMDOWN',
       230: 'KEY_KBDILLUMUP',
       231: 'KEY_SEND',
       232: 'KEY_REPLY',
       233: 'KEY_FORWARDMAIL',
       234: 'KEY_SAVE',
       235: 'KEY_DOCUMENTS',
       236: 'KEY_BATTERY',
       237: 'KEY_BLUETOOTH',
       238: 'KEY_WLAN',
       239: 'KEY_UWB',
       240: 'KEY_UNKNOWN',
       241: 'KEY_VIDEO_NEXT',
       242: 'KEY_VIDEO_PREV',
       243: 'KEY_BRIGHTNESS_CYCLE',
       244: ('KEY_BRIGHTNESS_AUTO', 'KEY_BRIGHTNESS_ZERO'),
       245: 'KEY_DISPLAY_OFF',
       246: ('KEY_WIMAX', 'KEY_WWAN'),
       247: 'KEY_RFKILL',
       248: 'KEY_MICMUTE',
       256: ('BTN_0', 'BTN_MISC'),
       257: 'BTN_1',
       258: 'BTN_2',
       259: 'BTN_3',
       260: 'BTN_4',
       261: 'BTN_5',
       262: 'BTN_6',
       263: 'BTN_7',
       264: 'BTN_8',
       265: 'BTN_9',
       272: ('BTN_LEFT', 'BTN_MOUSE'),
       273: 'BTN_RIGHT',
       274: 'BTN_MIDDLE',
       275: 'BTN_SIDE',
       276: 'BTN_EXTRA',
       277: 'BTN_FORWARD',
       278: 'BTN_BACK',
       279: 'BTN_TASK',
       288: ('BTN_JOYSTICK', 'BTN_TRIGGER'),
       289: 'BTN_THUMB',
       290: 'BTN_THUMB2',
       291: 'BTN_TOP',
       292: 'BTN_TOP2',
       293: 'BTN_PINKIE',
       294: 'BTN_BASE',
       295: 'BTN_BASE2',
       296: 'BTN_BASE3',
       297: 'BTN_BASE4',
       298: 'BTN_BASE5',
       299: 'BTN_BASE6',
       303: 'BTN_DEAD',
       304: ('BTN_A', 'BTN_GAMEPAD', 'BTN_SOUTH'),
       305: ('BTN_B', 'BTN_EAST'),
       306: 'BTN_C',
       307: ('BTN_NORTH', 'BTN_X'),
       308: ('BTN_WEST', 'BTN_Y'),
       309: 'BTN_Z',
       310: 'BTN_TL',
       311: 'BTN_TR',
       312: 'BTN_TL2',
       313: 'BTN_TR2',
       314: 'BTN_SELECT',
       315: 'BTN_START',
       316: 'BTN_MODE',
       317: 'BTN_THUMBL',
       318: 'BTN_THUMBR',
       320: ('BTN_DIGI', 'BTN_TOOL_PEN'),
       321: 'BTN_TOOL_RUBBER',
       322: 'BTN_TOOL_BRUSH',
       323: 'BTN_TOOL_PENCIL',
       324: 'BTN_TOOL_AIRBRUSH',
       325: 'BTN_TOOL_FINGER',
       326: 'BTN_TOOL_MOUSE',
       327: 'BTN_TOOL_LENS',
       328: 'BTN_TOOL_QUINTTAP',
       329: 'BTN_STYLUS3',
       330: 'BTN_TOUCH',
       331: 'BTN_STYLUS',
       332: 'BTN_STYLUS2',
       333: 'BTN_TOOL_DOUBLETAP',
       334: 'BTN_TOOL_TRIPLETAP',
       335: 'BTN_TOOL_QUADTAP',
       336: ('BTN_GEAR_DOWN', 'BTN_WHEEL'),
       337: 'BTN_GEAR_UP',
       352: 'KEY_OK',
       353: 'KEY_SELECT',
       354: 'KEY_GOTO',
       355: 'KEY_CLEAR',
       356: 'KEY_POWER2',
       357: 'KEY_OPTION',
       358: 'KEY_INFO',
       359: 'KEY_TIME',
       360: 'KEY_VENDOR',
       361: 'KEY_ARCHIVE',
       362: 'KEY_PROGRAM',
       363: 'KEY_CHANNEL',
       364: 'KEY_FAVORITES',
       365: 'KEY_EPG',
       366: 'KEY_PVR',
       367: 'KEY_MHP',
       368: 'KEY_LANGUAGE',
       369: 'KEY_TITLE',
       370: 'KEY_SUBTITLE',
       371: 'KEY_ANGLE',
       372: ('KEY_FULL_SCREEN', 'KEY_ZOOM'),
       373: 'KEY_MODE',
       374: 'KEY_KEYBOARD',
       375: ('KEY_ASPECT_RATIO', 'KEY_SCREEN'),
       376: 'KEY_PC',
       377: 'KEY_TV',
       378: 'KEY_TV2',
       379: 'KEY_VCR',
       380: 'KEY_VCR2',
       381: 'KEY_SAT',
       382: 'KEY_SAT2',
       383: 'KEY_CD',
       384: 'KEY_TAPE',
       385: 'KEY_RADIO',
       386: 'KEY_TUNER',
       387: 'KEY_PLAYER',
       388: 'KEY_TEXT',
       389: 'KEY_DVD',
       390: 'KEY_AUX',
       391: 'KEY_MP3',
       392: 'KEY_AUDIO',
       393: 'KEY_VIDEO',
       394: 'KEY_DIRECTORY',
       395: 'KEY_LIST',
       396: 'KEY_MEMO',
       397: 'KEY_CALENDAR',
       398: 'KEY_RED',
       399: 'KEY_GREEN',
       400: 'KEY_YELLOW',
       401: 'KEY_BLUE',
       402: 'KEY_CHANNELUP',
       403: 'KEY_CHANNELDOWN',
       404: 'KEY_FIRST',
       405: 'KEY_LAST',
       406: 'KEY_AB',
       407: 'KEY_NEXT',
       408: 'KEY_RESTART',
       409: 'KEY_SLOW',
       410: 'KEY_SHUFFLE',
       411: 'KEY_BREAK',
       412: 'KEY_PREVIOUS',
       413: 'KEY_DIGITS',
       414: 'KEY_TEEN',
       415: 'KEY_TWEN',
       416: 'KEY_VIDEOPHONE',
       417: 'KEY_GAMES',
       418: 'KEY_ZOOMIN',
       419: 'KEY_ZOOMOUT',
       420: 'KEY_ZOOMRESET',
       421: 'KEY_WORDPROCESSOR',
       422: 'KEY_EDITOR',
       423: 'KEY_SPREADSHEET',
       424: 'KEY_GRAPHICSEDITOR',
       425: 'KEY_PRESENTATION',
       426: 'KEY_DATABASE',
       427: 'KEY_NEWS',
       428: 'KEY_VOICEMAIL',
       429: 'KEY_ADDRESSBOOK',
       430: 'KEY_MESSENGER',
       431: ('KEY_BRIGHTNESS_TOGGLE', 'KEY_DISPLAYTOGGLE'),
       432: 'KEY_SPELLCHECK',
       433: 'KEY_LOGOFF',
       434: 'KEY_DOLLAR',
       435: 'KEY_EURO',
       436: 'KEY_FRAMEBACK',
       437: 'KEY_FRAMEFORWARD',
       438: 'KEY_CONTEXT_MENU',
       439: 'KEY_MEDIA_REPEAT',
       440: 'KEY_10CHANNELSUP',
       441: 'KEY_10CHANNELSDOWN',
       442: 'KEY_IMAGES',
       444: 'KEY_NOTIFICATION_CENTER',
       445: 'KEY_PICKUP_PHONE',
       446: 'KEY_HANGUP_PHONE',
       447: 'KEY_LINK_PHONE',
       448: 'KEY_DEL_EOL',
       449: 'KEY_DEL_EOS',
       450: 'KEY_INS_LINE',
       451: 'KEY_DEL_LINE',
       464: 'KEY_FN',
       465: 'KEY_FN_ESC',
       466: 'KEY_FN_F1',
       467: 'KEY_FN_F2',
       468: 'KEY_FN_F3',
       469: 'KEY_FN_F4',
       470: 'KEY_FN_F5',
       471: 'KEY_FN_F6',
       472: 'KEY_FN_F7',
       473: 'KEY_FN_F8',
       474: 'KEY_FN_F9',
       475: 'KEY_FN_F10',
       476: 'KEY_FN_F11',
       477: 'KEY_FN_F12',
       478: 'KEY_FN_1',
       479: 'KEY_FN_2',
       480: 'KEY_FN_D',
       481: 'KEY_FN_E',
       482: 'KEY_FN_F',
       483: 'KEY_FN_S',
       484: 'KEY_FN_B',
       485: 'KEY_FN_RIGHT_SHIFT',
       497: 'KEY_BRL_DOT1',
       498: 'KEY_BRL_DOT2',
       499: 'KEY_BRL_DOT3',
       500: 'KEY_BRL_DOT4',
       501: 'KEY_BRL_DOT5',
       502: 'KEY_BRL_DOT6',
       503: 'KEY_BRL_DOT7',
       504: 'KEY_BRL_DOT8',
       505: 'KEY_BRL_DOT9',
       506: 'KEY_BRL_DOT10',
       512: 'KEY_NUMERIC_0',
       513: 'KEY_NUMERIC_1',
       514: 'KEY_NUMERIC_2',
       515: 'KEY_NUMERIC_3',
       516: 'KEY_NUMERIC_4',
       517: 'KEY_NUMERIC_5',
       518: 'KEY_NUMERIC_6',
       519: 'KEY_NUMERIC_7',
       520: 'KEY_NUMERIC_8',
       521: 'KEY_NUMERIC_9',
       522: 'KEY_NUMERIC_STAR',
       523: 'KEY_NUMERIC_POUND',
       524: 'KEY_NUMERIC_A',
       525: 'KEY_NUMERIC_B',
       526: 'KEY_NUMERIC_C',
       527: 'KEY_NUMERIC_D',
       528: 'KEY_CAMERA_FOCUS',
       529: 'KEY_WPS_BUTTON',
       530: 'KEY_TOUCHPAD_TOGGLE',
       531: 'KEY_TOUCHPAD_ON',
       532: 'KEY_TOUCHPAD_OFF',
       533: 'KEY_CAMERA_ZOOMIN',
       534: 'KEY_CAMERA_ZOOMOUT',
       535: 'KEY_CAMERA_UP',
       536: 'KEY_CAMERA_DOWN',
       537: 'KEY_CAMERA_LEFT',
       538: 'KEY_CAMERA_RIGHT',
       539: 'KEY_ATTENDANT_ON',
       540: 'KEY_ATTENDANT_OFF',
       541: 'KEY_ATTENDANT_TOGGLE',
       542: 'KEY_LIGHTS_TOGGLE',
       544: 'BTN_DPAD_UP',
       545: 'BTN_DPAD_DOWN',
       546: 'BTN_DPAD_LEFT',
       547: 'BTN_DPAD_RIGHT',
       560: 'KEY_ALS_TOGGLE',
       561: 'KEY_ROTATE_LOCK_TOGGLE',
       562: 'KEY_REFRESH_RATE_TOGGLE',
       576: 'KEY_BUTTONCONFIG',
       577: 'KEY_TASKMANAGER',
       578: 'KEY_JOURNAL',
       579: 'KEY_CONTROLPANEL',
       580: 'KEY_APPSELECT',
       581: 'KEY_SCREENSAVER',
       582: 'KEY_VOICECOMMAND',
       583: 'KEY_ASSISTANT',
       584: 'KEY_KBD_LAYOUT_NEXT',
       585: 'KEY_EMOJI_PICKER',
       586: 'KEY_DICTATE',
       587: 'KEY_CAMERA_ACCESS_ENABLE',
       588: 'KEY_CAMERA_ACCESS_DISABLE',
       589: 'KEY_CAMERA_ACCESS_TOGGLE',
       590: 'KEY_ACCESSIBILITY',
       591: 'KEY_DO_NOT_DISTURB',
       592: 'KEY_BRIGHTNESS_MIN',
       593: 'KEY_BRIGHTNESS_MAX',
       608: 'KEY_KBDINPUTASSIST_PREV',
       609: 'KEY_KBDINPUTASSIST_NEXT',
       610: 'KEY_KBDINPUTASSIST_PREVGROUP',
       611: 'KEY_KBDINPUTASSIST_NEXTGROUP',
       612: 'KEY_KBDINPUTASSIST_ACCEPT',
       613: 'KEY_KBDINPUTASSIST_CANCEL',
       614: 'KEY_RIGHT_UP',
       615: 'KEY_RIGHT_DOWN',
       616: 'KEY_LEFT_UP',
       617: 'KEY_LEFT_DOWN',
       618: 'KEY_ROOT_MENU',
       619: 'KEY_MEDIA_TOP_MENU',
       620: 'KEY_NUMERIC_11',
       621: 'KEY_NUMERIC_12',
       622: 'KEY_AUDIO_DESC',
       623: 'KEY_3D_MODE',
       624: 'KEY_NEXT_FAVORITE',
       625: 'KEY_STOP_RECORD',
       626: 'KEY_PAUSE_RECORD',
       627: 'KEY_VOD',
       628: 'KEY_UNMUTE',
       629: 'KEY_FASTREVERSE',
       630: 'KEY_SLOWREVERSE',
       631: 'KEY_DATA',
       632: 'KEY_ONSCREEN_KEYBOARD',
       633: 'KEY_PRIVACY_SCREEN_TOGGLE',
       634: 'KEY_SELECTIVE_SCREENSHOT',
       635: 'KEY_NEXT_ELEMENT',
       636: 'KEY_PREVIOUS_ELEMENT',
       637: 'KEY_AUTOPILOT_ENGAGE_TOGGLE',
       638: 'KEY_MARK_WAYPOINT',
       639: 'KEY_SOS',
       640: 'KEY_NAV_CHART',
       641: 'KEY_FISHING_CHART',
       642: 'KEY_SINGLE_RANGE_RADAR',
       643: 'KEY_DUAL_RANGE_RADAR',
       644: 'KEY_RADAR_OVERLAY',
       645: 'KEY_TRADITIONAL_SONAR',
       646: 'KEY_CLEARVU_SONAR',
       647: 'KEY_SIDEVU_SONAR',
       648: 'KEY_NAV_INFO',
       649: 'KEY_BRIGHTNESS_MENU',
       656: 'KEY_MACRO1',
       657: 'KEY_MACRO2',
       658: 'KEY_MACRO3',
       659: 'KEY_MACRO4',
       660: 'KEY_MACRO5',
       661: 'KEY_MACRO6',
       662: 'KEY_MACRO7',
       663: 'KEY_MACRO8',
       664: 'KEY_MACRO9',
       665: 'KEY_MACRO10',
       666: 'KEY_MACRO11',
       667: 'KEY_MACRO12',
       668: 'KEY_MACRO13',
       669: 'KEY_MACRO14',
       670: 'KEY_MACRO15',
       671: 'KEY_MACRO16',
       672: 'KEY_MACRO17',
       673: 'KEY_MACRO18',
       674: 'KEY_MACRO19',
       675: 'KEY_MACRO20',
       676: 'KEY_MACRO21',
       677: 'KEY_MACRO22',
       678: 'KEY_MACRO23',
       679: 'KEY_MACRO24',
       680: 'KEY_MACRO25',
       681: 'KEY_MACRO26',
       682: 'KEY_MACRO27',
       683: 'KEY_MACRO28',
       684: 'KEY_MACRO29',
       685: 'KEY_MACRO30',
       688: 'KEY_MACRO_RECORD_START',
       689: 'KEY_MACRO_RECORD_STOP',
       690: 'KEY_MACRO_PRESET_CYCLE',
       691: 'KEY_MACRO_PRESET1',
       692: 'KEY_MACRO_PRESET2',
       693: 'KEY_MACRO_PRESET3',
       696: 'KEY_KBD_LCD_MENU1',
       697: 'KEY_KBD_LCD_MENU2',
       698: 'KEY_KBD_LCD_MENU3',
       699: 'KEY_KBD_LCD_MENU4',
       700: 'KEY_KBD_LCD_MENU5',
       704: ('BTN_TRIGGER_HAPPY', 'BTN_TRIGGER_HAPPY1'),
       705: 'BTN_TRIGGER_HAPPY2',
       706: 'BTN_TRIGGER_HAPPY3',
       707: 'BTN_TRIGGER_HAPPY4',
       708: 'BTN_TRIGGER_HAPPY5',
       709: 'BTN_TRIGGER_HAPPY6',
       710: 'BTN_TRIGGER_HAPPY7',
       711: 'BTN_TRIGGER_HAPPY8',
       712: 'BTN_TRIGGER_HAPPY9',
       713: 'BTN_TRIGGER_HAPPY10',
       714: 'BTN_TRIGGER_HAPPY11',
       715: 'BTN_TRIGGER_HAPPY12',
       716: 'BTN_TRIGGER_HAPPY13',
       717: 'BTN_TRIGGER_HAPPY14',
       718: 'BTN_TRIGGER_HAPPY15',
       719: 'BTN_TRIGGER_HAPPY16',
       720: 'BTN_TRIGGER_HAPPY17',
       721: 'BTN_TRIGGER_HAPPY18',
       722: 'BTN_TRIGGER_HAPPY19',
       723: 'BTN_TRIGGER_HAPPY20',
       724: 'BTN_TRIGGER_HAPPY21',
       725: 'BTN_TRIGGER_HAPPY22',
       726: 'BTN_TRIGGER_HAPPY23',
       727: 'BTN_TRIGGER_HAPPY24',
       728: 'BTN_TRIGGER_HAPPY25',
       729: 'BTN_TRIGGER_HAPPY26',
       730: 'BTN_TRIGGER_HAPPY27',
       731: 'BTN_TRIGGER_HAPPY28',
       732: 'BTN_TRIGGER_HAPPY29',
       733: 'BTN_TRIGGER_HAPPY30',
       734: 'BTN_TRIGGER_HAPPY31',
       735: 'BTN_TRIGGER_HAPPY32',
       736: 'BTN_TRIGGER_HAPPY33',
       737: 'BTN_TRIGGER_HAPPY34',
       738: 'BTN_TRIGGER_HAPPY35',
       739: 'BTN_TRIGGER_HAPPY36',
       740: 'BTN_TRIGGER_HAPPY37',
       741: 'BTN_TRIGGER_HAPPY38',
       742: 'BTN_TRIGGER_HAPPY39',
       743: 'BTN_TRIGGER_HAPPY40'},
  2: { 0: 'REL_X',
       1: 'REL_Y',
       2: 'REL_Z',
       3: 'REL_RX',
       4: 'REL_RY',
       5: 'REL_RZ',
       6: 'REL_HWHEEL',
       7: 'REL_DIAL',
       8: 'REL_WHEEL',
       9: 'REL_MISC',
       10: 'REL_RESERVED',
       11: 'REL_WHEEL_HI_RES',
       12: 'REL_HWHEEL_HI_RES',
       15: 'REL_MAX',
       16: 'REL_CNT'},
  3: { 0: 'ABS_X',
       1: 'ABS_Y',
       2: 'ABS_Z',
       3: 'ABS_RX',
       4: 'ABS_RY',
       5: 'ABS_RZ',
       6: 'ABS_THROTTLE',
       7: 'ABS_RUDDER',
       8: 'ABS_WHEEL',
       9: 'ABS_GAS',
       10: 'ABS_BRAKE',
       16: 'ABS_HAT0X',
       17: 'ABS_HAT0Y',
       18: 'ABS_HAT1X',
       19: 'ABS_HAT1Y',
       20: 'ABS_HAT2X',
       21: 'ABS_HAT2Y',
       22: 'ABS_HAT3X',
       23: 'ABS_HAT3Y',
       24: 'ABS_PRESSURE',
       25: 'ABS_DISTANCE',
       26: 'ABS_TILT_X',
       27: 'ABS_TILT_Y',
       28: 'ABS_TOOL_WIDTH',
       32: 'ABS_VOLUME',
       33: 'ABS_PROFILE',
       40: 'ABS_MISC',
       46: 'ABS_RESERVED',
       47: 'ABS_MT_SLOT',
       48: 'ABS_MT_TOUCH_MAJOR',
       49: 'ABS_MT_TOUCH_MINOR',
       50: 'ABS_MT_WIDTH_MAJOR',
       51: 'ABS_MT_WIDTH_MINOR',
       52: 'ABS_MT_ORIENTATION',
       53: 'ABS_MT_POSITION_X',
       54: 'ABS_MT_POSITION_Y',
       55: 'ABS_MT_TOOL_TYPE',
       56: 'ABS_MT_BLOB_ID',
       57: 'ABS_MT_TRACKING_ID',
       58: 'ABS_MT_PRESSURE',
       59: 'ABS_MT_DISTANCE',
       60: 'ABS_MT_TOOL_X',
       61: 'ABS_MT_TOOL_Y',
       63: 'ABS_MAX',
       64: 'ABS_CNT'},
  4: { 0: 'MSC_SERIAL',
       1: 'MSC_PULSELED',
       2: 'MSC_GESTURE',
       3: 'MSC_RAW',
       4: 'MSC_SCAN',
       5: 'MSC_TIMESTAMP',
       7: 'MSC_MAX',
       8: 'MSC_CNT'},
  5: { 0: 'SW_LID',
       1: 'SW_TABLET_MODE',
       2: 'SW_HEADPHONE_INSERT',
       3: ('SW_RADIO', 'SW_RFKILL_ALL'),
       4: 'SW_MICROPHONE_INSERT',
       5: 'SW_DOCK',
       6: 'SW_LINEOUT_INSERT',
       7: 'SW_JACK_PHYSICAL_INSERT',
       8: 'SW_VIDEOOUT_INSERT',
       9: 'SW_CAMERA_LENS_COVER',
       10: 'SW_KEYPAD_SLIDE',
       11: 'SW_FRONT_PROXIMITY',
       12: 'SW_ROTATE_LOCK',
       13: 'SW_LINEIN_INSERT',
       14: 'SW_MUTE_DEVICE',
       15: 'SW_PEN_INSERTED',
       16: ('SW_MACHINE_COVER', 'SW_MAX'),
       17: 'SW_CNT'},
  17: { 0: 'LED_NUML',
        1: 'LED_CAPSL',
        2: 'LED_SCROLLL',
        3: 'LED_COMPOSE',
        4: 'LED_KANA',
        5: 'LED_SLEEP',
        6: 'LED_SUSPEND',
        7: 'LED_MUTE',
        8: 'LED_MISC',
        9: 'LED_MAIL',
        10: 'LED_CHARGING',
        15: 'LED_MAX',
        16: 'LED_CNT'},
  18: {0: 'SND_CLICK', 1: 'SND_BELL', 2: 'SND_TONE', 7: 'SND_MAX', 8: 'SND_CNT'},
  20: {0: 'REP_DELAY', 1: ('REP_MAX', 'REP_PERIOD'), 2: 'REP_CNT'},
  21: { 80: ('FF_EFFECT_MIN', 'FF_RUMBLE'),
        81: 'FF_PERIODIC',
        82: 'FF_CONSTANT',
        83: 'FF_SPRING',
        84: 'FF_FRICTION',
        85: 'FF_DAMPER',
        86: 'FF_INERTIA',
        87: ('FF_EFFECT_MAX', 'FF_RAMP'),
        88: ('FF_SQUARE', 'FF_WAVEFORM_MIN'),
        89: 'FF_TRIANGLE',
        90: 'FF_SINE',
        91: 'FF_SAW_UP',
        92: 'FF_SAW_DOWN',
        93: ('FF_CUSTOM', 'FF_WAVEFORM_MAX'),
        96: ('FF_GAIN', 'FF_MAX_EFFECTS'),
        97: 'FF_AUTOCENTER',
        127: 'FF_MAX',
        128: 'FF_CNT'},
  23: {0: 'FF_STATUS_STOPPED', 1: ('FF_STATUS_MAX', 'FF_STATUS_PLAYING')}}

#: Keys are a combination of all BTN and KEY codes.
keys: Dict[int, Union[str, Tuple[str]]] = { 0: 'KEY_RESERVED',
  1: 'KEY_ESC',
  2: 'KEY_1',
  3: 'KEY_2',
  4: 'KEY_3',
  5: 'KEY_4',
  6: 'KEY_5',
  7: 'KEY_6',
  8: 'KEY_7',
  9: 'KEY_8',
  10: 'KEY_9',
  11: 'KEY_0',
  12: 'KEY_MINUS',
  13: 'KEY_EQUAL',
  14: 'KEY_BACKSPACE',
  15: 'KEY_TAB',
  16: 'KEY_Q',
  17: 'KEY_W',
  18: 'KEY_E',
  19: 'KEY_R',
  20: 'KEY_T',
  21: 'KEY_Y',
  22: 'KEY_U',
  23: 'KEY_I',
  24: 'KEY_O',
  25: 'KEY_P',
  26: 'KEY_LEFTBRACE',
  27: 'KEY_RIGHTBRACE',
  28: 'KEY_ENTER',
  29: 'KEY_LEFTCTRL',
  30: 'KEY_A',
  31: 'KEY_S',
  32: 'KEY_D',
  33: 'KEY_F',
  34: 'KEY_G',
  35: 'KEY_H',
  36: 'KEY_J',
  37: 'KEY_K',
  38: 'KEY_L',
  39: 'KEY_SEMICOLON',
  40: 'KEY_APOSTROPHE',
  41: 'KEY_GRAVE',
  42: 'KEY_LEFTSHIFT',
  43: 'KEY_BACKSLASH',
  44: 'KEY_Z',
  45: 'KEY_X',
  46: 'KEY_C',
  47: 'KEY_V',
  48: 'KEY_B',
  49: 'KEY_N',
  50: 'KEY_M',
  51: 'KEY_COMMA',
  52: 'KEY_DOT',
  53: 'KEY_SLASH',
  54: 'KEY_RIGHTSHIFT',
  55: 'KEY_KPASTERISK',
  56: 'KEY_LEFTALT',
  57: 'KEY_SPACE',
  58: 'KEY_CAPSLOCK',
  59: 'KEY_F1',
  60: 'KEY_F2',
  61: 'KEY_F3',
  62: 'KEY_F4',
  63: 'KEY_F5',
  64: 'KEY_F6',
  65: 'KEY_F7',
  66: 'KEY_F8',
  67: 'KEY_F9',
  68: 'KEY_F10',
  69: 'KEY_NUMLOCK',
  70: 'KEY_SCROLLLOCK',
  71: 'KEY_KP7',
  72: 'KEY_KP8',
  73: 'KEY_KP9',
  74: 'KEY_KPMINUS',
  75: 'KEY_KP4',
  76: 'KEY_KP5',
  77: 'KEY_KP6',
  78: 'KEY_KPPLUS',
  79: 'KEY_KP1',
  80: 'KEY_KP2',
  81: 'KEY_KP3',
  82: 'KEY_KP0',
  83: 'KEY_KPDOT',
  85: 'KEY_ZENKAKUHANKAKU',
  86: 'KEY_102ND',
  87: 'KEY_F11',
  88: 'KEY_F12',
  89: 'KEY_RO',
  90: 'KEY_KATAKANA',
  91: 'KEY_HIRAGANA',
  92: 'KEY_HENKAN',
  93: 'KEY_KATAKANAHIRAGANA',
  94: 'KEY_MUHENKAN',
  95: 'KEY_KPJPCOMMA',
  96: 'KEY_KPENTER',
  97: 'KEY_RIGHTCTRL',
  98: 'KEY_KPSLASH',
  99: 'KEY_SYSRQ',
  100: 'KEY_RIGHTALT',
  101: 'KEY_LINEFEED',
  102: 'KEY_HOME',
  103: 'KEY_UP',
  104: 'KEY_PAGEUP',
  105: 'KEY_LEFT',
  106: 'KEY_RIGHT',
  107: 'KEY_END',
  108: 'KEY_DOWN',
  109: 'KEY_PAGEDOWN',
  110: 'KEY_INSERT',
  111: 'KEY_DELETE',
  112: 'KEY_MACRO',
  113: ('KEY_MIN_INTERESTING', 'KEY_MUTE'),
  114: 'KEY_VOLUMEDOWN',
  115: 'KEY_VOLUMEUP',
  116: 'KEY_POWER',
  117: 'KEY_KPEQUAL',
  118: 'KEY_KPPLUSMINUS',
  119: 'KEY_PAUSE',
  120: 'KEY_SCALE',
  121: 'KEY_KPCOMMA',
  122: ('KEY_HANGEUL', 'KEY_HANGUEL'),
  123: 'KEY_HANJA',
  124: 'KEY_YEN',
  125: 'KEY_LEFTMETA',
  126: 'KEY_RIGHTMETA',
  127: 'KEY_COMPOSE',
  128: 'KEY_STOP',
  129: 'KEY_AGAIN',
  130: 'KEY_PROPS',
  131: 'KEY_UNDO',
  132: 'KEY_FRONT',
  133: 'KEY_COPY',
  134: 'KEY_OPEN',
  135: 'KEY_PASTE',
  136: 'KEY_FIND',
  137: 'KEY_CUT',
  138: 'KEY_HELP',
  139: 'KEY_MENU',
  140: 'KEY_CALC',
  141: 'KEY_SETUP',
  142: 'KEY_SLEEP',
  143: 'KEY_WAKEUP',
  144: 'KEY_FILE',
  145: 'KEY_SENDFILE',
  146: 'KEY_DELETEFILE',
  147: 'KEY_XFER',
  148: 'KEY_PROG1',
  149: 'KEY_PROG2',
  150: 'KEY_WWW',
  151: 'KEY_MSDOS',
  152: ('KEY_COFFEE', 'KEY_SCREENLOCK'),
  153: ('KEY_DIRECTION', 'KEY_ROTATE_DISPLAY'),
  154: 'KEY_CYCLEWINDOWS',
  155: 'KEY_MAIL',
  156: 'KEY_BOOKMARKS',
  157: 'KEY_COMPUTER',
  158: 'KEY_BACK',
  159: 'KEY_FORWARD',
  160: 'KEY_CLOSECD',
  161: 'KEY_EJECTCD',
  162: 'KEY_EJECTCLOSECD',
  163: 'KEY_NEXTSONG',
  164: 'KEY_PLAYPAUSE',
  165: 'KEY_PREVIOUSSONG',
  166: 'KEY_STOPCD',
  167: 'KEY_RECORD',
  168: 'KEY_REWIND',
  169: 'KEY_PHONE',
  170: 'KEY_ISO',
  171: 'KEY_CONFIG',
  172: 'KEY_HOMEPAGE',
  173: 'KEY_REFRESH',
  174: 'KEY_EXIT',
  175: 'KEY_MOVE',
  176: 'KEY_EDIT',
  177: 'KEY_SCROLLUP',
  178: 'KEY_SCROLLDOWN',
  179: 'KEY_KPLEFTPAREN',
  180: 'KEY_KPRIGHTPAREN',
  181: 'KEY_NEW',
  182: 'KEY_REDO',
  183: 'KEY_F13',
  184: 'KEY_F14',
  185: 'KEY_F15',
  186: 'KEY_F16',
  187: 'KEY_F17',
  188: 'KEY_F18',
  189: 'KEY_F19',
  190: 'KEY_F20',
  191: 'KEY_F21',
  192: 'KEY_F22',
  193: 'KEY_F23',
  194: 'KEY_F24',
  200: 'KEY_PLAYCD',
  201: 'KEY_PAUSECD',
  202: 'KEY_PROG3',
  203: 'KEY_PROG4',
  204: ('KEY_ALL_APPLICATIONS', 'KEY_DASHBOARD'),
  205: 'KEY_SUSPEND',
  206: 'KEY_CLOSE',
  207: 'KEY_PLAY',
  208: 'KEY_FASTFORWARD',
  209: 'KEY_BASSBOOST',
  210: 'KEY_PRINT',
  211: 'KEY_HP',
  212: 'KEY_CAMERA',
  213: 'KEY_SOUND',
  214: 'KEY_QUESTION',
  215: 'KEY_EMAIL',
  216: 'KEY_CHAT',
  217: 'KEY_SEARCH',
  218: 'KEY_CONNECT',
  219: 'KEY_FINANCE',
  220: 'KEY_SPORT',
  221: 'KEY_SHOP',
  222: 'KEY_ALTERASE',
  223: 'KEY_CANCEL',
  224: 'KEY_BRIGHTNESSDOWN',
  225: 'KEY_BRIGHTNESSUP',
  226: 'KEY_MEDIA',
  227: 'KEY_SWITCHVIDEOMODE',
  228: 'KEY_KBDILLUMTOGGLE',
  229: 'KEY_KBDILLUMDOWN',
  230: 'KEY_KBDILLUMUP',
  231: 'KEY_SEND',
  232: 'KEY_REPLY',
  233: 'KEY_FORWARDMAIL',
  234: 'KEY_SAVE',
  235: 'KEY_DOCUMENTS',
  236: 'KEY_BATTERY',
  237: 'KEY_BLUETOOTH',
  238: 'KEY_WLAN',
  239: 'KEY_UWB',
  240: 'KEY_UNKNOWN',
  241: 'KEY_VIDEO_NEXT',
  242: 'KEY_VIDEO_PREV',
  243: 'KEY_BRIGHTNESS_CYCLE',
  244: ('KEY_BRIGHTNESS_AUTO', 'KEY_BRIGHTNESS_ZERO'),
  245: 'KEY_DISPLAY_OFF',
  246: ('KEY_WIMAX', 'KEY_WWAN'),
  247: 'KEY_RFKILL',
  248: 'KEY_MICMUTE',
  256: ('BTN_0', 'BTN_MISC'),
  257: 'BTN_1',
  258: 'BTN_2',
  259: 'BTN_3',
  260: 'BTN_4',
  261: 'BTN_5',
  262: 'BTN_6',
  263: 'BTN_7',
  264: 'BTN_8',
  265: 'BTN_9',
  272: ('BTN_LEFT', 'BTN_MOUSE'),
  273: 'BTN_RIGHT',
  274: 'BTN_MIDDLE',
  275: 'BTN_SIDE',
  276: 'BTN_EXTRA',
  277: 'BTN_FORWARD',
  278: 'BTN_BACK',
  279: 'BTN_TASK',
  288: ('BTN_JOYSTICK', 'BTN_TRIGGER'),
  289: 'BTN_THUMB',
  290: 'BTN_THUMB2',
  291: 'BTN_TOP',
  292: 'BTN_TOP2',
  293: 'BTN_PINKIE',
  294: 'BTN_BASE',
  295: 'BTN_BASE2',
  296: 'BTN_BASE3',
  297: 'BTN_BASE4',
  298: 'BTN_BASE5',
  299: 'BTN_BASE6',
  303: 'BTN_DEAD',
  304: ('BTN_A', 'BTN_GAMEPAD', 'BTN_SOUTH'),
  305: ('BTN_B', 'BTN_EAST'),
  306: 'BTN_C',
  307: ('BTN_NORTH', 'BTN_X'),
  308: ('BTN_WEST', 'BTN_Y'),
  309: 'BTN_Z',
  310: 'BTN_TL',
  311: 'BTN_TR',
  312: 'BTN_TL2',
  313: 'BTN_TR2',
  314: 'BTN_SELECT',
  315: 'BTN_START',
  316: 'BTN_MODE',
  317: 'BTN_THUMBL',
  318: 'BTN_THUMBR',
  320: ('BTN_DIGI', 'BTN_TOOL_PEN'),
  321: 'BTN_TOOL_RUBBER',
  322: 'BTN_TOOL_BRUSH',
  323: 'BTN_TOOL_PENCIL',
  324: 'BTN_TOOL_AIRBRUSH',
  325: 'BTN_TOOL_FINGER',
  326: 'BTN_TOOL_MOUSE',
  327: 'BTN_TOOL_LENS',
  328: 'BTN_TOOL_QUINTTAP',
  329: 'BTN_STYLUS3',
  330: 'BTN_TOUCH',
  331: 'BTN_STYLUS',
  332: 'BTN_STYLUS2',
  333: 'BTN_TOOL_DOUBLETAP',
  334: 'BTN_TOOL_TRIPLETAP',
  335: 'BTN_TOOL_QUADTAP',
  336: ('BTN_GEAR_DOWN', 'BTN_WHEEL'),
  337: 'BTN_GEAR_UP',
  352: 'KEY_OK',
  353: 'KEY_SELECT',
  354: 'KEY_GOTO',
  355: 'KEY_CLEAR',
  356: 'KEY_POWER2',
  357: 'KEY_OPTION',
  358: 'KEY_INFO',
  359: 'KEY_TIME',
  360: 'KEY_VENDOR',
  361: 'KEY_ARCHIVE',
  362: 'KEY_PROGRAM',
  363: 'KEY_CHANNEL',
  364: 'KEY_FAVORITES',
  365: 'KEY_EPG',
  366: 'KEY_PVR',
  367: 'KEY_MHP',
  368: 'KEY_LANGUAGE',
  369: 'KEY_TITLE',
  370: 'KEY_SUBTITLE',
  371: 'KEY_ANGLE',
  372: ('KEY_FULL_SCREEN', 'KEY_ZOOM'),
  373: 'KEY_MODE',
  374: 'KEY_KEYBOARD',
  375: ('KEY_ASPECT_RATIO', 'KEY_SCREEN'),
  376: 'KEY_PC',
  377: 'KEY_TV',
  378: 'KEY_TV2',
  379: 'KEY_VCR',
  380: 'KEY_VCR2',
  381: 'KEY_SAT',
  382: 'KEY_SAT2',
  383: 'KEY_CD',
  384: 'KEY_TAPE',
  385: 'KEY_RADIO',
  386: 'KEY_TUNER',
  387: 'KEY_PLAYER',
  388: 'KEY_TEXT',
  389: 'KEY_DVD',
  390: 'KEY_AUX',
  391: 'KEY_MP3',
  392: 'KEY_AUDIO',
  393: 'KEY_VIDEO',
  394: 'KEY_DIRECTORY',
  395: 'KEY_LIST',
  396: 'KEY_MEMO',
  397: 'KEY_CALENDAR',
  398: 'KEY_RED',
  399: 'KEY_GREEN',
  400: 'KEY_YELLOW',
  401: 'KEY_BLUE',
  402: 'KEY_CHANNELUP',
  403: 'KEY_CHANNELDOWN',
  404: 'KEY_FIRST',
  405: 'KEY_LAST',
  406: 'KEY_AB',
  407: 'KEY_NEXT',
  408: 'KEY_RESTART',
  409: 'KEY_SLOW',
  410: 'KEY_SHUFFLE',
  411: 'KEY_BREAK',
  412: 'KEY_PREVIOUS',
  413: 'KEY_DIGITS',
  414: 'KEY_TEEN',
  415: 'KEY_TWEN',
  416: 'KEY_VIDEOPHONE',
  417: 'KEY_GAMES',
  418: 'KEY_ZOOMIN',
  419: 'KEY_ZOOMOUT',
  420: 'KEY_ZOOMRESET',
  421: 'KEY_WORDPROCESSOR',
  422: 'KEY_EDITOR',
  423: 'KEY_SPREADSHEET',
  424: 'KEY_GRAPHICSEDITOR',
  425: 'KEY_PRESENTATION',
  426: 'KEY_DATABASE',
  427: 'KEY_NEWS',
  428: 'KEY_VOICEMAIL',
  429: 'KEY_ADDRESSBOOK',
  430: 'KEY_MESSENGER',
  431: ('KEY_BRIGHTNESS_TOGGLE', 'KEY_DISPLAYTOGGLE'),
  432: 'KEY_SPELLCHECK',
  433: 'KEY_LOGOFF',
  434: 'KEY_DOLLAR',
  435: 'KEY_EURO',
  436: 'KEY_FRAMEBACK',
  437: 'KEY_FRAMEFORWARD',
  438: 'KEY_CONTEXT_MENU',
  439: 'KEY_MEDIA_REPEAT',
  440: 'KEY_10CHANNELSUP',
  441: 'KEY_10CHANNELSDOWN',
  442: 'KEY_IMAGES',
  444: 'KEY_NOTIFICATION_CENTER',
  445: 'KEY_PICKUP_PHONE',
  446: 'KEY_HANGUP_PHONE',
  447: 'KEY_LINK_PHONE',
  448: 'KEY_DEL_EOL',
  449: 'KEY_DEL_EOS',
  450: 'KEY_INS_LINE',
  451: 'KEY_DEL_LINE',
  464: 'KEY_FN',
  465: 'KEY_FN_ESC',
  466: 'KEY_FN_F1',
  467: 'KEY_FN_F2',
  468: 'KEY_FN_F3',
  469: 'KEY_FN_F4',
  470: 'KEY_FN_F5',
  471: 'KEY_FN_F6',
  472: 'KEY_FN_F7',
  473: 'KEY_FN_F8',
  474: 'KEY_FN_F9',
  475: 'KEY_FN_F10',
  476: 'KEY_FN_F11',
  477: 'KEY_FN_F12',
  478: 'KEY_FN_1',
  479: 'KEY_FN_2',
  480: 'KEY_FN_D',
  481: 'KEY_FN_E',
  482: 'KEY_FN_F',
  483: 'KEY_FN_S',
  484: 'KEY_FN_B',
  485: 'KEY_FN_RIGHT_SHIFT',
  497: 'KEY_BRL_DOT1',
  498: 'KEY_BRL_DOT2',
  499: 'KEY_BRL_DOT3',
  500: 'KEY_BRL_DOT4',
  501: 'KEY_BRL_DOT5',
  502: 'KEY_BRL_DOT6',
  503: 'KEY_BRL_DOT7',
  504: 'KEY_BRL_DOT8',
  505: 'KEY_BRL_DOT9',
  506: 'KEY_BRL_DOT10',
  512: 'KEY_NUMERIC_0',
  513: 'KEY_NUMERIC_1',
  514: 'KEY_NUMERIC_2',
  515: 'KEY_NUMERIC_3',
  516: 'KEY_NUMERIC_4',
  517: 'KEY_NUMERIC_5',
  518: 'KEY_NUMERIC_6',
  519: 'KEY_NUMERIC_7',
  520: 'KEY_NUMERIC_8',
  521: 'KEY_NUMERIC_9',
  522: 'KEY_NUMERIC_STAR',
  523: 'KEY_NUMERIC_POUND',
  524: 'KEY_NUMERIC_A',
  525: 'KEY_NUMERIC_B',
  526: 'KEY_NUMERIC_C',
  527: 'KEY_NUMERIC_D',
  528: 'KEY_CAMERA_FOCUS',
  529: 'KEY_WPS_BUTTON',
  530: 'KEY_TOUCHPAD_TOGGLE',
  531: 'KEY_TOUCHPAD_ON',
  532: 'KEY_TOUCHPAD_OFF',
  533: 'KEY_CAMERA_ZOOMIN',
  534: 'KEY_CAMERA_ZOOMOUT',
  535: 'KEY_CAMERA_UP',
  536: 'KEY_CAMERA_DOWN',
  537: 'KEY_CAMERA_LEFT',
  538: 'KEY_CAMERA_RIGHT',
  539: 'KEY_ATTENDANT_ON',
  540: 'KEY_ATTENDANT_OFF',
  541: 'KEY_ATTENDANT_TOGGLE',
  542: 'KEY_LIGHTS_TOGGLE',
  544: 'BTN_DPAD_UP',
  545: 'BTN_DPAD_DOWN',
  546: 'BTN_DPAD_LEFT',
  547: 'BTN_DPAD_RIGHT',
  560: 'KEY_ALS_TOGGLE',
  561: 'KEY_ROTATE_LOCK_TOGGLE',
  562: 'KEY_REFRESH_RATE_TOGGLE',
  576: 'KEY_BUTTONCONFIG',
  577: 'KEY_TASKMANAGER',
  578: 'KEY_JOURNAL',
  579: 'KEY_CONTROLPANEL',
  580: 'KEY_APPSELECT',
  581: 'KEY_SCREENSAVER',
  582: 'KEY_VOICECOMMAND',
  583: 'KEY_ASSISTANT',
  584: 'KEY_KBD_LAYOUT_NEXT',
  585: 'KEY_EMOJI_PICKER',
  586: 'KEY_DICTATE',
  587: 'KEY_CAMERA_ACCESS_ENABLE',
  588: 'KEY_CAMERA_ACCESS_DISABLE',
  589: 'KEY_CAMERA_ACCESS_TOGGLE',
  590: 'KEY_ACCESSIBILITY',
  591: 'KEY_DO_NOT_DISTURB',
  592: 'KEY_BRIGHTNESS_MIN',
  593: 'KEY_BRIGHTNESS_MAX',
  608: 'KEY_KBDINPUTASSIST_PREV',
  609: 'KEY_KBDINPUTASSIST_NEXT',
  610: 'KEY_KBDINPUTASSIST_PREVGROUP',
  611: 'KEY_KBDINPUTASSIST_NEXTGROUP',
  612: 'KEY_KBDINPUTASSIST_ACCEPT',
  613: 'KEY_KBDINPUTASSIST_CANCEL',
  614: 'KEY_RIGHT_UP',
  615: 'KEY_RIGHT_DOWN',
  616: 'KEY_LEFT_UP',
  617: 'KEY_LEFT_DOWN',
  618: 'KEY_ROOT_MENU',
  619: 'KEY_MEDIA_TOP_MENU',
  620: 'KEY_NUMERIC_11',
  621: 'KEY_NUMERIC_12',
  622: 'KEY_AUDIO_DESC',
  623: 'KEY_3D_MODE',
  624: 'KEY_NEXT_FAVORITE',
  625: 'KEY_STOP_RECORD',
  626: 'KEY_PAUSE_RECORD',
  627: 'KEY_VOD',
  628: 'KEY_UNMUTE',
  629: 'KEY_FASTREVERSE',
  630: 'KEY_SLOWREVERSE',
  631: 'KEY_DATA',
  632: 'KEY_ONSCREEN_KEYBOARD',
  633: 'KEY_PRIVACY_SCREEN_TOGGLE',
  634: 'KEY_SELECTIVE_SCREENSHOT',
  635: 'KEY_NEXT_ELEMENT',
  636: 'KEY_PREVIOUS_ELEMENT',
  637: 'KEY_AUTOPILOT_ENGAGE_TOGGLE',
  638: 'KEY_MARK_WAYPOINT',
  639: 'KEY_SOS',
  640: 'KEY_NAV_CHART',
  641: 'KEY_FISHING_CHART',
  642: 'KEY_SINGLE_RANGE_RADAR',
  643: 'KEY_DUAL_RANGE_RADAR',
  644: 'KEY_RADAR_OVERLAY',
  645: 'KEY_TRADITIONAL_SONAR',
  646: 'KEY_CLEARVU_SONAR',
  647: 'KEY_SIDEVU_SONAR',
  648: 'KEY_NAV_INFO',
  649: 'KEY_BRIGHTNESS_MENU',
  656: 'KEY_MACRO1',
  657: 'KEY_MACRO2',
  658: 'KEY_MACRO3',
  659: 'KEY_MACRO4',
  660: 'KEY_MACRO5',
  661: 'KEY_MACRO6',
  662: 'KEY_MACRO7',
  663: 'KEY_MACRO8',
  664: 'KEY_MACRO9',
  665: 'KEY_MACRO10',
  666: 'KEY_MACRO11',
  667: 'KEY_MACRO12',
  668: 'KEY_MACRO13',
  669: 'KEY_MACRO14',
  670: 'KEY_MACRO15',
  671: 'KEY_MACRO16',
  672: 'KEY_MACRO17',
  673: 'KEY_MACRO18',
  674: 'KEY_MACRO19',
  675: 'KEY_MACRO20',
  676: 'KEY_MACRO21',
  677: 'KEY_MACRO22',
  678: 'KEY_MACRO23',
  679: 'KEY_MACRO24',
  680: 'KEY_MACRO25',
  681: 'KEY_MACRO26',
  682: 'KEY_MACRO27',
  683: 'KEY_MACRO28',
  684: 'KEY_MACRO29',
  685: 'KEY_MACRO30',
  688: 'KEY_MACRO_RECORD_START',
  689: 'KEY_MACRO_RECORD_STOP',
  690: 'KEY_MACRO_PRESET_CYCLE',
  691: 'KEY_MACRO_PRESET1',
  692: 'KEY_MACRO_PRESET2',
  693: 'KEY_MACRO_PRESET3',
  696: 'KEY_KBD_LCD_MENU1',
  697: 'KEY_KBD_LCD_MENU2',
  698: 'KEY_KBD_LCD_MENU3',
  699: 'KEY_KBD_LCD_MENU4',
  700: 'KEY_KBD_LCD_MENU5',
  704: ('BTN_TRIGGER_HAPPY', 'BTN_TRIGGER_HAPPY1'),
  705: 'BTN_TRIGGER_HAPPY2',
  706: 'BTN_TRIGGER_HAPPY3',
  707: 'BTN_TRIGGER_HAPPY4',
  708: 'BTN_TRIGGER_HAPPY5',
  709: 'BTN_TRIGGER_HAPPY6',
  710: 'BTN_TRIGGER_HAPPY7',
  711: 'BTN_TRIGGER_HAPPY8',
  712: 'BTN_TRIGGER_HAPPY9',
  713: 'BTN_TRIGGER_HAPPY10',
  714: 'BTN_TRIGGER_HAPPY11',
  715: 'BTN_TRIGGER_HAPPY12',
  716: 'BTN_TRIGGER_HAPPY13',
  717: 'BTN_TRIGGER_HAPPY14',
  718: 'BTN_TRIGGER_HAPPY15',
  719: 'BTN_TRIGGER_HAPPY16',
  720: 'BTN_TRIGGER_HAPPY17',
  721: 'BTN_TRIGGER_HAPPY18',
  722: 'BTN_TRIGGER_HAPPY19',
  723: 'BTN_TRIGGER_HAPPY20',
  724: 'BTN_TRIGGER_HAPPY21',
  725: 'BTN_TRIGGER_HAPPY22',
  726: 'BTN_TRIGGER_HAPPY23',
  727: 'BTN_TRIGGER_HAPPY24',
  728: 'BTN_TRIGGER_HAPPY25',
  729: 'BTN_TRIGGER_HAPPY26',
  730: 'BTN_TRIGGER_HAPPY27',
  731: 'BTN_TRIGGER_HAPPY28',
  732: 'BTN_TRIGGER_HAPPY29',
  733: 'BTN_TRIGGER_HAPPY30',
  734: 'BTN_TRIGGER_HAPPY31',
  735: 'BTN_TRIGGER_HAPPY32',
  736: 'BTN_TRIGGER_HAPPY33',
  737: 'BTN_TRIGGER_HAPPY34',
  738: 'BTN_TRIGGER_HAPPY35',
  739: 'BTN_TRIGGER_HAPPY36',
  740: 'BTN_TRIGGER_HAPPY37',
  741: 'BTN_TRIGGER_HAPPY38',
  742: 'BTN_TRIGGER_HAPPY39',
  743: 'BTN_TRIGGER_HAPPY40'}

KEY: Dict[int, Union[str, Tuple[str]]] = { 0: 'KEY_RESERVED',
  1: 'KEY_ESC',
  2: 'KEY_1',
  3: 'KEY_2',
  4: 'KEY_3',
  5: 'KEY_4',
  6: 'KEY_5',
  7: 'KEY_6',
  8: 'KEY_7',
  9: 'KEY_8',
  10: 'KEY_9',
  11: 'KEY_0',
  12: 'KEY_MINUS',
  13: 'KEY_EQUAL',
  14: 'KEY_BACKSPACE',
  15: 'KEY_TAB',
  16: 'KEY_Q',
  17: 'KEY_W',
  18: 'KEY_E',
  19: 'KEY_R',
  20: 'KEY_T',
  21: 'KEY_Y',
  22: 'KEY_U',
  23: 'KEY_I',
  24: 'KEY_O',
  25: 'KEY_P',
  26: 'KEY_LEFTBRACE',
  27: 'KEY_RIGHTBRACE',
  28: 'KEY_ENTER',
  29: 'KEY_LEFTCTRL',
  30: 'KEY_A',
  31: 'KEY_S',
  32: 'KEY_D',
  33: 'KEY_F',
  34: 'KEY_G',
  35: 'KEY_H',
  36: 'KEY_J',
  37: 'KEY_K',
  38: 'KEY_L',
  39: 'KEY_SEMICOLON',
  40: 'KEY_APOSTROPHE',
  41: 'KEY_GRAVE',
  42: 'KEY_LEFTSHIFT',
  43: 'KEY_BACKSLASH',
  44: 'KEY_Z',
  45: 'KEY_X',
  46: 'KEY_C',
  47: 'KEY_V',
  48: 'KEY_B',
  49: 'KEY_N',
  50: 'KEY_M',
  51: 'KEY_COMMA',
  52: 'KEY_DOT',
  53: 'KEY_SLASH',
  54: 'KEY_RIGHTSHIFT',
  55: 'KEY_KPASTERISK',
  56: 'KEY_LEFTALT',
  57: 'KEY_SPACE',
  58: 'KEY_CAPSLOCK',
  59: 'KEY_F1',
  60: 'KEY_F2',
  61: 'KEY_F3',
  62: 'KEY_F4',
  63: 'KEY_F5',
  64: 'KEY_F6',
  65: 'KEY_F7',
  66: 'KEY_F8',
  67: 'KEY_F9',
  68: 'KEY_F10',
  69: 'KEY_NUMLOCK',
  70: 'KEY_SCROLLLOCK',
  71: 'KEY_KP7',
  72: 'KEY_KP8',
  73: 'KEY_KP9',
  74: 'KEY_KPMINUS',
  75: 'KEY_KP4',
  76: 'KEY_KP5',
  77: 'KEY_KP6',
  78: 'KEY_KPPLUS',
  79: 'KEY_KP1',
  80: 'KEY_KP2',
  81: 'KEY_KP3',
  82: 'KEY_KP0',
  83: 'KEY_KPDOT',
  85: 'KEY_ZENKAKUHANKAKU',
  86: 'KEY_102ND',
  87: 'KEY_F11',
  88: 'KEY_F12',
  89: 'KEY_RO',
  90: 'KEY_KATAKANA',
  91: 'KEY_HIRAGANA',
  92: 'KEY_HENKAN',
  93: 'KEY_KATAKANAHIRAGANA',
  94: 'KEY_MUHENKAN',
  95: 'KEY_KPJPCOMMA',
  96: 'KEY_KPENTER',
  97: 'KEY_RIGHTCTRL',
  98: 'KEY_KPSLASH',
  99: 'KEY_SYSRQ',
  100: 'KEY_RIGHTALT',
  101: 'KEY_LINEFEED',
  102: 'KEY_HOME',
  103: 'KEY_UP',
  104: 'KEY_PAGEUP',
  105: 'KEY_LEFT',
  106: 'KEY_RIGHT',
  107: 'KEY_END',
  108: 'KEY_DOWN',
  109: 'KEY_PAGEDOWN',
  110: 'KEY_INSERT',
  111: 'KEY_DELETE',
  112: 'KEY_MACRO',
  113: ('KEY_MIN_INTERESTING', 'KEY_MUTE'),
  114: 'KEY_VOLUMEDOWN',
  115: 'KEY_VOLUMEUP',
  116: 'KEY_POWER',
  117: 'KEY_KPEQUAL',
  118: 'KEY_KPPLUSMINUS',
  119: 'KEY_PAUSE',
  120: 'KEY_SCALE',
  121: 'KEY_KPCOMMA',
  122: ('KEY_HANGEUL', 'KEY_HANGUEL'),
  123: 'KEY_HANJA',
  124: 'KEY_YEN',
  125: 'KEY_LEFTMETA',
  126: 'KEY_RIGHTMETA',
  127: 'KEY_COMPOSE',
  128: 'KEY_STOP',
  129: 'KEY_AGAIN',
  130: 'KEY_PROPS',
  131: 'KEY_UNDO',
  132: 'KEY_FRONT',
  133: 'KEY_COPY',
  134: 'KEY_OPEN',
  135: 'KEY_PASTE',
  136: 'KEY_FIND',
  137: 'KEY_CUT',
  138: 'KEY_HELP',
  139: 'KEY_MENU',
  140: 'KEY_CALC',
  141: 'KEY_SETUP',
  142: 'KEY_SLEEP',
  143: 'KEY_WAKEUP',
  144: 'KEY_FILE',
  145: 'KEY_SENDFILE',
  146: 'KEY_DELETEFILE',
  147: 'KEY_XFER',
  148: 'KEY_PROG1',
  149: 'KEY_PROG2',
  150: 'KEY_WWW',
  151: 'KEY_MSDOS',
  152: ('KEY_COFFEE', 'KEY_SCREENLOCK'),
  153: ('KEY_DIRECTION', 'KEY_ROTATE_DISPLAY'),
  154: 'KEY_CYCLEWINDOWS',
  155: 'KEY_MAIL',
  156: 'KEY_BOOKMARKS',
  157: 'KEY_COMPUTER',
  158: 'KEY_BACK',
  159: 'KEY_FORWARD',
  160: 'KEY_CLOSECD',
  161: 'KEY_EJECTCD',
  162: 'KEY_EJECTCLOSECD',
  163: 'KEY_NEXTSONG',
  164: 'KEY_PLAYPAUSE',
  165: 'KEY_PREVIOUSSONG',
  166: 'KEY_STOPCD',
  167: 'KEY_RECORD',
  168: 'KEY_REWIND',
  169: 'KEY_PHONE',
  170: 'KEY_ISO',
  171: 'KEY_CONFIG',
  172: 'KEY_HOMEPAGE',
  173: 'KEY_REFRESH',
  174: 'KEY_EXIT',
  175: 'KEY_MOVE',
  176: 'KEY_EDIT',
  177: 'KEY_SCROLLUP',
  178: 'KEY_SCROLLDOWN',
  179: 'KEY_KPLEFTPAREN',
  180: 'KEY_KPRIGHTPAREN',
  181: 'KEY_NEW',
  182: 'KEY_REDO',
  183: 'KEY_F13',
  184: 'KEY_F14',
  185: 'KEY_F15',
  186: 'KEY_F16',
  187: 'KEY_F17',
  188: 'KEY_F18',
  189: 'KEY_F19',
  190: 'KEY_F20',
  191: 'KEY_F21',
  192: 'KEY_F22',
  193: 'KEY_F23',
  194: 'KEY_F24',
  200: 'KEY_PLAYCD',
  201: 'KEY_PAUSECD',
  202: 'KEY_PROG3',
  203: 'KEY_PROG4',
  204: ('KEY_ALL_APPLICATIONS', 'KEY_DASHBOARD'),
  205: 'KEY_SUSPEND',
  206: 'KEY_CLOSE',
  207: 'KEY_PLAY',
  208: 'KEY_FASTFORWARD',
  209: 'KEY_BASSBOOST',
  210: 'KEY_PRINT',
  211: 'KEY_HP',
  212: 'KEY_CAMERA',
  213: 'KEY_SOUND',
  214: 'KEY_QUESTION',
  215: 'KEY_EMAIL',
  216: 'KEY_CHAT',
  217: 'KEY_SEARCH',
  218: 'KEY_CONNECT',
  219: 'KEY_FINANCE',
  220: 'KEY_SPORT',
  221: 'KEY_SHOP',
  222: 'KEY_ALTERASE',
  223: 'KEY_CANCEL',
  224: 'KEY_BRIGHTNESSDOWN',
  225: 'KEY_BRIGHTNESSUP',
  226: 'KEY_MEDIA',
  227: 'KEY_SWITCHVIDEOMODE',
  228: 'KEY_KBDILLUMTOGGLE',
  229: 'KEY_KBDILLUMDOWN',
  230: 'KEY_KBDILLUMUP',
  231: 'KEY_SEND',
  232: 'KEY_REPLY',
  233: 'KEY_FORWARDMAIL',
  234: 'KEY_SAVE',
  235: 'KEY_DOCUMENTS',
  236: 'KEY_BATTERY',
  237: 'KEY_BLUETOOTH',
  238: 'KEY_WLAN',
  239: 'KEY_UWB',
  240: 'KEY_UNKNOWN',
  241: 'KEY_VIDEO_NEXT',
  242: 'KEY_VIDEO_PREV',
  243: 'KEY_BRIGHTNESS_CYCLE',
  244: ('KEY_BRIGHTNESS_AUTO', 'KEY_BRIGHTNESS_ZERO'),
  245: 'KEY_DISPLAY_OFF',
  246: ('KEY_WIMAX', 'KEY_WWAN'),
  247: 'KEY_RFKILL',
  248: 'KEY_MICMUTE',
  352: 'KEY_OK',
  353: 'KEY_SELECT',
  354: 'KEY_GOTO',
  355: 'KEY_CLEAR',
  356: 'KEY_POWER2',
  357: 'KEY_OPTION',
  358: 'KEY_INFO',
  359: 'KEY_TIME',
  360: 'KEY_VENDOR',
  361: 'KEY_ARCHIVE',
  362: 'KEY_PROGRAM',
  363: 'KEY_CHANNEL',
  364: 'KEY_FAVORITES',
  365: 'KEY_EPG',
  366: 'KEY_PVR',
  367: 'KEY_MHP',
  368: 'KEY_LANGUAGE',
  369: 'KEY_TITLE',
  370: 'KEY_SUBTITLE',
  371: 'KEY_ANGLE',
  372: ('KEY_FULL_SCREEN', 'KEY_ZOOM'),
  373: 'KEY_MODE',
  374: 'KEY_KEYBOARD',
  375: ('KEY_ASPECT_RATIO', 'KEY_SCREEN'),
  376: 'KEY_PC',
  377: 'KEY_TV',
  378: 'KEY_TV2',
  379: 'KEY_VCR',
  380: 'KEY_VCR2',
  381: 'KEY_SAT',
  382: 'KEY_SAT2',
  383: 'KEY_CD',
  384: 'KEY_TAPE',
  385: 'KEY_RADIO',
  386: 'KEY_TUNER',
  387: 'KEY_PLAYER',
  388: 'KEY_TEXT',
  389: 'KEY_DVD',
  390: 'KEY_AUX',
  391: 'KEY_MP3',
  392: 'KEY_AUDIO',
  393: 'KEY_VIDEO',
  394: 'KEY_DIRECTORY',
  395: 'KEY_LIST',
  396: 'KEY_MEMO',
  397: 'KEY_CALENDAR',
  398: 'KEY_RED',
  399: 'KEY_GREEN',
  400: 'KEY_YELLOW',
  401: 'KEY_BLUE',
  402: 'KEY_CHANNELUP',
  403: 'KEY_CHANNELDOWN',
  404: 'KEY_FIRST',
  405: 'KEY_LAST',
  406: 'KEY_AB',
  407: 'KEY_NEXT',
  408: 'KEY_RESTART',
  409: 'KEY_SLOW',
  410: 'KEY_SHUFFLE',
  411: 'KEY_BREAK',
  412: 'KEY_PREVIOUS',
  413: 'KEY_DIGITS',
  414: 'KEY_TEEN',
  415: 'KEY_TWEN',
  416: 'KEY_VIDEOPHONE',
  417: 'KEY_GAMES',
  418: 'KEY_ZOOMIN',
  419: 'KEY_ZOOMOUT',
  420: 'KEY_ZOOMRESET',
  421: 'KEY_WORDPROCESSOR',
  422: 'KEY_EDITOR',
  423: 'KEY_SPREADSHEET',
  424: 'KEY_GRAPHICSEDITOR',
  425: 'KEY_PRESENTATION',
  426: 'KEY_DATABASE',
  427: 'KEY_NEWS',
  428: 'KEY_VOICEMAIL',
  429: 'KEY_ADDRESSBOOK',
  430: 'KEY_MESSENGER',
  431: ('KEY_BRIGHTNESS_TOGGLE', 'KEY_DISPLAYTOGGLE'),
  432: 'KEY_SPELLCHECK',
  433: 'KEY_LOGOFF',
  434: 'KEY_DOLLAR',
  435: 'KEY_EURO',
  436: 'KEY_FRAMEBACK',
  437: 'KEY_FRAMEFORWARD',
  438: 'KEY_CONTEXT_MENU',
  439: 'KEY_MEDIA_REPEAT',
  440: 'KEY_10CHANNELSUP',
  441: 'KEY_10CHANNELSDOWN',
  442: 'KEY_IMAGES',
  444: 'KEY_NOTIFICATION_CENTER',
  445: 'KEY_PICKUP_PHONE',
  446: 'KEY_HANGUP_PHONE',
  447: 'KEY_LINK_PHONE',
  448: 'KEY_DEL_EOL',
  449: 'KEY_DEL_EOS',
  450: 'KEY_INS_LINE',
  451: 'KEY_DEL_LINE',
  464: 'KEY_FN',
  465: 'KEY_FN_ESC',
  466: 'KEY_FN_F1',
  467: 'KEY_FN_F2',
  468: 'KEY_FN_F3',
  469: 'KEY_FN_F4',
  470: 'KEY_FN_F5',
  471: 'KEY_FN_F6',
  472: 'KEY_FN_F7',
  473: 'KEY_FN_F8',
  474: 'KEY_FN_F9',
  475: 'KEY_FN_F10',
  476: 'KEY_FN_F11',
  477: 'KEY_FN_F12',
  478: 'KEY_FN_1',
  479: 'KEY_FN_2',
  480: 'KEY_FN_D',
  481: 'KEY_FN_E',
  482: 'KEY_FN_F',
  483: 'KEY_FN_S',
  484: 'KEY_FN_B',
  485: 'KEY_FN_RIGHT_SHIFT',
  497: 'KEY_BRL_DOT1',
  498: 'KEY_BRL_DOT2',
  499: 'KEY_BRL_DOT3',
  500: 'KEY_BRL_DOT4',
  501: 'KEY_BRL_DOT5',
  502: 'KEY_BRL_DOT6',
  503: 'KEY_BRL_DOT7',
  504: 'KEY_BRL_DOT8',
  505: 'KEY_BRL_DOT9',
  506: 'KEY_BRL_DOT10',
  512: 'KEY_NUMERIC_0',
  513: 'KEY_NUMERIC_1',
  514: 'KEY_NUMERIC_2',
  515: 'KEY_NUMERIC_3',
  516: 'KEY_NUMERIC_4',
  517: 'KEY_NUMERIC_5',
  518: 'KEY_NUMERIC_6',
  519: 'KEY_NUMERIC_7',
  520: 'KEY_NUMERIC_8',
  521: 'KEY_NUMERIC_9',
  522: 'KEY_NUMERIC_STAR',
  523: 'KEY_NUMERIC_POUND',
  524: 'KEY_NUMERIC_A',
  525: 'KEY_NUMERIC_B',
  526: 'KEY_NUMERIC_C',
  527: 'KEY_NUMERIC_D',
  528: 'KEY_CAMERA_FOCUS',
  529: 'KEY_WPS_BUTTON',
  530: 'KEY_TOUCHPAD_TOGGLE',
  531: 'KEY_TOUCHPAD_ON',
  532: 'KEY_TOUCHPAD_OFF',
  533: 'KEY_CAMERA_ZOOMIN',
  534: 'KEY_CAMERA_ZOOMOUT',
  535: 'KEY_CAMERA_UP',
  536: 'KEY_CAMERA_DOWN',
  537: 'KEY_CAMERA_LEFT',
  538: 'KEY_CAMERA_RIGHT',
  539: 'KEY_ATTENDANT_ON',
  540: 'KEY_ATTENDANT_OFF',
  541: 'KEY_ATTENDANT_TOGGLE',
  542: 'KEY_LIGHTS_TOGGLE',
  560: 'KEY_ALS_TOGGLE',
  561: 'KEY_ROTATE_LOCK_TOGGLE',
  562: 'KEY_REFRESH_RATE_TOGGLE',
  576: 'KEY_BUTTONCONFIG',
  577: 'KEY_TASKMANAGER',
  578: 'KEY_JOURNAL',
  579: 'KEY_CONTROLPANEL',
  580: 'KEY_APPSELECT',
  581: 'KEY_SCREENSAVER',
  582: 'KEY_VOICECOMMAND',
  583: 'KEY_ASSISTANT',
  584: 'KEY_KBD_LAYOUT_NEXT',
  585: 'KEY_EMOJI_PICKER',
  586: 'KEY_DICTATE',
  587: 'KEY_CAMERA_ACCESS_ENABLE',
  588: 'KEY_CAMERA_ACCESS_DISABLE',
  589: 'KEY_CAMERA_ACCESS_TOGGLE',
  590: 'KEY_ACCESSIBILITY',
  591: 'KEY_DO_NOT_DISTURB',
  592: 'KEY_BRIGHTNESS_MIN',
  593: 'KEY_BRIGHTNESS_MAX',
  608: 'KEY_KBDINPUTASSIST_PREV',
  609: 'KEY_KBDINPUTASSIST_NEXT',
  610: 'KEY_KBDINPUTASSIST_PREVGROUP',
  611: 'KEY_KBDINPUTASSIST_NEXTGROUP',
  612: 'KEY_KBDINPUTASSIST_ACCEPT',
  613: 'KEY_KBDINPUTASSIST_CANCEL',
  614: 'KEY_RIGHT_UP',
  615: 'KEY_RIGHT_DOWN',
  616: 'KEY_LEFT_UP',
  617: 'KEY_LEFT_DOWN',
  618: 'KEY_ROOT_MENU',
  619: 'KEY_MEDIA_TOP_MENU',
  620: 'KEY_NUMERIC_11',
  621: 'KEY_NUMERIC_12',
  622: 'KEY_AUDIO_DESC',
  623: 'KEY_3D_MODE',
  624: 'KEY_NEXT_FAVORITE',
  625: 'KEY_STOP_RECORD',
  626: 'KEY_PAUSE_RECORD',
  627: 'KEY_VOD',
  628: 'KEY_UNMUTE',
  629: 'KEY_FASTREVERSE',
  630: 'KEY_SLOWREVERSE',
  631: 'KEY_DATA',
  632: 'KEY_ONSCREEN_KEYBOARD',
  633: 'KEY_PRIVACY_SCREEN_TOGGLE',
  634: 'KEY_SELECTIVE_SCREENSHOT',
  635: 'KEY_NEXT_ELEMENT',
  636: 'KEY_PREVIOUS_ELEMENT',
  637: 'KEY_AUTOPILOT_ENGAGE_TOGGLE',
  638: 'KEY_MARK_WAYPOINT',
  639: 'KEY_SOS',
  640: 'KEY_NAV_CHART',
  641: 'KEY_FISHING_CHART',
  642: 'KEY_SINGLE_RANGE_RADAR',
  643: 'KEY_DUAL_RANGE_RADAR',
  644: 'KEY_RADAR_OVERLAY',
  645: 'KEY_TRADITIONAL_SONAR',
  646: 'KEY_CLEARVU_SONAR',
  647: 'KEY_SIDEVU_SONAR',
  648: 'KEY_NAV_INFO',
  649: 'KEY_BRIGHTNESS_MENU',
  656: 'KEY_MACRO1',
  657: 'KEY_MACRO2',
  658: 'KEY_MACRO3',
  659: 'KEY_MACRO4',
  660: 'KEY_MACRO5',
  661: 'KEY_MACRO6',
  662: 'KEY_MACRO7',
  663: 'KEY_MACRO8',
  664: 'KEY_MACRO9',
  665: 'KEY_MACRO10',
  666: 'KEY_MACRO11',
  667: 'KEY_MACRO12',
  668: 'KEY_MACRO13',
  669: 'KEY_MACRO14',
  670: 'KEY_MACRO15',
  671: 'KEY_MACRO16',
  672: 'KEY_MACRO17',
  673: 'KEY_MACRO18',
  674: 'KEY_MACRO19',
  675: 'KEY_MACRO20',
  676: 'KEY_MACRO21',
  677: 'KEY_MACRO22',
  678: 'KEY_MACRO23',
  679: 'KEY_MACRO24',
  680: 'KEY_MACRO25',
  681: 'KEY_MACRO26',
  682: 'KEY_MACRO27',
  683: 'KEY_MACRO28',
  684: 'KEY_MACRO29',
  685: 'KEY_MACRO30',
  688: 'KEY_MACRO_RECORD_START',
  689: 'KEY_MACRO_RECORD_STOP',
  690: 'KEY_MACRO_PRESET_CYCLE',
  691: 'KEY_MACRO_PRESET1',
  692: 'KEY_MACRO_PRESET2',
  693: 'KEY_MACRO_PRESET3',
  696: 'KEY_KBD_LCD_MENU1',
  697: 'KEY_KBD_LCD_MENU2',
  698: 'KEY_KBD_LCD_MENU3',
  699: 'KEY_KBD_LCD_MENU4',
  700: 'KEY_KBD_LCD_MENU5',
  767: 'KEY_MAX',
  768: 'KEY_CNT'}

ABS: Dict[int, Union[str, Tuple[str]]] = { 0: 'ABS_X',
  1: 'ABS_Y',
  2: 'ABS_Z',
  3: 'ABS_RX',
  4: 'ABS_RY',
  5: 'ABS_RZ',
  6: 'ABS_THROTTLE',
  7: 'ABS_RUDDER',
  8: 'ABS_WHEEL',
  9: 'ABS_GAS',
  10: 'ABS_BRAKE',
  16: 'ABS_HAT0X',
  17: 'ABS_HAT0Y',
  18: 'ABS_HAT1X',
  19: 'ABS_HAT1Y',
  20: 'ABS_HAT2X',
  21: 'ABS_HAT2Y',
  22: 'ABS_HAT3X',
  23: 'ABS_HAT3Y',
  24: 'ABS_PRESSURE',
  25: 'ABS_DISTANCE',
  26: 'ABS_TILT_X',
  27: 'ABS_TILT_Y',
  28: 'ABS_TOOL_WIDTH',
  32: 'ABS_VOLUME',
  33: 'ABS_PROFILE',
  40: 'ABS_MISC',
  46: 'ABS_RESERVED',
  47: 'ABS_MT_SLOT',
  48: 'ABS_MT_TOUCH_MAJOR',
  49: 'ABS_MT_TOUCH_MINOR',
  50: 'ABS_MT_WIDTH_MAJOR',
  51: 'ABS_MT_WIDTH_MINOR',
  52: 'ABS_MT_ORIENTATION',
  53: 'ABS_MT_POSITION_X',
  54: 'ABS_MT_POSITION_Y',
  55: 'ABS_MT_TOOL_TYPE',
  56: 'ABS_MT_BLOB_ID',
  57: 'ABS_MT_TRACKING_ID',
  58: 'ABS_MT_PRESSURE',
  59: 'ABS_MT_DISTANCE',
  60: 'ABS_MT_TOOL_X',
  61: 'ABS_MT_TOOL_Y',
  63: 'ABS_MAX',
  64: 'ABS_CNT'}

REL: Dict[int, Union[str, Tuple[str]]] = { 0: 'REL_X',
  1: 'REL_Y',
  2: 'REL_Z',
  3: 'REL_RX',
  4: 'REL_RY',
  5: 'REL_RZ',
  6: 'REL_HWHEEL',
  7: 'REL_DIAL',
  8: 'REL_WHEEL',
  9: 'REL_MISC',
  10: 'REL_RESERVED',
  11: 'REL_WHEEL_HI_RES',
  12: 'REL_HWHEEL_HI_RES',
  15: 'REL_MAX',
  16: 'REL_CNT'}

SW: Dict[int, Union[str, Tuple[str]]] = { 0: 'SW_LID',
  1: 'SW_TABLET_MODE',
  2: 'SW_HEADPHONE_INSERT',
  3: ('SW_RADIO', 'SW_RFKILL_ALL'),
  4: 'SW_MICROPHONE_INSERT',
  5: 'SW_DOCK',
  6: 'SW_LINEOUT_INSERT',
  7: 'SW_JACK_PHYSICAL_INSERT',
  8: 'SW_VIDEOOUT_INSERT',
  9: 'SW_CAMERA_LENS_COVER',
  10: 'SW_KEYPAD_SLIDE',
  11: 'SW_FRONT_PROXIMITY',
  12: 'SW_ROTATE_LOCK',
  13: 'SW_LINEIN_INSERT',
  14: 'SW_MUTE_DEVICE',
  15: 'SW_PEN_INSERTED',
  16: ('SW_MACHINE_COVER', 'SW_MAX'),
  17: 'SW_CNT'}

MSC: Dict[int, Union[str, Tuple[str]]] = { 0: 'MSC_SERIAL',
  1: 'MSC_PULSELED',
  2: 'MSC_GESTURE',
  3: 'MSC_RAW',
  4: 'MSC_SCAN',
  5: 'MSC_TIMESTAMP',
  7: 'MSC_MAX',
  8: 'MSC_CNT'}

LED: Dict[int, Union[str, Tuple[str]]] = { 0: 'LED_NUML',
  1: 'LED_CAPSL',
  2: 'LED_SCROLLL',
  3: 'LED_COMPOSE',
  4: 'LED_KANA',
  5: 'LED_SLEEP',
  6: 'LED_SUSPEND',
  7: 'LED_MUTE',
  8: 'LED_MISC',
  9: 'LED_MAIL',
  10: 'LED_CHARGING',
  15: 'LED_MAX',
  16: 'LED_CNT'}

BTN: Dict[int, Union[str, Tuple[str]]] = { 256: ('BTN_0', 'BTN_MISC'),
  257: 'BTN_1',
  258: 'BTN_2',
  259: 'BTN_3',
  260: 'BTN_4',
  261: 'BTN_5',
  262: 'BTN_6',
  263: 'BTN_7',
  264: 'BTN_8',
  265: 'BTN_9',
  272: ('BTN_LEFT', 'BTN_MOUSE'),
  273: 'BTN_RIGHT',
  274: 'BTN_MIDDLE',
  275: 'BTN_SIDE',
  276: 'BTN_EXTRA',
  277: 'BTN_FORWARD',
  278: 'BTN_BACK',
  279: 'BTN_TASK',
  288: ('BTN_JOYSTICK', 'BTN_TRIGGER'),
  289: 'BTN_THUMB',
  290: 'BTN_THUMB2',
  291: 'BTN_TOP',
  292: 'BTN_TOP2',
  293: 'BTN_PINKIE',
  294: 'BTN_BASE',
  295: 'BTN_BASE2',
  296: 'BTN_BASE3',
  297: 'BTN_BASE4',
  298: 'BTN_BASE5',
  299: 'BTN_BASE6',
  303: 'BTN_DEAD',
  304: ('BTN_A', 'BTN_GAMEPAD', 'BTN_SOUTH'),
  305: ('BTN_B', 'BTN_EAST'),
  306: 'BTN_C',
  307: ('BTN_NORTH', 'BTN_X'),
  308: ('BTN_WEST', 'BTN_Y'),
  309: 'BTN_Z',
  310: 'BTN_TL',
  311: 'BTN_TR',
  312: 'BTN_TL2',
  313: 'BTN_TR2',
  314: 'BTN_SELECT',
  315: 'BTN_START',
  316: 'BTN_MODE',
  317: 'BTN_THUMBL',
  318: 'BTN_THUMBR',
  320: ('BTN_DIGI', 'BTN_TOOL_PEN'),
  321: 'BTN_TOOL_RUBBER',
  322: 'BTN_TOOL_BRUSH',
  323: 'BTN_TOOL_PENCIL',
  324: 'BTN_TOOL_AIRBRUSH',
  325: 'BTN_TOOL_FINGER',
  326: 'BTN_TOOL_MOUSE',
  327: 'BTN_TOOL_LENS',
  328: 'BTN_TOOL_QUINTTAP',
  329: 'BTN_STYLUS3',
  330: 'BTN_TOUCH',
  331: 'BTN_STYLUS',
  332: 'BTN_STYLUS2',
  333: 'BTN_TOOL_DOUBLETAP',
  334: 'BTN_TOOL_TRIPLETAP',
  335: 'BTN_TOOL_QUADTAP',
  336: ('BTN_GEAR_DOWN', 'BTN_WHEEL'),
  337: 'BTN_GEAR_UP',
  544: 'BTN_DPAD_UP',
  545: 'BTN_DPAD_DOWN',
  546: 'BTN_DPAD_LEFT',
  547: 'BTN_DPAD_RIGHT',
  704: ('BTN_TRIGGER_HAPPY', 'BTN_TRIGGER_HAPPY1'),
  705: 'BTN_TRIGGER_HAPPY2',
  706: 'BTN_TRIGGER_HAPPY3',
  707: 'BTN_TRIGGER_HAPPY4',
  708: 'BTN_TRIGGER_HAPPY5',
  709: 'BTN_TRIGGER_HAPPY6',
  710: 'BTN_TRIGGER_HAPPY7',
  711: 'BTN_TRIGGER_HAPPY8',
  712: 'BTN_TRIGGER_HAPPY9',
  713: 'BTN_TRIGGER_HAPPY10',
  714: 'BTN_TRIGGER_HAPPY11',
  715: 'BTN_TRIGGER_HAPPY12',
  716: 'BTN_TRIGGER_HAPPY13',
  717: 'BTN_TRIGGER_HAPPY14',
  718: 'BTN_TRIGGER_HAPPY15',
  719: 'BTN_TRIGGER_HAPPY16',
  720: 'BTN_TRIGGER_HAPPY17',
  721: 'BTN_TRIGGER_HAPPY18',
  722: 'BTN_TRIGGER_HAPPY19',
  723: 'BTN_TRIGGER_HAPPY20',
  724: 'BTN_TRIGGER_HAPPY21',
  725: 'BTN_TRIGGER_HAPPY22',
  726: 'BTN_TRIGGER_HAPPY23',
  727: 'BTN_TRIGGER_HAPPY24',
  728: 'BTN_TRIGGER_HAPPY25',
  729: 'BTN_TRIGGER_HAPPY26',
  730: 'BTN_TRIGGER_HAPPY27',
  731: 'BTN_TRIGGER_HAPPY28',
  732: 'BTN_TRIGGER_HAPPY29',
  733: 'BTN_TRIGGER_HAPPY30',
  734: 'BTN_TRIGGER_HAPPY31',
  735: 'BTN_TRIGGER_HAPPY32',
  736: 'BTN_TRIGGER_HAPPY33',
  737: 'BTN_TRIGGER_HAPPY34',
  738: 'BTN_TRIGGER_HAPPY35',
  739: 'BTN_TRIGGER_HAPPY36',
  740: 'BTN_TRIGGER_HAPPY37',
  741: 'BTN_TRIGGER_HAPPY38',
  742: 'BTN_TRIGGER_HAPPY39',
  743: 'BTN_TRIGGER_HAPPY40'}

REP: Dict[int, Union[str, Tuple[str]]] = {0: 'REP_DELAY', 1: ('REP_MAX', 'REP_PERIOD'), 2: 'REP_CNT'}

SND: Dict[int, Union[str, Tuple[str]]] = {0: 'SND_CLICK', 1: 'SND_BELL', 2: 'SND_TONE', 7: 'SND_MAX', 8: 'SND_CNT'}

ID: Dict[int, Union[str, Tuple[str]]] = {0: 'ID_BUS', 1: 'ID_VENDOR', 2: 'ID_PRODUCT', 3: 'ID_VERSION'}

EV: Dict[int, Union[str, Tuple[str]]] = { 0: 'EV_SYN',
  1: 'EV_KEY',
  2: 'EV_REL',
  3: 'EV_ABS',
  4: 'EV_MSC',
  5: 'EV_SW',
  17: 'EV_LED',
  18: 'EV_SND',
  20: 'EV_REP',
  21: 'EV_FF',
  22: 'EV_PWR',
  23: 'EV_FF_STATUS',
  31: 'EV_MAX',
  32: 'EV_CNT',
  257: 'EV_UINPUT',
  65537: 'EV_VERSION'}

BUS: Dict[int, Union[str, Tuple[str]]] = { 1: 'BUS_PCI',
  2: 'BUS_ISAPNP',
  3: 'BUS_USB',
  4: 'BUS_HIL',
  5: 'BUS_BLUETOOTH',
  6: 'BUS_VIRTUAL',
  16: 'BUS_ISA',
  17: 'BUS_I8042',
  18: 'BUS_XTKBD',
  19: 'BUS_RS232',
  20: 'BUS_GAMEPORT',
  21: 'BUS_PARPORT',
  22: 'BUS_AMIGA',
  23: 'BUS_ADB',
  24: 'BUS_I2C',
  25: 'BUS_HOST',
  26: 'BUS_GSC',
  27: 'BUS_ATARI',
  28: 'BUS_SPI',
  29: 'BUS_RMI',
  30: 'BUS_CEC',
  31: 'BUS_INTEL_ISHTP',
  32: 'BUS_AMD_SFH'}

SYN: Dict[int, Union[str, Tuple[str]]] = {0: 'SYN_REPORT', 1: 'SYN_CONFIG', 2: 'SYN_MT_REPORT', 3: 'SYN_DROPPED', 15: 'SYN_MAX', 16: 'SYN_CNT'}

FF: Dict[int, Union[str, Tuple[str]]] = { 80: ('FF_EFFECT_MIN', 'FF_RUMBLE'),
  81: 'FF_PERIODIC',
  82: 'FF_CONSTANT',
  83: 'FF_SPRING',
  84: 'FF_FRICTION',
  85: 'FF_DAMPER',
  86: 'FF_INERTIA',
  87: ('FF_EFFECT_MAX', 'FF_RAMP'),
  88: ('FF_SQUARE', 'FF_WAVEFORM_MIN'),
  89: 'FF_TRIANGLE',
  90: 'FF_SINE',
  91: 'FF_SAW_UP',
  92: 'FF_SAW_DOWN',
  93: ('FF_CUSTOM', 'FF_WAVEFORM_MAX'),
  96: ('FF_GAIN', 'FF_MAX_EFFECTS'),
  97: 'FF_AUTOCENTER',
  127: 'FF_MAX',
  128: 'FF_CNT'}

UI_FF: Dict[int, Union[str, Tuple[str]]] = {1: 'UI_FF_UPLOAD', 2: 'UI_FF_ERASE'}

FF_STATUS: Dict[int, Union[str, Tuple[str]]] = {0: 'FF_STATUS_STOPPED', 1: ('FF_STATUS_MAX', 'FF_STATUS_PLAYING')}

INPUT_PROP: Dict[int, Union[str, Tuple[str]]] = { 0: 'INPUT_PROP_POINTER',
  1: 'INPUT_PROP_DIRECT',
  2: 'INPUT_PROP_BUTTONPAD',
  3: 'INPUT_PROP_SEMI_MT',
  4: 'INPUT_PROP_TOPBUTTONPAD',
  5: 'INPUT_PROP_POINTING_STICK',
  6: 'INPUT_PROP_ACCELEROMETER',
  31: 'INPUT_PROP_MAX',
  32: 'INPUT_PROP_CNT'}

