# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
botnet_env/
venv/
env/
ENV/

# Database files
*.db
*.sqlite*
*.db-journal

# Logs
logs/
*.log

# Temporary files
temp/
tmp/
*.tmp
*.temp

# IDE
.vscode/launch.json
.vscode/tasks.json
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Testing
.pytest_cache/
.coverage
htmlcov/

# Data files
data/
*.csv
*.json.bak
