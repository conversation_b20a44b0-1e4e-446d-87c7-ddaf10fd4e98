# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
botnet_env/
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
logs/

# Databases
*.db
*.sqlite3
data/

# OS
.DS_Store
Thumbs.db

# Security
*.key
*.pem
*.crt
bandit-report.json

# Testing
.coverage
.pytest_cache/
htmlcov/

# Documentation
site/

# Temporary files
*.tmp
*.temp
temp/

# Backup files
backup_*/
*.bak

# Node modules (for any JS dependencies)
node_modules/

# Environment variables
.env
.env.local

# Screenshots and media
screenshots/
*.png
*.jpg
*.jpeg
*.gif
*.mp4

# Cache
cache/
.cache/

# Results and reports
results/
reports/
