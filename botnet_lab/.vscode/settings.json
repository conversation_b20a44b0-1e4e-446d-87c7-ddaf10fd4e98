{"search.exclude": {"**/botnet_env/**": true, "**/__pycache__/**": true, "**/*.pyc": true, "**/*.db": true, "**/*.sqlite*": true, "**/logs/**": true, "**/data/**": true, "**/temp/**": true, "**/tmp/**": true}, "files.watcherExclude": {"**/botnet_env/**": true, "**/__pycache__/**": true, "**/logs/**": true, "**/data/**": true, "**/*.db": true, "**/*.sqlite*": true}, "python.analysis.autoImportCompletions": false, "python.analysis.indexing": false, "python.analysis.memory.keepLibraryAst": false, "editor.minimap.enabled": false, "editor.codeLens": false, "git.autorefresh": false, "extensions.autoUpdate": false, "telemetry.telemetryLevel": "off"}