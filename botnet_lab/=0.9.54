Collecting pyautogui
  Downloading PyAutoGUI-0.9.54.tar.gz (61 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting python3-Xlib (from pyautogui)
  Downloading python3-xlib-0.15.tar.gz (132 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting pymsgbox (from pyautogui)
  Downloading PyMsgBox-1.0.9.tar.gz (18 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting pytweening>=1.0.4 (from pyautogui)
  Downloading pytweening-1.2.0.tar.gz (171 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting pyscreeze>=0.1.21 (from pyautogui)
  Downloading pyscreeze-1.0.1.tar.gz (27 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting pygetwindow>=0.0.5 (from pyautogui)
  Downloading PyGetWindow-0.0.9.tar.gz (9.7 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting mouseinfo (from pyautogui)
  Downloading MouseInfo-0.1.3.tar.gz (10 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting pyrect (from pygetwindow>=0.0.5->pyautogui)
  Downloading PyRect-0.2.0.tar.gz (17 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting pyperclip (from mouseinfo->pyautogui)
  Downloading pyperclip-1.9.0.tar.gz (20 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Building wheels for collected packages: pyautogui, pygetwindow, pyscreeze, pytweening, mouseinfo, pymsgbox, pyperclip, pyrect, python3-Xlib
  Building wheel for pyautogui (pyproject.toml): started
  Building wheel for pyautogui (pyproject.toml): finished with status 'done'
  Created wheel for pyautogui: filename=pyautogui-0.9.54-py3-none-any.whl size=37684 sha256=fff81c2d2a0faf31d2558f4459a7bdda516f9c6a2d8e90cafd81e840fb5ce6e8
  Stored in directory: /home/<USER>/.cache/pip/wheels/9c/22/e7/c97f656d0790ef69feae4fcc7bbc1fc648f1b37879f52e7480
  Building wheel for pygetwindow (pyproject.toml): started
  Building wheel for pygetwindow (pyproject.toml): finished with status 'done'
  Created wheel for pygetwindow: filename=pygetwindow-0.0.9-py3-none-any.whl size=11118 sha256=75b1dbee7b02af95f6ff6759a25ea99ea2e4bf9841c539bf329fde8ca7e5292c
  Stored in directory: /home/<USER>/.cache/pip/wheels/87/94/20/4e4ddd07e7276e44a38ac39d5b6c919d38ef27b9ebc5041e0f
  Building wheel for pyscreeze (pyproject.toml): started
  Building wheel for pyscreeze (pyproject.toml): finished with status 'done'
  Created wheel for pyscreeze: filename=pyscreeze-1.0.1-py3-none-any.whl size=14459 sha256=a756c2f369bff6a27f35af0f3d873b69537b22c2b1844884748753f1e3952ef3
  Stored in directory: /home/<USER>/.cache/pip/wheels/1c/6e/d7/0acfbc5116e11006753649efc7cb40bb9df98990798a21960d
  Building wheel for pytweening (pyproject.toml): started
  Building wheel for pytweening (pyproject.toml): finished with status 'done'
  Created wheel for pytweening: filename=pytweening-1.2.0-py3-none-any.whl size=8113 sha256=9584d986eab9eaa1c8e56dd15d20842a67febdd22a242977e98c45638635d0cc
  Stored in directory: /home/<USER>/.cache/pip/wheels/e7/89/f5/71248cbe0cc120d985c1976f8f2f31675069644c23ff03abfc
  Building wheel for mouseinfo (pyproject.toml): started
  Building wheel for mouseinfo (pyproject.toml): finished with status 'done'
  Created wheel for mouseinfo: filename=mouseinfo-0.1.3-py3-none-any.whl size=10951 sha256=0c05e32e0b2daabaf5f657f3f948b8fb882e6e4573ea7491231d4c65e130a03b
  Stored in directory: /home/<USER>/.cache/pip/wheels/61/5c/24/7d8ed078555f30e8f23fbc97f6361242a2cb4437ddd0384389
  Building wheel for pymsgbox (pyproject.toml): started
  Building wheel for pymsgbox (pyproject.toml): finished with status 'done'
  Created wheel for pymsgbox: filename=pymsgbox-1.0.9-py3-none-any.whl size=7453 sha256=7b80d29022871c82011d612e77a576b5bc6a111407832ff5cee66357813cf979
  Stored in directory: /home/<USER>/.cache/pip/wheels/02/b3/8f/8105b9948d1e23d9ad0d9de8d7f198aee5c7575aeba878ea82
  Building wheel for pyperclip (pyproject.toml): started
  Building wheel for pyperclip (pyproject.toml): finished with status 'done'
  Created wheel for pyperclip: filename=pyperclip-1.9.0-py3-none-any.whl size=11103 sha256=2cbd309a8f7c2cff5b63f2923796690c3300c37c347782fc540d26a149ebc802
  Stored in directory: /home/<USER>/.cache/pip/wheels/9c/79/90/c9e831caaffa2633ad99f1d35c6ea39866b92de62e909e89ef
  Building wheel for pyrect (pyproject.toml): started
  Building wheel for pyrect (pyproject.toml): finished with status 'done'
  Created wheel for pyrect: filename=pyrect-0.2.0-py2.py3-none-any.whl size=11280 sha256=a9282615cdd1ee067cd81e24797fae18147ad19b9d2bddcc8b984fa3f5207e1a
  Stored in directory: /home/<USER>/.cache/pip/wheels/1e/84/e3/83782b4a92f3d4d5b7fff5c36bf8c31922719e0695ee854cf0
  Building wheel for python3-Xlib (pyproject.toml): started
  Building wheel for python3-Xlib (pyproject.toml): finished with status 'done'
  Created wheel for python3-Xlib: filename=python3_xlib-0.15-py3-none-any.whl size=109549 sha256=534e8bb9b07c6b4853c693a919476384df5ab5c99c192d8b823a7b4d6f43bf64
  Stored in directory: /home/<USER>/.cache/pip/wheels/96/6c/7d/73585a69a1ea6ae74a2618b549705cd2d7ea2b58e391e8fb81
Successfully built pyautogui pygetwindow pyscreeze pytweening mouseinfo pymsgbox pyperclip pyrect python3-Xlib
Installing collected packages: pytweening, python3-Xlib, pyscreeze, pyrect, pyperclip, pymsgbox, pygetwindow, mouseinfo, pyautogui

Successfully installed mouseinfo-0.1.3 pyautogui-0.9.54 pygetwindow-0.0.9 pymsgbox-1.0.9 pyperclip-1.9.0 pyrect-0.2.0 pyscreeze-1.0.1 python3-Xlib-0.15 pytweening-1.2.0
