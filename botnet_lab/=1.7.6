Collecting pynput
  Downloading pynput-1.8.1-py2.py3-none-any.whl.metadata (32 kB)
Collecting six (from pynput)
  Using cached six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Collecting evdev>=1.3 (from pynput)
  Downloading evdev-1.9.2.tar.gz (33 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting python-xlib>=0.17 (from pynput)
  Downloading python_xlib-0.33-py2.py3-none-any.whl.metadata (6.2 kB)
Downloading pynput-1.8.1-py2.py3-none-any.whl (91 kB)
Downloading python_xlib-0.33-py2.py3-none-any.whl (182 kB)
Using cached six-1.17.0-py2.py3-none-any.whl (11 kB)
Building wheels for collected packages: evdev
  Building wheel for evdev (pyproject.toml): started
  Building wheel for evdev (pyproject.toml): finished with status 'done'
  Created wheel for evdev: filename=evdev-1.9.2-cp313-cp313-linux_x86_64.whl size=111455 sha256=4486b9a84171ba63d5d5044f73153ead49c527fa5462a44187da403897daf5af
  Stored in directory: /home/<USER>/.cache/pip/wheels/27/ea/cd/28a208f3b99b6e4988104bd29067e9a47ac79b9bf5de67539d
Successfully built evdev
Installing collected packages: six, evdev, python-xlib, pynput

Successfully installed evdev-1.9.2 pynput-1.8.1 python-xlib-0.33 six-1.17.0
