Collecting selenium
  Using cached selenium-4.34.2-py3-none-any.whl.metadata (7.5 kB)
Collecting urllib3~=2.5.0 (from urllib3[socks]~=2.5.0->selenium)
  Using cached urllib3-2.5.0-py3-none-any.whl.metadata (6.5 kB)
Collecting trio~=0.30.0 (from selenium)
  Using cached trio-0.30.0-py3-none-any.whl.metadata (8.5 kB)
Collecting trio-websocket~=0.12.2 (from selenium)
  Using cached trio_websocket-0.12.2-py3-none-any.whl.metadata (5.1 kB)
Collecting certifi>=2025.6.15 (from selenium)
  Downloading certifi-2025.7.14-py3-none-any.whl.metadata (2.4 kB)
Requirement already satisfied: typing_extensions~=4.14.0 in ./botnet_env/lib/python3.13/site-packages (from selenium) (4.14.1)
Collecting websocket-client~=1.8.0 (from selenium)
  Using cached websocket_client-1.8.0-py3-none-any.whl.metadata (8.0 kB)
Collecting attrs>=23.2.0 (from trio~=0.30.0->selenium)
  Using cached attrs-25.3.0-py3-none-any.whl.metadata (10 kB)
Collecting sortedcontainers (from trio~=0.30.0->selenium)
  Using cached sortedcontainers-2.4.0-py2.py3-none-any.whl.metadata (10 kB)
Collecting idna (from trio~=0.30.0->selenium)
  Using cached idna-3.10-py3-none-any.whl.metadata (10 kB)
Collecting outcome (from trio~=0.30.0->selenium)
  Using cached outcome-1.3.0.post0-py2.py3-none-any.whl.metadata (2.6 kB)
Collecting sniffio>=1.3.0 (from trio~=0.30.0->selenium)
  Using cached sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting wsproto>=0.14 (from trio-websocket~=0.12.2->selenium)
  Using cached wsproto-1.2.0-py3-none-any.whl.metadata (5.6 kB)
Collecting pysocks!=1.5.7,<2.0,>=1.5.6 (from urllib3[socks]~=2.5.0->selenium)
  Using cached PySocks-1.7.1-py3-none-any.whl.metadata (13 kB)
Collecting h11<1,>=0.9.0 (from wsproto>=0.14->trio-websocket~=0.12.2->selenium)
  Using cached h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)
Using cached selenium-4.34.2-py3-none-any.whl (9.4 MB)
Using cached trio-0.30.0-py3-none-any.whl (499 kB)
Using cached trio_websocket-0.12.2-py3-none-any.whl (21 kB)
Using cached urllib3-2.5.0-py3-none-any.whl (129 kB)
Using cached PySocks-1.7.1-py3-none-any.whl (16 kB)
Using cached websocket_client-1.8.0-py3-none-any.whl (58 kB)
Using cached attrs-25.3.0-py3-none-any.whl (63 kB)
Downloading certifi-2025.7.14-py3-none-any.whl (162 kB)
Using cached outcome-1.3.0.post0-py2.py3-none-any.whl (10 kB)
Using cached sniffio-1.3.1-py3-none-any.whl (10 kB)
Using cached wsproto-1.2.0-py3-none-any.whl (24 kB)
Using cached h11-0.16.0-py3-none-any.whl (37 kB)
Using cached idna-3.10-py3-none-any.whl (70 kB)
Using cached sortedcontainers-2.4.0-py2.py3-none-any.whl (29 kB)
Installing collected packages: sortedcontainers, websocket-client, urllib3, sniffio, pysocks, idna, h11, certifi, attrs, wsproto, outcome, trio, trio-websocket, selenium

Successfully installed attrs-25.3.0 certifi-2025.7.14 h11-0.16.0 idna-3.10 outcome-1.3.0.post0 pysocks-1.7.1 selenium-4.34.2 sniffio-1.3.1 sortedcontainers-2.4.0 trio-0.30.0 trio-websocket-0.12.2 urllib3-2.5.0 websocket-client-1.8.0 wsproto-1.2.0
