Collecting flask-socketio
  Downloading Flask_SocketIO-5.5.1-py3-none-any.whl.metadata (2.6 kB)
Collecting Flask>=0.9 (from flask-socketio)
  Using cached flask-3.1.1-py3-none-any.whl.metadata (3.0 kB)
Collecting python-socketio>=5.12.0 (from flask-socketio)
  Downloading python_socketio-5.13.0-py3-none-any.whl.metadata (3.2 kB)
Collecting blinker>=1.9.0 (from Flask>=0.9->flask-socketio)
  Using cached blinker-1.9.0-py3-none-any.whl.metadata (1.6 kB)
Collecting click>=8.1.3 (from Flask>=0.9->flask-socketio)
  Using cached click-8.2.1-py3-none-any.whl.metadata (2.5 kB)
Collecting itsdangerous>=2.2.0 (from Flask>=0.9->flask-socketio)
  Using cached itsdangerous-2.2.0-py3-none-any.whl.metadata (1.9 kB)
Collecting jinja2>=3.1.2 (from Flask>=0.9->flask-socketio)
  Using cached jinja2-3.1.6-py3-none-any.whl.metadata (2.9 kB)
Collecting markupsafe>=2.1.1 (from Flask>=0.9->flask-socketio)
  Using cached MarkupSafe-3.0.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.0 kB)
Collecting werkzeug>=3.1.0 (from Flask>=0.9->flask-socketio)
  Using cached werkzeug-3.1.3-py3-none-any.whl.metadata (3.7 kB)
Collecting bidict>=0.21.0 (from python-socketio>=5.12.0->flask-socketio)
  Downloading bidict-0.23.1-py3-none-any.whl.metadata (8.7 kB)
Collecting python-engineio>=4.11.0 (from python-socketio>=5.12.0->flask-socketio)
  Downloading python_engineio-4.12.2-py3-none-any.whl.metadata (2.2 kB)
Collecting simple-websocket>=0.10.0 (from python-engineio>=4.11.0->python-socketio>=5.12.0->flask-socketio)
  Downloading simple_websocket-1.1.0-py3-none-any.whl.metadata (1.5 kB)
Requirement already satisfied: wsproto in ./botnet_env/lib/python3.13/site-packages (from simple-websocket>=0.10.0->python-engineio>=4.11.0->python-socketio>=5.12.0->flask-socketio) (1.2.0)
Requirement already satisfied: h11<1,>=0.9.0 in ./botnet_env/lib/python3.13/site-packages (from wsproto->simple-websocket>=0.10.0->python-engineio>=4.11.0->python-socketio>=5.12.0->flask-socketio) (0.16.0)
Downloading Flask_SocketIO-5.5.1-py3-none-any.whl (18 kB)
Using cached flask-3.1.1-py3-none-any.whl (103 kB)
Using cached blinker-1.9.0-py3-none-any.whl (8.5 kB)
Using cached click-8.2.1-py3-none-any.whl (102 kB)
Using cached itsdangerous-2.2.0-py3-none-any.whl (16 kB)
Using cached jinja2-3.1.6-py3-none-any.whl (134 kB)
Using cached MarkupSafe-3.0.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (23 kB)
Downloading python_socketio-5.13.0-py3-none-any.whl (77 kB)
Downloading bidict-0.23.1-py3-none-any.whl (32 kB)
Downloading python_engineio-4.12.2-py3-none-any.whl (59 kB)
Downloading simple_websocket-1.1.0-py3-none-any.whl (13 kB)
Using cached werkzeug-3.1.3-py3-none-any.whl (224 kB)
Installing collected packages: markupsafe, itsdangerous, click, blinker, bidict, werkzeug, simple-websocket, jinja2, python-engineio, Flask, python-socketio, flask-socketio

Successfully installed Flask-3.1.1 bidict-0.23.1 blinker-1.9.0 click-8.2.1 flask-socketio-5.5.1 itsdangerous-2.2.0 jinja2-3.1.6 markupsafe-3.0.2 python-engineio-4.12.2 python-socketio-5.13.0 simple-websocket-1.1.0 werkzeug-3.1.3
