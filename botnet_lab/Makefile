# Botnet Lab Makefile

.PHONY: help install test lint format clean setup-dev

help:
	@echo "Available commands:"
	@echo "  install     - Install dependencies"
	@echo "  test        - Run all tests"
	@echo "  lint        - Run code linting"
	@echo "  format      - Format code with black"
	@echo "  clean       - Clean temporary files"
	@echo "  setup-dev   - Setup development environment"

install:
	pip install -r scripts/requirements_unified.txt

test:
	python -m pytest tests/ -v
	cd standalone && python run_all_tests.py
	cd rat && python tests/test_rat_framework.py

lint:
	flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
	flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

format:
	black .

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type f -name "*.log" -delete
	find . -type f -name "*.db" -delete

setup-dev:
	python -m venv botnet_env
	source botnet_env/bin/activate && pip install -r scripts/requirements_unified.txt
	source botnet_env/bin/activate && pip install pre-commit pytest black flake8
	pre-commit install

security-scan:
	bandit -r . -f json -o bandit-report.json

docs:
	mkdocs serve

build-docs:
	mkdocs build
