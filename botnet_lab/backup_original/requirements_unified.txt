# Unified Requirements for Botnet Lab Project
# Generated automatically - includes all dependencies
# Educational and Research Purposes Only

# Core Dependencies

# Core System
cryptography==45.0.5
cryptography>=3.4.8
cryptography>=41.0.0
psutil==7.0.0
psutil>=5.8.0
psutil>=5.9.0
requests==2.32.4
requests>=2.25.0
requests>=2.31.0
requests[socks]>=2.25.0

# Network & Communication
flask-socketio>=5.1.0
flask-socketio>=5.3.0
flask>=2.0.0
flask>=2.3.0
flask>=3.0.0
paramiko==3.5.1
paramiko>=2.7.0
paramiko>=3.3.0
websockets==15.0.1
websockets>=11.0.0

# Data Processing
beautifulsoup4==4.13.4
beautifulsoup4>=4.12.0
beautifulsoup4>=4.9.0
lxml==6.0.0
lxml>=4.6.0
lxml>=4.9.0
numpy==2.2.6
numpy>=1.20.0
numpy>=1.21.0
numpy>=1.24.0

# GUI & Automation
Pillow>=10.0.0
Pillow>=10.1.0
Pillow>=8.0.0
Pillow>=8.3.2
pyautogui>=0.9.54
pynput==1.8.1
pynput>=1.7.6

# Database & Storage
sqlalchemy>=1.4.0
sqlalchemy>=2.0.0

# Utilities
colorama==0.4.6
colorama>=0.4.6
phonenumbers>=8.13.0
tqdm==4.67.1
tqdm>=4.65.0
tqdm>=4.66.0

# Advanced Features
opencv-python==4.12.0.88
opencv-python>=4.5.3.56
opencv-python>=4.8.0
selenium==4.34.2
selenium>=4.0.0
selenium>=4.15.0

# Other Dependencies
Flask-SocketIO==5.5.1
Flask==3.1.1
Jinja2==3.1.6
MarkupSafe==3.0.2
MouseInfo==0.1.3
PyAutoGUI==0.9.54
PyGetWindow==0.0.9
PyMsgBox==1.0.9
PyNaCl==1.5.0
PyRect==0.2.0
PyScreeze==1.0.1
PySocks==1.7.1
SQLAlchemy==2.0.41
WMI>=1.5.1; sys_platform == "win32"
Werkzeug==3.1.3
adversarial-robustness-toolbox>=1.15.0
aiohttp>=3.8.0
aiohttp>=3.9.0
asyncio-throttle>=1.0.0
attrs==25.3.0
base64 (built-in)
bcrypt==4.3.0
bcrypt>=4.0.1
bidict==0.23.1
bitcoin>=1.1.42
black>=21.0.0
black>=23.7.0
black>=23.9.0
blinker==1.9.0
certifi==2025.7.14
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
click>=8.1.0
colorlog>=6.0.0
configparser>=5.0.0
customtkinter>=5.2.0
cx-freeze>=6.15.0
datetime (built-in)
dlib>=19.24.0
email-validator>=2.1.0
ephem>=4.1.4
eth-account>=0.9.0
evdev==1.9.2
face-recognition>=1.3.0
fake-useragent>=1.4.0
flake8>=3.9.0
flake8>=6.0.0
flake8>=6.1.0
flask-socketio>=5.1.0
flask-socketio>=5.3.0
foolbox>=3.3.0
frida>=16.0.0
ftplib
geoip2>=4.5.0
gradio>=4.0.0
greenlet==3.2.3
h11==0.16.0
hashlib (built-in)
httpx>=0.25.0
idna==3.10
impacket>=0.9.24
itertools (built-in)
itsdangerous==2.2.0
jinja2>=3.1.0
json (built-in)
jwt>=1.3.1
librosa>=0.10.0
matplotlib>=3.3.0
matplotlib>=3.7.0
moviepy>=1.0.3
multiprocessing (built-in)
mypy>=0.812
mypy>=1.5.0
mypy>=1.6.0
networkx>=3.1.0
nltk>=3.8.0
nltk>=3.8.1
nmap>=0.0.1
os (built-in)
outcome==1.3.0.post0
pandas>=1.3.0
pandas>=2.0.0
pandas>=2.1.0
pefile>=2023.2.7
pickle (built-in)
pillow==11.3.0
platform (built-in)
pure-python-adb>=0.3.0
pyaudio>=0.2.11
pycountry>=22.3.0
pycparser==2.22
pycryptodome>=3.19.0
pydub>=0.25.0
pyinstaller>=5.13.0
pyperclip==1.9.0
pysocks>=1.7.0
pytest-asyncio>=0.21.0
pytest-cov>=2.12.0
pytest-cov>=4.1.0
pytest-mock>=3.6.0
pytest>=6.2.0
pytest>=7.4.0
python-dateutil>=2.8.2
python-dotenv>=1.0.0
python-engineio==4.12.2
python-socketio==5.13.0
python-xlib==0.33
python3-xlib==0.15
pytweening==1.2.0
pywin32>=227; sys_platform == "win32"
pywin32>=306; sys_platform == "win32"
pyyaml>=5.4.0
random (built-in)
re (built-in)
requests[socks]>=2.25.0
rich>=13.6.0
scapy>=2.5.0
schedule>=1.2.0
scikit-learn>=1.3.0
scrapy>=2.5.0
seaborn>=0.12.0
setproctitle>=1.2.0
simple-websocket==1.1.0
six==1.17.0
smtplib
sniffio==1.3.1
sortedcontainers==2.4.0
sounddevice>=0.4.6
soundfile>=0.12.0
soupsieve==2.7
sqlite3
sqlite3 (built-in)
statsmodels>=0.14.0
streamlit>=1.28.0
subprocess (built-in)
sys (built-in)
tensorflow>=2.13.0
textblob>=0.17.1
threading (built-in)
time (built-in)
tkinter-page>=0.1.0
torch>=2.0.0
torchvision>=0.15.0
transformers>=4.30.0
transformers_js_py
trio-websocket==0.12.2
trio==0.30.0
typing_extensions==4.14.1
undetected-chromedriver>=3.5.0
urllib3==2.5.0
urllib3>=2.0.0
uuid (built-in)
wave
web3>=6.0.0
webdriver-manager>=4.0.0
websocket-client==1.8.0
wmi>=1.5.1; sys_platform == "win32"
wsproto==1.2.0

# Platform-specific dependencies
pywin32>=306; sys_platform == 'win32'
wmi>=1.5.1; sys_platform == 'win32'

# Development dependencies (optional)
pytest>=7.4.0
black>=23.7.0
flake8>=6.0.0