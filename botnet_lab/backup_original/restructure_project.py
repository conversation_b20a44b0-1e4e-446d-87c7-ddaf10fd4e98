#!/usr/bin/env python3
"""
إعادة هيكلة مشروع Botnet Lab لتنظيم أفضل
"""

import os
import shutil
from pathlib import Path

class ProjectRestructurer:
    def __init__(self, project_path):
        self.project_path = Path(project_path)
        self.backup_path = self.project_path / "backup_original"
        
    def create_backup(self):
        """إنشاء نسخة احتياطية من المشروع الحالي"""
        print("💾 إنشاء نسخة احتياطية...")
        
        if self.backup_path.exists():
            shutil.rmtree(self.backup_path)
        
        # نسخ الملفات المهمة فقط
        important_files = [
            "*.py", "*.md", "*.txt", "*.json", "*.html", "*.js", "*.css"
        ]
        
        self.backup_path.mkdir(exist_ok=True)
        
        for pattern in important_files:
            for file in self.project_path.glob(pattern):
                if file.is_file():
                    shutil.copy2(file, self.backup_path / file.name)
        
        print(f"✅ تم إنشاء نسخة احتياطية في: {self.backup_path}")
    
    def create_new_structure(self):
        """إنشاء الهيكل الجديد المحسن"""
        print("\n🏗️ إنشاء الهيكل الجديد...")
        
        # الهيكل الجديد المقترح
        new_structure = {
            "core": "النواة الأساسية للمشروع",
            "modules": "الوحدات المتقدمة", 
            "standalone": "الوحدات المستقلة",
            "rat": "وحدة RAT",
            "tests": "جميع الاختبارات",
            "docs": "الوثائق والأدلة",
            "config": "ملفات الإعدادات",
            "tools": "الأدوات المساعدة",
            "templates": "القوالب والواجهات",
            "data": "البيانات وقواعد البيانات",
            "logs": "ملفات السجلات",
            "scripts": "سكريبتات التشغيل والصيانة"
        }
        
        # إنشاء المجلدات الجديدة
        for folder, description in new_structure.items():
            folder_path = self.project_path / folder
            folder_path.mkdir(exist_ok=True)
            
            # إنشاء ملف README لكل مجلد
            readme_content = f"# {folder.title()}\n\n{description}\n"
            (folder_path / "README.md").write_text(readme_content, encoding='utf-8')
            
            print(f"📁 تم إنشاء: {folder}/ - {description}")
    
    def move_files_to_new_structure(self):
        """نقل الملفات إلى الهيكل الجديد"""
        print("\n📦 نقل الملفات إلى الهيكل الجديد...")
        
        # قواعد النقل
        move_rules = {
            # النواة الأساسية
            "core": [
                "c2_server.py", "advanced_c2_server.py", "bot_real.py", 
                "bot_unrestricted.py", "bot_moving.py", "config.py"
            ],
            
            # الوحدات المتقدمة
            "modules": [
                "advanced_*.py", "ai_*.py", "blockchain_*.py", "deep_fake_*.py",
                "distributed_*.py", "financial_*.py", "intelligence_*.py",
                "mobile_*.py", "monetization_*.py", "network_*.py",
                "neural_*.py", "password_cracking.py", "persistence_*.py",
                "phone_number_targeting.py", "predictive_*.py", "realistic_*.py",
                "satellite_*.py", "smart_*.py", "social_engineering.py",
                "social_media_accounts.py", "social_media_blocking.py",
                "stealth_*.py", "system_*.py", "webcam_*.py"
            ],
            
            # الاختبارات
            "tests": ["test_*.py", "performance_test.py"],
            
            # الوثائق
            "docs": ["*_GUIDE.md", "README.md"],
            
            # الإعدادات
            "config": ["*config*.json", "*config*.py"],
            
            # الأدوات
            "tools": ["cleanup_code.py", "create_unified_requirements.py", 
                     "fix_dependencies.sh", "restructure_project.py"],
            
            # القوالب
            "templates": ["templates/"],
            
            # السكريبتات
            "scripts": ["*.sh", "requirements*.txt"]
        }
        
        # تنفيذ قواعد النقل
        for target_folder, patterns in move_rules.items():
            target_path = self.project_path / target_folder
            
            for pattern in patterns:
                # البحث عن الملفات المطابقة
                if pattern.endswith("/"):
                    # مجلد
                    source_folder = self.project_path / pattern.rstrip("/")
                    if source_folder.exists() and source_folder.is_dir():
                        dest_folder = target_path / source_folder.name
                        if not dest_folder.exists():
                            shutil.move(str(source_folder), str(dest_folder))
                            print(f"  📁 {source_folder.name}/ → {target_folder}/")
                else:
                    # ملفات
                    for file_path in self.project_path.glob(pattern):
                        if file_path.is_file() and file_path.parent == self.project_path:
                            dest_file = target_path / file_path.name
                            if not dest_file.exists():
                                shutil.move(str(file_path), str(dest_file))
                                print(f"  📄 {file_path.name} → {target_folder}/")
    
    def handle_special_folders(self):
        """التعامل مع المجلدات الخاصة"""
        print("\n🔄 التعامل مع المجلدات الخاصة...")
        
        # نقل standalone_modules إلى standalone
        standalone_source = self.project_path / "standalone_modules"
        standalone_dest = self.project_path / "standalone"
        
        if standalone_source.exists():
            if standalone_dest.exists():
                shutil.rmtree(standalone_dest)
            shutil.move(str(standalone_source), str(standalone_dest))
            print("  📁 standalone_modules/ → standalone/")
        
        # نقل rat_module إلى rat
        rat_source = self.project_path / "rat_module"
        rat_dest = self.project_path / "rat"
        
        if rat_source.exists():
            if rat_dest.exists():
                shutil.rmtree(rat_dest)
            shutil.move(str(rat_source), str(rat_dest))
            print("  📁 rat_module/ → rat/")
        
        # إنشاء مجلدات البيانات والسجلات
        (self.project_path / "data").mkdir(exist_ok=True)
        (self.project_path / "logs").mkdir(exist_ok=True)
        
        # نقل ملفات البيانات
        for db_file in self.project_path.glob("*.db"):
            dest_file = self.project_path / "data" / db_file.name
            if not dest_file.exists():
                shutil.move(str(db_file), str(dest_file))
                print(f"  📄 {db_file.name} → data/")
        
        # نقل ملفات السجلات
        for log_file in self.project_path.glob("*.log"):
            dest_file = self.project_path / "logs" / log_file.name
            if not dest_file.exists():
                shutil.move(str(log_file), str(dest_file))
                print(f"  📄 {log_file.name} → logs/")
    
    def create_main_readme(self):
        """إنشاء ملف README رئيسي محدث"""
        readme_content = """# 🤖 Botnet Lab - Educational Cybersecurity Framework

## 📁 Project Structure

```
botnet_lab/
├── 🏗️ core/           # Core Components (C2 Server, Bots, Config)
├── 🧩 modules/         # Advanced Modules (AI, Blockchain, etc.)
├── 🔧 standalone/      # Standalone Modules (Independent Testing)
├── 🐀 rat/            # RAT Module (Remote Access Trojan)
├── 🧪 tests/          # All Test Suites
├── 📚 docs/           # Documentation and Guides
├── ⚙️ config/         # Configuration Files
├── 🛠️ tools/          # Utility Tools and Scripts
├── 🎨 templates/      # Web Templates and UI
├── 💾 data/           # Databases and Data Files
├── 📝 logs/           # Log Files
└── 📜 scripts/        # Automation Scripts
```

## 🚀 Quick Start

### 1. Setup Environment
```bash
# Install dependencies
pip install -r scripts/requirements_unified.txt

# Activate environment
source botnet_env/bin/activate
```

### 2. Run Core Components
```bash
# Start C2 Server
python core/c2_server.py

# Start Advanced C2 Server
python core/advanced_c2_server.py
```

### 3. Test Standalone Modules
```bash
# Phone Number Targeting
cd standalone/phone_number_targeting
python phone_targeting_standalone.py

# Social Media Intelligence
cd standalone/social_media_accounts
python social_accounts_standalone.py
```

### 4. Use RAT Module
```bash
# Start RAT Server
cd rat
python core/rat_server.py

# Build RAT Client
python tools/builder.py --server-ip 127.0.0.1 --server-port 4444 --output client
```

## ⚠️ Legal Notice

This framework is for **EDUCATIONAL PURPOSES ONLY**. Users must:
- Only test on systems they own or have explicit permission to test
- Follow all applicable laws and regulations
- Use for defensive and research purposes only
- Respect privacy and computer crime laws

## 📖 Documentation

- 📚 **Core Documentation**: `docs/`
- 🧩 **Module Guides**: Each module includes detailed guides
- 🧪 **Testing Instructions**: `tests/README.md`
- 🔧 **Setup Guides**: `scripts/` and individual module directories

## 🛡️ Security Features

- 🔐 **Advanced Encryption** (AES-256, RSA-2048)
- 🛡️ **Anti-Detection Techniques**
- 🔄 **Modular Architecture**
- 🧪 **Comprehensive Testing**
- 📊 **Real-time Monitoring**

---

**Remember: Use responsibly and ethically for educational and authorized testing only!** 🛡️
"""
        
        readme_path = self.project_path / "README.md"
        readme_path.write_text(readme_content, encoding='utf-8')
        print("✅ تم إنشاء ملف README رئيسي محدث")
    
    def cleanup_empty_folders(self):
        """تنظيف المجلدات الفارغة"""
        print("\n🧹 تنظيف المجلدات الفارغة...")
        
        for item in self.project_path.iterdir():
            if item.is_dir() and item.name not in ['backup_original', 'botnet_env']:
                try:
                    if not any(item.iterdir()):
                        item.rmdir()
                        print(f"  🗑️ حذف مجلد فارغ: {item.name}")
                except:
                    pass
    
    def restructure(self):
        """تنفيذ إعادة الهيكلة الكاملة"""
        print("🔄 بدء إعادة هيكلة المشروع...")
        
        # إنشاء نسخة احتياطية
        self.create_backup()
        
        # إنشاء الهيكل الجديد
        self.create_new_structure()
        
        # نقل الملفات
        self.move_files_to_new_structure()
        
        # التعامل مع المجلدات الخاصة
        self.handle_special_folders()
        
        # إنشاء README محدث
        self.create_main_readme()
        
        # تنظيف المجلدات الفارغة
        self.cleanup_empty_folders()
        
        print("\n🎉 تم إكمال إعادة الهيكلة بنجاح!")
        print("💾 النسخة الاحتياطية متوفرة في: backup_original/")

def main():
    project_path = "/home/<USER>/Desktop/Year3/botnet/botnet_lab"
    
    restructurer = ProjectRestructurer(project_path)
    
    # تأكيد من المستخدم
    print("⚠️ هذا سيعيد تنظيم هيكل المشروع بالكامل")
    print("💾 سيتم إنشاء نسخة احتياطية تلقائياً")
    
    response = input("\nهل تريد المتابعة؟ (yes/no): ")
    if response.lower() in ['yes', 'y', 'نعم']:
        restructurer.restructure()
    else:
        print("❌ تم إلغاء العملية")

if __name__ == "__main__":
    main()
