{"folders": [{"name": "🏗️ Core", "path": "./core"}, {"name": "🧩 <PERSON><PERSON><PERSON>", "path": "./modules"}, {"name": "🔧 Standalone", "path": "./standalone"}, {"name": "🐀 RAT Module", "path": "./rat_module"}, {"name": "🧪 Tests", "path": "./tests"}, {"name": "📚 Documentation", "path": "./docs"}, {"name": "🛠️ Tools", "path": "./tools"}, {"name": "📜 Scripts", "path": "./scripts"}], "settings": {"// تحسينات الأداء": "إعدادات محسنة للمشروع الكبير", "search.exclude": {"**/botnet_env/**": true, "**/__pycache__/**": true, "**/*.pyc": true, "**/*.pyo": true, "**/*.db": true, "**/*.sqlite*": true, "**/logs/**": true, "**/data/**": true, "**/temp/**": true, "**/tmp/**": true, "**/.pytest_cache/**": true, "**/node_modules/**": true, "**/.git/**": true}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/*.pyo": true, "**/.pytest_cache": true}, "files.watcherExclude": {"**/botnet_env/**": true, "**/__pycache__/**": true, "**/logs/**": true, "**/data/**": true, "**/*.db": true, "**/*.sqlite*": true, "**/temp/**": true, "**/tmp/**": true}, "python.analysis.autoImportCompletions": false, "python.analysis.indexing": false, "python.analysis.memory.keepLibraryAst": false, "python.analysis.autoSearchPaths": false, "python.analysis.packageIndexDepths": [{"name": "", "depth": 1}], "python.defaultInterpreterPath": "./botnet_env/bin/python", "python.terminal.activateEnvironment": true, "python.linting.enabled": false, "python.formatting.provider": "none", "editor.minimap.enabled": false, "editor.codeLens": false, "editor.quickSuggestions": {"other": false, "comments": false, "strings": false}, "editor.suggestOnTriggerCharacters": false, "editor.acceptSuggestionOnEnter": "off", "editor.wordBasedSuggestions": "off", "editor.parameterHints.enabled": false, "editor.hover.enabled": false, "editor.lightbulb.enabled": false, "editor.folding": false, "editor.foldingHighlight": false, "editor.showFoldingControls": "never", "editor.occurrencesHighlight": "off", "editor.selectionHighlight": false, "editor.wordWrap": "off", "editor.renderWhitespace": "none", "editor.renderControlCharacters": false, "editor.renderIndentGuides": false, "editor.smoothScrolling": false, "editor.cursorBlinking": "solid", "editor.cursorSmoothCaretAnimation": "off", "git.enabled": false, "git.autorefresh": false, "git.decorations.enabled": false, "files.autoSave": "off", "files.autoSaveDelay": 10000, "files.hotExit": "off", "files.defaultLanguage": "python", "problems.decorations.enabled": false, "problems.showCurrentInStatus": false, "terminal.integrated.enablePersistentSessions": false, "terminal.integrated.persistentSessionReviveProcess": "never", "extensions.autoUpdate": false, "extensions.autoCheckUpdates": false, "security.workspace.trust.enabled": false, "security.workspace.trust.startupPrompt": "never", "security.workspace.trust.banner": "never", "update.mode": "none", "update.showReleaseNotes": false, "telemetry.telemetryLevel": "off", "workbench.enableExperiments": false, "workbench.settings.enableNaturalLanguageSearch": false, "typescript.preferences.includePackageJsonAutoImports": "off", "typescript.suggest.autoImports": false, "javascript.suggest.autoImports": false}, "extensions": {"recommendations": ["ms-python.python"], "unwantedRecommendations": ["ms-python.pylint", "ms-python.flake8", "ms-python.mypy-type-checker", "ms-python.black-formatter", "ms-python.isort", "ms-toolsai.jupyter", "ms-toolsai.jupyter-keymap", "ms-toolsai.jupyter-renderers", "ms-vscode.hexeditor"]}, "launch": {"version": "0.2.0", "configurations": [{"name": "🚀 Core Server", "type": "python", "request": "launch", "program": "${workspaceFolder}/core/c2_server.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "🔧 Module Test", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "🧹 Quick Cleanup", "type": "shell", "command": "./tools/quick_cleanup.sh", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "📊 Performance Monitor", "type": "shell", "command": "./tools/monitor_vscode_performance.sh", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "🔧 Optimize Performance", "type": "shell", "command": "python", "args": ["tools/optimize_vscode_performance.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}}]}}