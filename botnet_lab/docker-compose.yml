version: '3.8'

services:
  c2-server:
    build: .
    ports:
      - "8080:8080"
      - "8443:8443"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
    command: python core/c2_server.py --host 0.0.0.0

  advanced-c2:
    build: .
    ports:
      - "8081:8080"
      - "8444:8443"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
    command: python core/advanced_c2_server.py --host 0.0.0.0

  rat-server:
    build: .
    ports:
      - "4444:4444"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
    command: python rat/core/rat_server.py --host 0.0.0.0

  dashboard:
    build: .
    ports:
      - "8082:8080"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
    command: python rat/tools/dashboard.py --host 0.0.0.0

volumes:
  data:
  logs:
