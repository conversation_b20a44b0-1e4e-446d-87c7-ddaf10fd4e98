# 🔍 **دليل تعلم تقنيات Info-Stealer في مشروع Botnet Lab**

## ⚠️ **إخلاء مسؤولية قانوني**

هذا الدليل مخصص **للأغراض التعليمية والبحثية الدفاعية فقط**. يجب:
- ✅ الاختبار فقط على الأنظمة التي تملكها أو مصرح لك باختبارها
- ✅ استخدام هذه المعرفة لتطوير الدفاعات وتحسين الأمان
- ✅ اتباع جميع القوانين المحلية والدولية
- ❌ عدم استخدام هذه التقنيات لأغراض ضارة أو غير قانونية

---

## 📋 **فهرس المحتويات**

1. [مقدمة عن Info-Stealers](#introduction)
2. [الوحدة التعليمية الجديدة](#module)
3. [تقنيات RedLine Stealer](#redline)
4. [تقنيات AZORult Stealer](#azorult)
5. [تقنيات Emotet](#emotet)
6. [التكامل مع الوحدات الأخرى](#integration)
7. [الاعتبارات الدفاعية](#defense)

---

## 🎯 **1. مقدمة عن Info-Stealers** {#introduction}

### **ما هي Info-Stealers؟**
Info-Stealers هي برامج ضارة مصممة لسرقة المعلومات الحساسة من أجهزة الضحايا، مثل:
- 🔑 **كلمات المرور** المحفوظة في المتصفحات
- 🍪 **الكوكيز** وجلسات المواقع
- 💳 **بيانات بطاقات الائتمان**
- 💰 **محافظ العملات المشفرة**
- 🎮 **حسابات الألعاب**
- 📧 **بيانات البريد الإلكتروني**
- 💬 **رموز وسائل التواصل الاجتماعي**

### **أشهر أمثلة Info-Stealers:**

#### **🔴 RedLine Stealer**
- **التخصص**: سرقة بيانات المتصفحات والعملات المشفرة
- **الانتشار**: يُباع في الإنترنت المظلم
- **الاستخدام**: حملات سرقة حسابات فيسبوك/إنستغرام/جيميل
- **المميزات**: سهل الاستخدام، واجهة بسيطة، تحديثات مستمرة

#### **🔵 AZORult**
- **التخصص**: سرقة شاملة للبيانات وحسابات الألعاب
- **الانتشار**: يُدمج مع ألعاب وهمية أو تطبيقات مزيفة
- **المميزات**: قديم وموثوق، يدعم العديد من التطبيقات
- **التطور**: تطور عبر سنوات عديدة

#### **🟡 Emotet (سابقاً)**
- **التخصص**: منصة لنشر برامج ضارة أخرى
- **الاستخدام**: مقدمة لتثبيت info-stealers أخرى
- **المميزات**: انتشار عبر البريد الإلكتروني، حركة جانبية
- **الحالة**: تم إيقافه من قبل السلطات (2021)

---

## 🧩 **2. الوحدة التعليمية الجديدة** {#module}

### **أ) الوحدة الرئيسية**
📁 **المسار**: `modules/info_stealer_educational.py`

**الميزات الرئيسية:**
- ✅ محاكاة تقنيات RedLine, AZORult, Emotet
- ✅ قاعدة بيانات SQLite لتسجيل النتائج
- ✅ تشفير البيانات الحساسة
- ✅ واجهة تفاعلية للتعلم
- ✅ تقارير شاملة ومفصلة

**كيفية الاستخدام:**
```bash
cd /home/<USER>/Desktop/Year3/botnet/botnet_lab
source botnet_env/bin/activate
python modules/info_stealer_educational.py
```

### **ب) قاعدة البيانات التعليمية**
📁 **الملف**: `info_stealer_educational.db`

**الجداول المتاحة:**
- `browser_data` - بيانات المتصفحات
- `system_info` - معلومات النظام
- `crypto_wallets` - محافظ العملات المشفرة
- `gaming_accounts` - حسابات الألعاب
- `email_clients` - عملاء البريد الإلكتروني
- `social_tokens` - رموز وسائل التواصل الاجتماعي

---

## 🔴 **3. تقنيات RedLine Stealer** {#redline}

### **أ) استخراج بيانات المتصفحات**

**المتصفحات المستهدفة:**
- **Chrome**: `%LOCALAPPDATA%/Google/Chrome/User Data/Default`
- **Firefox**: `%APPDATA%/Mozilla/Firefox/Profiles`
- **Edge**: `%LOCALAPPDATA%/Microsoft/Edge/User Data/Default`

**أنواع البيانات المسروقة:**
```python
# مثال على البيانات المستخرجة
browser_data = {
    'passwords': [
        {'url': 'facebook.com', 'username': '<EMAIL>', 'password': '[ENCRYPTED]'},
        {'url': 'gmail.com', 'username': '<EMAIL>', 'password': '[ENCRYPTED]'}
    ],
    'cookies': [
        {'domain': 'facebook.com', 'name': 'session_id', 'value': '[ENCRYPTED]'}
    ],
    'credit_cards': [
        {'number': '****-****-****-1234', 'expiry': '12/25', 'name': 'John Doe'}
    ],
    'autofill_data': [
        {'field': 'email', 'value': '<EMAIL>'}
    ]
}
```

### **ب) كشف محافظ العملات المشفرة**

**المحافظ المستهدفة:**
- **Electrum**: `%APPDATA%/Electrum/wallets`
- **Exodus**: `%APPDATA%/Exodus`
- **MetaMask**: Chrome Extension
- **Coinbase**: Chrome Extension

**البيانات المسروقة:**
```python
crypto_wallets = [
    {
        'wallet_type': 'MetaMask',
        'seed_phrase': '[ENCRYPTED_SEED_PHRASE]',
        'private_keys': '[ENCRYPTED_PRIVATE_KEYS]',
        'balance_estimate': '1500 USD'
    }
]
```

### **ج) استخراج رموز Discord**

**مواقع الرموز:**
- `%APPDATA%/discord/Local Storage/leveldb`
- `localStorage.token`
- `sessionStorage.discord_token`

**التطبيق في الوحدة:**
```bash
# تشغيل محاكاة RedLine
Info-Stealer-EDU> 1
```

---

## 🔵 **4. تقنيات AZORult Stealer** {#azorult}

### **أ) استخراج بيانات عملاء FTP**

**العملاء المستهدفة:**
- **FileZilla**: `%APPDATA%/FileZilla`
- **WinSCP**: `%APPDATA%/WinSCP.ini`
- **Total Commander**: `%APPDATA%/GHISLER`

### **ب) حسابات منصات الألعاب**

**المنصات المستهدفة:**
- **Steam**: `%PROGRAMFILES%/Steam`
- **Epic Games**: `%LOCALAPPDATA%/EpicGamesLauncher`
- **Battle.net**: `%PROGRAMDATA%/Battle.net`
- **Origin**: `%PROGRAMDATA%/Origin`

**البيانات المستخرجة:**
```python
gaming_accounts = [
    {
        'platform': 'Steam',
        'username': 'gamer_steam',
        'games_owned': 45,
        'account_value': '$1200'
    }
]
```

### **ج) تعداد البرامج المثبتة**

**مصادر المعلومات:**
- **Windows Registry**: `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall`
- **Linux**: Package managers (apt, yum, pacman)
- **macOS**: Applications folder

**التطبيق في الوحدة:**
```bash
# تشغيل محاكاة AZORult
Info-Stealer-EDU> 2
```

---

## 🟡 **5. تقنيات Emotet** {#emotet}

### **أ) حصاد جهات الاتصال**

**مصادر جهات الاتصال:**
- **Outlook**: `*.pst`, `*.ost` files
- **Thunderbird**: `*.mab` files
- **Windows Address Book**: `*.wab` files

### **ب) الاستطلاع الشبكي**

**تقنيات الاستطلاع:**
- فحص الشبكة المحلية
- اكتشاف أجهزة أخرى
- تحديد خدمات مفتوحة
- جمع معلومات النطاق

**البيانات المجمعة:**
```python
network_info = {
    'local_network': {
        'ip_range': '***********/24',
        'discovered_hosts': [
            {'ip': '*************', 'hostname': 'desktop-pc', 'ports': [135, 139, 445]}
        ]
    }
}
```

### **ج) آليات البقاء**

**تقنيات البقاء:**
- تعديل سجل Windows
- إنشاء خدمات النظام
- إضافة مهام مجدولة
- تعديل مجلد بدء التشغيل

**التطبيق في الوحدة:**
```bash
# تشغيل محاكاة Emotet
Info-Stealer-EDU> 3
```

---

## 🔗 **6. التكامل مع الوحدات الأخرى** {#integration}

### **أ) التكامل مع Social Media Accounts**

**الاستخدام المشترك:**
```python
# دمج Info-Stealer مع Social Media
from modules.info_stealer_educational import InfoStealerEducational
from modules.social_media_accounts import SocialMediaAccounts

stealer = InfoStealerEducational()
social_module = SocialMediaAccounts()

# استخراج رموز وسائل التواصل
tokens = stealer.extract_social_media_tokens_simulation()

# استخدام الرموز في وحدة Social Media
for token_data in tokens:
    social_module.use_extracted_token(token_data)
```

### **ب) التكامل مع Password Cracking**

**الاستخدام المشترك:**
```python
# دمج Info-Stealer مع Password Cracking
from modules.password_cracking import PasswordCracking

stealer = InfoStealerEducational()
password_module = PasswordCracking()

# استخراج كلمات المرور المشفرة
browser_data = stealer.extract_browser_data_simulation()

# محاولة فك تشفير كلمات المرور
for password_entry in browser_data['passwords']:
    decrypted = password_module.decrypt_browser_password(password_entry)
```

### **ج) التكامل مع XSS Module**

**الاستخدام المشترك:**
```python
# دمج Info-Stealer مع XSS
from modules.web_exploitation_xss import XSSEducationalFramework

stealer = InfoStealerEducational()
xss_module = XSSEducationalFramework()

# إنشاء XSS payload لسرقة البيانات
payload = xss_module.create_xss_payload_for_cookie_theft("http://127.0.0.1:8888")

# محاكاة جمع البيانات المسروقة عبر XSS
stolen_data = stealer.simulate_xss_data_collection(payload)
```

### **د) الوحدات المستقلة**

**التكامل مع:**
- `standalone/social_media_accounts/` - لاستهداف المنصات الاجتماعية
- `standalone/password_cracking/` - لفك تشفير كلمات المرور
- `standalone/phone_number_targeting/` - لربط البيانات بأرقام الهواتف

---

## 🛡️ **7. الاعتبارات الدفاعية** {#defense}

### **أ) كشف Info-Stealers**

**علامات الإنذار:**
- وصول غير مبرر لملفات المتصفح
- قراءة ملفات محافظ العملات المشفرة
- فحص مجلدات التطبيقات الحساسة
- اتصالات شبكة مشبوهة
- تشفير أو ضغط ملفات البيانات

### **ب) تقنيات الحماية**

**الحماية على مستوى النظام:**
```bash
# مراقبة الوصول للملفات الحساسة
auditctl -w /home/<USER>/.config/google-chrome -p rwxa -k browser_access
auditctl -w /home/<USER>/.electrum -p rwxa -k crypto_wallet_access

# مراقبة العمليات المشبوهة
ps aux | grep -E "(chrome|firefox|electrum)" | grep -v grep
```

**الحماية على مستوى التطبيق:**
- تشفير قواعد بيانات المتصفح
- استخدام كلمات مرور رئيسية
- تفعيل المصادقة الثنائية
- تحديث البرامج بانتظام

### **ج) مراقبة الشبكة**

**مؤشرات الاختراق (IOCs):**
```bash
# مراقبة الاتصالات المشبوهة
netstat -an | grep ESTABLISHED | grep -E "(443|80|8080)"

# فحص DNS queries مشبوهة
dig +short suspicious-domain.com

# مراقبة حركة البيانات الصادرة
iftop -i eth0 -P
```

### **د) الاستجابة للحوادث**

**خطوات الاستجابة:**
1. **العزل الفوري** - قطع الاتصال بالشبكة
2. **تحليل النظام** - فحص الملفات المتأثرة
3. **تغيير كلمات المرور** - جميع الحسابات المعرضة
4. **مراجعة السجلات** - تتبع النشاط المشبوه
5. **التعافي** - استعادة البيانات من النسخ الاحتياطية

---

## 🚀 **8. البدء في التعلم**

### **الخطوة 1: تشغيل الوحدة التعليمية**
```bash
cd /home/<USER>/Desktop/Year3/botnet/botnet_lab
source botnet_env/bin/activate
python modules/info_stealer_educational.py
```

### **الخطوة 2: استكشاف التقنيات**
```bash
# محاكاة RedLine Stealer
Info-Stealer-EDU> 1

# محاكاة AZORult Stealer  
Info-Stealer-EDU> 2

# محاكاة Emotet
Info-Stealer-EDU> 3

# استخراج رموز وسائل التواصل
Info-Stealer-EDU> 4

# إنشاء تقرير شامل
Info-Stealer-EDU> 5
```

### **الخطوة 3: تحليل النتائج**
```bash
# عرض إحصائيات قاعدة البيانات
Info-Stealer-EDU> 6

# فحص ملف قاعدة البيانات
sqlite3 info_stealer_educational.db
.tables
SELECT * FROM browser_data LIMIT 5;
```

### **الخطوة 4: التطوير والتحسين**
- دراسة الكود المصدري للوحدة
- إضافة تقنيات جديدة
- تطوير آليات دفاعية
- اختبار تقنيات الكشف

---

## 📚 **9. مصادر إضافية للتعلم**

### **التقارير الأمنية:**
- MITRE ATT&CK Framework - Credential Access
- SANS InfoSec Reading Room
- Symantec Threat Intelligence Reports

### **الأدوات التحليلية:**
- **Process Monitor** - مراقبة الوصول للملفات
- **Wireshark** - تحليل حركة الشبكة
- **Volatility** - تحليل ذاكرة النظام
- **YARA** - كشف البرامج الضارة

### **البيئات التعليمية:**
- **Malware Analysis Sandbox** - تحليل آمن للبرامج الضارة
- **FLARE VM** - بيئة تحليل البرامج الضارة
- **REMnux** - توزيعة Linux لتحليل البرامج الضارة

---

## ⚠️ **تذكير نهائي**

هذا الدليل مخصص **للتعليم والبحث الأخلاقي فقط**. استخدم هذه المعرفة لـ:
- 🛡️ **تطوير الدفاعات** ضد Info-Stealers
- 🎓 **التعلم والتطوير** المهني في الأمن السيبراني
- 🔍 **البحث الأكاديمي** المسؤول
- 🤝 **مساعدة المؤسسات** في تأمين أنظمتها

**لا تستخدم هذه التقنيات لأغراض ضارة أو غير قانونية!**

---

## 📞 **الدعم والمساعدة**

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع ملفات الوثائق في `docs/`
- تحقق من ملفات السجلات في `logs/`
- استخدم قاعدة البيانات لتتبع النتائج
- اتبع أفضل الممارسات الأمنية دائماً
