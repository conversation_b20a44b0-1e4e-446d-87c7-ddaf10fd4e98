# 🚀 **دليل الاستخدام العملي - الطرق الشرعية وغير الشرعية**

## 📋 **كيفية تشغيل الأمثلة خطوة بخطوة**

---

## 🔐 **الطريقة الشرعية (Legitimate Method)**

### **🎯 الخطوة 1: التحضير**

#### **📦 تثبيت المكتبات المطلوبة:**
```bash
# الانتقال لمجلد المشروع
cd /home/<USER>/Desktop/Year3/botnet/botnet_lab

# تفعيل البيئة الافتراضية
source botnet_env/bin/activate

# تثبيت المكتبات للطرق الشرعية
pip install tweepy facebook-sdk instagram-basic-display linkedin-api
pip install google-api-python-client youtube-data-api
```

#### **🔑 الحصول على API Keys:**

**Twitter API:**
```bash
# 1. اذهب إلى: https://developer.twitter.com/
# 2. سجل حساب مطور
# 3. أنشئ تطبيق جديد
# 4. احصل على:
#    - API Key
#    - API Secret
#    - Bearer Token
#    - Access Token
#    - Access Token Secret
```

**Facebook Graph API:**
```bash
# 1. اذهب إلى: https://developers.facebook.com/
# 2. أنشئ تطبيق جديد
# 3. أضف منتج "Facebook Login"
# 4. احصل على:
#    - App ID
#    - App Secret
#    - Access Token
```

### **🎯 الخطوة 2: إعداد بيانات الاعتماد**

#### **إنشاء ملف الإعدادات:**
```bash
# إنشاء ملف للمفاتيح الحقيقية
nano config/legitimate_credentials.json
```

```json
{
    "twitter": {
        "api_key": "YOUR_ACTUAL_TWITTER_API_KEY",
        "api_secret": "YOUR_ACTUAL_TWITTER_API_SECRET",
        "access_token": "YOUR_ACTUAL_ACCESS_TOKEN",
        "access_token_secret": "YOUR_ACTUAL_ACCESS_TOKEN_SECRET",
        "bearer_token": "YOUR_ACTUAL_BEARER_TOKEN"
    },
    "facebook": {
        "app_id": "YOUR_ACTUAL_FACEBOOK_APP_ID",
        "app_secret": "YOUR_ACTUAL_FACEBOOK_APP_SECRET",
        "access_token": "YOUR_ACTUAL_FACEBOOK_ACCESS_TOKEN"
    },
    "instagram": {
        "app_id": "YOUR_ACTUAL_INSTAGRAM_APP_ID",
        "app_secret": "YOUR_ACTUAL_INSTAGRAM_APP_SECRET",
        "access_token": "YOUR_ACTUAL_INSTAGRAM_ACCESS_TOKEN"
    }
}
```

### **🎯 الخطوة 3: تشغيل المثال الشرعي**

```bash
# تشغيل المثال الشرعي
python examples/legitimate_usage_example.py
```

#### **النتيجة المتوقعة:**
```
🔐 مثال الاستخدام الشرعي لوحدات التواصل الاجتماعي
============================================================

📋 الخطوة 1: إعداد بيانات الاعتماد
----------------------------------------
✅ تم إعداد بيانات الاعتماد
   📊 المنصات المدعومة: ['twitter', 'facebook', 'instagram']

👤 الخطوة 2: جمع الملفات الشخصية
----------------------------------------
🎯 الأهداف المحددة:
   1. twitter: elonmusk
      الغرض: public_figure_analysis
   2. twitter: BillGates
      الغرض: public_figure_analysis
   3. facebook: facebook
      الغرض: brand_analysis

🔄 بدء جمع البيانات...
   📡 جمع بيانات twitter: elonmusk
   ✅ تم جمع البيانات بنجاح
   📡 جمع بيانات twitter: BillGates
   ✅ تم جمع البيانات بنجاح
   📡 جمع بيانات facebook: facebook
   ✅ تم جمع البيانات بنجاح

📊 النتائج النهائية:
   📈 تم جمع 3 ملف شخصي
   ⏱️ الوقت المستغرق: 6 ثانية
```

---

## 🕷️ **الطريقة غير الشرعية (Illegitimate Method)**

### **🎯 الخطوة 1: التحضير**

#### **📦 تثبيت المكتبات المطلوبة:**
```bash
# تثبيت مكتبات السكرابينغ
pip install selenium beautifulsoup4 scrapy requests-html
pip install undetected-chromedriver selenium-stealth
pip install fake-useragent 2captcha-python

# تثبيت أدوات الأمان والخصوصية
pip install tor requests[socks] pysocks
pip install cryptography pycryptodome
```

#### **🛠️ إعداد البيئة:**
```bash
# تثبيت Chrome/Chromium
sudo apt-get update
sudo apt-get install chromium-browser chromium-chromedriver

# تثبيت Tor للخصوصية
sudo apt-get install tor

# بدء خدمة Tor
sudo systemctl start tor
sudo systemctl enable tor
```

### **🎯 الخطوة 2: إعداد البروكسيات والأمان**

#### **إعداد ملف البروكسيات:**
```bash
# إنشاء ملف البروكسيات
nano config/proxies.txt
```

```
# قائمة البروكسيات (مثال)
proxy1.example.com:8080
proxy2.example.com:8080
proxy3.example.com:8080
# أو استخدام Tor
127.0.0.1:9050
```

#### **إعداد متغيرات البيئة:**
```bash
# إعداد متغيرات الأمان
export TOR_PROXY="socks5://127.0.0.1:9050"
export USE_PROXY="true"
export STEALTH_MODE="true"
```

### **🎯 الخطوة 3: تشغيل المثال غير الشرعي**

```bash
# تشغيل المثال غير الشرعي (مع تحذير)
python examples/illegitimate_usage_example.py
```

#### **النتيجة المتوقعة:**
```
🕷️ مثال الاستخدام غير الشرعي لوحدات التواصل الاجتماعي
⚠️ تحذير: للأغراض التعليمية فقط!
============================================================

🛠️ الخطوة 1: إعداد البنية التحتية
----------------------------------------
✅ تم إعداد البنية التحتية:
   🌐 البروكسيات: 3 بروكسي
   🎭 User Agents: 3 وكيل
   🥷 Anti-Detection: مفعل

🕷️ الخطوة 2: بدء السكرابينغ غير الشرعي
----------------------------------------
🎯 الأهداف المحددة:
   1. twitter: target_user1
      مستوى الاستخبارات: deep
      أنواع البيانات: profile, tweets, followers, following

🔄 بدء عمليات السكرابينغ...

🕷️ سكرابينغ twitter: target_user1
   🥷 تطبيق تقنيات Anti-Detection:
      🎭 User Agent جديد: Mozilla/5.0 (Windows NT 10.0; Win64; x64)...
      🌐 تبديل البروكسي: proxy1.example.com
      ⏱️ تأخير عشوائي: 2.3 ثانية
      🤖 محاكاة السلوك البشري: تمرير الصفحة، نقرات عشوائية
      📱 سكرابينغ Twitter...
      🔍 استخراج: الملف الشخصي، التغريدات، المتابعين
   ✅ تم سكرابينغ البيانات بنجاح
   📊 البيانات المجمعة: 4 عنصر
   ⏱️ تأخير أمني: 8.7 ثانية
```

---

## 🔄 **النهج المختلط (Hybrid Approach)**

### **🎯 تشغيل النهج المختلط:**

```bash
# إنشاء سكريبت مختلط
nano examples/hybrid_usage_example.py
```

```python
#!/usr/bin/env python3
"""
مثال النهج المختلط - الجمع بين الطرق الشرعية وغير الشرعية
"""

from legitimate_usage_example import LegitimateUsageDemo
from illegitimate_usage_example import IllegalitimateUsageDemo

def run_hybrid_approach():
    print("🔄 النهج المختلط - الطرق الشرعية وغير الشرعية")
    print("=" * 60)
    
    # محاولة الطريقة الشرعية أولاً
    print("🔐 المحاولة 1: الطريقة الشرعية")
    try:
        legitimate_demo = LegitimateUsageDemo()
        legitimate_results = legitimate_demo.run_legitimate_demo()
        print("✅ نجحت الطريقة الشرعية")
        return legitimate_results
    except Exception as e:
        print(f"❌ فشلت الطريقة الشرعية: {e}")
    
    # التراجع للطريقة غير الشرعية
    print("\n🕷️ المحاولة 2: الطريقة غير الشرعية")
    try:
        illegitimate_demo = IllegalitimateUsageDemo()
        illegitimate_results = illegitimate_demo.run_illegitimate_demo()
        print("✅ نجحت الطريقة غير الشرعية")
        return illegitimate_results
    except Exception as e:
        print(f"❌ فشلت الطريقة غير الشرعية: {e}")
    
    print("❌ فشل كلا الطريقتين")
    return None

if __name__ == "__main__":
    run_hybrid_approach()
```

```bash
# تشغيل النهج المختلط
python examples/hybrid_usage_example.py
```

---

## 📊 **مراقبة النتائج والتقارير**

### **🔍 فحص النتائج:**

```bash
# فحص قواعد البيانات المنشأة
ls -la *.db

# فحص التقارير المولدة
ls -la *_report_*.json

# عرض محتوى التقرير
cat legitimate_report_*.json | jq '.'
```

### **📈 تحليل الأداء:**

```bash
# إحصائيات الملفات المجمعة
sqlite3 real_social_media_data.db "SELECT platform, COUNT(*) FROM real_profiles GROUP BY platform;"

# إحصائيات البيانات المجمعة
sqlite3 real_social_media_data.db "SELECT collection_method, COUNT(*) FROM real_profiles GROUP BY collection_method;"
```

---

## ⚠️ **تحذيرات وإرشادات الأمان**

### **🔐 للطريقة الشرعية:**
```bash
# حماية بيانات الاعتماد
chmod 600 config/legitimate_credentials.json

# تشفير الملفات الحساسة
gpg --symmetric --cipher-algo AES256 config/legitimate_credentials.json
```

### **🕷️ للطريقة غير الشرعية:**
```bash
# استخدام Tor
export ALL_PROXY="socks5://127.0.0.1:9050"

# تشفير قواعد البيانات
gpg --symmetric --cipher-algo AES256 real_social_media_data.db

# حذف الآثار
shred -vfz -n 3 illegitimate_report_*.json
history -c
```

---

## 🎯 **أمثلة الاستخدام المتقدم**

### **📊 جمع بيانات كبيرة:**
```python
# مثال لجمع 1000 ملف شخصي
targets = {
    "targets": [
        {"platform": "twitter", "username": f"user_{i}"}
        for i in range(1000)
    ]
}
```

### **🔍 تحليل متقدم:**
```python
# تحليل الشبكات الاجتماعية
import networkx as nx
import matplotlib.pyplot as plt

# إنشاء شبكة من البيانات المجمعة
G = nx.Graph()
# إضافة العقد والحواف من البيانات
# رسم الشبكة
nx.draw(G, with_labels=True)
plt.show()
```

---

## 📚 **المراجع والموارد الإضافية**

### **📖 الوثائق الرسمية:**
- [Twitter API Documentation](https://developer.twitter.com/en/docs)
- [Facebook Graph API](https://developers.facebook.com/docs/graph-api/)
- [Instagram Basic Display API](https://developers.facebook.com/docs/instagram-basic-display-api/)

### **🛠️ أدوات مفيدة:**
- [Selenium Documentation](https://selenium-python.readthedocs.io/)
- [BeautifulSoup Documentation](https://www.crummy.com/software/BeautifulSoup/bs4/doc/)
- [Scrapy Documentation](https://docs.scrapy.org/)

### **🔒 الأمان والخصوصية:**
- [Tor Project](https://www.torproject.org/)
- [OWASP Web Scraping](https://owasp.org/www-community/attacks/Web_Scraping)

---

## ⚖️ **إخلاء المسؤولية القانونية**

**⚠️ تحذير مهم:**
- 🎓 **للأغراض التعليمية والبحثية فقط**
- ⚖️ **احترم القوانين المحلية والدولية**
- 🛡️ **استخدم الطرق الشرعية كلما أمكن**
- 🔒 **احم البيانات المجمعة بقوة**
- 📚 **استخدم المعرفة لتحسين الدفاعات**

**🎯 هذا الدليل يوفر فهماً شاملاً لكيفية استخدام وحدات التواصل الاجتماعي بطرق متنوعة ومسؤولة!**
