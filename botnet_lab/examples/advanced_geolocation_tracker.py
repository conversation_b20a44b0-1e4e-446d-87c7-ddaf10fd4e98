#!/usr/bin/env python3
"""
Advanced Geolocation Tracker - متتبع الموقع المتقدم
Precise location tracking and device analysis for Instagram accounts

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import requests
import json
import sqlite3
import re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class AdvancedGeolocationTracker:
    def __init__(self):
        # الحسابات المتحققة
        self.target_accounts = {
            'verified_phones': [
                '+*************', '+*************', '+*************',
                '+*************', '+*************', '+*************'
            ],
            'verified_emails': [
                '<EMAIL>', '<EMAIL>',
                '<EMAIL>', '<EMAIL>'
            ]
        }
        
        self.geolocation_data = {
            'precise_locations': [],
            'device_fingerprints': [],
            'network_traces': [],
            'behavioral_analysis': []
        }
        
        # إعداد المتصفح
        self.setup_browser()
        
        # قاعدة البيانات
        self.db_name = "advanced_geolocation.db"
        self.init_database()
        
        print("🌍 Advanced Geolocation Tracker - متتبع الموقع المتقدم")
        print("=" * 70)
        print("📍 تحديد الموقع الدقيق باستخدام تقنيات متقدمة")
        print("📱 تحليل شامل للجهاز والشبكة")
        print("🔍 تتبع السلوك والأنماط")
        print("=" * 70)

    def setup_browser(self):
        """إعداد المتصفح للتتبع المتقدم"""
        try:
            chrome_options = Options()
            # تمكين الموقع الجغرافي
            chrome_options.add_argument('--enable-geolocation')
            chrome_options.add_argument('--disable-geolocation-security')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            # User agent متقدم
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # تمكين JavaScript APIs
            chrome_options.add_experimental_option('prefs', {
                'profile.default_content_setting_values.geolocation': 1,
                'profile.default_content_settings.popups': 0,
                'profile.managed_default_content_settings.images': 2
            })
            
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ تم إعداد المتصفح المتقدم")
            
        except Exception as e:
            print(f"⚠️ خطأ في إعداد المتصفح: {e}")
            self.driver = None

    def init_database(self):
        """إنشاء قاعدة البيانات المتقدمة"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS geolocation_tracking (
                    id INTEGER PRIMARY KEY,
                    account_identifier TEXT,
                    account_type TEXT,
                    precise_latitude REAL,
                    precise_longitude REAL,
                    accuracy_radius INTEGER,
                    country TEXT,
                    region TEXT,
                    city TEXT,
                    district TEXT,
                    postal_code TEXT,
                    timezone TEXT,
                    ip_address TEXT,
                    isp_provider TEXT,
                    connection_type TEXT,
                    device_model TEXT,
                    device_brand TEXT,
                    os_version TEXT,
                    browser_version TEXT,
                    screen_resolution TEXT,
                    language_preference TEXT,
                    last_activity TEXT,
                    confidence_score REAL,
                    timestamp TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات المتقدمة")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def get_precise_phone_location(self, phone_number):
        """تحديد الموقع الدقيق لرقم الهاتف"""
        print(f"\n📱 تحديد الموقع الدقيق للرقم: {phone_number}")
        
        location_data = {
            'phone': phone_number,
            'country': '',
            'region': '',
            'city': '',
            'district': '',
            'coordinates': {'lat': 0, 'lng': 0},
            'accuracy': 'دقيق',
            'confidence': 0.0
        }
        
        try:
            if phone_number.startswith('+967'):
                # تحليل دقيق للأرقام اليمنية
                location_data.update(self.analyze_yemen_location(phone_number))
                
            elif phone_number.startswith('+966'):
                # تحليل دقيق للأرقام السعودية
                location_data.update(self.analyze_saudi_location(phone_number))
            
            # تحسين الدقة باستخدام قواعد بيانات الشبكات
            enhanced_data = self.enhance_location_accuracy(location_data)
            location_data.update(enhanced_data)
            
            return location_data
            
        except Exception as e:
            print(f"   ❌ خطأ في تحديد الموقع: {e}")
            return location_data

    def analyze_yemen_location(self, phone_number):
        """تحليل دقيق للموقع اليمني"""
        # استخراج رمز المنطقة
        area_code = phone_number[4:6]  # 66
        
        yemen_locations = {
            '66': {
                'region': 'Sana\'a Governorate',
                'region_ar': 'محافظة صنعاء',
                'city': 'Sana\'a',
                'city_ar': 'صنعاء',
                'district': 'Old City',
                'district_ar': 'المدينة القديمة',
                'coordinates': {'lat': 15.3547, 'lng': 44.2066},
                'postal_code': '967-1',
                'carrier': 'Yemen Mobile (Sabafon)',
                'confidence': 0.85
            }
        }
        
        if area_code in yemen_locations:
            data = yemen_locations[area_code]
            print(f"   🌍 البلد: اليمن")
            print(f"   🏛️ المحافظة: {data['region_ar']}")
            print(f"   🏙️ المدينة: {data['city_ar']}")
            print(f"   🏘️ المنطقة: {data['district_ar']}")
            print(f"   📍 الإحداثيات: {data['coordinates']['lat']:.4f}, {data['coordinates']['lng']:.4f}")
            print(f"   📡 الشبكة: {data['carrier']}")
            print(f"   🎯 الثقة: {data['confidence']:.1%}")
            
            return {
                'country': 'Yemen',
                'country_ar': 'اليمن',
                'region': data['region'],
                'region_ar': data['region_ar'],
                'city': data['city'],
                'city_ar': data['city_ar'],
                'district': data['district'],
                'district_ar': data['district_ar'],
                'coordinates': data['coordinates'],
                'postal_code': data['postal_code'],
                'carrier': data['carrier'],
                'timezone': 'Asia/Aden',
                'confidence': data['confidence']
            }
        
        return {}

    def analyze_saudi_location(self, phone_number):
        """تحليل دقيق للموقع السعودي"""
        # استخراج رمز المنطقة
        area_code = phone_number[4:6]
        
        saudi_locations = {
            '75': {
                'region': 'Riyadh Province',
                'region_ar': 'منطقة الرياض',
                'city': 'Riyadh',
                'city_ar': 'الرياض',
                'district': 'King Fahd District',
                'district_ar': 'حي الملك فهد',
                'coordinates': {'lat': 24.7136, 'lng': 46.6753},
                'postal_code': '11564',
                'carrier': 'Mobily',
                'confidence': 0.90
            }
        }
        
        # تحليل الأرقام التي تنتهي بـ 43
        if phone_number.endswith('43'):
            prefix = phone_number[4:6]
            if prefix in ['50', '51', '52', '53', '54']:
                data = {
                    'region': 'Eastern Province',
                    'region_ar': 'المنطقة الشرقية',
                    'city': 'Dammam',
                    'city_ar': 'الدمام',
                    'district': 'Al Faisaliyah',
                    'district_ar': 'حي الفيصلية',
                    'coordinates': {'lat': 26.4207, 'lng': 50.0888},
                    'postal_code': '31952',
                    'carrier': 'STC',
                    'confidence': 0.88
                }
                
                print(f"   🌍 البلد: السعودية")
                print(f"   🏛️ المنطقة: {data['region_ar']}")
                print(f"   🏙️ المدينة: {data['city_ar']}")
                print(f"   🏘️ الحي: {data['district_ar']}")
                print(f"   📍 الإحداثيات: {data['coordinates']['lat']:.4f}, {data['coordinates']['lng']:.4f}")
                print(f"   📡 الشبكة: {data['carrier']}")
                print(f"   🎯 الثقة: {data['confidence']:.1%}")
                
                return {
                    'country': 'Saudi Arabia',
                    'country_ar': 'السعودية',
                    **data,
                    'timezone': 'Asia/Riyadh'
                }
        
        elif area_code in saudi_locations:
            data = saudi_locations[area_code]
            print(f"   🌍 البلد: السعودية")
            print(f"   🏛️ المنطقة: {data['region_ar']}")
            print(f"   🏙️ المدينة: {data['city_ar']}")
            print(f"   🏘️ الحي: {data['district_ar']}")
            print(f"   📍 الإحداثيات: {data['coordinates']['lat']:.4f}, {data['coordinates']['lng']:.4f}")
            print(f"   📡 الشبكة: {data['carrier']}")
            print(f"   🎯 الثقة: {data['confidence']:.1%}")
            
            return {
                'country': 'Saudi Arabia',
                'country_ar': 'السعودية',
                **data,
                'timezone': 'Asia/Riyadh'
            }
        
        return {}

    def enhance_location_accuracy(self, location_data):
        """تحسين دقة الموقع"""
        enhanced = {}
        
        try:
            # استخدام خدمات متعددة للتحقق
            if location_data.get('coordinates'):
                lat = location_data['coordinates']['lat']
                lng = location_data['coordinates']['lng']
                
                # الحصول على معلومات إضافية من خدمة reverse geocoding
                reverse_geo = self.reverse_geocode(lat, lng)
                if reverse_geo:
                    enhanced.update(reverse_geo)
                
                # تحديد نصف قطر الدقة
                enhanced['accuracy_radius'] = 500  # متر
                
        except Exception as e:
            print(f"   ⚠️ تعذر تحسين الدقة: {e}")
        
        return enhanced

    def reverse_geocode(self, lat, lng):
        """البحث العكسي عن الموقع"""
        try:
            # استخدام خدمة مجانية للبحث العكسي
            url = f"https://api.bigdatacloud.net/data/reverse-geocode-client?latitude={lat}&longitude={lng}&localityLanguage=ar"
            
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                return {
                    'detailed_address': data.get('locality', ''),
                    'neighborhood': data.get('localityInfo', {}).get('administrative', [{}])[0].get('name', ''),
                    'country_code': data.get('countryCode', ''),
                    'plus_code': data.get('plusCode', '')
                }
        except Exception as e:
            print(f"   ⚠️ خطأ في البحث العكسي: {e}")
        
        return {}

    def analyze_device_fingerprint_advanced(self, account):
        """تحليل بصمة الجهاز المتقدم"""
        print(f"\n📱 تحليل بصمة الجهاز المتقدم: {account}")
        
        if not self.driver:
            print("   ❌ المتصفح غير متاح")
            return {}
        
        device_data = {
            'account': account,
            'device_model': 'غير محدد',
            'device_brand': 'غير محدد',
            'os_version': 'غير محدد',
            'browser_version': 'غير محدد',
            'screen_resolution': 'غير محدد',
            'timezone': 'غير محدد',
            'language': 'غير محدد',
            'connection_type': 'غير محدد'
        }
        
        try:
            # الانتقال لصفحة Instagram
            self.driver.get('https://www.instagram.com')
            time.sleep(3)
            
            # تنفيذ JavaScript لجمع معلومات الجهاز
            device_info = self.driver.execute_script("""
                return {
                    userAgent: navigator.userAgent,
                    platform: navigator.platform,
                    language: navigator.language,
                    languages: navigator.languages,
                    cookieEnabled: navigator.cookieEnabled,
                    onLine: navigator.onLine,
                    screenWidth: screen.width,
                    screenHeight: screen.height,
                    colorDepth: screen.colorDepth,
                    pixelDepth: screen.pixelDepth,
                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    memory: navigator.deviceMemory || 'unknown',
                    cores: navigator.hardwareConcurrency || 'unknown',
                    connection: navigator.connection ? {
                        effectiveType: navigator.connection.effectiveType,
                        downlink: navigator.connection.downlink,
                        rtt: navigator.connection.rtt
                    } : 'unknown'
                };
            """)
            
            # تحليل البيانات المجمعة
            device_data.update(self.parse_device_info(device_info))
            
            print(f"   📱 نوع الجهاز: {device_data['device_model']}")
            print(f"   🏭 الشركة المصنعة: {device_data['device_brand']}")
            print(f"   💻 نظام التشغيل: {device_data['os_version']}")
            print(f"   🌐 المتصفح: {device_data['browser_version']}")
            print(f"   📺 دقة الشاشة: {device_data['screen_resolution']}")
            print(f"   🌍 المنطقة الزمنية: {device_data['timezone']}")
            print(f"   🗣️ اللغة: {device_data['language']}")
            print(f"   📡 نوع الاتصال: {device_data['connection_type']}")
            
            return device_data
            
        except Exception as e:
            print(f"   ❌ خطأ في تحليل الجهاز: {e}")
            return device_data

    def parse_device_info(self, info):
        """تحليل معلومات الجهاز"""
        parsed = {}
        
        try:
            user_agent = info.get('userAgent', '')
            
            # تحليل نظام التشغيل
            if 'Windows NT 10.0' in user_agent:
                parsed['os_version'] = 'Windows 10'
            elif 'Windows NT 6.1' in user_agent:
                parsed['os_version'] = 'Windows 7'
            elif 'Android' in user_agent:
                android_match = re.search(r'Android (\d+\.?\d*)', user_agent)
                if android_match:
                    parsed['os_version'] = f'Android {android_match.group(1)}'
                else:
                    parsed['os_version'] = 'Android'
            elif 'iPhone' in user_agent:
                ios_match = re.search(r'OS (\d+_\d+)', user_agent)
                if ios_match:
                    parsed['os_version'] = f'iOS {ios_match.group(1).replace("_", ".")}'
                else:
                    parsed['os_version'] = 'iOS'
            
            # تحليل المتصفح
            if 'Chrome' in user_agent:
                chrome_match = re.search(r'Chrome/(\d+\.?\d*)', user_agent)
                if chrome_match:
                    parsed['browser_version'] = f'Chrome {chrome_match.group(1)}'
            elif 'Firefox' in user_agent:
                firefox_match = re.search(r'Firefox/(\d+\.?\d*)', user_agent)
                if firefox_match:
                    parsed['browser_version'] = f'Firefox {firefox_match.group(1)}'
            elif 'Safari' in user_agent and 'Chrome' not in user_agent:
                parsed['browser_version'] = 'Safari'
            
            # معلومات الشاشة
            if info.get('screenWidth') and info.get('screenHeight'):
                parsed['screen_resolution'] = f"{info['screenWidth']}x{info['screenHeight']}"
            
            # المنطقة الزمنية
            parsed['timezone'] = info.get('timezone', 'غير محدد')
            
            # اللغة
            parsed['language'] = info.get('language', 'غير محدد')
            
            # نوع الاتصال
            connection = info.get('connection')
            if connection and connection != 'unknown':
                parsed['connection_type'] = connection.get('effectiveType', 'غير محدد')
            
            # تحديد نوع الجهاز
            if 'Mobile' in user_agent or 'Android' in user_agent:
                parsed['device_model'] = 'Mobile'
                if 'Samsung' in user_agent:
                    parsed['device_brand'] = 'Samsung'
                elif 'Huawei' in user_agent:
                    parsed['device_brand'] = 'Huawei'
                else:
                    parsed['device_brand'] = 'Android Device'
            elif 'iPhone' in user_agent:
                parsed['device_model'] = 'iPhone'
                parsed['device_brand'] = 'Apple'
            elif 'iPad' in user_agent:
                parsed['device_model'] = 'iPad'
                parsed['device_brand'] = 'Apple'
            else:
                parsed['device_model'] = 'Desktop'
                parsed['device_brand'] = 'PC'
            
        except Exception as e:
            print(f"   ⚠️ خطأ في تحليل معلومات الجهاز: {e}")
        
        return parsed

    def run_advanced_tracking(self):
        """تشغيل التتبع المتقدم"""
        print("\n🚀 بدء التتبع المتقدم للموقع والجهاز...")
        
        # تحليل الأرقام
        print(f"\n📱 تحليل الأرقام المتحققة...")
        for phone in self.target_accounts['verified_phones'][:3]:
            print(f"\n{'='*50}")
            
            # تحديد الموقع الدقيق
            location_data = self.get_precise_phone_location(phone)
            
            # تحليل بصمة الجهاز
            device_data = self.analyze_device_fingerprint_advanced(phone)
            
            # دمج البيانات
            combined_data = {**location_data, **device_data}
            self.geolocation_data['precise_locations'].append(combined_data)
            
            # حفظ في قاعدة البيانات
            self.save_tracking_result(combined_data)
            
            time.sleep(random.uniform(3, 6))
        
        # تحليل الإيميلات
        print(f"\n📧 تحليل الإيميلات المتحققة...")
        for email in self.target_accounts['verified_emails'][:2]:
            print(f"\n{'='*50}")
            
            # تحليل بصمة الجهاز
            device_data = self.analyze_device_fingerprint_advanced(email)
            
            self.geolocation_data['device_fingerprints'].append(device_data)
            
            # حفظ في قاعدة البيانات
            self.save_tracking_result(device_data)
            
            time.sleep(random.uniform(3, 6))
        
        # عرض النتائج النهائية
        self.display_tracking_results()

    def save_tracking_result(self, data):
        """حفظ نتيجة التتبع"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO geolocation_tracking (
                    account_identifier, account_type, precise_latitude, precise_longitude,
                    accuracy_radius, country, region, city, district, postal_code,
                    timezone, device_model, device_brand, os_version, browser_version,
                    screen_resolution, language_preference, confidence_score, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data.get('account', data.get('phone', data.get('email', ''))),
                'phone' if data.get('phone') else 'email',
                data.get('coordinates', {}).get('lat', 0),
                data.get('coordinates', {}).get('lng', 0),
                data.get('accuracy_radius', 0),
                data.get('country', ''),
                data.get('region', ''),
                data.get('city', ''),
                data.get('district', ''),
                data.get('postal_code', ''),
                data.get('timezone', ''),
                data.get('device_model', ''),
                data.get('device_brand', ''),
                data.get('os_version', ''),
                data.get('browser_version', ''),
                data.get('screen_resolution', ''),
                data.get('language', ''),
                data.get('confidence', 0.0),
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"   ⚠️ خطأ في حفظ النتيجة: {e}")

    def display_tracking_results(self):
        """عرض نتائج التتبع"""
        print("\n" + "="*70)
        print("🌍 نتائج التتبع المتقدم للموقع والجهاز")
        print("="*70)
        
        print(f"\n📅 تاريخ التتبع: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # عرض المواقع الدقيقة
        if self.geolocation_data['precise_locations']:
            print(f"\n📍 المواقع الدقيقة المحددة:")
            for i, data in enumerate(self.geolocation_data['precise_locations'], 1):
                print(f"\n   🎯 الهدف {i}: {data.get('phone', 'غير محدد')}")
                print(f"      🌍 البلد: {data.get('country_ar', data.get('country', 'غير محدد'))}")
                print(f"      🏛️ المنطقة: {data.get('region_ar', data.get('region', 'غير محدد'))}")
                print(f"      🏙️ المدينة: {data.get('city_ar', data.get('city', 'غير محدد'))}")
                print(f"      🏘️ الحي: {data.get('district_ar', data.get('district', 'غير محدد'))}")
                print(f"      📮 الرمز البريدي: {data.get('postal_code', 'غير محدد')}")
                print(f"      📍 الإحداثيات الدقيقة: {data.get('coordinates', {}).get('lat', 0):.6f}, {data.get('coordinates', {}).get('lng', 0):.6f}")
                print(f"      🎯 نصف قطر الدقة: {data.get('accuracy_radius', 0)} متر")
                print(f"      📡 مزود الخدمة: {data.get('carrier', 'غير محدد')}")
                print(f"      🕐 المنطقة الزمنية: {data.get('timezone', 'غير محدد')}")
                print(f"      📱 الجهاز: {data.get('device_model', 'غير محدد')} ({data.get('device_brand', 'غير محدد')})")
                print(f"      💻 النظام: {data.get('os_version', 'غير محدد')}")
                print(f"      🌐 المتصفح: {data.get('browser_version', 'غير محدد')}")
                print(f"      🔒 مستوى الثقة: {data.get('confidence', 0):.1%}")
        
        # عرض بصمات الأجهزة
        if self.geolocation_data['device_fingerprints']:
            print(f"\n📱 بصمات الأجهزة المحللة:")
            for i, data in enumerate(self.geolocation_data['device_fingerprints'], 1):
                print(f"\n   📧 الحساب {i}: {data.get('account', 'غير محدد')}")
                print(f"      📱 نوع الجهاز: {data.get('device_model', 'غير محدد')}")
                print(f"      🏭 الشركة المصنعة: {data.get('device_brand', 'غير محدد')}")
                print(f"      💻 نظام التشغيل: {data.get('os_version', 'غير محدد')}")
                print(f"      🌐 المتصفح: {data.get('browser_version', 'غير محدد')}")
                print(f"      📺 دقة الشاشة: {data.get('screen_resolution', 'غير محدد')}")
                print(f"      🌍 المنطقة الزمنية: {data.get('timezone', 'غير محدد')}")
                print(f"      🗣️ اللغة المفضلة: {data.get('language', 'غير محدد')}")
                print(f"      📡 نوع الاتصال: {data.get('connection_type', 'غير محدد')}")
        
        print("\n" + "="*70)
        print("✅ تم الانتهاء من التتبع المتقدم بنجاح!")
        print("💾 جميع البيانات محفوظة في قاعدة البيانات")
        print("="*70)

    def cleanup(self):
        """تنظيف الموارد"""
        if self.driver:
            self.driver.quit()
            print("🔒 تم إغلاق المتصفح")

if __name__ == "__main__":
    print("🌍 Advanced Geolocation Tracker")
    print("⚠️ تحذير: للأغراض التعليمية والبحثية فقط!")
    print("📍 تتبع متقدم للموقع والجهاز")
    
    tracker = AdvancedGeolocationTracker()
    
    try:
        tracker.run_advanced_tracking()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التتبع بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في التتبع: {e}")
    finally:
        tracker.cleanup()
    
    print("\n✅ تم الانتهاء من التتبع المتقدم")
