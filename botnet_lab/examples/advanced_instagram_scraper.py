#!/usr/bin/env python3
"""
Advanced Instagram Scraper - استخراج البيانات العميقة
Deep Data Extraction from Instagram Profiles

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import json
import sqlite3
import re
from datetime import datetime, timedelta
from urllib.parse import urlparse, parse_qs

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    from selenium.webdriver.common.action_chains import Action<PERSON>hains
    from selenium.webdriver.common.keys import Keys
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

try:
    import requests
    from bs4 import BeautifulSoup
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    from fake_useragent import UserAgent
    FAKE_UA_AVAILABLE = True
except ImportError:
    FAKE_UA_AVAILABLE = False

class AdvancedInstagramScraper:
    def __init__(self, target_username):
        self.target_username = target_username.replace('@', '')
        self.target_url = f"https://www.instagram.com/{self.target_username}/"
        self.driver = None
        self.scraped_data = {
            'basic_info': {},
            'posts_data': [],
            'followers_sample': [],
            'following_sample': [],
            'story_highlights': [],
            'engagement_analysis': {},
            'posting_patterns': {},
            'hashtag_analysis': {},
            'location_data': [],
            'tagged_users': [],
            'external_links': [],
            'account_insights': {}
        }
        
        # إعداد قاعدة البيانات المتقدمة
        self.db_name = "advanced_instagram_data.db"
        self.init_advanced_database()
        
        print("🕷️ Advanced Instagram Scraper - استخراج البيانات العميقة")
        print("=" * 60)
        print(f"🎯 الهدف: @{self.target_username}")
        print("🔍 سيتم استخراج:")
        print("   📊 البيانات الأساسية المتقدمة")
        print("   📸 تحليل المنشورات والمحتوى")
        print("   👥 عينة من المتابعين والمتابعة")
        print("   📈 تحليل أنماط النشر")
        print("   🏷️ تحليل الهاشتاغات")
        print("   📍 بيانات المواقع")
        print("   🔗 الروابط الخارجية")
        print("   💡 رؤى الحساب")
        print("=" * 60)

    def init_advanced_database(self):
        """إنشاء قاعدة بيانات متقدمة"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            # جدول البيانات الأساسية المتقدمة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS advanced_profiles (
                    id INTEGER PRIMARY KEY,
                    username TEXT,
                    display_name TEXT,
                    bio TEXT,
                    followers_count INTEGER,
                    following_count INTEGER,
                    posts_count INTEGER,
                    profile_image_url TEXT,
                    is_verified BOOLEAN,
                    is_private BOOLEAN,
                    is_business BOOLEAN,
                    business_category TEXT,
                    external_url TEXT,
                    email TEXT,
                    phone TEXT,
                    address TEXT,
                    account_created_estimate TEXT,
                    last_post_date TEXT,
                    avg_likes INTEGER,
                    avg_comments INTEGER,
                    engagement_rate REAL,
                    posting_frequency TEXT,
                    most_used_hashtags TEXT,
                    scraping_timestamp TEXT
                )
            ''')
            
            # جدول المنشورات المفصل
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS detailed_posts (
                    id INTEGER PRIMARY KEY,
                    username TEXT,
                    post_id TEXT,
                    post_url TEXT,
                    post_type TEXT,
                    caption TEXT,
                    hashtags TEXT,
                    mentions TEXT,
                    likes_count INTEGER,
                    comments_count INTEGER,
                    post_date TEXT,
                    location TEXT,
                    image_urls TEXT,
                    video_url TEXT,
                    comments_sample TEXT,
                    scraping_timestamp TEXT
                )
            ''')
            
            # جدول المتابعين/المتابعة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS followers_following (
                    id INTEGER PRIMARY KEY,
                    target_username TEXT,
                    user_username TEXT,
                    user_display_name TEXT,
                    user_profile_image TEXT,
                    user_is_verified BOOLEAN,
                    user_is_private BOOLEAN,
                    user_followers_count INTEGER,
                    relationship_type TEXT,
                    scraping_timestamp TEXT
                )
            ''')
            
            # جدول تحليل التفاعل
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS engagement_analysis (
                    id INTEGER PRIMARY KEY,
                    username TEXT,
                    analysis_type TEXT,
                    analysis_data TEXT,
                    analysis_timestamp TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات المتقدمة")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def setup_advanced_driver(self):
        """إعداد WebDriver متقدم مع تقنيات Anti-Detection محسنة"""
        if not SELENIUM_AVAILABLE:
            return False
            
        try:
            print("🔧 إعداد WebDriver متقدم...")
            
            chrome_options = Options()
            
            # إعدادات Anti-Detection متقدمة
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--disable-features=VizDisplayCompositor')
            
            # تقليل استهلاك الموارد
            chrome_options.add_argument('--memory-pressure-off')
            chrome_options.add_argument('--max_old_space_size=4096')
            
            # User Agent متقدم
            if FAKE_UA_AVAILABLE:
                ua = UserAgent()
                user_agent = ua.random
            else:
                user_agents = [
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                ]
                user_agent = random.choice(user_agents)
            
            chrome_options.add_argument(f'--user-agent={user_agent}')
            
            # إنشاء WebDriver
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # تنفيذ سكريبتات Anti-Detection
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
            self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")
            
            print(f"   🎭 User Agent: {user_agent[:50]}...")
            print("✅ تم إعداد WebDriver المتقدم")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إعداد WebDriver: {e}")
            return False

    def human_like_scroll(self, pause_time=2):
        """تمرير بشري طبيعي"""
        try:
            # تمرير تدريجي
            for i in range(3):
                scroll_height = random.randint(300, 800)
                self.driver.execute_script(f"window.scrollBy(0, {scroll_height});")
                time.sleep(random.uniform(0.5, 1.5))
            
            # تمرير للأعلى قليلاً (سلوك بشري)
            self.driver.execute_script("window.scrollBy(0, -200);")
            time.sleep(pause_time)
            
        except Exception as e:
            print(f"⚠️ خطأ في التمرير: {e}")

    def extract_advanced_profile_data(self):
        """استخراج البيانات الأساسية المتقدمة"""
        print("\n📊 استخراج البيانات الأساسية المتقدمة...")
        
        try:
            # الانتقال للصفحة
            self.driver.get(self.target_url)
            time.sleep(random.uniform(3, 6))
            
            profile_data = {}
            profile_data['username'] = self.target_username
            
            # البيانات الأساسية
            try:
                display_name = self.driver.find_element(By.CSS_SELECTOR, "h2").text
                profile_data['display_name'] = display_name
                print(f"   ✅ الاسم: {display_name}")
            except:
                profile_data['display_name'] = "غير متاح"
            
            # البايو المفصل
            try:
                bio_element = self.driver.find_element(By.CSS_SELECTOR, "div.-vDIg span")
                bio_text = bio_element.text
                profile_data['bio'] = bio_text
                
                # استخراج الإيميل من البايو
                email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
                emails = re.findall(email_pattern, bio_text)
                profile_data['email'] = emails[0] if emails else None
                
                # استخراج رقم الهاتف
                phone_pattern = r'[\+]?[1-9]?[0-9]{7,15}'
                phones = re.findall(phone_pattern, bio_text)
                profile_data['phone'] = phones[0] if phones else None
                
                print(f"   ✅ البايو: {bio_text[:50]}...")
                if emails:
                    print(f"   📧 إيميل مستخرج: {emails[0]}")
                if phones:
                    print(f"   📱 هاتف مستخرج: {phones[0]}")
                    
            except:
                profile_data['bio'] = "غير متاح"
                profile_data['email'] = None
                profile_data['phone'] = None
            
            # الإحصائيات المتقدمة
            try:
                stats_elements = self.driver.find_elements(By.CSS_SELECTOR, "ul li div span")
                if len(stats_elements) >= 3:
                    profile_data['posts_count'] = self.extract_number_from_text(stats_elements[0].text)
                    profile_data['followers_count'] = self.extract_number_from_text(stats_elements[1].text)
                    profile_data['following_count'] = self.extract_number_from_text(stats_elements[2].text)
                    
                    print(f"   📸 المنشورات: {profile_data['posts_count']:,}")
                    print(f"   👥 المتابعين: {profile_data['followers_count']:,}")
                    print(f"   👤 المتابعة: {profile_data['following_count']:,}")
            except:
                profile_data['posts_count'] = 0
                profile_data['followers_count'] = 0
                profile_data['following_count'] = 0
            
            # التحقق من نوع الحساب
            try:
                business_element = self.driver.find_element(By.XPATH, "//*[contains(text(), 'Contact')]")
                profile_data['is_business'] = True
                
                # محاولة استخراج فئة الأعمال
                try:
                    category_element = self.driver.find_element(By.CSS_SELECTOR, "div.QGHVm")
                    profile_data['business_category'] = category_element.text
                    print(f"   🏢 حساب أعمال: {profile_data['business_category']}")
                except:
                    profile_data['business_category'] = "غير محدد"
                    
            except:
                profile_data['is_business'] = False
                profile_data['business_category'] = None
            
            # الرابط الخارجي
            try:
                external_link = self.driver.find_element(By.CSS_SELECTOR, "a[href*='http']")
                profile_data['external_url'] = external_link.get_attribute('href')
                print(f"   🔗 رابط خارجي: {profile_data['external_url']}")
            except:
                profile_data['external_url'] = None
            
            # صورة الملف الشخصي عالية الجودة
            try:
                profile_img = self.driver.find_element(By.CSS_SELECTOR, "img[alt*='profile picture']")
                img_src = profile_img.get_attribute('src')
                # محاولة الحصول على صورة عالية الجودة
                if 's150x150' in img_src:
                    img_src = img_src.replace('s150x150', 's320x320')
                profile_data['profile_image_url'] = img_src
                print("   🖼️ صورة الملف الشخصي: عالية الجودة")
            except:
                profile_data['profile_image_url'] = None
            
            # التحقق من التوثيق والخصوصية
            profile_data['is_verified'] = len(self.driver.find_elements(By.CSS_SELECTOR, "[title='Verified']")) > 0
            profile_data['is_private'] = len(self.driver.find_elements(By.XPATH, "//*[contains(text(), 'This account is private')]")) > 0
            
            self.scraped_data['basic_info'] = profile_data
            print("✅ تم استخراج البيانات الأساسية المتقدمة")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في استخراج البيانات الأساسية: {e}")
            return False

    def extract_posts_data(self, max_posts=20):
        """استخراج بيانات المنشورات المفصلة"""
        print(f"\n📸 استخراج بيانات المنشورات (حد أقصى {max_posts})...")
        
        if self.scraped_data['basic_info'].get('is_private'):
            print("🔒 الحساب خاص - لا يمكن استخراج المنشورات")
            return False
        
        try:
            # التمرير لتحميل المنشورات
            self.human_like_scroll()
            
            # البحث عن المنشورات
            posts_elements = self.driver.find_elements(By.CSS_SELECTOR, "article div div div a")
            
            if not posts_elements:
                print("❌ لم يتم العثور على منشورات")
                return False
            
            posts_data = []
            processed_posts = 0
            
            for i, post_element in enumerate(posts_elements[:max_posts]):
                if processed_posts >= max_posts:
                    break
                    
                try:
                    print(f"   📸 معالجة المنشور {i+1}/{min(len(posts_elements), max_posts)}")
                    
                    # النقر على المنشور
                    post_url = post_element.get_attribute('href')
                    self.driver.execute_script("arguments[0].click();", post_element)
                    time.sleep(random.uniform(2, 4))
                    
                    post_data = self.extract_single_post_data(post_url)
                    if post_data:
                        posts_data.append(post_data)
                        processed_posts += 1
                    
                    # إغلاق المنشور
                    try:
                        close_button = self.driver.find_element(By.CSS_SELECTOR, "button[aria-label='Close']")
                        close_button.click()
                        time.sleep(random.uniform(1, 2))
                    except:
                        # إذا لم يتم العثور على زر الإغلاق، العودة للصفحة الرئيسية
                        self.driver.get(self.target_url)
                        time.sleep(random.uniform(2, 3))
                    
                except Exception as e:
                    print(f"   ⚠️ خطأ في معالجة المنشور {i+1}: {e}")
                    continue
            
            self.scraped_data['posts_data'] = posts_data
            print(f"✅ تم استخراج {len(posts_data)} منشور")
            
            # تحليل أنماط النشر
            self.analyze_posting_patterns(posts_data)
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في استخراج المنشورات: {e}")
            return False

    def extract_single_post_data(self, post_url):
        """استخراج بيانات منشور واحد"""
        try:
            post_data = {
                'post_url': post_url,
                'post_id': post_url.split('/')[-2] if post_url else None
            }
            
            # نوع المنشور
            if len(self.driver.find_elements(By.CSS_SELECTOR, "video")) > 0:
                post_data['post_type'] = 'video'
            elif len(self.driver.find_elements(By.CSS_SELECTOR, "div[role='button'][aria-label='Next']")) > 0:
                post_data['post_type'] = 'carousel'
            else:
                post_data['post_type'] = 'image'
            
            # النص والتعليق
            try:
                caption_element = self.driver.find_element(By.CSS_SELECTOR, "div.C4VMK span")
                caption_text = caption_element.text
                post_data['caption'] = caption_text
                
                # استخراج الهاشتاغات
                hashtags = re.findall(r'#\w+', caption_text)
                post_data['hashtags'] = ', '.join(hashtags)
                
                # استخراج المنشنز
                mentions = re.findall(r'@\w+', caption_text)
                post_data['mentions'] = ', '.join(mentions)
                
            except:
                post_data['caption'] = ""
                post_data['hashtags'] = ""
                post_data['mentions'] = ""
            
            # عدد الإعجابات
            try:
                likes_element = self.driver.find_element(By.CSS_SELECTOR, "button span")
                likes_text = likes_element.text
                post_data['likes_count'] = self.extract_number_from_text(likes_text)
            except:
                post_data['likes_count'] = 0
            
            # عدد التعليقات
            try:
                comments_elements = self.driver.find_elements(By.CSS_SELECTOR, "ul div[role='button']")
                post_data['comments_count'] = len(comments_elements)
            except:
                post_data['comments_count'] = 0
            
            # الموقع
            try:
                location_element = self.driver.find_element(By.CSS_SELECTOR, "div.M30cS a")
                post_data['location'] = location_element.text
            except:
                post_data['location'] = None
            
            # تاريخ النشر
            try:
                time_element = self.driver.find_element(By.CSS_SELECTOR, "time")
                post_data['post_date'] = time_element.get_attribute('datetime')
            except:
                post_data['post_date'] = None
            
            # عينة من التعليقات
            try:
                comment_elements = self.driver.find_elements(By.CSS_SELECTOR, "ul div span")[:5]
                comments_sample = [elem.text for elem in comment_elements if elem.text]
                post_data['comments_sample'] = json.dumps(comments_sample, ensure_ascii=False)
            except:
                post_data['comments_sample'] = "[]"
            
            post_data['scraping_timestamp'] = datetime.now().isoformat()
            
            return post_data
            
        except Exception as e:
            print(f"   ⚠️ خطأ في استخراج بيانات المنشور: {e}")
            return None

    def extract_followers_sample(self, max_followers=50):
        """استخراج عينة من المتابعين"""
        print(f"\n👥 استخراج عينة من المتابعين (حد أقصى {max_followers})...")
        
        try:
            # النقر على رابط المتابعين
            followers_link = self.driver.find_element(By.CSS_SELECTOR, "a[href*='/followers/']")
            followers_link.click()
            time.sleep(random.uniform(3, 5))
            
            followers_data = []
            processed_followers = 0
            
            # التمرير لتحميل المزيد من المتابعين
            for scroll in range(5):  # 5 جولات تمرير
                if processed_followers >= max_followers:
                    break
                    
                # استخراج المتابعين المرئيين حالياً
                follower_elements = self.driver.find_elements(By.CSS_SELECTOR, "div._7UhW9 div")
                
                for element in follower_elements[processed_followers:]:
                    if processed_followers >= max_followers:
                        break
                        
                    try:
                        follower_data = self.extract_follower_data(element)
                        if follower_data:
                            followers_data.append(follower_data)
                            processed_followers += 1
                            
                    except Exception as e:
                        continue
                
                # التمرير لتحميل المزيد
                self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", 
                                         self.driver.find_element(By.CSS_SELECTOR, "div._7UhW9"))
                time.sleep(random.uniform(1, 2))
            
            self.scraped_data['followers_sample'] = followers_data
            print(f"✅ تم استخراج {len(followers_data)} متابع")
            
            # إغلاق نافذة المتابعين
            try:
                close_button = self.driver.find_element(By.CSS_SELECTOR, "button[aria-label='Close']")
                close_button.click()
                time.sleep(1)
            except:
                pass
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في استخراج المتابعين: {e}")
            return False

    def extract_follower_data(self, element):
        """استخراج بيانات متابع واحد"""
        try:
            follower_data = {}
            
            # اسم المستخدم
            username_element = element.find_element(By.CSS_SELECTOR, "a")
            follower_data['user_username'] = username_element.get_attribute('href').split('/')[-2]
            
            # الاسم المعروض
            try:
                display_name_element = element.find_element(By.CSS_SELECTOR, "div div")
                follower_data['user_display_name'] = display_name_element.text
            except:
                follower_data['user_display_name'] = follower_data['user_username']
            
            # صورة الملف الشخصي
            try:
                img_element = element.find_element(By.CSS_SELECTOR, "img")
                follower_data['user_profile_image'] = img_element.get_attribute('src')
            except:
                follower_data['user_profile_image'] = None
            
            # التحقق من التوثيق
            follower_data['user_is_verified'] = len(element.find_elements(By.CSS_SELECTOR, "[title='Verified']")) > 0
            
            follower_data['relationship_type'] = 'follower'
            follower_data['scraping_timestamp'] = datetime.now().isoformat()
            
            return follower_data
            
        except Exception as e:
            return None

    def analyze_posting_patterns(self, posts_data):
        """تحليل أنماط النشر"""
        print("\n📈 تحليل أنماط النشر...")
        
        try:
            if not posts_data:
                return
            
            # تحليل التوقيتات
            post_times = []
            hashtags_all = []
            locations = []
            
            for post in posts_data:
                if post.get('post_date'):
                    post_times.append(post['post_date'])
                
                if post.get('hashtags'):
                    hashtags_all.extend(post['hashtags'].split(', '))
                
                if post.get('location'):
                    locations.append(post['location'])
            
            # أكثر الهاشتاغات استخداماً
            hashtag_counts = {}
            for hashtag in hashtags_all:
                hashtag_counts[hashtag] = hashtag_counts.get(hashtag, 0) + 1
            
            top_hashtags = sorted(hashtag_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            
            # حساب معدل التفاعل
            total_likes = sum(post.get('likes_count', 0) for post in posts_data)
            total_comments = sum(post.get('comments_count', 0) for post in posts_data)
            avg_likes = total_likes / len(posts_data) if posts_data else 0
            avg_comments = total_comments / len(posts_data) if posts_data else 0
            
            followers_count = self.scraped_data['basic_info'].get('followers_count', 1)
            engagement_rate = ((avg_likes + avg_comments) / followers_count) * 100 if followers_count > 0 else 0
            
            analysis = {
                'total_posts_analyzed': len(posts_data),
                'avg_likes': avg_likes,
                'avg_comments': avg_comments,
                'engagement_rate': engagement_rate,
                'top_hashtags': top_hashtags,
                'unique_locations': list(set(locations)),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
            self.scraped_data['engagement_analysis'] = analysis
            
            print(f"   📊 متوسط الإعجابات: {avg_likes:.1f}")
            print(f"   💬 متوسط التعليقات: {avg_comments:.1f}")
            print(f"   📈 معدل التفاعل: {engagement_rate:.2f}%")
            print(f"   🏷️ أهم الهاشتاغات: {[h[0] for h in top_hashtags[:3]]}")
            
        except Exception as e:
            print(f"❌ خطأ في تحليل أنماط النشر: {e}")

    def extract_number_from_text(self, text):
        """استخراج الأرقام من النص"""
        try:
            text = str(text).replace(',', '').replace(' ', '').lower()
            if 'k' in text:
                return int(float(text.replace('k', '')) * 1000)
            elif 'm' in text:
                return int(float(text.replace('m', '')) * 1000000)
            else:
                return int(re.sub(r'[^\d]', '', text))
        except:
            return 0

    def save_advanced_data(self):
        """حفظ البيانات المتقدمة في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            # حفظ البيانات الأساسية
            basic_info = self.scraped_data['basic_info']
            engagement = self.scraped_data.get('engagement_analysis', {})
            
            cursor.execute('''
                INSERT INTO advanced_profiles (
                    username, display_name, bio, followers_count, following_count,
                    posts_count, profile_image_url, is_verified, is_private,
                    is_business, business_category, external_url, email, phone,
                    avg_likes, avg_comments, engagement_rate, most_used_hashtags,
                    scraping_timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                basic_info.get('username'),
                basic_info.get('display_name'),
                basic_info.get('bio'),
                basic_info.get('followers_count', 0),
                basic_info.get('following_count', 0),
                basic_info.get('posts_count', 0),
                basic_info.get('profile_image_url'),
                basic_info.get('is_verified', False),
                basic_info.get('is_private', False),
                basic_info.get('is_business', False),
                basic_info.get('business_category'),
                basic_info.get('external_url'),
                basic_info.get('email'),
                basic_info.get('phone'),
                engagement.get('avg_likes', 0),
                engagement.get('avg_comments', 0),
                engagement.get('engagement_rate', 0),
                json.dumps(engagement.get('top_hashtags', []), ensure_ascii=False),
                datetime.now().isoformat()
            ))
            
            # حفظ بيانات المنشورات
            for post in self.scraped_data['posts_data']:
                cursor.execute('''
                    INSERT INTO detailed_posts (
                        username, post_id, post_url, post_type, caption,
                        hashtags, mentions, likes_count, comments_count,
                        post_date, location, comments_sample, scraping_timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    self.target_username,
                    post.get('post_id'),
                    post.get('post_url'),
                    post.get('post_type'),
                    post.get('caption'),
                    post.get('hashtags'),
                    post.get('mentions'),
                    post.get('likes_count', 0),
                    post.get('comments_count', 0),
                    post.get('post_date'),
                    post.get('location'),
                    post.get('comments_sample'),
                    post.get('scraping_timestamp')
                ))
            
            # حفظ بيانات المتابعين
            for follower in self.scraped_data['followers_sample']:
                cursor.execute('''
                    INSERT INTO followers_following (
                        target_username, user_username, user_display_name,
                        user_profile_image, user_is_verified, relationship_type,
                        scraping_timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    self.target_username,
                    follower.get('user_username'),
                    follower.get('user_display_name'),
                    follower.get('user_profile_image'),
                    follower.get('user_is_verified', False),
                    follower.get('relationship_type'),
                    follower.get('scraping_timestamp')
                ))
            
            conn.commit()
            conn.close()
            
            print("💾 تم حفظ جميع البيانات المتقدمة في قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")

    def display_advanced_results(self):
        """عرض النتائج المتقدمة"""
        print("\n" + "="*60)
        print("📊 تقرير البيانات المتقدمة المستخرجة")
        print("="*60)
        
        # البيانات الأساسية
        basic = self.scraped_data['basic_info']
        print(f"\n👤 معلومات الحساب:")
        print(f"   📝 اسم المستخدم: @{basic.get('username')}")
        print(f"   🏷️ الاسم المعروض: {basic.get('display_name')}")
        print(f"   📄 البايو: {basic.get('bio', 'غير متاح')[:100]}...")
        print(f"   👥 المتابعين: {basic.get('followers_count', 0):,}")
        print(f"   👤 المتابعة: {basic.get('following_count', 0):,}")
        print(f"   📸 المنشورات: {basic.get('posts_count', 0):,}")
        print(f"   ✅ موثق: {'نعم' if basic.get('is_verified') else 'لا'}")
        print(f"   🔒 خاص: {'نعم' if basic.get('is_private') else 'لا'}")
        print(f"   🏢 حساب أعمال: {'نعم' if basic.get('is_business') else 'لا'}")
        
        if basic.get('email'):
            print(f"   📧 إيميل: {basic.get('email')}")
        if basic.get('phone'):
            print(f"   📱 هاتف: {basic.get('phone')}")
        if basic.get('external_url'):
            print(f"   🔗 رابط خارجي: {basic.get('external_url')}")
        
        # تحليل التفاعل
        engagement = self.scraped_data.get('engagement_analysis', {})
        if engagement:
            print(f"\n📈 تحليل التفاعل:")
            print(f"   📊 متوسط الإعجابات: {engagement.get('avg_likes', 0):.1f}")
            print(f"   💬 متوسط التعليقات: {engagement.get('avg_comments', 0):.1f}")
            print(f"   📈 معدل التفاعل: {engagement.get('engagement_rate', 0):.2f}%")
        
        # المنشورات
        posts_count = len(self.scraped_data['posts_data'])
        print(f"\n📸 المنشورات المستخرجة: {posts_count}")
        
        # المتابعين
        followers_count = len(self.scraped_data['followers_sample'])
        print(f"👥 عينة المتابعين: {followers_count}")
        
        print("\n✅ تم إكمال استخراج البيانات المتقدمة بنجاح!")

    def cleanup(self):
        """تنظيف الموارد"""
        if self.driver:
            self.driver.quit()

    def run_advanced_scraping(self):
        """تشغيل السكرابينغ المتقدم"""
        print("🚀 بدء السكرابينغ المتقدم...")
        
        try:
            if not self.setup_advanced_driver():
                return False
            
            # استخراج البيانات الأساسية المتقدمة
            if not self.extract_advanced_profile_data():
                return False
            
            # استخراج المنشورات (إذا لم يكن الحساب خاص)
            if not self.scraped_data['basic_info'].get('is_private'):
                self.extract_posts_data(max_posts=10)
                
                # استخراج عينة من المتابعين
                if self.scraped_data['basic_info'].get('followers_count', 0) > 0:
                    self.extract_followers_sample(max_followers=20)
            
            # حفظ البيانات
            self.save_advanced_data()
            
            # عرض النتائج
            self.display_advanced_results()
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في السكرابينغ المتقدم: {e}")
            return False
        finally:
            self.cleanup()

if __name__ == "__main__":
    target_username = "mhamd6220"  # يمكن تغييره
    
    print("🕷️ Advanced Instagram Scraper")
    print("⚠️ تحذير: للأغراض التعليمية فقط!")
    
    scraper = AdvancedInstagramScraper(target_username)
    scraper.run_advanced_scraping()
