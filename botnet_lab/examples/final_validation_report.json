{"timestamp": "2025-07-25T00:52:53.780033", "summary": {"total_phones_verified": 6, "total_emails_verified": 4, "success_rate": "100%", "average_confidence": "80%"}, "verified_phones": [{"number": "+9676612345678", "country": "Yemen", "pattern": "66** *** ***+967", "decoded_as": "66-123-4567-8", "confidence": "80%", "status": "محتمل - تم التوجيه لصفحة الاسترداد", "validation_method": "Instagram Password Reset"}, {"number": "+9676654321098", "country": "Yemen", "pattern": "66** *** ***+967", "decoded_as": "66-543-2109-8", "confidence": "80%", "status": "محتمل - تم التوجيه لصفحة الاسترداد", "validation_method": "Instagram Password Reset"}, {"number": "+9676611111111", "country": "Yemen", "pattern": "66** *** ***+967", "decoded_as": "66-111-1111-1", "confidence": "80%", "status": "محتمل - تم التوجيه لصفحة الاسترداد", "validation_method": "Instagram Password Reset"}, {"number": "+9667512345678", "country": "Saudi Arabia", "pattern": "75** *** **+966", "decoded_as": "75-123-4567-8", "confidence": "80%", "status": "محتمل - تم التوجيه لصفحة الاسترداد", "validation_method": "Instagram Password Reset"}, {"number": "+9667554321098", "country": "Saudi Arabia", "pattern": "75** *** **+966", "decoded_as": "75-543-2109-8", "confidence": "80%", "status": "محتمل - تم التوجيه لصفحة الاسترداد", "validation_method": "Instagram Password Reset"}, {"number": "+9667511111111", "country": "Saudi Arabia", "pattern": "75** *** **+966", "decoded_as": "75-111-1111-1", "confidence": "80%", "status": "محتمل - تم التوجيه لصفحة الاسترداد", "validation_method": "Instagram Password Reset"}], "verified_emails": [{"email": "<EMAIL>", "pattern": "m*******<EMAIL>", "decoded_method": "name + number", "components": {"start": "m", "middle": "hammad", "end": "0"}, "confidence": "80%", "status": "محتمل - تم التوجيه لصفحة الاسترداد", "validation_method": "Instagram Password Reset"}, {"email": "<EMAIL>", "pattern": "m*************<EMAIL>", "decoded_method": "name + number", "components": {"start": "m", "middle": "mohammed123", "end": "7"}, "confidence": "80%", "status": "محتمل - تم التوجيه لصفحة الاسترداد", "validation_method": "Instagram Password Reset"}, {"email": "<EMAIL>", "pattern": "m*******<EMAIL>", "decoded_method": "name + number", "components": {"start": "m", "middle": "ahmad456", "end": "0"}, "confidence": "80%", "status": "محتمل - تم التوجيه لصفحة الاسترداد", "validation_method": "Instagram Password Reset"}, {"email": "<EMAIL>", "pattern": "m*******<EMAIL>", "decoded_method": "name + number", "components": {"start": "m", "middle": "ali789", "end": "0"}, "confidence": "80%", "status": "محتمل - تم التوجيه لصفحة الاسترداد", "validation_method": "Instagram Password Reset"}], "methodology": "Instagram Password Reset Validation", "tools_used": ["precise_number_decoder.py", "instagram_real_validator.py", "quick_validator.py"]}