#!/usr/bin/env python3
"""
Instagram Recovery Validator - أداة التحقق من استرداد Instagram
Advanced validation tool for Instagram account recovery patterns

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import json
import sqlite3
import re
import requests
import itertools
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class InstagramRecoveryValidator:
    def __init__(self):
        self.validated_results = {
            'confirmed_emails': [],
            'confirmed_phones': [],
            'failed_attempts': [],
            'validation_metadata': {}
        }

        # إعداد المتصفح
        self.setup_browser()

        # قاعدة البيانات للنتائج
        self.db_name = "validation_results.db"
        self.init_database()

        print("🔍 Instagram Recovery Validator")
        print("=" * 70)
        print("🎯 أداة التحقق المتقدمة من أنماط استرداد Instagram")
        print("✅ التحقق الفعلي من الإيميلات والأرقام")
        print("=" * 70)

    def setup_browser(self):
        """إعداد المتصفح للتحقق"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # تشغيل خفي
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ تم إعداد المتصفح بنجاح")

        except Exception as e:
            print(f"⚠️ خطأ في إعداد المتصفح: {e}")
            self.driver = None

    def init_database(self):
        """إنشاء قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS validation_results (
                    id INTEGER PRIMARY KEY,
                    validation_type TEXT,
                    input_value TEXT,
                    validation_status TEXT,
                    recovery_options TEXT,
                    confidence_score REAL,
                    timestamp TEXT,
                    metadata TEXT
                )
            ''')

            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات")

        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def validate_instagram_recovery(self, username_or_email):
        """التحقق من صفحة استرداد Instagram"""
        print(f"\n🔍 التحقق من: {username_or_email}")

        if not self.driver:
            print("❌ المتصفح غير متاح")
            return None

        try:
            # الانتقال لصفحة استرداد Instagram
            recovery_url = "https://www.instagram.com/accounts/password/reset/"
            self.driver.get(recovery_url)

            # انتظار تحميل الصفحة
            wait = WebDriverWait(self.driver, 10)

            # البحث عن حقل الإدخال
            input_field = wait.until(
                EC.presence_of_element_located((By.NAME, "cppEmailOrUsername"))
            )

            # إدخال اسم المستخدم أو الإيميل
            input_field.clear()
            input_field.send_keys(username_or_email)

            # النقر على زر "إرسال رابط تسجيل الدخول"
            submit_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            submit_button.click()

            # انتظار النتيجة
            time.sleep(3)

            # تحليل النتيجة
            recovery_options = self.extract_recovery_options()

            return recovery_options

        except TimeoutException:
            print("⏰ انتهت مهلة الانتظار")
            return None
        except Exception as e:
            print(f"❌ خطأ في التحقق: {e}")
            return None

    def extract_recovery_options(self):
        """استخراج خيارات الاسترداد من الصفحة"""
        recovery_options = {
            'emails': [],
            'phones': [],
            'status': 'unknown'
        }

        try:
            # البحث عن خيارات الاسترداد
            page_source = self.driver.page_source

            # البحث عن أنماط الإيميلات المشفرة
            email_patterns = re.findall(r'm\*+\d@gmail\.com', page_source)
            recovery_options['emails'] = email_patterns

            # البحث عن أنماط الأرقام المشفرة
            phone_patterns = re.findall(r'\d{2}\*+ \*+ \*+\+\d{3}', page_source)
            recovery_options['phones'] = phone_patterns

            # تحديد حالة الحساب
            if email_patterns or phone_patterns:
                recovery_options['status'] = 'found'
                print(f"✅ تم العثور على خيارات الاسترداد")
                print(f"   📧 إيميلات: {len(email_patterns)}")
                print(f"   📱 أرقام: {len(phone_patterns)}")
            else:
                recovery_options['status'] = 'not_found'
                print("❌ لم يتم العثور على خيارات استرداد")

        except Exception as e:
            print(f"⚠️ خطأ في استخراج البيانات: {e}")
            recovery_options['status'] = 'error'

        return recovery_options

    def validate_pattern_matches(self, expected_patterns, found_patterns):
        """التحقق من تطابق الأنماط"""
        print("\n🔍 التحقق من تطابق الأنماط...")

        matches = {
            'exact_matches': [],
            'partial_matches': [],
            'no_matches': []
        }

        for expected in expected_patterns:
            found_match = False
            for found in found_patterns:
                # تحقق دقيق
                if expected == found:
                    matches['exact_matches'].append(expected)
                    found_match = True
                    break
                # تحقق جزئي
                elif self.is_partial_match(expected, found):
                    matches['partial_matches'].append({'expected': expected, 'found': found})
                    found_match = True
                    break

            if not found_match:
                matches['no_matches'].append(expected)

        return matches

    def is_partial_match(self, pattern1, pattern2):
        """التحقق من التطابق الجزئي"""
        # إزالة الرموز المشفرة للمقارنة
        clean1 = re.sub(r'\*+', '', pattern1)
        clean2 = re.sub(r'\*+', '', pattern2)

        # مقارنة الأجزاء المرئية
        return clean1 in clean2 or clean2 in clean1

    def comprehensive_validation_test(self):
        """اختبار شامل للتحقق"""
        print("\n🚀 بدء الاختبار الشامل...")

        # تحليل دقيق بناءً على الصورة المرسلة
        print("📸 تحليل الصورة المرجعية:")
        print("   📧 إيميلات مكتشفة: 2")
        print("   📱 أرقام مكتشفة: 3")
        print("   🌍 دول: اليمن (+967)، السعودية (+966)")

        # قائمة أسماء المستخدمين المحتملة للاختبار
        test_usernames = [
            'mhamd6220',
            'mohammed6220',
            'ahmad6220',
            'mhamd1230',
            'mali2020'
        ]

        # الأنماط المتوقعة من الصورة (تحليل دقيق)
        expected_patterns = {
            'emails': [
                'm*******<EMAIL>',      # 9 أحرف إجمالي (m + 7 مخفية + 0)
                'm*************<EMAIL>' # 15 حرف إجمالي (m + 13 مخفية + 7)
            ],
            'phones': [
                '66** *** **+967',  # يمني يبدأ بـ 66
                '75** *** **+966',  # سعودي يبدأ بـ 75
                '*** **+966 43**'   # سعودي ينتهي بـ 43
            ]
        }

        validation_results = []

        for username in test_usernames:
            print(f"\n📝 اختبار: {username}")

            # التحقق من Instagram
            recovery_options = self.validate_instagram_recovery(username)

            if recovery_options:
                # التحقق من تطابق الإيميلات
                email_matches = self.validate_pattern_matches(
                    expected_patterns['emails'],
                    recovery_options['emails']
                )

                # التحقق من تطابق الأرقام
                phone_matches = self.validate_pattern_matches(
                    expected_patterns['phones'],
                    recovery_options['phones']
                )

                # حساب نسبة التطابق
                match_score = self.calculate_match_score(email_matches, phone_matches)

                result = {
                    'username': username,
                    'recovery_options': recovery_options,
                    'email_matches': email_matches,
                    'phone_matches': phone_matches,
                    'match_score': match_score,
                    'timestamp': datetime.now().isoformat()
                }

                validation_results.append(result)

                # حفظ النتيجة
                self.save_validation_result(result)

                print(f"   📊 نسبة التطابق: {match_score:.1%}")

            else:
                print(f"   ❌ فشل في التحقق من {username}")

            # توقف قصير لتجنب الحظر
            time.sleep(random.uniform(2, 5))

        return validation_results

    def calculate_match_score(self, email_matches, phone_matches):
        """حساب نسبة التطابق"""
        total_expected = 5  # 2 إيميل + 3 أرقام

        exact_matches = len(email_matches['exact_matches']) + len(phone_matches['exact_matches'])
        partial_matches = len(email_matches['partial_matches']) + len(phone_matches['partial_matches'])

        # نسبة التطابق (التطابق الدقيق = 100%، الجزئي = 50%)
        score = (exact_matches + partial_matches * 0.5) / total_expected

        return min(score, 1.0)

    def save_validation_result(self, result):
        """حفظ نتيجة التحقق"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO validation_results (
                    validation_type, input_value, validation_status,
                    recovery_options, confidence_score, timestamp, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                'instagram_recovery',
                result['username'],
                result['recovery_options']['status'],
                json.dumps(result['recovery_options'], ensure_ascii=False),
                result['match_score'],
                result['timestamp'],
                json.dumps(result, ensure_ascii=False)
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"⚠️ خطأ في حفظ النتيجة: {e}")

    def advanced_pattern_analysis(self, validation_results):
        """تحليل متقدم للأنماط المكتشفة"""
        print("\n🧠 تحليل متقدم للنتائج...")

        # تجميع جميع الأنماط المكتشفة
        all_emails = []
        all_phones = []

        for result in validation_results:
            if result['recovery_options']['status'] == 'found':
                all_emails.extend(result['recovery_options']['emails'])
                all_phones.extend(result['recovery_options']['phones'])

        # تحليل أنماط الإيميلات
        email_analysis = self.analyze_email_patterns(all_emails)

        # تحليل أنماط الأرقام
        phone_analysis = self.analyze_phone_patterns(all_phones)

        return {
            'email_analysis': email_analysis,
            'phone_analysis': phone_analysis,
            'total_validation_attempts': len(validation_results),
            'successful_validations': len([r for r in validation_results if r['recovery_options']['status'] == 'found'])
        }

    def analyze_email_patterns(self, emails):
        """تحليل أنماط الإيميلات"""
        patterns = {}

        for email in emails:
            # استخراج النمط
            pattern = re.sub(r'[a-zA-Z0-9]', '*', email)
            if pattern in patterns:
                patterns[pattern] += 1
            else:
                patterns[pattern] = 1

        return patterns

    def analyze_phone_patterns(self, phones):
        """تحليل أنماط الأرقام"""
        patterns = {}

        for phone in phones:
            # استخراج النمط
            pattern = re.sub(r'\d', '*', phone)
            if pattern in patterns:
                patterns[pattern] += 1
            else:
                patterns[pattern] = 1

        return patterns

    def display_validation_report(self, validation_results, analysis):
        """عرض تقرير التحقق"""
        print("\n" + "="*70)
        print("📊 تقرير التحقق والاختبار النهائي")
        print("="*70)

        print(f"\n📅 تاريخ التحقق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔍 إجمالي محاولات التحقق: {analysis['total_validation_attempts']}")
        print(f"✅ التحققات الناجحة: {analysis['successful_validations']}")

        # أفضل النتائج
        best_results = sorted(validation_results, key=lambda x: x['match_score'], reverse=True)

        print(f"\n🏆 أفضل النتائج:")
        for i, result in enumerate(best_results[:3], 1):
            print(f"   {i}. {result['username']} - تطابق: {result['match_score']:.1%}")
            if result['recovery_options']['emails']:
                print(f"      📧 إيميلات: {result['recovery_options']['emails']}")
            if result['recovery_options']['phones']:
                print(f"      📱 أرقام: {result['recovery_options']['phones']}")

        # تحليل الأنماط
        print(f"\n📧 أنماط الإيميلات المكتشفة:")
        for pattern, count in analysis['email_analysis'].items():
            print(f"   {pattern} (تكرار: {count})")

        print(f"\n📱 أنماط الأرقام المكتشفة:")
        for pattern, count in analysis['phone_analysis'].items():
            print(f"   {pattern} (تكرار: {count})")

        print("\n" + "="*70)

    def run_validation(self):
        """تشغيل التحقق الكامل"""
        print("🚀 بدء عملية التحقق والاختبار...")

        try:
            # تشغيل الاختبار الشامل
            validation_results = self.comprehensive_validation_test()

            # تحليل النتائج
            analysis = self.advanced_pattern_analysis(validation_results)

            # عرض التقرير
            self.display_validation_report(validation_results, analysis)

            return validation_results

        except Exception as e:
            print(f"❌ خطأ في التحقق: {e}")
            return None

        finally:
            # إغلاق المتصفح
            if self.driver:
                self.driver.quit()
                print("🔒 تم إغلاق المتصفح")

if __name__ == "__main__":
    print("🔍 Instagram Recovery Validator")
    print("⚠️ تحذير: للأغراض التعليمية والبحثية فقط!")
    print("✅ أداة التحقق المتقدمة من أنماط استرداد Instagram")

    validator = InstagramRecoveryValidator()
    results = validator.run_validation()

    if results:
        print("\n✅ تم التحقق بنجاح!")
        print("📊 النتائج محفوظة في قاعدة البيانات")
    else:
        print("\n❌ فشل في عملية التحقق")
