#!/usr/bin/env python3
"""
Instagram Scraper Demo - تجربة سكرابينغ Instagram
Demo for scraping Instagram profile @mhamd6220

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import json
import sqlite3
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("❌ Selenium غير متاح. تثبيت: pip install selenium")

try:
    import requests
    from bs4 import BeautifulSoup
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("❌ Requests/BeautifulSoup غير متاح. تثبيت: pip install requests beautifulsoup4")

try:
    from fake_useragent import UserAgent
    FAKE_UA_AVAILABLE = True
except ImportError:
    FAKE_UA_AVAILABLE = False
    print("⚠️ fake-useragent غير متاح. سيتم استخدام user agent ثابت")

class InstagramScraperDemo:
    def __init__(self):
        self.target_username = "mhamd6220"
        self.target_url = f"https://www.instagram.com/{self.target_username}/"
        self.scraped_data = {}
        self.driver = None
        self.session = None
        
        # إعداد قاعدة البيانات
        self.db_name = "instagram_scraping_demo.db"
        self.init_database()
        
        print("🕷️ Instagram Scraper Demo - تجربة سكرابينغ Instagram")
        print("=" * 60)
        print(f"🎯 الهدف: @{self.target_username}")
        print(f"🌐 الرابط: {self.target_url}")
        print("⚠️ تحذير: للأغراض التعليمية فقط!")
        print("=" * 60)

    def init_database(self):
        """إنشاء قاعدة بيانات لحفظ النتائج"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS instagram_profiles (
                    id INTEGER PRIMARY KEY,
                    username TEXT,
                    display_name TEXT,
                    bio TEXT,
                    followers_count INTEGER,
                    following_count INTEGER,
                    posts_count INTEGER,
                    profile_image_url TEXT,
                    is_verified BOOLEAN,
                    is_private BOOLEAN,
                    external_url TEXT,
                    scraping_method TEXT,
                    scraping_timestamp TEXT,
                    success BOOLEAN,
                    error_message TEXT
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS instagram_posts (
                    id INTEGER PRIMARY KEY,
                    username TEXT,
                    post_url TEXT,
                    image_url TEXT,
                    caption TEXT,
                    likes_count INTEGER,
                    comments_count INTEGER,
                    post_date TEXT,
                    scraping_timestamp TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def setup_selenium_driver(self):
        """إعداد Selenium WebDriver مع تقنيات Anti-Detection"""
        if not SELENIUM_AVAILABLE:
            print("❌ Selenium غير متاح")
            return False
            
        try:
            print("🔧 إعداد Selenium WebDriver...")
            
            # إعدادات Chrome للتخفي
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')  # لتوفير عرض النطاق
            
            # User Agent عشوائي
            if FAKE_UA_AVAILABLE:
                ua = UserAgent()
                user_agent = ua.random
            else:
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            
            chrome_options.add_argument(f'--user-agent={user_agent}')
            
            print(f"   🎭 User Agent: {user_agent[:50]}...")
            
            # إنشاء WebDriver
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # إزالة خصائص الأتمتة
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ تم إعداد Selenium WebDriver بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إعداد Selenium: {e}")
            return False

    def setup_requests_session(self):
        """إعداد Requests Session مع Headers مخفية"""
        if not REQUESTS_AVAILABLE:
            print("❌ Requests غير متاح")
            return False
            
        try:
            print("🔧 إعداد Requests Session...")
            
            self.session = requests.Session()
            
            # Headers للتخفي
            if FAKE_UA_AVAILABLE:
                ua = UserAgent()
                user_agent = ua.random
            else:
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            
            headers = {
                'User-Agent': user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0'
            }
            
            self.session.headers.update(headers)
            
            print(f"   🎭 User Agent: {user_agent[:50]}...")
            print("✅ تم إعداد Requests Session بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إعداد Requests: {e}")
            return False

    def scrape_with_selenium(self):
        """سكرابينغ باستخدام Selenium"""
        print("\n🕷️ بدء السكرابينغ باستخدام Selenium...")
        print("-" * 40)
        
        try:
            # الانتقال للصفحة
            print(f"🌐 الانتقال إلى: {self.target_url}")
            self.driver.get(self.target_url)
            
            # تأخير عشوائي لمحاكاة السلوك البشري
            delay = random.uniform(3, 7)
            print(f"⏱️ تأخير بشري: {delay:.1f} ثانية")
            time.sleep(delay)
            
            # محاولة استخراج البيانات
            profile_data = {}
            
            # اسم المستخدم
            profile_data['username'] = self.target_username
            
            # الاسم المعروض
            try:
                display_name_element = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "h2"))
                )
                profile_data['display_name'] = display_name_element.text
                print(f"   ✅ الاسم المعروض: {profile_data['display_name']}")
            except TimeoutException:
                profile_data['display_name'] = "غير متاح"
                print("   ⚠️ لم يتم العثور على الاسم المعروض")
            
            # البايو
            try:
                bio_elements = self.driver.find_elements(By.CSS_SELECTOR, "div.-vDIg span")
                if bio_elements:
                    profile_data['bio'] = bio_elements[0].text
                    print(f"   ✅ البايو: {profile_data['bio'][:50]}...")
                else:
                    profile_data['bio'] = "غير متاح"
            except:
                profile_data['bio'] = "غير متاح"
                print("   ⚠️ لم يتم العثور على البايو")
            
            # الإحصائيات (المنشورات، المتابعين، المتابعة)
            try:
                stats_elements = self.driver.find_elements(By.CSS_SELECTOR, "ul li div span")
                if len(stats_elements) >= 3:
                    profile_data['posts_count'] = self.extract_number_from_text(stats_elements[0].text)
                    profile_data['followers_count'] = self.extract_number_from_text(stats_elements[1].text)
                    profile_data['following_count'] = self.extract_number_from_text(stats_elements[2].text)
                    
                    print(f"   ✅ المنشورات: {profile_data['posts_count']:,}")
                    print(f"   ✅ المتابعين: {profile_data['followers_count']:,}")
                    print(f"   ✅ المتابعة: {profile_data['following_count']:,}")
                else:
                    profile_data['posts_count'] = 0
                    profile_data['followers_count'] = 0
                    profile_data['following_count'] = 0
            except:
                profile_data['posts_count'] = 0
                profile_data['followers_count'] = 0
                profile_data['following_count'] = 0
                print("   ⚠️ لم يتم العثور على الإحصائيات")
            
            # صورة الملف الشخصي
            try:
                profile_img = self.driver.find_element(By.CSS_SELECTOR, "img[alt*='profile picture']")
                profile_data['profile_image_url'] = profile_img.get_attribute('src')
                print("   ✅ تم العثور على صورة الملف الشخصي")
            except:
                profile_data['profile_image_url'] = "غير متاح"
                print("   ⚠️ لم يتم العثور على صورة الملف الشخصي")
            
            # التحقق من الحساب الموثق
            try:
                verified_element = self.driver.find_element(By.CSS_SELECTOR, "[title='Verified']")
                profile_data['is_verified'] = True
                print("   ✅ حساب موثق")
            except:
                profile_data['is_verified'] = False
                print("   ➖ حساب غير موثق")
            
            # التحقق من الحساب الخاص
            try:
                private_element = self.driver.find_element(By.XPATH, "//*[contains(text(), 'This account is private')]")
                profile_data['is_private'] = True
                print("   🔒 حساب خاص")
            except:
                profile_data['is_private'] = False
                print("   🌐 حساب عام")
            
            # إضافة معلومات السكرابينغ
            profile_data['scraping_method'] = 'selenium'
            profile_data['scraping_timestamp'] = datetime.now().isoformat()
            profile_data['success'] = True
            profile_data['error_message'] = None
            
            self.scraped_data = profile_data
            
            print("\n✅ تم السكرابينغ بنجاح باستخدام Selenium!")
            return True
            
        except Exception as e:
            print(f"\n❌ خطأ في السكرابينغ باستخدام Selenium: {e}")
            
            # حفظ معلومات الخطأ
            error_data = {
                'username': self.target_username,
                'scraping_method': 'selenium',
                'scraping_timestamp': datetime.now().isoformat(),
                'success': False,
                'error_message': str(e)
            }
            self.scraped_data = error_data
            return False

    def scrape_with_requests(self):
        """سكرابينغ باستخدام Requests (محدود بسبب JavaScript)"""
        print("\n🌐 بدء السكرابينغ باستخدام Requests...")
        print("-" * 40)
        
        try:
            # طلب الصفحة
            print(f"📡 طلب الصفحة: {self.target_url}")
            response = self.session.get(self.target_url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ تم الحصول على الصفحة (Status: {response.status_code})")
                
                # تحليل HTML
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # محاولة استخراج البيانات من meta tags
                profile_data = {}
                profile_data['username'] = self.target_username
                
                # البحث عن meta tags
                og_title = soup.find('meta', property='og:title')
                if og_title:
                    profile_data['display_name'] = og_title.get('content', '').split('(')[0].strip()
                    print(f"   ✅ الاسم من meta: {profile_data['display_name']}")
                else:
                    profile_data['display_name'] = "غير متاح"
                
                og_description = soup.find('meta', property='og:description')
                if og_description:
                    description = og_description.get('content', '')
                    # محاولة استخراج الإحصائيات من الوصف
                    import re
                    numbers = re.findall(r'([\d,]+)', description)
                    if len(numbers) >= 3:
                        profile_data['followers_count'] = self.extract_number_from_text(numbers[0])
                        profile_data['following_count'] = self.extract_number_from_text(numbers[1])
                        profile_data['posts_count'] = self.extract_number_from_text(numbers[2])
                    print(f"   ✅ الوصف: {description[:50]}...")
                else:
                    profile_data['followers_count'] = 0
                    profile_data['following_count'] = 0
                    profile_data['posts_count'] = 0
                
                og_image = soup.find('meta', property='og:image')
                if og_image:
                    profile_data['profile_image_url'] = og_image.get('content', '')
                    print("   ✅ تم العثور على صورة الملف الشخصي")
                else:
                    profile_data['profile_image_url'] = "غير متاح"
                
                # معلومات افتراضية (محدودة مع Requests)
                profile_data['bio'] = "غير متاح (يتطلب JavaScript)"
                profile_data['is_verified'] = False
                profile_data['is_private'] = False
                profile_data['external_url'] = "غير متاح"
                
                # إضافة معلومات السكرابينغ
                profile_data['scraping_method'] = 'requests'
                profile_data['scraping_timestamp'] = datetime.now().isoformat()
                profile_data['success'] = True
                profile_data['error_message'] = None
                
                self.scraped_data = profile_data
                
                print("\n✅ تم السكرابينغ بنجاح باستخدام Requests!")
                print("⚠️ ملاحظة: البيانات محدودة بسبب اعتماد Instagram على JavaScript")
                return True
                
            else:
                print(f"❌ فشل في الحصول على الصفحة (Status: {response.status_code})")
                return False
                
        except Exception as e:
            print(f"\n❌ خطأ في السكرابينغ باستخدام Requests: {e}")
            
            # حفظ معلومات الخطأ
            error_data = {
                'username': self.target_username,
                'scraping_method': 'requests',
                'scraping_timestamp': datetime.now().isoformat(),
                'success': False,
                'error_message': str(e)
            }
            self.scraped_data = error_data
            return False

    def extract_number_from_text(self, text):
        """استخراج الأرقام من النص (يتعامل مع K, M)"""
        try:
            text = str(text).replace(',', '').replace(' ', '').lower()
            if 'k' in text:
                return int(float(text.replace('k', '')) * 1000)
            elif 'm' in text:
                return int(float(text.replace('m', '')) * 1000000)
            else:
                return int(text)
        except:
            return 0

    def save_to_database(self):
        """حفظ البيانات في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO instagram_profiles (
                    username, display_name, bio, followers_count, following_count,
                    posts_count, profile_image_url, is_verified, is_private,
                    external_url, scraping_method, scraping_timestamp, success, error_message
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.scraped_data.get('username'),
                self.scraped_data.get('display_name'),
                self.scraped_data.get('bio'),
                self.scraped_data.get('followers_count', 0),
                self.scraped_data.get('following_count', 0),
                self.scraped_data.get('posts_count', 0),
                self.scraped_data.get('profile_image_url'),
                self.scraped_data.get('is_verified', False),
                self.scraped_data.get('is_private', False),
                self.scraped_data.get('external_url'),
                self.scraped_data.get('scraping_method'),
                self.scraped_data.get('scraping_timestamp'),
                self.scraped_data.get('success', False),
                self.scraped_data.get('error_message')
            ))
            
            conn.commit()
            conn.close()
            
            print("💾 تم حفظ البيانات في قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")

    def display_results(self):
        """عرض النتائج"""
        print("\n📊 نتائج السكرابينغ:")
        print("=" * 40)
        
        if self.scraped_data.get('success'):
            print(f"👤 اسم المستخدم: @{self.scraped_data.get('username')}")
            print(f"📝 الاسم المعروض: {self.scraped_data.get('display_name')}")
            print(f"📄 البايو: {self.scraped_data.get('bio')}")
            print(f"👥 المتابعين: {self.scraped_data.get('followers_count', 0):,}")
            print(f"👤 المتابعة: {self.scraped_data.get('following_count', 0):,}")
            print(f"📸 المنشورات: {self.scraped_data.get('posts_count', 0):,}")
            print(f"✅ موثق: {'نعم' if self.scraped_data.get('is_verified') else 'لا'}")
            print(f"🔒 خاص: {'نعم' if self.scraped_data.get('is_private') else 'لا'}")
            print(f"🕷️ طريقة السكرابينغ: {self.scraped_data.get('scraping_method')}")
            print(f"⏰ وقت السكرابينغ: {self.scraped_data.get('scraping_timestamp')}")
        else:
            print("❌ فشل السكرابينغ")
            print(f"🚫 رسالة الخطأ: {self.scraped_data.get('error_message')}")

    def cleanup(self):
        """تنظيف الموارد"""
        if self.driver:
            self.driver.quit()
            print("🧹 تم إغلاق WebDriver")
        
        if self.session:
            self.session.close()
            print("🧹 تم إغلاق Session")

    def run_demo(self):
        """تشغيل التجربة الكاملة"""
        print("🚀 بدء تجربة سكرابينغ Instagram...")
        
        try:
            # محاولة Selenium أولاً
            if self.setup_selenium_driver():
                if self.scrape_with_selenium():
                    self.save_to_database()
                    self.display_results()
                    return
            
            # التراجع إلى Requests
            print("\n🔄 التراجع إلى طريقة Requests...")
            if self.setup_requests_session():
                if self.scrape_with_requests():
                    self.save_to_database()
                    self.display_results()
                    return
            
            print("\n❌ فشل في جميع طرق السكرابينغ")
            
        except KeyboardInterrupt:
            print("\n\n⏹️ تم إيقاف العملية بواسطة المستخدم")
        except Exception as e:
            print(f"\n❌ خطأ عام: {e}")
        finally:
            self.cleanup()

if __name__ == "__main__":
    print("⚠️ تحذير: هذا المثال للأغراض التعليمية فقط!")
    print("📚 يهدف لتوضيح تقنيات السكرابينغ وليس للاستخدام الضار")
    print("\nهل تريد المتابعة؟ (y/N): ", end="")
    
    # في بيئة تفاعلية، يمكن إضافة تأكيد
    # response = input().lower()
    # if response == 'y':
    
    demo = InstagramScraperDemo()
    demo.run_demo()
    
    # else:
    #     print("تم إلغاء العملية.")
