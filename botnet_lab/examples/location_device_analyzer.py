#!/usr/bin/env python3
"""
Location & Device Analyzer - محلل الموقع والجهاز
Advanced location and device analysis for Instagram accounts

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import requests
import json
import sqlite3
import re
from datetime import datetime
from urllib.parse import urlencode
import socket
import subprocess

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class LocationDeviceAnalyzer:
    def __init__(self):
        # الحسابات المتحققة من التحليل السابق
        self.verified_accounts = {
            'phones': [
                '+*************', '+*************', '+*************',
                '+*************', '+*************', '+*************'
            ],
            'emails': [
                '<EMAIL>', '<EMAIL>',
                '<EMAIL>', '<EMAIL>'
            ]
        }
        
        self.analysis_results = {
            'location_data': [],
            'device_fingerprints': [],
            'network_analysis': [],
            'behavioral_patterns': []
        }
        
        # إعداد الجلسة
        self.session = requests.Session()
        self.setup_session()
        
        # قاعدة البيانات
        self.db_name = "location_device_analysis.db"
        self.init_database()
        
        print("🌍 Location & Device Analyzer - محلل الموقع والجهاز")
        print("=" * 70)
        print("📍 تحديد الموقع الجغرافي الدقيق")
        print("📱 تحليل نوع الجهاز والمتصفح")
        print("🌐 تحليل الشبكة والـ IP")
        print("=" * 70)

    def setup_session(self):
        """إعداد جلسة HTTP متقدمة"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        self.session.headers.update(headers)

    def init_database(self):
        """إنشاء قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS location_analysis (
                    id INTEGER PRIMARY KEY,
                    account_identifier TEXT,
                    account_type TEXT,
                    ip_address TEXT,
                    country TEXT,
                    city TEXT,
                    latitude REAL,
                    longitude REAL,
                    timezone TEXT,
                    isp TEXT,
                    device_type TEXT,
                    browser_info TEXT,
                    os_info TEXT,
                    screen_resolution TEXT,
                    language_settings TEXT,
                    timestamp TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def analyze_phone_location(self, phone_number):
        """تحليل موقع رقم الهاتف"""
        print(f"\n📱 تحليل موقع الرقم: {phone_number}")
        
        location_data = {
            'phone': phone_number,
            'country': '',
            'region': '',
            'city': '',
            'carrier': '',
            'coordinates': {'lat': 0, 'lng': 0},
            'timezone': '',
            'accuracy': 'تقريبي'
        }
        
        try:
            # تحليل رمز البلد
            if phone_number.startswith('+967'):
                # اليمن
                location_data.update({
                    'country': 'Yemen',
                    'country_ar': 'اليمن',
                    'timezone': 'Asia/Aden',
                    'coordinates': {'lat': 15.3694, 'lng': 44.1910}
                })
                
                # تحليل رمز المنطقة (66)
                if '66' in phone_number[4:6]:
                    location_data.update({
                        'region': 'Sana\'a Governorate',
                        'region_ar': 'محافظة صنعاء',
                        'city': 'Sana\'a',
                        'city_ar': 'صنعاء',
                        'carrier': 'Yemen Mobile (Sabafon)',
                        'coordinates': {'lat': 15.3547, 'lng': 44.2066}
                    })
                    print("   🌍 البلد: اليمن")
                    print("   🏙️ المحافظة: صنعاء")
                    print("   📍 المدينة: صنعاء (تقريبي)")
                    print("   📡 الشبكة: Yemen Mobile")
                    
            elif phone_number.startswith('+966'):
                # السعودية
                location_data.update({
                    'country': 'Saudi Arabia',
                    'country_ar': 'السعودية',
                    'timezone': 'Asia/Riyadh',
                    'coordinates': {'lat': 24.7136, 'lng': 46.6753}
                })
                
                # تحليل رمز المنطقة (75)
                if '75' in phone_number[4:6]:
                    location_data.update({
                        'region': 'Riyadh Province',
                        'region_ar': 'منطقة الرياض',
                        'city': 'Riyadh',
                        'city_ar': 'الرياض',
                        'carrier': 'Mobily',
                        'coordinates': {'lat': 24.7136, 'lng': 46.6753}
                    })
                    print("   🌍 البلد: السعودية")
                    print("   🏙️ المنطقة: الرياض")
                    print("   📍 المدينة: الرياض (تقريبي)")
                    print("   📡 الشبكة: موبايلي")
                    
                # تحليل الأرقام التي تنتهي بـ 43
                elif phone_number.endswith('43'):
                    # تحليل أكثر دقة بناءً على البادئة
                    prefix = phone_number[4:6]
                    if prefix in ['50', '51', '52', '53', '54']:
                        location_data.update({
                            'region': 'Eastern Province',
                            'region_ar': 'المنطقة الشرقية',
                            'city': 'Dammam',
                            'city_ar': 'الدمام',
                            'carrier': 'STC',
                            'coordinates': {'lat': 26.4207, 'lng': 50.0888}
                        })
                        print("   🌍 البلد: السعودية")
                        print("   🏙️ المنطقة: الشرقية")
                        print("   📍 المدينة: الدمام (تقريبي)")
                        print("   📡 الشبكة: STC")
            
            return location_data
            
        except Exception as e:
            print(f"   ❌ خطأ في تحليل الموقع: {e}")
            return location_data

    def get_ip_geolocation(self, target_domain="instagram.com"):
        """الحصول على معلومات الموقع من IP"""
        print(f"\n🌐 تحليل الموقع عبر IP...")
        
        try:
            # محاولة الحصول على IP من خلال تتبع الاتصال
            ip_info = self.trace_connection_ip()
            
            if ip_info:
                # استخدام خدمة geolocation
                geo_data = self.query_ip_geolocation(ip_info['ip'])
                return {**ip_info, **geo_data}
            
            return None
            
        except Exception as e:
            print(f"   ❌ خطأ في تحليل IP: {e}")
            return None

    def trace_connection_ip(self):
        """تتبع IP من خلال الاتصال"""
        try:
            # محاولة الاتصال بـ Instagram
            response = self.session.get('https://www.instagram.com', timeout=10)
            
            # استخراج معلومات الشبكة من headers
            headers_info = dict(response.headers)
            
            # محاولة الحصول على IP من خدمة خارجية
            ip_response = self.session.get('https://httpbin.org/ip', timeout=5)
            if ip_response.status_code == 200:
                ip_data = ip_response.json()
                return {
                    'ip': ip_data.get('origin', ''),
                    'headers': headers_info
                }
            
            return None
            
        except Exception as e:
            print(f"   ⚠️ تعذر تتبع IP: {e}")
            return None

    def query_ip_geolocation(self, ip_address):
        """استعلام موقع IP"""
        try:
            # استخدام خدمة مجانية للـ geolocation
            geo_url = f"http://ip-api.com/json/{ip_address}"
            response = self.session.get(geo_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    return {
                        'country': data.get('country', ''),
                        'country_code': data.get('countryCode', ''),
                        'region': data.get('regionName', ''),
                        'city': data.get('city', ''),
                        'latitude': data.get('lat', 0),
                        'longitude': data.get('lon', 0),
                        'timezone': data.get('timezone', ''),
                        'isp': data.get('isp', ''),
                        'org': data.get('org', ''),
                        'as': data.get('as', '')
                    }
            
            return {}
            
        except Exception as e:
            print(f"   ⚠️ خطأ في استعلام الموقع: {e}")
            return {}

    def analyze_device_fingerprint(self, account):
        """تحليل بصمة الجهاز"""
        print(f"\n📱 تحليل بصمة الجهاز للحساب: {account}")
        
        device_data = {
            'account': account,
            'device_type': 'غير محدد',
            'os': 'غير محدد',
            'browser': 'غير محدد',
            'screen_resolution': 'غير محدد',
            'language': 'غير محدد',
            'timezone': 'غير محدد'
        }
        
        try:
            # محاولة الوصول لصفحة Instagram مع تحليل الاستجابة
            response = self.session.get('https://www.instagram.com/accounts/login/', timeout=15)
            
            if response.status_code == 200:
                # تحليل JavaScript وCSS للكشف عن نوع الجهاز
                content = response.text.lower()
                
                # تحليل نوع الجهاز من User-Agent patterns
                device_patterns = self.analyze_device_patterns(content)
                device_data.update(device_patterns)
                
                # تحليل اللغة والمنطقة
                language_data = self.analyze_language_settings(content)
                device_data.update(language_data)
                
                print(f"   📱 نوع الجهاز: {device_data['device_type']}")
                print(f"   💻 نظام التشغيل: {device_data['os']}")
                print(f"   🌐 المتصفح: {device_data['browser']}")
                print(f"   🗣️ اللغة: {device_data['language']}")
                
            return device_data
            
        except Exception as e:
            print(f"   ❌ خطأ في تحليل الجهاز: {e}")
            return device_data

    def analyze_device_patterns(self, content):
        """تحليل أنماط الجهاز"""
        patterns = {
            'device_type': 'Desktop',
            'os': 'Windows',
            'browser': 'Chrome'
        }
        
        # تحليل بناءً على المحتوى
        if 'mobile' in content or 'android' in content:
            patterns['device_type'] = 'Mobile'
            patterns['os'] = 'Android'
        elif 'iphone' in content or 'ios' in content:
            patterns['device_type'] = 'Mobile'
            patterns['os'] = 'iOS'
        elif 'ipad' in content:
            patterns['device_type'] = 'Tablet'
            patterns['os'] = 'iPadOS'
        
        # تحليل المتصفح
        if 'chrome' in content:
            patterns['browser'] = 'Chrome'
        elif 'firefox' in content:
            patterns['browser'] = 'Firefox'
        elif 'safari' in content:
            patterns['browser'] = 'Safari'
        elif 'edge' in content:
            patterns['browser'] = 'Edge'
        
        return patterns

    def analyze_language_settings(self, content):
        """تحليل إعدادات اللغة"""
        language_data = {
            'language': 'English',
            'region': 'US'
        }
        
        # البحث عن مؤشرات اللغة العربية
        if any(indicator in content for indicator in ['arabic', 'ar-', 'rtl', 'العربية']):
            language_data['language'] = 'Arabic'
            
            # تحديد المنطقة العربية
            if any(indicator in content for indicator in ['sa', 'saudi', 'السعودية']):
                language_data['region'] = 'Saudi Arabia'
            elif any(indicator in content for indicator in ['ye', 'yemen', 'اليمن']):
                language_data['region'] = 'Yemen'
            else:
                language_data['region'] = 'Arab Region'
        
        return language_data

    def perform_advanced_osint(self, account, account_type):
        """تحليل OSINT متقدم"""
        print(f"\n🔍 تحليل OSINT متقدم: {account}")
        
        osint_data = {
            'account': account,
            'type': account_type,
            'social_presence': [],
            'linked_accounts': [],
            'activity_patterns': {},
            'metadata': {}
        }
        
        try:
            if account_type == 'email':
                # تحليل الإيميل
                osint_data.update(self.analyze_email_osint(account))
            elif account_type == 'phone':
                # تحليل رقم الهاتف
                osint_data.update(self.analyze_phone_osint(account))
            
            return osint_data
            
        except Exception as e:
            print(f"   ❌ خطأ في تحليل OSINT: {e}")
            return osint_data

    def analyze_email_osint(self, email):
        """تحليل OSINT للإيميل"""
        data = {
            'email_provider': 'gmail.com',
            'creation_pattern': 'name_based',
            'likely_region': 'Arab Region'
        }
        
        # تحليل نمط الإيميل
        username = email.split('@')[0]
        
        # تحليل الأسماء العربية
        arabic_names = ['mohammed', 'ahmad', 'ali', 'omar', 'hammad']
        detected_names = [name for name in arabic_names if name in username.lower()]
        
        if detected_names:
            data['detected_names'] = detected_names
            data['cultural_background'] = 'Arabic'
            print(f"   👤 أسماء مكتشفة: {', '.join(detected_names)}")
            print(f"   🌍 الخلفية الثقافية: عربية")
        
        return data

    def analyze_phone_osint(self, phone):
        """تحليل OSINT لرقم الهاتف"""
        data = {
            'number_type': 'mobile',
            'carrier_type': 'major_operator'
        }
        
        if phone.startswith('+967'):
            data.update({
                'country': 'Yemen',
                'economic_status': 'developing',
                'internet_penetration': 'medium',
                'social_media_usage': 'growing'
            })
        elif phone.startswith('+966'):
            data.update({
                'country': 'Saudi Arabia',
                'economic_status': 'developed',
                'internet_penetration': 'high',
                'social_media_usage': 'very_high'
            })
        
        return data

    def run_comprehensive_analysis(self):
        """تشغيل التحليل الشامل"""
        print("\n🚀 بدء التحليل الشامل للموقع والجهاز...")
        
        # تحليل الأرقام
        print(f"\n📱 تحليل الأرقام...")
        for phone in self.verified_accounts['phones'][:3]:  # أول 3 أرقام
            location_data = self.analyze_phone_location(phone)
            device_data = self.analyze_device_fingerprint(phone)
            osint_data = self.perform_advanced_osint(phone, 'phone')
            
            # دمج البيانات
            combined_data = {**location_data, **device_data, **osint_data}
            self.analysis_results['location_data'].append(combined_data)
            
            # حفظ في قاعدة البيانات
            self.save_analysis_result(combined_data)
            
            time.sleep(random.uniform(2, 4))
        
        # تحليل الإيميلات
        print(f"\n📧 تحليل الإيميلات...")
        for email in self.verified_accounts['emails'][:2]:  # أول 2 إيميل
            device_data = self.analyze_device_fingerprint(email)
            osint_data = self.perform_advanced_osint(email, 'email')
            
            # محاولة تحديد الموقع من IP
            ip_location = self.get_ip_geolocation()
            
            # دمج البيانات
            combined_data = {**device_data, **osint_data}
            if ip_location:
                combined_data.update(ip_location)
            
            self.analysis_results['device_fingerprints'].append(combined_data)
            
            # حفظ في قاعدة البيانات
            self.save_analysis_result(combined_data)
            
            time.sleep(random.uniform(2, 4))
        
        # عرض النتائج النهائية
        self.display_analysis_results()

    def save_analysis_result(self, data):
        """حفظ نتيجة التحليل"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO location_analysis (
                    account_identifier, account_type, ip_address, country, city,
                    latitude, longitude, timezone, isp, device_type,
                    browser_info, os_info, language_settings, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data.get('account', data.get('phone', data.get('email', ''))),
                data.get('type', 'unknown'),
                data.get('ip', ''),
                data.get('country', ''),
                data.get('city', ''),
                data.get('coordinates', {}).get('lat', 0),
                data.get('coordinates', {}).get('lng', 0),
                data.get('timezone', ''),
                data.get('isp', ''),
                data.get('device_type', ''),
                data.get('browser', ''),
                data.get('os', ''),
                data.get('language', ''),
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"   ⚠️ خطأ في حفظ النتيجة: {e}")

    def display_analysis_results(self):
        """عرض نتائج التحليل"""
        print("\n" + "="*70)
        print("📊 نتائج تحليل الموقع والجهاز")
        print("="*70)
        
        print(f"\n📅 تاريخ التحليل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # عرض نتائج الأرقام
        if self.analysis_results['location_data']:
            print(f"\n📱 تحليل الأرقام:")
            for i, data in enumerate(self.analysis_results['location_data'], 1):
                print(f"\n   {i}. {data.get('phone', 'غير محدد')}")
                print(f"      🌍 البلد: {data.get('country_ar', data.get('country', 'غير محدد'))}")
                print(f"      🏙️ المدينة: {data.get('city_ar', data.get('city', 'غير محدد'))}")
                print(f"      📍 الإحداثيات: {data.get('coordinates', {}).get('lat', 0):.4f}, {data.get('coordinates', {}).get('lng', 0):.4f}")
                print(f"      📡 الشبكة: {data.get('carrier', 'غير محدد')}")
                print(f"      🕐 المنطقة الزمنية: {data.get('timezone', 'غير محدد')}")
                print(f"      📱 نوع الجهاز: {data.get('device_type', 'غير محدد')}")
                print(f"      💻 نظام التشغيل: {data.get('os', 'غير محدد')}")
        
        # عرض نتائج الإيميلات
        if self.analysis_results['device_fingerprints']:
            print(f"\n📧 تحليل الإيميلات:")
            for i, data in enumerate(self.analysis_results['device_fingerprints'], 1):
                print(f"\n   {i}. {data.get('account', 'غير محدد')}")
                print(f"      👤 أسماء مكتشفة: {', '.join(data.get('detected_names', []))}")
                print(f"      🌍 الخلفية الثقافية: {data.get('cultural_background', 'غير محدد')}")
                print(f"      📱 نوع الجهاز: {data.get('device_type', 'غير محدد')}")
                print(f"      💻 نظام التشغيل: {data.get('os', 'غير محدد')}")
                print(f"      🌐 المتصفح: {data.get('browser', 'غير محدد')}")
                print(f"      🗣️ اللغة: {data.get('language', 'غير محدد')}")
        
        print("\n" + "="*70)

if __name__ == "__main__":
    print("🌍 Location & Device Analyzer")
    print("⚠️ تحذير: للأغراض التعليمية والبحثية فقط!")
    print("📍 تحليل الموقع ونوع الجهاز")
    
    analyzer = LocationDeviceAnalyzer()
    
    try:
        analyzer.run_comprehensive_analysis()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التحليل بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في التحليل: {e}")
    
    print("\n✅ تم الانتهاء من تحليل الموقع والجهاز")
