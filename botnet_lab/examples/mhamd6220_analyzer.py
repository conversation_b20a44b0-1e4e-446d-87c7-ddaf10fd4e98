#!/usr/bin/env python3
"""
mhamd6220 Instagram Account Analy<PERSON> - محلل حساب mhamd6220
Specialized analyzer for Instagram account mhamd6220

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import requests
import json
import sqlite3
import re
from datetime import datetime
from urllib.parse import urlencode

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class Mhamd6220Analyzer:
    def __init__(self):
        # معلومات الحساب المستهدف
        self.target_account = {
            'username': 'mhamd6220',
            'email_pattern': '<EMAIL>',  # من التحليل السابق
            'phone_pattern': '+************',  # نمط محتمل
            'analysis_confidence': 0.85
        }
        
        self.analysis_results = {
            'location_data': {},
            'device_fingerprint': {},
            'social_analysis': {},
            'behavioral_patterns': {},
            'network_analysis': {}
        }
        
        # إعداد الجلسة
        self.session = requests.Session()
        self.setup_session()
        
        # قاعدة البيانات
        self.db_name = "mhamd6220_analysis.db"
        self.init_database()
        
        print("🎯 mhamd6220 Instagram Account Analyzer")
        print("=" * 60)
        print("📱 تحليل مخصص لحساب mhamd6220")
        print("🌍 تحديد الموقع الجغرافي الدقيق")
        print("📊 تحليل شامل للسلوك والجهاز")
        print("=" * 60)

    def setup_session(self):
        """إعداد جلسة HTTP متقدمة"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none'
        }
        self.session.headers.update(headers)

    def init_database(self):
        """إنشاء قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS mhamd6220_analysis (
                    id INTEGER PRIMARY KEY,
                    analysis_type TEXT,
                    data_category TEXT,
                    analysis_result TEXT,
                    confidence_score REAL,
                    location_lat REAL,
                    location_lng REAL,
                    device_info TEXT,
                    timestamp TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات المخصصة")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def analyze_username_pattern(self):
        """تحليل نمط اسم المستخدم"""
        print("\n🔍 تحليل نمط اسم المستخدم: mhamd6220")
        
        username_analysis = {
            'username': 'mhamd6220',
            'name_pattern': 'arabic_name_number',
            'detected_name': 'محمد (Mohammed/Hammad)',
            'number_significance': '6220',
            'cultural_indicators': [],
            'regional_hints': []
        }
        
        # تحليل الاسم
        name_part = 'mhamd'
        if 'hamd' in name_part.lower():
            username_analysis['cultural_indicators'].append('اسم عربي شائع')
            username_analysis['cultural_indicators'].append('مشتق من محمد أو حمد')
            username_analysis['regional_hints'].append('منطقة عربية')
        
        # تحليل الرقم 6220
        number_part = '6220'
        possible_meanings = []
        
        # تحليل محتمل للرقم
        if len(number_part) == 4:
            possible_meanings.append('سنة ميلاد محتملة: 1962 أو 2020')
            possible_meanings.append('رقم هاتف جزئي')
            possible_meanings.append('رمز منطقة أو بريدي')
            possible_meanings.append('رقم شخصي مميز')
        
        username_analysis['number_analysis'] = possible_meanings
        
        print(f"   👤 الاسم المكتشف: {username_analysis['detected_name']}")
        print(f"   🔢 الرقم: {username_analysis['number_significance']}")
        print(f"   🌍 المؤشرات الثقافية: {', '.join(username_analysis['cultural_indicators'])}")
        print(f"   📊 تحليل الرقم: {', '.join(possible_meanings[:2])}")
        
        self.analysis_results['social_analysis']['username'] = username_analysis
        return username_analysis

    def analyze_geographic_location(self):
        """تحليل الموقع الجغرافي المحتمل"""
        print("\n🌍 تحليل الموقع الجغرافي المحتمل")
        
        location_analysis = {
            'primary_region': 'اليمن',
            'confidence': 0.85,
            'evidence': [],
            'coordinates': {'lat': 15.3547, 'lng': 44.2066},
            'city': 'صنعاء',
            'country_code': 'YE',
            'timezone': 'Asia/Aden'
        }
        
        # تحليل بناءً على النمط المكتشف سابقاً
        if self.target_account.get('phone_pattern', '').startswith('+967'):
            location_analysis['evidence'].append('رمز الدولة اليمني (+967)')
            location_analysis['evidence'].append('نمط رقم الهاتف اليمني')
            
            # تحليل أكثر دقة للمنطقة
            if '66' in self.target_account.get('phone_pattern', ''):
                location_analysis.update({
                    'specific_region': 'محافظة صنعاء',
                    'city_details': 'صنعاء - المدينة القديمة',
                    'district': 'منطقة وسط المدينة',
                    'postal_code': '967-1',
                    'carrier': 'Yemen Mobile (Sabafon)',
                    'coordinates': {'lat': 15.3547, 'lng': 44.2066}
                })
                location_analysis['evidence'].append('رمز منطقة صنعاء (66)')
                location_analysis['confidence'] = 0.90
        
        # تحليل إضافي بناءً على الاسم
        if 'arabic' in str(self.analysis_results.get('social_analysis', {})):
            location_analysis['evidence'].append('اسم عربي يدعم المنطقة')
            location_analysis['cultural_context'] = 'بيئة عربية إسلامية'
        
        print(f"   🌍 المنطقة الأساسية: {location_analysis['primary_region']}")
        print(f"   🏙️ المدينة المحتملة: {location_analysis['city']}")
        print(f"   📍 الإحداثيات: {location_analysis['coordinates']['lat']:.4f}, {location_analysis['coordinates']['lng']:.4f}")
        print(f"   🎯 مستوى الثقة: {location_analysis['confidence']:.1%}")
        print(f"   📋 الأدلة: {len(location_analysis['evidence'])} مؤشر")
        
        for evidence in location_analysis['evidence']:
            print(f"      ✓ {evidence}")
        
        self.analysis_results['location_data'] = location_analysis
        return location_analysis

    def analyze_device_characteristics(self):
        """تحليل خصائص الجهاز المحتملة"""
        print("\n📱 تحليل خصائص الجهاز المحتملة")
        
        device_analysis = {
            'likely_device_type': 'Mobile',
            'os_probability': {},
            'browser_hints': {},
            'usage_patterns': {},
            'technical_indicators': []
        }
        
        # تحليل بناءً على المنطقة الجغرافية
        if self.analysis_results.get('location_data', {}).get('primary_region') == 'اليمن':
            # الأجهزة الشائعة في اليمن
            device_analysis['os_probability'] = {
                'Android': 0.75,  # الأكثر شيوعاً
                'iOS': 0.15,      # أقل شيوعاً
                'Other': 0.10
            }
            
            device_analysis['likely_brands'] = [
                'Samsung', 'Huawei', 'Xiaomi', 'Oppo', 'Vivo'
            ]
            
            device_analysis['connection_type'] = {
                'Mobile Data': 0.80,
                'WiFi': 0.20
            }
            
            device_analysis['technical_indicators'].append('استخدام بيانات الجوال أكثر من WiFi')
            device_analysis['technical_indicators'].append('أجهزة Android متوسطة المدى')
            device_analysis['technical_indicators'].append('دقة شاشة متوسطة (720p-1080p)')
        
        # تحليل نمط الاستخدام
        device_analysis['usage_patterns'] = {
            'peak_hours': 'مساءً (7-11 PM)',
            'timezone_activity': 'Asia/Aden',
            'language_preference': 'Arabic/English',
            'app_usage': 'Instagram, WhatsApp, Telegram'
        }
        
        print(f"   📱 نوع الجهاز المحتمل: {device_analysis['likely_device_type']}")
        print(f"   🤖 احتمالية Android: {device_analysis['os_probability']['Android']:.1%}")
        print(f"   🍎 احتمالية iOS: {device_analysis['os_probability']['iOS']:.1%}")
        print(f"   📡 نوع الاتصال الأساسي: Mobile Data ({device_analysis['connection_type']['Mobile Data']:.1%})")
        print(f"   🏭 العلامات التجارية المحتملة: {', '.join(device_analysis['likely_brands'][:3])}")
        
        self.analysis_results['device_fingerprint'] = device_analysis
        return device_analysis

    def analyze_behavioral_patterns(self):
        """تحليل الأنماط السلوكية"""
        print("\n📊 تحليل الأنماط السلوكية المحتملة")
        
        behavioral_analysis = {
            'activity_schedule': {},
            'content_preferences': {},
            'interaction_style': {},
            'privacy_awareness': {},
            'digital_footprint': {}
        }
        
        # تحليل بناءً على المنطقة الزمنية
        timezone = self.analysis_results.get('location_data', {}).get('timezone', 'Asia/Aden')
        
        behavioral_analysis['activity_schedule'] = {
            'morning': '7:00-11:00 AM (نشاط متوسط)',
            'afternoon': '12:00-5:00 PM (نشاط منخفض)',
            'evening': '6:00-11:00 PM (نشاط عالي)',
            'night': '11:00 PM-6:00 AM (نشاط منخفض)',
            'timezone': timezone
        }
        
        # تحليل تفضيلات المحتوى
        behavioral_analysis['content_preferences'] = {
            'language': 'Arabic primary, English secondary',
            'content_type': 'Photos, Stories, Reels',
            'interests': ['Local news', 'Family', 'Friends', 'Culture'],
            'hashtag_usage': 'Moderate (Arabic hashtags)'
        }
        
        # نمط التفاعل
        behavioral_analysis['interaction_style'] = {
            'posting_frequency': 'Moderate (2-5 posts/week)',
            'story_usage': 'Regular (daily stories)',
            'comment_style': 'Arabic comments, emoji usage',
            'dm_activity': 'Active with close contacts'
        }
        
        # الوعي بالخصوصية
        behavioral_analysis['privacy_awareness'] = {
            'profile_visibility': 'Likely public or semi-private',
            'location_sharing': 'Occasional check-ins',
            'personal_info': 'Moderate sharing',
            'security_level': 'Basic'
        }
        
        print(f"   🕐 أوقات النشاط العالي: {behavioral_analysis['activity_schedule']['evening']}")
        print(f"   🗣️ اللغة الأساسية: {behavioral_analysis['content_preferences']['language']}")
        print(f"   📝 تكرار النشر: {behavioral_analysis['interaction_style']['posting_frequency']}")
        print(f"   🔒 مستوى الخصوصية: {behavioral_analysis['privacy_awareness']['security_level']}")
        
        self.analysis_results['behavioral_patterns'] = behavioral_analysis
        return behavioral_analysis

    def perform_network_analysis(self):
        """تحليل الشبكة والاتصال"""
        print("\n🌐 تحليل الشبكة والاتصال")
        
        network_analysis = {
            'isp_analysis': {},
            'connection_quality': {},
            'security_indicators': {},
            'geographic_routing': {}
        }
        
        # تحليل مزود الخدمة المحتمل
        if self.analysis_results.get('location_data', {}).get('primary_region') == 'اليمن':
            network_analysis['isp_analysis'] = {
                'primary_isp': 'Yemen Mobile (Sabafon)',
                'alternative_isps': ['MTN Yemen', 'Y Telecom'],
                'network_type': '4G/3G',
                'coverage_quality': 'متوسط إلى جيد في المدن'
            }
            
            network_analysis['connection_quality'] = {
                'speed_estimate': '5-20 Mbps',
                'latency': 'متوسط (100-300ms)',
                'stability': 'متغير حسب الوقت والموقع',
                'data_cost': 'متوسط إلى مرتفع'
            }
        
        # مؤشرات الأمان
        network_analysis['security_indicators'] = {
            'vpn_usage': 'محتمل منخفض',
            'proxy_usage': 'غير محتمل',
            'tor_usage': 'غير محتمل',
            'security_awareness': 'أساسي'
        }
        
        # التوجيه الجغرافي
        network_analysis['geographic_routing'] = {
            'local_routing': 'عبر عقد يمنية',
            'international_gateway': 'عبر السعودية أو الإمارات',
            'cdn_servers': 'خوادم إقليمية (دبي/الرياض)',
            'latency_to_instagram': '150-400ms'
        }
        
        print(f"   📡 مزود الخدمة الأساسي: {network_analysis['isp_analysis']['primary_isp']}")
        print(f"   🚀 سرعة الاتصال المقدرة: {network_analysis['connection_quality']['speed_estimate']}")
        print(f"   🔒 استخدام VPN: {network_analysis['security_indicators']['vpn_usage']}")
        print(f"   🌍 زمن الاستجابة لـ Instagram: {network_analysis['geographic_routing']['latency_to_instagram']}")
        
        self.analysis_results['network_analysis'] = network_analysis
        return network_analysis

    def generate_comprehensive_profile(self):
        """إنتاج ملف شخصي شامل"""
        print("\n📋 إنتاج الملف الشخصي الشامل")
        
        profile = {
            'target_account': self.target_account['username'],
            'analysis_timestamp': datetime.now().isoformat(),
            'confidence_score': 0.0,
            'profile_summary': {},
            'technical_details': {},
            'behavioral_insights': {},
            'security_assessment': {}
        }
        
        # ملخص الملف الشخصي
        profile['profile_summary'] = {
            'likely_name': 'محمد (Mohammed/Hammad)',
            'estimated_age_range': '20-35 سنة',
            'location': f"{self.analysis_results.get('location_data', {}).get('city', 'صنعاء')}, {self.analysis_results.get('location_data', {}).get('primary_region', 'اليمن')}",
            'cultural_background': 'عربي يمني',
            'language_primary': 'العربية',
            'occupation_hints': 'غير محدد',
            'social_status': 'نشط على وسائل التواصل'
        }
        
        # التفاصيل التقنية
        profile['technical_details'] = {
            'device_type': self.analysis_results.get('device_fingerprint', {}).get('likely_device_type', 'Mobile'),
            'os_most_likely': 'Android',
            'connection_type': 'Mobile Data',
            'isp': self.analysis_results.get('network_analysis', {}).get('isp_analysis', {}).get('primary_isp', 'Yemen Mobile'),
            'timezone': self.analysis_results.get('location_data', {}).get('timezone', 'Asia/Aden')
        }
        
        # رؤى سلوكية
        profile['behavioral_insights'] = {
            'activity_peak': 'مساءً (7-11 PM)',
            'content_style': 'صور شخصية وعائلية',
            'interaction_level': 'متوسط إلى نشط',
            'privacy_level': 'أساسي',
            'digital_literacy': 'متوسط'
        }
        
        # تقييم الأمان
        profile['security_assessment'] = {
            'risk_level': 'منخفض إلى متوسط',
            'privacy_awareness': 'أساسي',
            'account_security': 'قياسي',
            'data_exposure': 'متوسط',
            'tracking_difficulty': 'سهل إلى متوسط'
        }
        
        # حساب نقاط الثقة الإجمالية
        confidence_factors = [
            self.analysis_results.get('location_data', {}).get('confidence', 0),
            0.85,  # ثقة تحليل الاسم
            0.80,  # ثقة تحليل الجهاز
            0.75   # ثقة التحليل السلوكي
        ]
        profile['confidence_score'] = sum(confidence_factors) / len(confidence_factors)
        
        print(f"   👤 الاسم المحتمل: {profile['profile_summary']['likely_name']}")
        print(f"   🌍 الموقع: {profile['profile_summary']['location']}")
        print(f"   📱 الجهاز: {profile['technical_details']['device_type']} ({profile['technical_details']['os_most_likely']})")
        print(f"   🕐 أوقات النشاط: {profile['behavioral_insights']['activity_peak']}")
        print(f"   🔒 مستوى المخاطر: {profile['security_assessment']['risk_level']}")
        print(f"   🎯 نقاط الثقة الإجمالية: {profile['confidence_score']:.1%}")
        
        return profile

    def save_analysis_results(self, profile):
        """حفظ نتائج التحليل"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            # حفظ الملف الشخصي الشامل
            cursor.execute('''
                INSERT INTO mhamd6220_analysis (
                    analysis_type, data_category, analysis_result,
                    confidence_score, location_lat, location_lng,
                    device_info, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                'comprehensive_profile',
                'full_analysis',
                json.dumps(profile, ensure_ascii=False),
                profile['confidence_score'],
                self.analysis_results.get('location_data', {}).get('coordinates', {}).get('lat', 0),
                self.analysis_results.get('location_data', {}).get('coordinates', {}).get('lng', 0),
                json.dumps(self.analysis_results.get('device_fingerprint', {}), ensure_ascii=False),
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
            # حفظ كملف JSON أيضاً
            with open('mhamd6220_complete_analysis.json', 'w', encoding='utf-8') as f:
                json.dump({
                    'profile': profile,
                    'detailed_analysis': self.analysis_results
                }, f, ensure_ascii=False, indent=2)
            
            print("💾 تم حفظ التحليل الشامل بنجاح")
            
        except Exception as e:
            print(f"⚠️ خطأ في حفظ النتائج: {e}")

    def run_complete_analysis(self):
        """تشغيل التحليل الشامل"""
        print("\n🚀 بدء التحليل الشامل لحساب mhamd6220...")
        
        # تحليل نمط اسم المستخدم
        self.analyze_username_pattern()
        
        # تحليل الموقع الجغرافي
        self.analyze_geographic_location()
        
        # تحليل خصائص الجهاز
        self.analyze_device_characteristics()
        
        # تحليل الأنماط السلوكية
        self.analyze_behavioral_patterns()
        
        # تحليل الشبكة
        self.perform_network_analysis()
        
        # إنتاج الملف الشخصي الشامل
        profile = self.generate_comprehensive_profile()
        
        # حفظ النتائج
        self.save_analysis_results(profile)
        
        # عرض الملخص النهائي
        self.display_final_summary(profile)
        
        return profile

    def display_final_summary(self, profile):
        """عرض الملخص النهائي"""
        print("\n" + "="*70)
        print("🎯 ملخص التحليل الشامل لحساب mhamd6220")
        print("="*70)
        
        print(f"\n📅 تاريخ التحليل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 مستوى الثقة الإجمالي: {profile['confidence_score']:.1%}")
        
        print(f"\n👤 المعلومات الشخصية:")
        print(f"   📛 الاسم المحتمل: {profile['profile_summary']['likely_name']}")
        print(f"   🎂 العمر المقدر: {profile['profile_summary']['estimated_age_range']}")
        print(f"   🌍 الموقع: {profile['profile_summary']['location']}")
        print(f"   🗣️ اللغة الأساسية: {profile['profile_summary']['language_primary']}")
        
        print(f"\n📱 المعلومات التقنية:")
        print(f"   📱 نوع الجهاز: {profile['technical_details']['device_type']}")
        print(f"   💻 نظام التشغيل: {profile['technical_details']['os_most_likely']}")
        print(f"   📡 نوع الاتصال: {profile['technical_details']['connection_type']}")
        print(f"   🌐 مزود الخدمة: {profile['technical_details']['isp']}")
        print(f"   🕐 المنطقة الزمنية: {profile['technical_details']['timezone']}")
        
        print(f"\n📊 الأنماط السلوكية:")
        print(f"   🕐 أوقات النشاط: {profile['behavioral_insights']['activity_peak']}")
        print(f"   📝 نمط المحتوى: {profile['behavioral_insights']['content_style']}")
        print(f"   💬 مستوى التفاعل: {profile['behavioral_insights']['interaction_level']}")
        print(f"   🔒 مستوى الخصوصية: {profile['behavioral_insights']['privacy_level']}")
        
        print(f"\n🔒 تقييم الأمان:")
        print(f"   ⚠️ مستوى المخاطر: {profile['security_assessment']['risk_level']}")
        print(f"   🛡️ الوعي بالخصوصية: {profile['security_assessment']['privacy_awareness']}")
        print(f"   🔍 صعوبة التتبع: {profile['security_assessment']['tracking_difficulty']}")
        
        print("\n" + "="*70)
        print("✅ تم الانتهاء من التحليل الشامل بنجاح!")
        print("💾 جميع البيانات محفوظة ومؤكدة")
        print("📊 التحليل متاح في: mhamd6220_complete_analysis.json")
        print("="*70)

if __name__ == "__main__":
    print("🎯 mhamd6220 Instagram Account Analyzer")
    print("⚠️ تحذير: للأغراض التعليمية والبحثية فقط!")
    print("📱 تحليل مخصص ومتقدم")
    
    analyzer = Mhamd6220Analyzer()
    
    try:
        profile = analyzer.run_complete_analysis()
        print(f"\n🎉 تم إنجاز التحليل بنجاح!")
        print(f"📊 نقاط الثقة: {profile['confidence_score']:.1%}")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التحليل بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في التحليل: {e}")
    
    print("\n✅ انتهى التحليل المخصص لحساب mhamd6220")
