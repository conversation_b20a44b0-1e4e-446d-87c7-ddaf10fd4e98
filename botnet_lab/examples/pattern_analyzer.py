#!/usr/bin/env python3
"""
Pattern Analyzer - محلل الأنماط المشفرة
Encrypted Pattern Analysis and Information Extraction

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import json
import sqlite3
import re
import itertools
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class PatternAnalyzer:
    def __init__(self):
        self.extracted_patterns = {
            'phones': [],
            'emails': [],
            'possible_phones': [],
            'possible_emails': []
        }
        
        # إعداد قاعدة البيانات
        self.db_name = "pattern_analysis.db"
        self.init_database()
        
        print("🔍 Pattern Analyzer - محلل الأنماط المشفرة")
        print("=" * 70)
        print("🎯 تحليل المعلومات المشفرة من صورة Instagram")
        print("📱 استخراج أرقام الهواتف والإيميلات المخفية")
        print("=" * 70)

    def init_database(self):
        """إنشاء قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS pattern_analysis (
                    id INTEGER PRIMARY KEY,
                    pattern_type TEXT,
                    original_pattern TEXT,
                    analyzed_pattern TEXT,
                    confidence_score REAL,
                    possible_values TEXT,
                    country_code TEXT,
                    provider TEXT,
                    timestamp TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def analyze_phone_patterns(self):
        """تحليل أنماط أرقام الهواتف"""
        print("\n📱 تحليل أنماط أرقام الهواتف...")
        
        # الأنماط المكتشفة من الصورة
        phone_patterns = [
            {
                'pattern': '66** *** **+967',
                'country': 'Yemen',
                'country_code': '+967',
                'visible_digits': '66',
                'position': 'start'
            },
            {
                'pattern': '75** *** **+966',
                'country': 'Saudi Arabia',
                'country_code': '+966',
                'visible_digits': '75',
                'position': 'start'
            },
            {
                'pattern': '*** **+966 43**',
                'country': 'Saudi Arabia',
                'country_code': '+966',
                'visible_digits': '43',
                'position': 'end'
            }
        ]
        
        for pattern_data in phone_patterns:
            print(f"\n   🔍 تحليل النمط: {pattern_data['pattern']}")
            print(f"      🌍 البلد: {pattern_data['country']}")
            print(f"      📞 رمز البلد: {pattern_data['country_code']}")
            
            # توليد الأرقام المحتملة
            possible_numbers = self.generate_possible_phones(pattern_data)
            
            print(f"      🎯 الأرقام المحتملة ({len(possible_numbers)}):")
            for i, number in enumerate(possible_numbers[:10], 1):  # أول 10 أرقام
                print(f"         {i}. {number}")
            
            if len(possible_numbers) > 10:
                print(f"         ... و {len(possible_numbers) - 10} رقم إضافي")
            
            # حفظ في قاعدة البيانات
            self.save_pattern_analysis('phone', pattern_data, possible_numbers)
            
            self.extracted_patterns['phones'].append(pattern_data)
            self.extracted_patterns['possible_phones'].extend(possible_numbers)

    def generate_possible_phones(self, pattern_data):
        """توليد الأرقام المحتملة"""
        possible_numbers = []
        
        country_code = pattern_data['country_code']
        visible_digits = pattern_data['visible_digits']
        
        if pattern_data['country'] == 'Yemen':
            # أرقام يمنية تبدأ بـ 66
            prefixes = ['66']
            for prefix in prefixes:
                # توليد أرقام محتملة (7 أرقام إضافية)
                for i in range(1000000, 1000100):  # عينة من الأرقام
                    number = f"{country_code}-{prefix}-{str(i)[1:]}"
                    possible_numbers.append(number)
        
        elif pattern_data['country'] == 'Saudi Arabia':
            if pattern_data['position'] == 'start':
                # أرقام سعودية تبدأ بـ 75
                prefixes = ['75']
                for prefix in prefixes:
                    for i in range(1000000, 1000100):
                        number = f"{country_code}-{prefix}-{str(i)[1:]}"
                        possible_numbers.append(number)
            else:
                # أرقام سعودية تنتهي بـ 43
                for i in range(500000000, 500000100):
                    number_str = str(i)
                    if number_str.endswith('43'):
                        number = f"{country_code}-{number_str[:3]}-{number_str[3:6]}-{number_str[6:]}"
                        possible_numbers.append(number)
        
        return possible_numbers[:100]  # أول 100 رقم محتمل

    def analyze_email_patterns(self):
        """تحليل أنماط البريد الإلكتروني"""
        print("\n📧 تحليل أنماط البريد الإلكتروني...")
        
        # الأنماط المكتشفة من الصورة
        email_patterns = [
            {
                'pattern': 'm*******<EMAIL>',
                'provider': 'Gmail',
                'start_char': 'm',
                'end_char': '0',
                'hidden_length': 7,
                'total_length': 9
            },
            {
                'pattern': 'm*************<EMAIL>',
                'provider': 'Gmail',
                'start_char': 'm',
                'end_char': '7',
                'hidden_length': 13,
                'total_length': 15
            }
        ]
        
        for pattern_data in email_patterns:
            print(f"\n   🔍 تحليل النمط: {pattern_data['pattern']}")
            print(f"      📮 المزود: {pattern_data['provider']}")
            print(f"      🔤 يبدأ بـ: {pattern_data['start_char']}")
            print(f"      🔤 ينتهي بـ: {pattern_data['end_char']}")
            print(f"      📏 الطول المخفي: {pattern_data['hidden_length']} أحرف")
            
            # توليد الإيميلات المحتملة
            possible_emails = self.generate_possible_emails(pattern_data)
            
            print(f"      🎯 الإيميلات المحتملة ({len(possible_emails)}):")
            for i, email in enumerate(possible_emails[:10], 1):  # أول 10 إيميلات
                print(f"         {i}. {email}")
            
            if len(possible_emails) > 10:
                print(f"         ... و {len(possible_emails) - 10} إيميل إضافي")
            
            # حفظ في قاعدة البيانات
            self.save_pattern_analysis('email', pattern_data, possible_emails)
            
            self.extracted_patterns['emails'].append(pattern_data)
            self.extracted_patterns['possible_emails'].extend(possible_emails)

    def generate_possible_emails(self, pattern_data):
        """توليد الإيميلات المحتملة"""
        possible_emails = []
        
        start_char = pattern_data['start_char']
        end_char = pattern_data['end_char']
        hidden_length = pattern_data['hidden_length']
        provider = pattern_data['provider'].lower()
        
        # أنماط شائعة للأسماء
        common_patterns = [
            'hamd', 'hammad', 'mohammed', 'ahmad', 'ali', 'omar', 'khalid',
            'abdallah', 'hassan', 'hussein', 'youssef', 'ibrahim', 'salem',
            'fahad', 'majed', 'saud', 'faisal', 'nasser', 'turki'
        ]
        
        # أرقام شائعة
        common_numbers = ['123', '456', '789', '2020', '2021', '2022', '2023', '2024']
        
        # توليد تركيبات محتملة
        for pattern in common_patterns:
            for number in common_numbers:
                # تركيبة 1: اسم + رقم
                combination = pattern + number
                if len(combination) == hidden_length:
                    email = f"{start_char}{combination}{end_char}@{provider}.com"
                    possible_emails.append(email)
                
                # تركيبة 2: اسم مكرر + رقم
                if len(pattern) * 2 + len(number) == hidden_length:
                    combination = pattern + pattern + number
                    email = f"{start_char}{combination}{end_char}@{provider}.com"
                    possible_emails.append(email)
        
        # توليد تركيبات عشوائية
        import string
        for _ in range(50):
            random_chars = ''.join(random.choices(string.ascii_lowercase + string.digits, k=hidden_length))
            email = f"{start_char}{random_chars}{end_char}@{provider}.com"
            possible_emails.append(email)
        
        return list(set(possible_emails))[:100]  # إزالة التكرارات وأول 100

    def advanced_pattern_analysis(self):
        """تحليل متقدم للأنماط"""
        print("\n🧠 تحليل متقدم للأنماط...")
        
        # تحليل الأنماط المحتملة بناءً على اسم المستخدم
        username = "mhamd6220"
        
        print(f"   🎯 تحليل بناءً على اسم المستخدم: {username}")
        
        # تحليل الإيميلات المحتملة
        likely_emails = self.analyze_username_based_emails(username)
        print(f"   📧 إيميلات محتملة بناءً على اسم المستخدم ({len(likely_emails)}):")
        for i, email in enumerate(likely_emails, 1):
            print(f"      {i}. {email}")
        
        # تحليل أرقام الهواتف المحتملة
        likely_phones = self.analyze_username_based_phones(username)
        print(f"   📱 أرقام محتملة بناءً على اسم المستخدم ({len(likely_phones)}):")
        for i, phone in enumerate(likely_phones, 1):
            print(f"      {i}. {phone}")

    def analyze_username_based_emails(self, username):
        """تحليل الإيميلات بناءً على اسم المستخدم"""
        likely_emails = []
        
        # استخراج الأجزاء من اسم المستخدم
        base_name = username.replace('6220', '')  # mhamd
        numbers = '6220'
        
        # تركيبات محتملة للإيميل الأول (m*******<EMAIL>)
        # الطول المطلوب: 7 أحرف بين m و 0
        possible_combinations_7 = [
            f"hamd{numbers[:-1]}",  # mhamd622 + 0
            f"hammad{numbers[:2]}",  # mhammad62 + 0
            f"ohammed{numbers[0]}",  # mohammed6 + 0
            f"ustafa{numbers[:2]}",  # mustafa62 + 0
        ]
        
        for combo in possible_combinations_7:
            if len(combo) == 7:
                email = f"m{combo}<EMAIL>"
                likely_emails.append(email)
        
        # تركيبات محتملة للإيميل الثاني (m*************<EMAIL>)
        # الطول المطلوب: 13 حرف بين m و 7
        possible_combinations_13 = [
            f"hamd{numbers}hamd{numbers[:3]}",  # mhamd6220hamd622 + 7
            f"ohammed{numbers}ali",  # mohammed6220ali + 7
            f"ustafa{numbers}ahmad",  # mustafa6220ahmad + 7
        ]
        
        for combo in possible_combinations_13:
            if len(combo) == 13:
                email = f"m{combo}<EMAIL>"
                likely_emails.append(email)
        
        return likely_emails

    def analyze_username_based_phones(self, username):
        """تحليل أرقام الهواتف بناءً على اسم المستخدم"""
        likely_phones = []
        
        # استخراج الأرقام من اسم المستخدم
        numbers = '6220'
        
        # أرقام يمنية محتملة
        yemen_phones = [
            f"+967-66-{numbers}-123",
            f"+967-66-{numbers}-456",
            f"+967-66-{numbers}-789",
            f"+967-66-123-{numbers}",
            f"+967-66-456-{numbers}",
        ]
        
        # أرقام سعودية محتملة
        saudi_phones = [
            f"+966-75-{numbers}-123",
            f"+966-75-{numbers}-456",
            f"+966-75-123-{numbers}",
            f"+966-50-{numbers}-43",
            f"+966-55-{numbers}-43",
        ]
        
        likely_phones.extend(yemen_phones)
        likely_phones.extend(saudi_phones)
        
        return likely_phones

    def save_pattern_analysis(self, pattern_type, pattern_data, possible_values):
        """حفظ تحليل الأنماط"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO pattern_analysis (
                    pattern_type, original_pattern, analyzed_pattern,
                    confidence_score, possible_values, country_code,
                    provider, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                pattern_type,
                pattern_data.get('pattern', ''),
                json.dumps(pattern_data, ensure_ascii=False),
                0.8,  # نسبة ثقة
                json.dumps(possible_values[:20], ensure_ascii=False),  # أول 20 قيمة
                pattern_data.get('country_code', ''),
                pattern_data.get('provider', ''),
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"   ⚠️ خطأ في حفظ التحليل: {e}")

    def display_final_analysis(self):
        """عرض التحليل النهائي"""
        print("\n" + "="*70)
        print("🔍 تقرير تحليل الأنماط المشفرة")
        print("="*70)
        
        print(f"\n📅 تاريخ التحليل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # ملخص أرقام الهواتف
        print(f"\n📱 ملخص أرقام الهواتف:")
        print(f"   🔍 أنماط مكتشفة: {len(self.extracted_patterns['phones'])}")
        print(f"   🎯 أرقام محتملة: {len(self.extracted_patterns['possible_phones'])}")
        
        # ملخص الإيميلات
        print(f"\n📧 ملخص الإيميلات:")
        print(f"   🔍 أنماط مكتشفة: {len(self.extracted_patterns['emails'])}")
        print(f"   🎯 إيميلات محتملة: {len(self.extracted_patterns['possible_emails'])}")
        
        # أهم النتائج
        print(f"\n🏆 أهم النتائج المحتملة:")
        
        # أرقام الهواتف الأكثر احتمالاً
        if self.extracted_patterns['possible_phones']:
            print(f"   📱 أرقام الهواتف الأكثر احتمالاً:")
            for i, phone in enumerate(self.extracted_patterns['possible_phones'][:5], 1):
                print(f"      {i}. {phone}")
        
        # الإيميلات الأكثر احتمالاً
        if self.extracted_patterns['possible_emails']:
            print(f"   📧 الإيميلات الأكثر احتمالاً:")
            for i, email in enumerate(self.extracted_patterns['possible_emails'][:5], 1):
                print(f"      {i}. {email}")
        
        print("\n" + "="*70)

    def run_analysis(self):
        """تشغيل التحليل الكامل"""
        print("🚀 بدء تحليل الأنماط المشفرة...")
        
        try:
            # تحليل أنماط الهواتف
            self.analyze_phone_patterns()
            
            # تحليل أنماط الإيميلات
            self.analyze_email_patterns()
            
            # تحليل متقدم
            self.advanced_pattern_analysis()
            
            # عرض النتائج النهائية
            self.display_final_analysis()
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في التحليل: {e}")
            return False

if __name__ == "__main__":
    print("🔍 Pattern Analyzer")
    print("⚠️ تحذير: للأغراض التعليمية والبحثية فقط!")
    print("📱 تحليل الأنماط المشفرة من صورة Instagram")
    
    analyzer = PatternAnalyzer()
    analyzer.run_analysis()
