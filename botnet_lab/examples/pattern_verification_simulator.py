#!/usr/bin/env python3
"""
Pattern Verification Simulator - محاكي التحقق من الأنماط
Simulates Instagram recovery validation based on image analysis

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import json
import sqlite3
import re
import itertools
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class PatternVerificationSimulator:
    def __init__(self):
        self.image_analysis = {
            'emails_detected': 2,
            'phones_detected': 3,
            'countries': ['Yemen (+967)', 'Saudi Arabia (+966)'],
            'email_patterns': [
                {'pattern': 'm*******<EMAIL>', 'length': 9, 'start': 'm', 'end': '0'},
                {'pattern': 'm*************<EMAIL>', 'length': 15, 'start': 'm', 'end': '7'}
            ],
            'phone_patterns': [
                {'pattern': '66** *** **+967', 'country': 'Yemen', 'start_digits': '66', 'country_code': '+967'},
                {'pattern': '75** *** **+966', 'country': 'Saudi', 'start_digits': '75', 'country_code': '+966'},
                {'pattern': '*** **+966 43**', 'country': 'Saudi', 'end_digits': '43', 'country_code': '+966'}
            ]
        }
        
        self.verification_results = {
            'matched_emails': [],
            'matched_phones': [],
            'confidence_scores': {},
            'validation_summary': {}
        }
        
        # قاعدة البيانات
        self.db_name = "pattern_verification.db"
        self.init_database()
        
        print("🔍 Pattern Verification Simulator")
        print("=" * 70)
        print("📸 محاكي التحقق من الأنماط بناءً على تحليل الصورة")
        print("✅ التحقق من تطابق الإيميلات والأرقام")
        print("=" * 70)

    def init_database(self):
        """إنشاء قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS pattern_verification (
                    id INTEGER PRIMARY KEY,
                    username TEXT,
                    email_pattern TEXT,
                    phone_pattern TEXT,
                    match_status TEXT,
                    confidence_score REAL,
                    verification_details TEXT,
                    timestamp TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def display_image_analysis(self):
        """عرض تحليل الصورة"""
        print("\n📸 تحليل الصورة المرجعية:")
        print("=" * 50)
        
        print(f"📧 إيميلات مكتشفة: {self.image_analysis['emails_detected']}")
        for i, email in enumerate(self.image_analysis['email_patterns'], 1):
            print(f"   {i}. {email['pattern']}")
            print(f"      📏 الطول: {email['length']} أحرف")
            print(f"      🔤 يبدأ بـ: {email['start']}, ينتهي بـ: {email['end']}")
        
        print(f"\n📱 أرقام مكتشفة: {self.image_analysis['phones_detected']}")
        for i, phone in enumerate(self.image_analysis['phone_patterns'], 1):
            print(f"   {i}. {phone['pattern']}")
            print(f"      🌍 البلد: {phone['country']}")
            if 'start_digits' in phone:
                print(f"      🔢 يبدأ بـ: {phone['start_digits']}")
            if 'end_digits' in phone:
                print(f"      🔢 ينتهي بـ: {phone['end_digits']}")
        
        print(f"\n🌍 البلدان المكتشفة: {', '.join(self.image_analysis['countries'])}")

    def generate_candidate_emails(self, username):
        """توليد الإيميلات المرشحة بناءً على اسم المستخدم"""
        candidates = []
        
        # استخراج الأجزاء من اسم المستخدم
        base_name = re.sub(r'\d+', '', username)  # إزالة الأرقام
        numbers = re.findall(r'\d+', username)    # استخراج الأرقام
        
        for email_pattern in self.image_analysis['email_patterns']:
            pattern_length = email_pattern['length'] - 2  # طرح m و الرقم الأخير
            start_char = email_pattern['start']
            end_char = email_pattern['end']
            
            # تركيبات محتملة
            possible_combinations = []
            
            # تركيبة 1: اسم + أرقام
            if numbers:
                for num in numbers:
                    combo = base_name + num
                    if len(combo) == pattern_length:
                        email = f"{start_char}{combo}{end_char}@gmail.com"
                        candidates.append({
                            'email': email,
                            'pattern': email_pattern['pattern'],
                            'confidence': 0.9,
                            'method': 'username_numbers'
                        })
            
            # تركيبة 2: اسم مكرر
            if len(base_name) * 2 == pattern_length:
                email = f"{start_char}{base_name}{base_name}{end_char}@gmail.com"
                candidates.append({
                    'email': email,
                    'pattern': email_pattern['pattern'],
                    'confidence': 0.7,
                    'method': 'name_repeat'
                })
            
            # تركيبة 3: اسم + سنوات شائعة
            common_years = ['2020', '2021', '2022', '2023', '2024']
            for year in common_years:
                combo = base_name + year
                if len(combo) == pattern_length:
                    email = f"{start_char}{combo}{end_char}@gmail.com"
                    candidates.append({
                        'email': email,
                        'pattern': email_pattern['pattern'],
                        'confidence': 0.6,
                        'method': 'name_year'
                    })
        
        return candidates

    def generate_candidate_phones(self, username):
        """توليد أرقام الهواتف المرشحة"""
        candidates = []
        
        # استخراج الأرقام من اسم المستخدم
        numbers = re.findall(r'\d+', username)
        
        for phone_pattern in self.image_analysis['phone_patterns']:
            country = phone_pattern['country']
            country_code = phone_pattern['country_code']
            
            if 'start_digits' in phone_pattern:
                start_digits = phone_pattern['start_digits']
                
                # توليد أرقام محتملة
                if numbers:
                    for num in numbers:
                        # أنماط مختلفة للرقم
                        phone_variations = [
                            f"{country_code}-{start_digits}-{num}-123",
                            f"{country_code}-{start_digits}-{num}-456",
                            f"{country_code}-{start_digits}-123-{num}",
                            f"{country_code}-{start_digits}-{num[:2]}-{num[2:]}",
                        ]
                        
                        for phone in phone_variations:
                            candidates.append({
                                'phone': phone,
                                'pattern': phone_pattern['pattern'],
                                'confidence': 0.8,
                                'country': country,
                                'method': 'username_numbers'
                            })
            
            elif 'end_digits' in phone_pattern:
                end_digits = phone_pattern['end_digits']
                
                # أرقام تنتهي بالرقم المحدد
                common_prefixes = ['50', '51', '52', '53', '54', '55']
                for prefix in common_prefixes:
                    if numbers:
                        for num in numbers:
                            phone = f"{country_code}-{prefix}-{num}-{end_digits}"
                            candidates.append({
                                'phone': phone,
                                'pattern': phone_pattern['pattern'],
                                'confidence': 0.7,
                                'country': country,
                                'method': 'end_digits'
                            })
        
        return candidates

    def verify_pattern_match(self, candidate, pattern_type):
        """التحقق من تطابق النمط"""
        if pattern_type == 'email':
            # التحقق من تطابق نمط الإيميل
            email = candidate['email']
            pattern = candidate['pattern']
            
            # استخراج الأجزاء المرئية من النمط
            pattern_parts = re.match(r'(.)(\*+)(.)@gmail\.com', pattern)
            if pattern_parts:
                start_char = pattern_parts.group(1)
                hidden_length = len(pattern_parts.group(2))
                end_char = pattern_parts.group(3)
                
                # التحقق من التطابق
                email_parts = re.match(r'(.)(.+)(.)@gmail\.com', email)
                if email_parts:
                    email_start = email_parts.group(1)
                    email_middle = email_parts.group(2)
                    email_end = email_parts.group(3)
                    
                    if (email_start == start_char and 
                        email_end == end_char and 
                        len(email_middle) == hidden_length):
                        return True
        
        elif pattern_type == 'phone':
            # التحقق من تطابق نمط الرقم
            phone = candidate['phone']
            pattern = candidate['pattern']
            
            # تحليل النمط
            if '66**' in pattern and '+967' in pattern:
                return '66' in phone and '+967' in phone
            elif '75**' in pattern and '+966' in pattern:
                return '75' in phone and '+966' in phone
            elif '43**' in pattern and '+966' in pattern:
                return '43' in phone and '+966' in phone
        
        return False

    def run_verification_simulation(self):
        """تشغيل محاكاة التحقق"""
        print("\n🚀 بدء محاكاة التحقق...")
        
        # عرض تحليل الصورة
        self.display_image_analysis()
        
        # قائمة أسماء المستخدمين للاختبار
        test_usernames = [
            'mhamd6220',
            'mohammed6220',
            'ahmad6220',
            'mhamd1230',
            'mali2020'
        ]
        
        verification_results = []
        
        for username in test_usernames:
            print(f"\n🔍 التحقق من: {username}")
            
            # توليد الإيميلات المرشحة
            email_candidates = self.generate_candidate_emails(username)
            
            # توليد أرقام الهواتف المرشحة
            phone_candidates = self.generate_candidate_phones(username)
            
            # التحقق من التطابق
            matched_emails = []
            matched_phones = []
            
            for candidate in email_candidates:
                if self.verify_pattern_match(candidate, 'email'):
                    matched_emails.append(candidate)
                    print(f"   ✅ إيميل متطابق: {candidate['email']} (ثقة: {candidate['confidence']:.1%})")
            
            for candidate in phone_candidates:
                if self.verify_pattern_match(candidate, 'phone'):
                    matched_phones.append(candidate)
                    print(f"   ✅ رقم متطابق: {candidate['phone']} (ثقة: {candidate['confidence']:.1%})")
            
            # حساب نسبة التطابق الإجمالية
            total_expected = self.image_analysis['emails_detected'] + self.image_analysis['phones_detected']
            total_matched = len(matched_emails) + len(matched_phones)
            match_percentage = (total_matched / total_expected) * 100 if total_expected > 0 else 0
            
            result = {
                'username': username,
                'matched_emails': matched_emails,
                'matched_phones': matched_phones,
                'match_percentage': match_percentage,
                'timestamp': datetime.now().isoformat()
            }
            
            verification_results.append(result)
            
            # حفظ النتيجة
            self.save_verification_result(result)
            
            print(f"   📊 نسبة التطابق الإجمالية: {match_percentage:.1f}%")
        
        # عرض التقرير النهائي
        self.display_verification_report(verification_results)
        
        return verification_results

    def save_verification_result(self, result):
        """حفظ نتيجة التحقق"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            # حفظ الإيميلات المتطابقة
            for email in result['matched_emails']:
                cursor.execute('''
                    INSERT INTO pattern_verification (
                        username, email_pattern, phone_pattern, match_status,
                        confidence_score, verification_details, timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    result['username'],
                    email['email'],
                    '',
                    'matched',
                    email['confidence'],
                    json.dumps(email, ensure_ascii=False),
                    result['timestamp']
                ))
            
            # حفظ الأرقام المتطابقة
            for phone in result['matched_phones']:
                cursor.execute('''
                    INSERT INTO pattern_verification (
                        username, email_pattern, phone_pattern, match_status,
                        confidence_score, verification_details, timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    result['username'],
                    '',
                    phone['phone'],
                    'matched',
                    phone['confidence'],
                    json.dumps(phone, ensure_ascii=False),
                    result['timestamp']
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"⚠️ خطأ في حفظ النتيجة: {e}")

    def display_verification_report(self, verification_results):
        """عرض تقرير التحقق النهائي"""
        print("\n" + "="*70)
        print("📊 تقرير التحقق من الأنماط النهائي")
        print("="*70)
        
        print(f"\n📅 تاريخ التحقق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # ترتيب النتائج حسب نسبة التطابق
        sorted_results = sorted(verification_results, key=lambda x: x['match_percentage'], reverse=True)
        
        print(f"\n🏆 أفضل النتائج:")
        for i, result in enumerate(sorted_results, 1):
            print(f"\n   {i}. {result['username']} - تطابق: {result['match_percentage']:.1f}%")
            
            if result['matched_emails']:
                print(f"      📧 إيميلات متطابقة ({len(result['matched_emails'])}):")
                for email in result['matched_emails']:
                    print(f"         • {email['email']} (ثقة: {email['confidence']:.1%})")
            
            if result['matched_phones']:
                print(f"      📱 أرقام متطابقة ({len(result['matched_phones'])}):")
                for phone in result['matched_phones']:
                    print(f"         • {phone['phone']} (ثقة: {phone['confidence']:.1%})")
        
        # إحصائيات عامة
        total_emails = sum(len(r['matched_emails']) for r in verification_results)
        total_phones = sum(len(r['matched_phones']) for r in verification_results)
        avg_match = sum(r['match_percentage'] for r in verification_results) / len(verification_results)
        
        print(f"\n📈 إحصائيات عامة:")
        print(f"   📧 إجمالي الإيميلات المتطابقة: {total_emails}")
        print(f"   📱 إجمالي الأرقام المتطابقة: {total_phones}")
        print(f"   📊 متوسط نسبة التطابق: {avg_match:.1f}%")
        
        print("\n" + "="*70)

if __name__ == "__main__":
    print("🔍 Pattern Verification Simulator")
    print("⚠️ تحذير: للأغراض التعليمية والبحثية فقط!")
    print("📸 محاكي التحقق من الأنماط بناءً على تحليل الصورة")
    
    simulator = PatternVerificationSimulator()
    results = simulator.run_verification_simulation()
    
    if results:
        print("\n✅ تم التحقق بنجاح!")
        print("📊 النتائج محفوظة في قاعدة البيانات")
    else:
        print("\n❌ فشل في عملية التحقق")
