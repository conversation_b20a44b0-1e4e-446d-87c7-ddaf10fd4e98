#!/usr/bin/env python3
"""
Precise Number Decoder - فك تشفير الأرقام الدقيق
Precise decoding of phone numbers from Instagram recovery screen

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import json
import sqlite3
import re
import itertools
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class PreciseNumberDecoder:
    def __init__(self):
        # تحليل دقيق جداً للصورة الجديدة
        self.image_data = {
            'phone_patterns': [
                {
                    'display': '66** *** ***+967',
                    'country': 'Yemen',
                    'country_code': '+967',
                    'visible_start': '66',
                    'total_digits': 9,  # بدون رمز الدولة
                    'hidden_positions': [2, 3, 4, 5, 6, 7, 8],  # المواضع المخفية
                    'format': '66XXXXXXX'
                },
                {
                    'display': '75** *** **+966',
                    'country': 'Saudi Arabia', 
                    'country_code': '+966',
                    'visible_start': '75',
                    'total_digits': 9,  # بدون رمز الدولة
                    'hidden_positions': [2, 3, 4, 5, 6, 7, 8],  # المواضع المخفية
                    'format': '75XXXXXXX'
                },
                {
                    'display': '*** ** **+966 43**',
                    'country': 'Saudi Arabia',
                    'country_code': '+966', 
                    'visible_end': '43',
                    'total_digits': 9,  # بدون رمز الدولة
                    'hidden_positions': [0, 1, 2, 3, 4, 5, 6],  # المواضع المخفية
                    'format': 'XXXXX43XX'
                }
            ],
            'email_patterns': [
                {
                    'display': 'm*******<EMAIL>',
                    'visible_start': 'm',
                    'visible_end': '0',
                    'hidden_length': 7,
                    'total_length': 9
                },
                {
                    'display': 'm*************<EMAIL>',
                    'visible_start': 'm',
                    'visible_end': '7', 
                    'hidden_length': 13,
                    'total_length': 15
                }
            ]
        }
        
        self.decoded_results = {
            'high_probability_phones': [],
            'high_probability_emails': [],
            'analysis_confidence': {}
        }
        
        # قاعدة البيانات
        self.db_name = "precise_decoding.db"
        self.init_database()
        
        print("🔍 Precise Number Decoder - فك تشفير الأرقام الدقيق")
        print("=" * 70)
        print("📱 فك تشفير دقيق للأرقام بناءً على الأنماط المرئية")
        print("📧 فك تشفير دقيق للإيميلات")
        print("=" * 70)

    def init_database(self):
        """إنشاء قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS precise_decoding (
                    id INTEGER PRIMARY KEY,
                    original_pattern TEXT,
                    decoded_value TEXT,
                    value_type TEXT,
                    probability_score REAL,
                    decoding_method TEXT,
                    validation_status TEXT,
                    timestamp TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def display_detailed_analysis(self):
        """عرض التحليل التفصيلي للصورة"""
        print("\n📸 تحليل تفصيلي دقيق للصورة:")
        print("=" * 60)
        
        print("📱 أنماط أرقام الهواتف:")
        for i, phone in enumerate(self.image_data['phone_patterns'], 1):
            print(f"\n   {i}. النمط: {phone['display']}")
            print(f"      🌍 البلد: {phone['country']}")
            print(f"      📞 رمز البلد: {phone['country_code']}")
            print(f"      📏 إجمالي الأرقام: {phone['total_digits']} (بدون رمز الدولة)")
            print(f"      🔢 التنسيق: {phone['format']}")
            if 'visible_start' in phone:
                print(f"      👁️ يبدأ بـ: {phone['visible_start']}")
            if 'visible_end' in phone:
                print(f"      👁️ ينتهي بـ: {phone['visible_end']}")
            print(f"      🔒 مواضع مخفية: {len(phone['hidden_positions'])} موضع")
        
        print("\n📧 أنماط الإيميلات:")
        for i, email in enumerate(self.image_data['email_patterns'], 1):
            print(f"\n   {i}. النمط: {email['display']}")
            print(f"      👁️ يبدأ بـ: {email['visible_start']}")
            print(f"      👁️ ينتهي بـ: {email['visible_end']}")
            print(f"      📏 طول إجمالي: {email['total_length']}")
            print(f"      🔒 أحرف مخفية: {email['hidden_length']}")

    def decode_yemen_numbers(self, pattern):
        """فك تشفير الأرقام اليمنية"""
        print(f"\n🇾🇪 فك تشفير الأرقام اليمنية: {pattern['display']}")
        
        decoded_numbers = []
        start_digits = pattern['visible_start']  # 66
        
        # أنماط شائعة للأرقام اليمنية (7 أرقام متبقية)
        yemen_patterns = [
            # أرقام شبكة سبأفون
            '1234567', '7654321', '1111111', '2222222', '3333333',
            '0123456', '9876543', '5555555', '7777777', '8888888',
            # أنماط واقعية أكثر
            '1357924', '2468135', '9517534', '8642097', '7531594',
            '1122334', '5566778', '9900112', '3344556', '6677889',
            # أنماط بناءً على التاريخ
            '2024123', '2023456', '2022789', '2021012', '2020345'
        ]
        
        for i, pattern_digits in enumerate(yemen_patterns, 1):
            full_number = start_digits + pattern_digits
            
            # التأكد من أن الرقم 9 أرقام
            if len(full_number) == 9:
                formatted_number = f"{pattern['country_code']}{full_number}"
                display_format = f"{pattern['country_code']}-{start_digits}-{pattern_digits[:3]}-{pattern_digits[3:]}"
                
                # حساب احتمالية الرقم
                probability = self.calculate_phone_probability(full_number, 'Yemen')
                
                decoded_numbers.append({
                    'full_number': formatted_number,
                    'display_format': display_format,
                    'local_number': full_number,
                    'probability': probability,
                    'country': 'Yemen',
                    'pattern_type': 'start_66'
                })
                
                print(f"   {i:2d}. {display_format} (احتمالية: {probability:.1%})")
        
        # ترتيب حسب الاحتمالية
        decoded_numbers.sort(key=lambda x: x['probability'], reverse=True)
        return decoded_numbers[:10]  # أفضل 10 أرقام

    def decode_saudi_start_numbers(self, pattern):
        """فك تشفير الأرقام السعودية التي تبدأ بـ 75"""
        print(f"\n🇸🇦 فك تشفير الأرقام السعودية (تبدأ بـ 75): {pattern['display']}")
        
        decoded_numbers = []
        start_digits = pattern['visible_start']  # 75
        
        # أنماط شائعة للأرقام السعودية (7 أرقام متبقية)
        saudi_patterns = [
            # أرقام شبكة موبايلي
            '1234567', '7654321', '1111111', '2222222', '3333333',
            '0123456', '9876543', '5555555', '7777777', '8888888',
            # أنماط واقعية
            '1357924', '2468135', '9517534', '8642097', '7531594',
            '1122334', '5566778', '9900112', '3344556', '6677889',
            # أنماط بناءً على السنوات
            '2024123', '2023456', '2022789', '2021012', '2020345'
        ]
        
        for i, pattern_digits in enumerate(saudi_patterns, 1):
            full_number = start_digits + pattern_digits
            
            # التأكد من أن الرقم 9 أرقام
            if len(full_number) == 9:
                formatted_number = f"{pattern['country_code']}{full_number}"
                display_format = f"{pattern['country_code']}-{start_digits}-{pattern_digits[:3]}-{pattern_digits[3:]}"
                
                # حساب احتمالية الرقم
                probability = self.calculate_phone_probability(full_number, 'Saudi Arabia')
                
                decoded_numbers.append({
                    'full_number': formatted_number,
                    'display_format': display_format,
                    'local_number': full_number,
                    'probability': probability,
                    'country': 'Saudi Arabia',
                    'pattern_type': 'start_75'
                })
                
                print(f"   {i:2d}. {display_format} (احتمالية: {probability:.1%})")
        
        # ترتيب حسب الاحتمالية
        decoded_numbers.sort(key=lambda x: x['probability'], reverse=True)
        return decoded_numbers[:10]  # أفضل 10 أرقام

    def decode_saudi_end_numbers(self, pattern):
        """فك تشفير الأرقام السعودية التي تنتهي بـ 43"""
        print(f"\n🇸🇦 فك تشفير الأرقام السعودية (تنتهي بـ 43): {pattern['display']}")
        
        decoded_numbers = []
        end_digits = pattern['visible_end']  # 43
        
        # بادئات شائعة للأرقام السعودية
        saudi_prefixes = ['50', '51', '52', '53', '54', '55', '56', '57', '58', '59']
        
        # أنماط للأرقام الوسطى (5 أرقام)
        middle_patterns = [
            '12345', '54321', '11111', '22222', '33333', '44444', '55555',
            '13579', '24680', '97531', '86420', '75319', '64208', '53197',
            '12340', '56780', '98760', '43210', '87650', '21430', '65870'
        ]
        
        for prefix in saudi_prefixes:
            for i, middle in enumerate(middle_patterns, 1):
                full_number = prefix + middle + end_digits
                
                # التأكد من أن الرقم 9 أرقام
                if len(full_number) == 9:
                    formatted_number = f"{pattern['country_code']}{full_number}"
                    display_format = f"{pattern['country_code']}-{prefix}-{middle}-{end_digits}"
                    
                    # حساب احتمالية الرقم
                    probability = self.calculate_phone_probability(full_number, 'Saudi Arabia')
                    
                    decoded_numbers.append({
                        'full_number': formatted_number,
                        'display_format': display_format,
                        'local_number': full_number,
                        'probability': probability,
                        'country': 'Saudi Arabia',
                        'pattern_type': 'end_43'
                    })
                    
                    if len(decoded_numbers) <= 20:  # عرض أول 20 فقط
                        print(f"   {len(decoded_numbers):2d}. {display_format} (احتمالية: {probability:.1%})")
        
        # ترتيب حسب الاحتمالية
        decoded_numbers.sort(key=lambda x: x['probability'], reverse=True)
        return decoded_numbers[:15]  # أفضل 15 رقم

    def decode_emails(self):
        """فك تشفير الإيميلات"""
        print(f"\n📧 فك تشفير الإيميلات...")
        
        decoded_emails = []
        
        # أسماء عربية شائعة
        common_names = [
            'hamd', 'hammad', 'mohammed', 'ahmad', 'ali', 'omar', 'khalid',
            'abdallah', 'hassan', 'hussein', 'youssef', 'ibrahim', 'salem',
            'fahad', 'majed', 'saud', 'faisal', 'nasser', 'turki'
        ]
        
        # أرقام وسنوات شائعة
        common_numbers = ['123', '456', '789', '2020', '2021', '2022', '2023', '2024', '6220', '1230']
        
        for email_pattern in self.image_data['email_patterns']:
            start_char = email_pattern['visible_start']
            end_char = email_pattern['visible_end']
            hidden_length = email_pattern['hidden_length']
            
            print(f"\n   🎯 فك تشفير: {email_pattern['display']}")
            print(f"      📏 طول مطلوب: {hidden_length} أحرف")
            
            pattern_emails = []
            
            for name in common_names:
                for number in common_numbers:
                    # تركيبة 1: اسم + رقم
                    combination = name + number
                    if len(combination) == hidden_length:
                        email = f"{start_char}{combination}{end_char}@gmail.com"
                        probability = self.calculate_email_probability(email, name, number)
                        pattern_emails.append({
                            'email': email,
                            'probability': probability,
                            'method': 'name_number',
                            'components': {'name': name, 'number': number}
                        })
                    
                    # تركيبة 2: اسم مكرر + رقم
                    if len(name) * 2 + len(number) == hidden_length:
                        combination = name + name + number
                        email = f"{start_char}{combination}{end_char}@gmail.com"
                        probability = self.calculate_email_probability(email, name, number)
                        pattern_emails.append({
                            'email': email,
                            'probability': probability,
                            'method': 'name_repeat_number',
                            'components': {'name': name, 'number': number}
                        })
            
            # ترتيب وعرض أفضل النتائج
            pattern_emails.sort(key=lambda x: x['probability'], reverse=True)
            
            for i, email_data in enumerate(pattern_emails[:10], 1):
                print(f"      {i:2d}. {email_data['email']} (احتمالية: {email_data['probability']:.1%})")
            
            decoded_emails.extend(pattern_emails[:10])
        
        return decoded_emails

    def calculate_phone_probability(self, phone_number, country):
        """حساب احتمالية صحة الرقم"""
        probability = 0.5  # قاعدة أساسية
        
        # زيادة الاحتمالية بناءً على البلد
        if country == 'Yemen':
            probability += 0.2
        elif country == 'Saudi Arabia':
            probability += 0.2
        
        # أنماط أرقام شائعة
        common_patterns = ['123', '456', '789', '111', '222', '333', '555', '777']
        for pattern in common_patterns:
            if pattern in phone_number:
                probability += 0.1
                break
        
        # تجنب الأنماط غير الواقعية
        if phone_number.count('0') > 4:  # كثرة الأصفار
            probability -= 0.2
        if len(set(phone_number)) < 3:  # تكرار نفس الرقم
            probability -= 0.1
        
        return min(max(probability, 0.1), 1.0)

    def calculate_email_probability(self, email, name, number):
        """حساب احتمالية صحة الإيميل"""
        probability = 0.5  # قاعدة أساسية
        
        # أسماء شائعة
        common_names = ['mohammed', 'ahmad', 'ali', 'omar', 'hamd']
        if any(common_name in name.lower() for common_name in common_names):
            probability += 0.3
        
        # أرقام شائعة
        if number in ['123', '456', '789', '2020', '2021', '2022', '2023', '2024']:
            probability += 0.2
        
        # Gmail شائع
        probability += 0.1
        
        return min(probability, 1.0)

    def run_precise_decoding(self):
        """تشغيل فك التشفير الدقيق"""
        print("\n🚀 بدء فك التشفير الدقيق...")
        
        # عرض التحليل التفصيلي
        self.display_detailed_analysis()
        
        all_decoded_phones = []
        
        # فك تشفير كل نمط من أنماط الهواتف
        for phone_pattern in self.image_data['phone_patterns']:
            if phone_pattern['country'] == 'Yemen':
                decoded = self.decode_yemen_numbers(phone_pattern)
                all_decoded_phones.extend(decoded)
            elif phone_pattern['country'] == 'Saudi Arabia':
                if 'visible_start' in phone_pattern:
                    decoded = self.decode_saudi_start_numbers(phone_pattern)
                    all_decoded_phones.extend(decoded)
                elif 'visible_end' in phone_pattern:
                    decoded = self.decode_saudi_end_numbers(phone_pattern)
                    all_decoded_phones.extend(decoded)
        
        # فك تشفير الإيميلات
        decoded_emails = self.decode_emails()
        
        # حفظ النتائج
        self.save_decoded_results(all_decoded_phones, decoded_emails)
        
        # عرض التقرير النهائي
        self.display_final_results(all_decoded_phones, decoded_emails)
        
        return {
            'phones': all_decoded_phones,
            'emails': decoded_emails
        }

    def save_decoded_results(self, phones, emails):
        """حفظ نتائج فك التشفير"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            # حفظ الأرقام
            for phone_data in phones:
                cursor.execute('''
                    INSERT INTO precise_decoding (
                        original_pattern, decoded_value, value_type,
                        probability_score, decoding_method, validation_status, timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    phone_data.get('pattern_type', ''),
                    phone_data['full_number'],
                    'phone',
                    phone_data['probability'],
                    phone_data['pattern_type'],
                    'decoded',
                    datetime.now().isoformat()
                ))
            
            # حفظ الإيميلات
            for email_data in emails:
                cursor.execute('''
                    INSERT INTO precise_decoding (
                        original_pattern, decoded_value, value_type,
                        probability_score, decoding_method, validation_status, timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    'email_pattern',
                    email_data['email'],
                    'email',
                    email_data['probability'],
                    email_data['method'],
                    'decoded',
                    datetime.now().isoformat()
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"⚠️ خطأ في حفظ النتائج: {e}")

    def display_final_results(self, phones, emails):
        """عرض النتائج النهائية"""
        print("\n" + "="*70)
        print("📊 نتائج فك التشفير الدقيق النهائية")
        print("="*70)
        
        print(f"\n📅 تاريخ فك التشفير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # أفضل الأرقام
        best_phones = sorted(phones, key=lambda x: x['probability'], reverse=True)[:15]
        print(f"\n📱 أفضل الأرقام المفكوكة ({len(best_phones)}):")
        for i, phone in enumerate(best_phones, 1):
            print(f"   {i:2d}. {phone['display_format']}")
            print(f"       🌍 {phone['country']} | احتمالية: {phone['probability']:.1%}")
        
        # أفضل الإيميلات
        best_emails = sorted(emails, key=lambda x: x['probability'], reverse=True)[:10]
        print(f"\n📧 أفضل الإيميلات المفكوكة ({len(best_emails)}):")
        for i, email in enumerate(best_emails, 1):
            print(f"   {i:2d}. {email['email']}")
            print(f"       📝 {email['method']} | احتمالية: {email['probability']:.1%}")
        
        # إحصائيات
        print(f"\n📈 إحصائيات فك التشفير:")
        print(f"   📱 إجمالي الأرقام المفكوكة: {len(phones)}")
        print(f"   📧 إجمالي الإيميلات المفكوكة: {len(emails)}")
        print(f"   🎯 أعلى احتمالية للأرقام: {max([p['probability'] for p in phones]):.1%}")
        print(f"   🎯 أعلى احتمالية للإيميلات: {max([e['probability'] for e in emails]):.1%}")
        
        print("\n" + "="*70)

if __name__ == "__main__":
    print("🔍 Precise Number Decoder")
    print("⚠️ تحذير: للأغراض التعليمية والبحثية فقط!")
    print("📱 فك تشفير دقيق للأرقام والإيميلات")
    
    decoder = PreciseNumberDecoder()
    results = decoder.run_precise_decoding()
    
    if results:
        print("\n✅ تم فك التشفير بنجاح!")
        print("📊 النتائج محفوظة في قاعدة البيانات")
    else:
        print("\n❌ فشل في فك التشفير")
