#!/usr/bin/env python3
"""
Precise Pattern Matcher - مطابق الأنماط الدقيق
Precise matching for Instagram recovery patterns based on image analysis

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import json
import sqlite3
import re
import itertools
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class PrecisePatternMatcher:
    def __init__(self):
        # تحليل دقيق للصورة المرسلة
        self.image_data = {
            'emails': [
                {
                    'pattern': 'm*******<EMAIL>',
                    'visible_chars': ['m', '0'],
                    'hidden_length': 7,
                    'total_length': 9,
                    'provider': 'gmail.com'
                },
                {
                    'pattern': 'm*************<EMAIL>', 
                    'visible_chars': ['m', '7'],
                    'hidden_length': 13,
                    'total_length': 15,
                    'provider': 'gmail.com'
                }
            ],
            'phones': [
                {
                    'pattern': '66** *** **+967',
                    'country': 'Yemen',
                    'country_code': '+967',
                    'visible_start': '66',
                    'format': 'XX** *** **+967'
                },
                {
                    'pattern': '75** *** **+966',
                    'country': 'Saudi Arabia', 
                    'country_code': '+966',
                    'visible_start': '75',
                    'format': 'XX** *** **+966'
                },
                {
                    'pattern': '*** **+966 43**',
                    'country': 'Saudi Arabia',
                    'country_code': '+966', 
                    'visible_end': '43',
                    'format': '*** **+966 XX**'
                }
            ]
        }
        
        self.matched_results = {
            'confirmed_emails': [],
            'confirmed_phones': [],
            'confidence_analysis': {}
        }
        
        # قاعدة البيانات
        self.db_name = "precise_matching.db"
        self.init_database()
        
        print("🎯 Precise Pattern Matcher - مطابق الأنماط الدقيق")
        print("=" * 70)
        print("📸 مطابقة دقيقة بناءً على تحليل الصورة")
        print("✅ التحقق من الإيميلات والأرقام بدقة عالية")
        print("=" * 70)

    def init_database(self):
        """إنشاء قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS precise_matches (
                    id INTEGER PRIMARY KEY,
                    username TEXT,
                    match_type TEXT,
                    original_pattern TEXT,
                    matched_value TEXT,
                    confidence_score REAL,
                    match_method TEXT,
                    validation_status TEXT,
                    timestamp TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def display_image_analysis(self):
        """عرض تحليل دقيق للصورة"""
        print("\n📸 تحليل دقيق للصورة المرجعية:")
        print("=" * 60)
        
        print("📧 الإيميلات المكتشفة:")
        for i, email in enumerate(self.image_data['emails'], 1):
            print(f"   {i}. {email['pattern']}")
            print(f"      🔤 الأحرف المرئية: {email['visible_chars']}")
            print(f"      📏 الأحرف المخفية: {email['hidden_length']}")
            print(f"      📮 المزود: {email['provider']}")
        
        print("\n📱 الأرقام المكتشفة:")
        for i, phone in enumerate(self.image_data['phones'], 1):
            print(f"   {i}. {phone['pattern']}")
            print(f"      🌍 البلد: {phone['country']}")
            print(f"      📞 رمز البلد: {phone['country_code']}")
            if 'visible_start' in phone:
                print(f"      🔢 يبدأ بـ: {phone['visible_start']}")
            if 'visible_end' in phone:
                print(f"      🔢 ينتهي بـ: {phone['visible_end']}")

    def generate_precise_emails(self, username):
        """توليد إيميلات دقيقة بناءً على اسم المستخدم"""
        precise_emails = []
        
        # استخراج المكونات من اسم المستخدم
        name_part = re.sub(r'\d+', '', username)  # الجزء النصي
        number_part = re.findall(r'\d+', username)  # الأرقام
        
        print(f"\n🔍 توليد إيميلات دقيقة لـ: {username}")
        print(f"   📝 الجزء النصي: {name_part}")
        print(f"   🔢 الأرقام: {number_part}")
        
        for email_pattern in self.image_data['emails']:
            start_char = email_pattern['visible_chars'][0]
            end_char = email_pattern['visible_chars'][1]
            hidden_length = email_pattern['hidden_length']
            
            print(f"\n   🎯 مطابقة النمط: {email_pattern['pattern']}")
            print(f"      📏 طول مطلوب: {hidden_length} أحرف")
            
            # طريقة 1: اسم + أرقام
            if number_part:
                for num in number_part:
                    combination = name_part + num
                    if len(combination) == hidden_length:
                        email = f"{start_char}{combination}{end_char}@gmail.com"
                        confidence = self.calculate_email_confidence(email, username, 'name_numbers')
                        precise_emails.append({
                            'email': email,
                            'pattern': email_pattern['pattern'],
                            'confidence': confidence,
                            'method': 'name_numbers',
                            'components': {'name': name_part, 'number': num}
                        })
                        print(f"         ✅ {email} (ثقة: {confidence:.1%})")
            
            # طريقة 2: اسم مكرر
            if len(name_part) * 2 == hidden_length:
                email = f"{start_char}{name_part}{name_part}{end_char}@gmail.com"
                confidence = self.calculate_email_confidence(email, username, 'name_repeat')
                precise_emails.append({
                    'email': email,
                    'pattern': email_pattern['pattern'],
                    'confidence': confidence,
                    'method': 'name_repeat',
                    'components': {'name': name_part}
                })
                print(f"         ✅ {email} (ثقة: {confidence:.1%})")
            
            # طريقة 3: تركيبات ذكية
            smart_combinations = self.generate_smart_combinations(name_part, number_part, hidden_length)
            for combo in smart_combinations:
                if len(combo) == hidden_length:
                    email = f"{start_char}{combo}{end_char}@gmail.com"
                    confidence = self.calculate_email_confidence(email, username, 'smart_combo')
                    precise_emails.append({
                        'email': email,
                        'pattern': email_pattern['pattern'],
                        'confidence': confidence,
                        'method': 'smart_combo',
                        'components': {'combination': combo}
                    })
                    print(f"         ✅ {email} (ثقة: {confidence:.1%})")
        
        return precise_emails

    def generate_smart_combinations(self, name_part, number_part, target_length):
        """توليد تركيبات ذكية"""
        combinations = []
        
        # تركيبات مع سنوات شائعة
        common_years = ['20', '21', '22', '23', '24', '2020', '2021', '2022', '2023', '2024']
        for year in common_years:
            combo = name_part + year
            if len(combo) <= target_length:
                combinations.append(combo)
        
        # تركيبات مع أرقام شائعة
        common_numbers = ['123', '456', '789', '111', '222', '333']
        for num in common_numbers:
            combo = name_part + num
            if len(combo) <= target_length:
                combinations.append(combo)
        
        # تركيبات مع الأرقام الموجودة
        if number_part:
            for num in number_part:
                # تقسيم الرقم
                if len(num) > 2:
                    parts = [num[:2], num[2:], num[:3], num[3:]]
                    for part in parts:
                        if part:
                            combo = name_part + part
                            if len(combo) <= target_length:
                                combinations.append(combo)
        
        return list(set(combinations))  # إزالة التكرارات

    def generate_precise_phones(self, username):
        """توليد أرقام هواتف دقيقة"""
        precise_phones = []
        
        # استخراج الأرقام من اسم المستخدم
        numbers = re.findall(r'\d+', username)
        
        print(f"\n📱 توليد أرقام دقيقة لـ: {username}")
        print(f"   🔢 الأرقام المستخرجة: {numbers}")
        
        for phone_pattern in self.image_data['phones']:
            country = phone_pattern['country']
            country_code = phone_pattern['country_code']
            
            print(f"\n   🎯 مطابقة النمط: {phone_pattern['pattern']}")
            print(f"      🌍 البلد: {country}")
            
            if 'visible_start' in phone_pattern:
                # أرقام تبدأ برقم محدد
                start_digits = phone_pattern['visible_start']
                
                if numbers:
                    for num in numbers:
                        # تنسيقات مختلفة للرقم
                        phone_formats = [
                            f"{country_code}-{start_digits}-{num}-123",
                            f"{country_code}-{start_digits}-{num}-456", 
                            f"{country_code}-{start_digits}-{num}-789",
                            f"{country_code}-{start_digits}-123-{num}",
                            f"{country_code}-{start_digits}-456-{num}",
                        ]
                        
                        # إضافة تنسيقات بتقسيم الرقم
                        if len(num) >= 4:
                            mid = len(num) // 2
                            part1 = num[:mid]
                            part2 = num[mid:]
                            phone_formats.extend([
                                f"{country_code}-{start_digits}-{part1}-{part2}",
                                f"{country_code}-{start_digits}-{part2}-{part1}"
                            ])
                        
                        for phone in phone_formats:
                            confidence = self.calculate_phone_confidence(phone, username, country)
                            precise_phones.append({
                                'phone': phone,
                                'pattern': phone_pattern['pattern'],
                                'confidence': confidence,
                                'country': country,
                                'method': 'start_digits',
                                'components': {'start': start_digits, 'number': num}
                            })
                            print(f"         ✅ {phone} (ثقة: {confidence:.1%})")
            
            elif 'visible_end' in phone_pattern:
                # أرقام تنتهي برقم محدد
                end_digits = phone_pattern['visible_end']
                
                # بادئات شائعة للأرقام السعودية
                common_prefixes = ['50', '51', '52', '53', '54', '55', '56', '57', '58', '59']
                
                if numbers:
                    for prefix in common_prefixes:
                        for num in numbers:
                            phone_formats = [
                                f"{country_code}-{prefix}-{num}-{end_digits}",
                                f"{country_code}-{prefix}-{end_digits}-{num}",
                            ]
                            
                            for phone in phone_formats:
                                confidence = self.calculate_phone_confidence(phone, username, country)
                                precise_phones.append({
                                    'phone': phone,
                                    'pattern': phone_pattern['pattern'],
                                    'confidence': confidence,
                                    'country': country,
                                    'method': 'end_digits',
                                    'components': {'prefix': prefix, 'number': num, 'end': end_digits}
                                })
                                print(f"         ✅ {phone} (ثقة: {confidence:.1%})")
        
        return precise_phones

    def calculate_email_confidence(self, email, username, method):
        """حساب مستوى الثقة للإيميل"""
        confidence = 0.5  # قاعدة أساسية
        
        # زيادة الثقة بناءً على الطريقة
        if method == 'name_numbers':
            confidence += 0.4  # أعلى ثقة
        elif method == 'name_repeat':
            confidence += 0.3
        elif method == 'smart_combo':
            confidence += 0.2
        
        # زيادة الثقة إذا كان الإيميل يحتوي على أجزاء من اسم المستخدم
        username_parts = re.findall(r'[a-zA-Z]+|\d+', username)
        for part in username_parts:
            if part.lower() in email.lower():
                confidence += 0.1
        
        return min(confidence, 1.0)

    def calculate_phone_confidence(self, phone, username, country):
        """حساب مستوى الثقة للرقم"""
        confidence = 0.6  # قاعدة أساسية
        
        # زيادة الثقة بناءً على البلد
        if country == 'Yemen':
            confidence += 0.2
        elif country == 'Saudi Arabia':
            confidence += 0.2
        
        # زيادة الثقة إذا كان الرقم يحتوي على أرقام من اسم المستخدم
        username_numbers = re.findall(r'\d+', username)
        for num in username_numbers:
            if num in phone:
                confidence += 0.2
                break
        
        return min(confidence, 1.0)

    def run_precise_matching(self):
        """تشغيل المطابقة الدقيقة"""
        print("\n🚀 بدء المطابقة الدقيقة...")
        
        # عرض تحليل الصورة
        self.display_image_analysis()
        
        # قائمة أسماء المستخدمين للاختبار
        test_usernames = [
            'mhamd6220',
            'mohammed6220',
            'ahmad6220'
        ]
        
        all_results = []
        
        for username in test_usernames:
            print(f"\n{'='*60}")
            print(f"🔍 المطابقة الدقيقة لـ: {username}")
            print(f"{'='*60}")
            
            # توليد الإيميلات الدقيقة
            precise_emails = self.generate_precise_emails(username)
            
            # توليد الأرقام الدقيقة
            precise_phones = self.generate_precise_phones(username)
            
            # تحليل النتائج
            result = {
                'username': username,
                'emails': precise_emails,
                'phones': precise_phones,
                'total_matches': len(precise_emails) + len(precise_phones),
                'timestamp': datetime.now().isoformat()
            }
            
            all_results.append(result)
            
            # حفظ النتائج
            self.save_precise_results(result)
            
            print(f"\n📊 ملخص النتائج لـ {username}:")
            print(f"   📧 إيميلات مطابقة: {len(precise_emails)}")
            print(f"   📱 أرقام مطابقة: {len(precise_phones)}")
            print(f"   🎯 إجمالي المطابقات: {result['total_matches']}")
        
        # عرض التقرير النهائي
        self.display_final_report(all_results)
        
        return all_results

    def save_precise_results(self, result):
        """حفظ النتائج الدقيقة"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            # حفظ الإيميلات
            for email_data in result['emails']:
                cursor.execute('''
                    INSERT INTO precise_matches (
                        username, match_type, original_pattern, matched_value,
                        confidence_score, match_method, validation_status, timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    result['username'],
                    'email',
                    email_data['pattern'],
                    email_data['email'],
                    email_data['confidence'],
                    email_data['method'],
                    'generated',
                    result['timestamp']
                ))
            
            # حفظ الأرقام
            for phone_data in result['phones']:
                cursor.execute('''
                    INSERT INTO precise_matches (
                        username, match_type, original_pattern, matched_value,
                        confidence_score, match_method, validation_status, timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    result['username'],
                    'phone',
                    phone_data['pattern'],
                    phone_data['phone'],
                    phone_data['confidence'],
                    phone_data['method'],
                    'generated',
                    result['timestamp']
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"⚠️ خطأ في حفظ النتائج: {e}")

    def display_final_report(self, all_results):
        """عرض التقرير النهائي"""
        print("\n" + "="*70)
        print("📊 تقرير المطابقة الدقيقة النهائي")
        print("="*70)
        
        print(f"\n📅 تاريخ التحليل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # ترتيب النتائج حسب عدد المطابقات
        sorted_results = sorted(all_results, key=lambda x: x['total_matches'], reverse=True)
        
        print(f"\n🏆 أفضل المطابقات:")
        for i, result in enumerate(sorted_results, 1):
            print(f"\n   {i}. {result['username']} - إجمالي: {result['total_matches']} مطابقة")
            
            # أفضل الإيميلات
            if result['emails']:
                best_emails = sorted(result['emails'], key=lambda x: x['confidence'], reverse=True)[:3]
                print(f"      📧 أفضل الإيميلات:")
                for email in best_emails:
                    print(f"         • {email['email']} (ثقة: {email['confidence']:.1%})")
            
            # أفضل الأرقام
            if result['phones']:
                best_phones = sorted(result['phones'], key=lambda x: x['confidence'], reverse=True)[:3]
                print(f"      📱 أفضل الأرقام:")
                for phone in best_phones:
                    print(f"         • {phone['phone']} (ثقة: {phone['confidence']:.1%})")
        
        # إحصائيات عامة
        total_emails = sum(len(r['emails']) for r in all_results)
        total_phones = sum(len(r['phones']) for r in all_results)
        
        print(f"\n📈 إحصائيات عامة:")
        print(f"   📧 إجمالي الإيميلات المولدة: {total_emails}")
        print(f"   📱 إجمالي الأرقام المولدة: {total_phones}")
        print(f"   👥 أسماء المستخدمين المختبرة: {len(all_results)}")
        
        print("\n" + "="*70)

if __name__ == "__main__":
    print("🎯 Precise Pattern Matcher")
    print("⚠️ تحذير: للأغراض التعليمية والبحثية فقط!")
    print("📸 مطابقة دقيقة للأنماط بناءً على تحليل الصورة")
    
    matcher = PrecisePatternMatcher()
    results = matcher.run_precise_matching()
    
    if results:
        print("\n✅ تم التحليل الدقيق بنجاح!")
        print("📊 النتائج محفوظة في قاعدة البيانات")
    else:
        print("\n❌ فشل في التحليل الدقيق")
