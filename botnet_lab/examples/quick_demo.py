#!/usr/bin/env python3
"""
سكريبت تشغيل سريع لوحدات التواصل الاجتماعي
Quick Demo Script for Social Media Modules
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    print("🚀 مرحباً بك في وحدات التواصل الاجتماعي المطورة")
    print("=" * 60)
    print("📋 الخيارات المتاحة:")
    print("   1. 🔐 الطريقة الشرعية (Legitimate)")
    print("   2. 🕷️ الطريقة غير الشرعية (Illegitimate)")
    print("   3. 🔄 النهج المختلط (Hybrid)")
    print("   4. 📊 عرض الحالة (Status)")
    print("   5. 🚪 خروج (Exit)")
    
    while True:
        try:
            choice = input("\n🎯 اختر رقم الخيار (1-5): ").strip()
            
            if choice == "1":
                run_legitimate_demo()
            elif choice == "2":
                run_illegitimate_demo()
            elif choice == "3":
                run_hybrid_demo()
            elif choice == "4":
                show_status()
            elif choice == "5":
                print("👋 شكراً لاستخدام وحدات التواصل الاجتماعي!")
                break
            else:
                print("❌ خيار غير صحيح. اختر رقم من 1 إلى 5.")
                
        except KeyboardInterrupt:
            print("\n\n👋 تم إيقاف البرنامج بواسطة المستخدم.")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

def run_legitimate_demo():
    """تشغيل المثال الشرعي"""
    print("\n🔐 تشغيل المثال الشرعي...")
    print("-" * 40)
    
    try:
        from legitimate_usage_example import LegitimateUsageDemo
        demo = LegitimateUsageDemo()
        demo.run_legitimate_demo()
    except ImportError:
        print("❌ لم يتم العثور على ملف المثال الشرعي")
    except Exception as e:
        print(f"❌ خطأ في تشغيل المثال الشرعي: {e}")

def run_illegitimate_demo():
    """تشغيل المثال غير الشرعي"""
    print("\n🕷️ تشغيل المثال غير الشرعي...")
    print("⚠️ تحذير: للأغراض التعليمية فقط!")
    print("-" * 40)
    
    # تأكيد من المستخدم
    confirm = input("هل أنت متأكد من المتابعة؟ (y/N): ").lower()
    if confirm != 'y':
        print("تم إلغاء العملية.")
        return
    
    try:
        from illegitimate_usage_example import IllegalitimateUsageDemo
        demo = IllegalitimateUsageDemo()
        demo.run_illegitimate_demo()
    except ImportError:
        print("❌ لم يتم العثور على ملف المثال غير الشرعي")
    except Exception as e:
        print(f"❌ خطأ في تشغيل المثال غير الشرعي: {e}")

def run_hybrid_demo():
    """تشغيل النهج المختلط"""
    print("\n🔄 تشغيل النهج المختلط...")
    print("-" * 40)
    
    try:
        # محاولة الطريقة الشرعية أولاً
        print("🔐 المحاولة 1: الطريقة الشرعية")
        try:
            from legitimate_usage_example import LegitimateUsageDemo
            demo = LegitimateUsageDemo()
            demo.run_legitimate_demo()
            print("✅ نجحت الطريقة الشرعية")
            return
        except Exception as e:
            print(f"❌ فشلت الطريقة الشرعية: {e}")
        
        # التراجع للطريقة غير الشرعية
        print("\n🕷️ المحاولة 2: الطريقة غير الشرعية")
        confirm = input("هل تريد المتابعة بالطريقة غير الشرعية؟ (y/N): ").lower()
        if confirm == 'y':
            from illegitimate_usage_example import IllegalitimateUsageDemo
            demo = IllegalitimateUsageDemo()
            demo.run_illegitimate_demo()
            print("✅ نجحت الطريقة غير الشرعية")
        else:
            print("تم إلغاء العملية.")
            
    except Exception as e:
        print(f"❌ خطأ في النهج المختلط: {e}")

def show_status():
    """عرض حالة النظام"""
    print("\n📊 حالة النظام...")
    print("-" * 40)
    
    try:
        # فحص الملفات المطلوبة
        required_files = [
            "modules/social_media/real_data_integration.py",
            "modules/social_media/real_data_config.py",
            "examples/legitimate_usage_example.py",
            "examples/illegitimate_usage_example.py"
        ]
        
        print("📁 فحص الملفات المطلوبة:")
        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"   ✅ {file_path}")
            else:
                print(f"   ❌ {file_path}")
        
        # فحص المكتبات
        print("\n📦 فحص المكتبات:")
        libraries = [
            ("requests", "طلبات HTTP"),
            ("json", "معالجة JSON"),
            ("sqlite3", "قاعدة البيانات"),
            ("time", "إدارة الوقت")
        ]
        
        for lib, desc in libraries:
            try:
                __import__(lib)
                print(f"   ✅ {lib} - {desc}")
            except ImportError:
                print(f"   ❌ {lib} - {desc}")
        
        # فحص المكتبات الاختيارية
        print("\n📦 فحص المكتبات الاختيارية:")
        optional_libraries = [
            ("tweepy", "Twitter API"),
            ("selenium", "Web Scraping"),
            ("beautifulsoup4", "HTML Parsing"),
            ("fake_useragent", "User Agent Rotation")
        ]
        
        for lib, desc in optional_libraries:
            try:
                __import__(lib)
                print(f"   ✅ {lib} - {desc}")
            except ImportError:
                print(f"   ⚠️ {lib} - {desc} (اختياري)")
        
        # فحص قواعد البيانات
        print("\n🗄️ فحص قواعد البيانات:")
        db_files = [
            "real_social_media_data.db",
            "social_media.db",
            "blocking_operations.db",
            "social_engineering.db"
        ]
        
        for db_file in db_files:
            if os.path.exists(db_file):
                size = os.path.getsize(db_file)
                print(f"   ✅ {db_file} ({size:,} bytes)")
            else:
                print(f"   ➖ {db_file} (غير موجود)")
        
        # فحص التقارير
        print("\n📋 فحص التقارير:")
        import glob
        reports = glob.glob("*_report_*.json")
        if reports:
            for report in reports:
                size = os.path.getsize(report)
                print(f"   📄 {report} ({size:,} bytes)")
        else:
            print("   ➖ لا توجد تقارير")
            
    except Exception as e:
        print(f"❌ خطأ في فحص الحالة: {e}")

if __name__ == "__main__":
    main()
