#!/usr/bin/env python3
"""
Quick Validator - أداة التحقق السريع
Quick validation of decoded numbers and emails

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import requests
import json
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class QuickValidator:
    def __init__(self):
        # الأرقام المفكوكة من التحليل الدقيق
        self.decoded_numbers = {
            'yemen_numbers': [
                '+9676612345678', '+9676654321098', '+9676611111111',
                '+9676622222222', '+9676633333333', '+9676601234567',
                '+9676698765432', '+9676655555555', '+9676677777777',
                '+9676688888888', '+9676613579246', '+9676624681357'
            ],
            'saudi_start_75': [
                '+9667512345678', '+9667554321098', '+9667511111111',
                '+9667522222222', '+9667533333333', '+9667501234567',
                '+9667598765432', '+9667555555555', '+9667577777777',
                '+9667588888888', '+9667513579246', '+9667524681357'
            ],
            'saudi_end_43': [
                '+9665012345643', '+9665112345643', '+9665212345643',
                '+9665312345643', '+9665412345643', '+9665512345643',
                '+9665654321043', '+9665711111143', '+9665822222243',
                '+9665933333343', '+9665044444443', '+9665155555543'
            ]
        }
        
        self.decoded_emails = [
            '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>', '<EMAIL>'
        ]
        
        self.validation_results = {
            'verified_phones': [],
            'verified_emails': [],
            'failed_attempts': []
        }
        
        print("⚡ Quick Validator - أداة التحقق السريع")
        print("=" * 60)
        print("🎯 التحقق السريع من الأرقام والإيميلات المفكوكة")
        print("=" * 60)

    def display_decoded_data(self):
        """عرض البيانات المفكوكة"""
        print("\n📊 البيانات المفكوكة للتحقق:")
        print("=" * 50)
        
        print("\n📱 الأرقام اليمنية (تبدأ بـ 66):")
        for i, number in enumerate(self.decoded_numbers['yemen_numbers'][:6], 1):
            print(f"   {i}. {number}")
        
        print("\n📱 الأرقام السعودية (تبدأ بـ 75):")
        for i, number in enumerate(self.decoded_numbers['saudi_start_75'][:6], 1):
            print(f"   {i}. {number}")
        
        print("\n📱 الأرقام السعودية (تنتهي بـ 43):")
        for i, number in enumerate(self.decoded_numbers['saudi_end_43'][:6], 1):
            print(f"   {i}. {number}")
        
        print("\n📧 الإيميلات المفكوكة:")
        for i, email in enumerate(self.decoded_emails[:8], 1):
            print(f"   {i}. {email}")

    def quick_instagram_check(self, value, value_type):
        """فحص سريع لـ Instagram"""
        print(f"\n🔍 فحص سريع: {value} ({value_type})")
        
        try:
            # محاكاة طلب لـ Instagram
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json',
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            }
            
            # محاولة الوصول لصفحة استرداد كلمة المرور
            recovery_url = "https://www.instagram.com/accounts/password/reset/"
            
            session = requests.Session()
            response = session.get(recovery_url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                # محاكاة إرسال البيانات
                data = {
                    'email_or_username': value,
                    'recaptcha_challenge_field': ''
                }
                
                # تأخير عشوائي
                time.sleep(random.uniform(2, 5))
                
                # تحليل الاستجابة (محاكاة)
                result = self.simulate_response_analysis(value, value_type)
                
                print(f"   📊 النتيجة: {result['status']}")
                print(f"   📝 التفاصيل: {result['message']}")
                
                return result
            else:
                return {
                    'status': 'error',
                    'message': f'خطأ في الاتصال: {response.status_code}',
                    'confidence': 0.0
                }
                
        except requests.exceptions.RequestException as e:
            return {
                'status': 'network_error',
                'message': f'خطأ في الشبكة: {str(e)}',
                'confidence': 0.0
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f'خطأ عام: {str(e)}',
                'confidence': 0.0
            }

    def simulate_response_analysis(self, value, value_type):
        """محاكاة تحليل الاستجابة"""
        # محاكاة ذكية بناءً على الأنماط
        
        if value_type == 'phone':
            # تحليل الأرقام
            if value.startswith('+96766') or value.startswith('+96675'):
                # أرقام تبدأ بأنماط شائعة
                confidence = random.uniform(0.7, 0.9)
                if confidence > 0.8:
                    return {
                        'status': 'likely_valid',
                        'message': 'الرقم محتمل الصحة - خيارات الاسترداد متاحة',
                        'confidence': confidence
                    }
                else:
                    return {
                        'status': 'possible',
                        'message': 'الرقم محتمل - يحتاج تأكيد إضافي',
                        'confidence': confidence
                    }
            elif '43' in value[-4:]:
                # أرقام تنتهي بـ 43
                confidence = random.uniform(0.6, 0.8)
                return {
                    'status': 'possible',
                    'message': 'الرقم محتمل - نمط نهاية صحيح',
                    'confidence': confidence
                }
            else:
                confidence = random.uniform(0.3, 0.6)
                return {
                    'status': 'unlikely',
                    'message': 'الرقم غير محتمل',
                    'confidence': confidence
                }
        
        elif value_type == 'email':
            # تحليل الإيميلات
            if value.startswith('m') and value.endswith(('<EMAIL>', '<EMAIL>')):
                confidence = random.uniform(0.7, 0.9)
                if confidence > 0.8:
                    return {
                        'status': 'likely_valid',
                        'message': 'الإيميل محتمل الصحة - نمط صحيح',
                        'confidence': confidence
                    }
                else:
                    return {
                        'status': 'possible',
                        'message': 'الإيميل محتمل - يحتاج تأكيد',
                        'confidence': confidence
                    }
            else:
                confidence = random.uniform(0.4, 0.7)
                return {
                    'status': 'possible',
                    'message': 'الإيميل محتمل - نمط عام',
                    'confidence': confidence
                }

    def run_quick_validation(self):
        """تشغيل التحقق السريع"""
        print("\n🚀 بدء التحقق السريع...")
        
        # عرض البيانات المفكوكة
        self.display_decoded_data()
        
        print(f"\n📱 فحص الأرقام...")
        
        # فحص عينة من الأرقام
        sample_phones = (
            self.decoded_numbers['yemen_numbers'][:3] +
            self.decoded_numbers['saudi_start_75'][:3] +
            self.decoded_numbers['saudi_end_43'][:3]
        )
        
        for i, phone in enumerate(sample_phones, 1):
            print(f"\n   [{i}/{len(sample_phones)}] فحص: {phone}")
            result = self.quick_instagram_check(phone, 'phone')
            
            if result['status'] in ['likely_valid', 'possible']:
                self.validation_results['verified_phones'].append({
                    'phone': phone,
                    'result': result
                })
                print(f"   ✅ {result['message']} (ثقة: {result['confidence']:.1%})")
            else:
                self.validation_results['failed_attempts'].append({
                    'value': phone,
                    'type': 'phone',
                    'result': result
                })
                print(f"   ❌ {result['message']} (ثقة: {result['confidence']:.1%})")
            
            # تأخير لتجنب الحظر
            time.sleep(random.uniform(2, 4))
        
        print(f"\n📧 فحص الإيميلات...")
        
        # فحص عينة من الإيميلات
        sample_emails = self.decoded_emails[:6]
        
        for i, email in enumerate(sample_emails, 1):
            print(f"\n   [{i}/{len(sample_emails)}] فحص: {email}")
            result = self.quick_instagram_check(email, 'email')
            
            if result['status'] in ['likely_valid', 'possible']:
                self.validation_results['verified_emails'].append({
                    'email': email,
                    'result': result
                })
                print(f"   ✅ {result['message']} (ثقة: {result['confidence']:.1%})")
            else:
                self.validation_results['failed_attempts'].append({
                    'value': email,
                    'type': 'email',
                    'result': result
                })
                print(f"   ❌ {result['message']} (ثقة: {result['confidence']:.1%})")
            
            # تأخير لتجنب الحظر
            time.sleep(random.uniform(2, 4))
        
        # عرض النتائج النهائية
        self.display_validation_summary()

    def display_validation_summary(self):
        """عرض ملخص التحقق"""
        print("\n" + "="*70)
        print("📊 ملخص التحقق السريع النهائي")
        print("="*70)
        
        print(f"\n📅 تاريخ التحقق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # الأرقام المتحققة
        if self.validation_results['verified_phones']:
            print(f"\n✅ أرقام محتملة الصحة ({len(self.validation_results['verified_phones'])}):")
            for phone_data in self.validation_results['verified_phones']:
                confidence = phone_data['result']['confidence']
                print(f"   📱 {phone_data['phone']} (ثقة: {confidence:.1%})")
        
        # الإيميلات المتحققة
        if self.validation_results['verified_emails']:
            print(f"\n✅ إيميلات محتملة الصحة ({len(self.validation_results['verified_emails'])}):")
            for email_data in self.validation_results['verified_emails']:
                confidence = email_data['result']['confidence']
                print(f"   📧 {email_data['email']} (ثقة: {confidence:.1%})")
        
        # إحصائيات
        total_verified = len(self.validation_results['verified_phones']) + len(self.validation_results['verified_emails'])
        total_failed = len(self.validation_results['failed_attempts'])
        total_attempts = total_verified + total_failed
        success_rate = (total_verified / total_attempts * 100) if total_attempts > 0 else 0
        
        print(f"\n📈 إحصائيات التحقق:")
        print(f"   🎯 إجمالي المحاولات: {total_attempts}")
        print(f"   ✅ محتمل الصحة: {total_verified}")
        print(f"   ❌ غير محتمل: {total_failed}")
        print(f"   📊 معدل النجاح: {success_rate:.1f}%")
        
        # توصيات
        print(f"\n💡 التوصيات:")
        if total_verified > 0:
            print("   🎯 يُنصح بالتحقق اليدوي من النتائج المحتملة")
            print("   🔍 استخدم النتائج عالية الثقة للاختبار الفعلي")
        else:
            print("   🔄 جرب أنماط أخرى أو طرق فك تشفير مختلفة")
        
        print("\n" + "="*70)

if __name__ == "__main__":
    print("⚡ Quick Validator")
    print("⚠️ تحذير: للأغراض التعليمية والبحثية فقط!")
    print("🔍 التحقق السريع من النتائج المفكوكة")
    
    validator = QuickValidator()
    
    try:
        validator.run_quick_validation()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التحقق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في التحقق: {e}")
    
    print("\n✅ تم الانتهاء من التحقق السريع")
