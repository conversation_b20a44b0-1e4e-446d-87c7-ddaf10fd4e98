#!/usr/bin/env python3
"""
عارض البيانات المسكربة من Instagram
Instagram Scraped Data Viewer
"""

import sqlite3
import json
from datetime import datetime

def view_scraped_data():
    """عرض البيانات المسكربة من قاعدة البيانات"""
    
    print("📊 عارض البيانات المسكربة من Instagram")
    print("=" * 50)
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('instagram_scraping_demo.db')
        cursor = conn.cursor()
        
        # جلب جميع البيانات
        cursor.execute("SELECT * FROM instagram_profiles ORDER BY id DESC")
        profiles = cursor.fetchall()
        
        if not profiles:
            print("❌ لا توجد بيانات في قاعدة البيانات")
            return
        
        print(f"📈 تم العثور على {len(profiles)} ملف شخصي")
        print("-" * 50)
        
        for i, profile in enumerate(profiles, 1):
            (id, username, display_name, bio, followers_count, following_count, 
             posts_count, profile_image_url, is_verified, is_private, external_url,
             scraping_method, scraping_timestamp, success, error_message) = profile
            
            print(f"\n🔍 الملف الشخصي #{i} (ID: {id})")
            print("-" * 30)
            print(f"👤 اسم المستخدم: @{username}")
            print(f"📝 الاسم المعروض: {display_name}")
            print(f"📄 البايو: {bio}")
            print(f"👥 المتابعين: {followers_count:,}")
            print(f"👤 المتابعة: {following_count:,}")
            print(f"📸 المنشورات: {posts_count:,}")
            print(f"✅ موثق: {'نعم' if is_verified else 'لا'}")
            print(f"🔒 خاص: {'نعم' if is_private else 'لا'}")
            
            if profile_image_url and profile_image_url != "غير متاح":
                print(f"🖼️ صورة الملف الشخصي: متوفرة")
                print(f"   🔗 الرابط: {profile_image_url[:60]}...")
            else:
                print(f"🖼️ صورة الملف الشخصي: غير متوفرة")
            
            if external_url:
                print(f"🌐 الرابط الخارجي: {external_url}")
            
            print(f"🕷️ طريقة السكرابينغ: {scraping_method}")
            print(f"⏰ وقت السكرابينغ: {scraping_timestamp}")
            print(f"✅ حالة النجاح: {'نجح' if success else 'فشل'}")
            
            if error_message:
                print(f"❌ رسالة الخطأ: {error_message}")
        
        # إحصائيات إضافية
        print("\n" + "=" * 50)
        print("📊 إحصائيات إضافية:")
        print("-" * 30)
        
        # إجمالي المتابعين
        cursor.execute("SELECT SUM(followers_count) FROM instagram_profiles WHERE success = 1")
        total_followers = cursor.fetchone()[0] or 0
        print(f"👥 إجمالي المتابعين: {total_followers:,}")
        
        # إجمالي المنشورات
        cursor.execute("SELECT SUM(posts_count) FROM instagram_profiles WHERE success = 1")
        total_posts = cursor.fetchone()[0] or 0
        print(f"📸 إجمالي المنشورات: {total_posts:,}")
        
        # عدد الحسابات الموثقة
        cursor.execute("SELECT COUNT(*) FROM instagram_profiles WHERE is_verified = 1")
        verified_count = cursor.fetchone()[0]
        print(f"✅ الحسابات الموثقة: {verified_count}")
        
        # عدد الحسابات الخاصة
        cursor.execute("SELECT COUNT(*) FROM instagram_profiles WHERE is_private = 1")
        private_count = cursor.fetchone()[0]
        print(f"🔒 الحسابات الخاصة: {private_count}")
        
        # طرق السكرابينغ المستخدمة
        cursor.execute("SELECT scraping_method, COUNT(*) FROM instagram_profiles GROUP BY scraping_method")
        methods = cursor.fetchall()
        print(f"🕷️ طرق السكرابينغ:")
        for method, count in methods:
            print(f"   - {method}: {count}")
        
        # معدل النجاح
        cursor.execute("SELECT COUNT(*) FROM instagram_profiles WHERE success = 1")
        success_count = cursor.fetchone()[0]
        success_rate = (success_count / len(profiles)) * 100 if profiles else 0
        print(f"📈 معدل النجاح: {success_rate:.1f}%")
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
    except Exception as e:
        print(f"❌ خطأ عام: {e}")

def export_to_json():
    """تصدير البيانات إلى ملف JSON"""
    
    print("\n💾 تصدير البيانات إلى JSON...")
    
    try:
        conn = sqlite3.connect('instagram_scraping_demo.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM instagram_profiles")
        profiles = cursor.fetchall()
        
        # تحويل البيانات إلى قاموس
        columns = [description[0] for description in cursor.description]
        profiles_data = []
        
        for profile in profiles:
            profile_dict = dict(zip(columns, profile))
            profiles_data.append(profile_dict)
        
        # حفظ في ملف JSON
        filename = f"instagram_scraped_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(profiles_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم تصدير البيانات إلى: {filename}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في التصدير: {e}")

def main():
    """الدالة الرئيسية"""
    
    while True:
        print("\n🎯 الخيارات المتاحة:")
        print("1. 📊 عرض البيانات المسكربة")
        print("2. 💾 تصدير البيانات إلى JSON")
        print("3. 🗑️ حذف جميع البيانات")
        print("4. 🚪 خروج")
        
        choice = input("\nاختر رقم الخيار (1-4): ").strip()
        
        if choice == "1":
            view_scraped_data()
        elif choice == "2":
            export_to_json()
        elif choice == "3":
            confirm = input("⚠️ هل أنت متأكد من حذف جميع البيانات؟ (y/N): ").lower()
            if confirm == 'y':
                try:
                    conn = sqlite3.connect('instagram_scraping_demo.db')
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM instagram_profiles")
                    cursor.execute("DELETE FROM instagram_posts")
                    conn.commit()
                    conn.close()
                    print("✅ تم حذف جميع البيانات")
                except Exception as e:
                    print(f"❌ خطأ في الحذف: {e}")
            else:
                print("تم إلغاء العملية")
        elif choice == "4":
            print("👋 شكراً لاستخدام عارض البيانات!")
            break
        else:
            print("❌ خيار غير صحيح")

if __name__ == "__main__":
    main()
