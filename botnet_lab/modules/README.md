# 🧩 **Botnet Lab Modules Directory**
## مجلد وحدات مشروع Botnet Lab

هذا المجلد يحتوي على جميع وحدات المشروع منظمة حسب الوظيفة والتخصص.

---

## 📁 **الهيكل الجديد المنظم:**

### **🔐 1. Security Exploitation** (`security_exploitation/`)
**وحدات الأمان والاختراق - 4 وحدات**
- `password_cracking.py` - تقنيات كسر كلمات المرور
- `realistic_password_cracking.py` - كسر كلمات المرور الواقعي
- `web_exploitation_xss.py` - استغلال ثغرات XSS
- `info_stealer_educational.py` - سرقة المعلومات التعليمية

### **📱 2. Phone Targeting** (`phone_targeting/`)
**وحدات استهداف الهواتف - 6 وحدات**
- `phone_number_targeting.py` - استهداف أرقام الهواتف
- `smart_phone_targeting.py` - استهداف الهواتف الذكية
- `advanced_phone_attacks.py` - هجمات متقدمة على الهواتف
- `advanced_phone_osint.py` - OSINT متقدم للهواتف
- `ai_phone_intelligence.py` - ذكاء اصطناعي للهواتف
- `mobile_capabilities.py` - قدرات الأجهزة المحمولة

### **🌐 3. Social Media** (`social_media/`)
**وحدات وسائل التواصل الاجتماعي - 3 وحدات**
- `social_media_accounts.py` - إدارة حسابات وسائل التواصل
- `social_media_blocking.py` - تقنيات حظر الحسابات
- `social_engineering.py` - الهندسة الاجتماعية

### **🔍 4. Intelligence Gathering** (`intelligence_gathering/`)
**وحدات جمع المعلومات - 3 وحدات**
- `intelligence_gathering.py` - جمع المعلومات الأساسي
- `advanced_intelligence.py` - الذكاء المتقدم
- `predictive_analytics.py` - التحليلات التنبؤية

### **🥷 5. Stealth Evasion** (`stealth_evasion/`)
**وحدات التخفي والتهرب - 4 وحدات**
- `stealth_evasion.py` - تقنيات التخفي الأساسية
- `advanced_stealth_evasion.py` - التخفي المتقدم
- `advanced_evasion.py` - تقنيات التهرب المتطورة
- `neural_network_evasion.py` - التهرب بالشبكات العصبية

### **🚀 6. Propagation Persistence** (`propagation_persistence/`)
**وحدات الانتشار والبقاء - 2 وحدة**
- `advanced_propagation.py` - تقنيات الانتشار المتقدمة
- `persistence_survival.py` - آليات البقاء والاستمرارية

### **🌍 7. Network Communications** (`network_communications/`)
**وحدات الشبكات والاتصالات - 3 وحدات**
- `network_pivoting.py` - التنقل في الشبكات
- `satellite_communication.py` - الاتصالات عبر الأقمار الصناعية
- `distributed_operations.py` - العمليات الموزعة

### **💰 8. Financial Exploitation** (`financial_exploitation/`)
**وحدات الاستغلال المالي - 2 وحدة**
- `monetization_exploitation.py` - تحقيق الأرباح
- `financial_exploitation.py` - الاستغلال المالي المتقدم

### **🔧 9. System Control** (`system_control/`)
**وحدات التحكم والتلاعب - 2 وحدة**
- `system_manipulation.py` - التلاعب بالأنظمة
- `webcam_microphone.py` - التحكم في الكاميرا والميكروفون

### **🤖 10. Advanced Technologies** (`advanced_technologies/`)
**وحدات التقنيات المتقدمة - 2 وحدة**
- `blockchain_integration.py` - تكامل البلوك تشين
- `deep_fake_technology.py` - تقنيات التزييف العميق

---

## 📊 **الإحصائيات:**
- **إجمالي المجلدات**: 10 مجلدات متخصصة
- **إجمالي الوحدات**: 30 وحدة منظمة
- **ملفات __init__.py**: 10 ملفات
- **ملفات README**: 11 ملف (10 + الرئيسي)

---

## 🚀 **كيفية الاستخدام:**

### **استيراد وحدة محددة:**
```python
# استيراد وحدة من مجلد محدد
from modules.security_exploitation.password_cracking import PasswordCracking
from modules.phone_targeting.phone_number_targeting import PhoneNumberTargeting
```

### **استيراد مجلد كامل:**
```python
# استيراد جميع وحدات مجلد
from modules.security_exploitation import *
from modules.social_media import *
```

### **تشغيل وحدة:**
```bash
# من مجلد المشروع الرئيسي
python modules/security_exploitation/web_exploitation_xss.py
python modules/phone_targeting/ai_phone_intelligence.py
```

---

## ⚠️ **تحذيرات مهمة:**
- 🎓 **للأغراض التعليمية والبحثية فقط**
- ✅ **اختبر فقط على الأنظمة التي تملكها**
- ❌ **لا تستخدم لأغراض ضارة أو غير قانونية**
- 🛡️ **استخدم هذه المعرفة للدفاع وتحسين الأمان**

---

## 📚 **للمزيد من المعلومات:**
راجع ملف README.md في كل مجلد فرعي للحصول على تفاصيل أكثر حول الوحدات المتاحة.
