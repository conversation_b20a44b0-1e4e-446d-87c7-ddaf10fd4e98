"""
Botnet Lab Module Index
فهرس وحدات مشروع Botnet Lab

This file provides easy access to all reorganized modules.
"""

# Security Exploitation Modules
try:
    from modules.security_exploitation import (
        password_cracking,
        realistic_password_cracking,
        web_exploitation_xss,
        info_stealer_educational
    )
except ImportError as e:
    print(f"Warning: Could not import security_exploitation modules: {e}")

# Phone Targeting Modules
try:
    from modules.phone_targeting import (
        phone_number_targeting,
        smart_phone_targeting,
        advanced_phone_attacks,
        advanced_phone_osint,
        ai_phone_intelligence,
        mobile_capabilities
    )
except ImportError as e:
    print(f"Warning: Could not import phone_targeting modules: {e}")

# Social Media Modules
try:
    from modules.social_media import (
        social_media_accounts,
        social_media_blocking,
        social_engineering
    )
except ImportError as e:
    print(f"Warning: Could not import social_media modules: {e}")

# Intelligence Gathering Modules
try:
    from modules.intelligence_gathering import (
        intelligence_gathering,
        advanced_intelligence,
        predictive_analytics
    )
except ImportError as e:
    print(f"Warning: Could not import intelligence_gathering modules: {e}")

# Stealth Evasion Modules
try:
    from modules.stealth_evasion import (
        stealth_evasion,
        advanced_stealth_evasion,
        advanced_evasion,
        neural_network_evasion
    )
except ImportError as e:
    print(f"Warning: Could not import stealth_evasion modules: {e}")

# Propagation Persistence Modules
try:
    from modules.propagation_persistence import (
        advanced_propagation,
        persistence_survival
    )
except ImportError as e:
    print(f"Warning: Could not import propagation_persistence modules: {e}")

# Network Communications Modules
try:
    from modules.network_communications import (
        network_pivoting,
        satellite_communication,
        distributed_operations
    )
except ImportError as e:
    print(f"Warning: Could not import network_communications modules: {e}")

# Financial Exploitation Modules
try:
    from modules.financial_exploitation import (
        monetization_exploitation,
        financial_exploitation
    )
except ImportError as e:
    print(f"Warning: Could not import financial_exploitation modules: {e}")

# System Control Modules
try:
    from modules.system_control import (
        system_manipulation,
        webcam_microphone
    )
except ImportError as e:
    print(f"Warning: Could not import system_control modules: {e}")

# Advanced Technologies Modules
try:
    from modules.advanced_technologies import (
        blockchain_integration,
        deep_fake_technology
    )
except ImportError as e:
    print(f"Warning: Could not import advanced_technologies modules: {e}")

__all__ = [
    # Security Exploitation
    'password_cracking', 'realistic_password_cracking', 'web_exploitation_xss', 'info_stealer_educational',

    # Phone Targeting
    'phone_number_targeting', 'smart_phone_targeting', 'advanced_phone_attacks',
    'advanced_phone_osint', 'ai_phone_intelligence', 'mobile_capabilities',

    # Social Media
    'social_media_accounts', 'social_media_blocking', 'social_engineering',

    # Intelligence Gathering
    'intelligence_gathering', 'advanced_intelligence', 'predictive_analytics',

    # Stealth Evasion
    'stealth_evasion', 'advanced_stealth_evasion', 'advanced_evasion', 'neural_network_evasion',

    # Propagation Persistence
    'advanced_propagation', 'persistence_survival',

    # Network Communications
    'network_pivoting', 'satellite_communication', 'distributed_operations',

    # Financial Exploitation
    'monetization_exploitation', 'financial_exploitation',

    # System Control
    'system_manipulation', 'webcam_microphone',

    # Advanced Technologies
    'blockchain_integration', 'deep_fake_technology'
]
