"""
Intelligence Gathering Module Package
وحدات جمع المعلومات

This package contains modules for intelligence gathering and analysis:
- intelligence_gathering: Basic intelligence gathering capabilities
- advanced_intelligence: Advanced intelligence and data analysis
- predictive_analytics: Predictive analytics and forecasting
"""

__version__ = "1.0.0"
__author__ = "Botnet Lab Team"

# Import all modules for easy access
try:
    from .intelligence_gathering import *
except ImportError:
    pass

try:
    from .advanced_intelligence import *
except ImportError:
    pass

try:
    from .predictive_analytics import *
except ImportError:
    pass

__all__ = [
    'intelligence_gathering',
    'advanced_intelligence',
    'predictive_analytics'
]
