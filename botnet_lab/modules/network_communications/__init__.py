"""
Network Communications Module Package
وحدات الشبكات والاتصالات

This package contains modules for network operations and communications:
- network_pivoting: Network pivoting and lateral movement
- satellite_communication: Satellite communication capabilities
- distributed_operations: Distributed operations and coordination
"""

__version__ = "1.0.0"
__author__ = "Botnet Lab Team"

# Import all modules for easy access
try:
    from .network_pivoting import *
except ImportError:
    pass

try:
    from .satellite_communication import *
except ImportError:
    pass

try:
    from .distributed_operations import *
except ImportError:
    pass

__all__ = [
    'network_pivoting',
    'satellite_communication',
    'distributed_operations'
]
