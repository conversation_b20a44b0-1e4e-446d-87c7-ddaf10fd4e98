"""
Phone Targeting Module Package
وحدات استهداف الهواتف

This package contains modules for phone and mobile device targeting:
- phone_number_targeting: Phone number operations and targeting
- smart_phone_targeting: Smart phone specific attacks
- advanced_phone_attacks: Advanced phone attack techniques
- advanced_phone_osint: Advanced phone OSINT capabilities
- ai_phone_intelligence: AI-powered phone intelligence
- mobile_capabilities: Mobile device targeting capabilities
"""

__version__ = "1.0.0"
__author__ = "Botnet Lab Team"

# Import all modules for easy access
try:
    from .phone_number_targeting import *
except ImportError:
    pass

try:
    from .smart_phone_targeting import *
except ImportError:
    pass

try:
    from .advanced_phone_attacks import *
except ImportError:
    pass

try:
    from .advanced_phone_osint import *
except ImportError:
    pass

try:
    from .ai_phone_intelligence import *
except ImportError:
    pass

try:
    from .mobile_capabilities import *
except ImportError:
    pass

__all__ = [
    'phone_number_targeting',
    'smart_phone_targeting',
    'advanced_phone_attacks',
    'advanced_phone_osint',
    'ai_phone_intelligence',
    'mobile_capabilities'
]
