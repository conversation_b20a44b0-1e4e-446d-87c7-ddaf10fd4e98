"""
Social Media Module Package
وحدات وسائل التواصل الاجتماعي

This package contains modules for social media operations:
- social_media_accounts: Social media account management
- social_media_blocking: Account blocking and suspension techniques
- social_engineering: Social engineering and manipulation techniques
"""

__version__ = "1.0.0"
__author__ = "Botnet Lab Team"

# Import all modules for easy access
try:
    from .social_media_accounts import *
except ImportError:
    pass

try:
    from .social_media_blocking import *
except ImportError:
    pass

try:
    from .social_engineering import *
except ImportError:
    pass

__all__ = [
    'social_media_accounts',
    'social_media_blocking',
    'social_engineering'
]
