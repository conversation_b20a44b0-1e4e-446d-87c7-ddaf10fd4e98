#!/usr/bin/env python3
"""
Real Data Configuration Module
وحدة إعدادات البيانات الحقيقية

Configuration templates and examples for both legitimate and illegitimate data access methods.
"""

# ==========================================
# LEGITIMATE API CONFIGURATIONS
# ==========================================

LEGITIMATE_API_CONFIG = {
    "twitter": {
        "api_key": "YOUR_TWITTER_API_KEY",
        "api_secret": "YOUR_TWITTER_API_SECRET", 
        "access_token": "YOUR_TWITTER_ACCESS_TOKEN",
        "access_token_secret": "YOUR_TWITTER_ACCESS_TOKEN_SECRET",
        "bearer_token": "YOUR_TWITTER_BEARER_TOKEN",
        "rate_limit": 300,  # requests per 15 minutes
        "daily_limit": 10000,
        "endpoints": {
            "user_lookup": "https://api.twitter.com/2/users/by/username/",
            "user_tweets": "https://api.twitter.com/2/users/{id}/tweets",
            "followers": "https://api.twitter.com/2/users/{id}/followers",
            "following": "https://api.twitter.com/2/users/{id}/following"
        }
    },
    
    "facebook": {
        "app_id": "YOUR_FACEBOOK_APP_ID",
        "app_secret": "YOUR_FACEBOOK_APP_SECRET",
        "access_token": "YOUR_FACEBOOK_ACCESS_TOKEN",
        "rate_limit": 200,  # requests per hour
        "daily_limit": 5000,
        "endpoints": {
            "user_profile": "https://graph.facebook.com/v18.0/{user-id}",
            "user_posts": "https://graph.facebook.com/v18.0/{user-id}/posts",
            "user_friends": "https://graph.facebook.com/v18.0/{user-id}/friends"
        }
    },
    
    "instagram": {
        "app_id": "YOUR_INSTAGRAM_APP_ID",
        "app_secret": "YOUR_INSTAGRAM_APP_SECRET",
        "redirect_uri": "YOUR_REDIRECT_URI",
        "access_token": "YOUR_INSTAGRAM_ACCESS_TOKEN",
        "rate_limit": 240,  # requests per hour
        "daily_limit": 5000,
        "endpoints": {
            "user_profile": "https://graph.instagram.com/me",
            "user_media": "https://graph.instagram.com/me/media"
        }
    },
    
    "linkedin": {
        "client_id": "YOUR_LINKEDIN_CLIENT_ID",
        "client_secret": "YOUR_LINKEDIN_CLIENT_SECRET",
        "access_token": "YOUR_LINKEDIN_ACCESS_TOKEN",
        "rate_limit": 100,  # requests per hour
        "daily_limit": 2000,
        "endpoints": {
            "profile": "https://api.linkedin.com/v2/people/(id:{person-id})",
            "connections": "https://api.linkedin.com/v2/connections"
        }
    },
    
    "youtube": {
        "api_key": "YOUR_YOUTUBE_API_KEY",
        "rate_limit": 10000,  # quota units per day
        "endpoints": {
            "channels": "https://www.googleapis.com/youtube/v3/channels",
            "videos": "https://www.googleapis.com/youtube/v3/videos",
            "search": "https://www.googleapis.com/youtube/v3/search"
        }
    }
}

# ==========================================
# ILLEGITIMATE SCRAPING CONFIGURATIONS
# ==========================================

ILLEGITIMATE_SCRAPING_CONFIG = {
    "proxies": [
        {"http": "http://proxy1:port", "https": "https://proxy1:port"},
        {"http": "http://proxy2:port", "https": "https://proxy2:port"},
        {"http": "http://proxy3:port", "https": "https://proxy3:port"}
    ],
    
    "user_agents": [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0"
    ],
    
    "scraping_methods": {
        "twitter": {
            "method": "selenium_requests",
            "selectors": {
                "profile_name": '[data-testid="UserName"] span',
                "bio": '[data-testid="UserDescription"]',
                "followers": 'a[href*="/followers"] span',
                "following": 'a[href*="/following"] span',
                "tweets": '[data-testid="tweet"]',
                "verified": '[data-testid="icon-verified"]'
            },
            "rate_limit": {
                "requests_per_minute": 10,
                "delay_between_requests": [5, 15],
                "session_duration": 1800  # 30 minutes
            }
        },
        
        "facebook": {
            "method": "selenium_requests",
            "login_required": True,
            "selectors": {
                "profile_name": 'h1[data-overviewsection="contact_basic_info"]',
                "bio": '[data-overviewsection="places_lived"]',
                "friends_count": 'a[href*="/friends"] span',
                "posts": '[data-pagelet="FeedUnit"]'
            },
            "rate_limit": {
                "requests_per_minute": 5,
                "delay_between_requests": [10, 30],
                "session_duration": 3600  # 1 hour
            }
        },
        
        "instagram": {
            "method": "requests_beautifulsoup",
            "selectors": {
                "profile_name": 'h2.x1lliihq',
                "bio": 'h1.x1lliihq',
                "followers": 'a[href*="/followers/"] span',
                "following": 'a[href*="/following/"] span',
                "posts_count": 'div.-nal3 span'
            },
            "rate_limit": {
                "requests_per_minute": 8,
                "delay_between_requests": [7, 20],
                "session_duration": 2400  # 40 minutes
            }
        },
        
        "linkedin": {
            "method": "selenium_requests",
            "login_required": True,
            "premium_required": False,
            "selectors": {
                "profile_name": 'h1.text-heading-xlarge',
                "headline": 'div.text-body-medium',
                "connections": 'span.t-bold',
                "experience": 'section[data-section="experience"]'
            },
            "rate_limit": {
                "requests_per_minute": 3,
                "delay_between_requests": [15, 45],
                "session_duration": 1200  # 20 minutes
            }
        },
        
        "tiktok": {
            "method": "selenium_requests",
            "selectors": {
                "profile_name": 'h2[data-e2e="user-title"]',
                "bio": 'h2[data-e2e="user-bio"]',
                "followers": 'strong[data-e2e="followers-count"]',
                "following": 'strong[data-e2e="following-count"]',
                "likes": 'strong[data-e2e="likes-count"]'
            },
            "rate_limit": {
                "requests_per_minute": 12,
                "delay_between_requests": [3, 10],
                "session_duration": 1800  # 30 minutes
            }
        }
    },
    
    "anti_detection": {
        "rotate_proxy_every": 20,  # requests
        "rotate_user_agent_every": 15,  # requests
        "clear_cookies_every": 50,  # requests
        "restart_session_every": 100,  # requests
        "random_delays": True,
        "human_like_behavior": True,
        "captcha_solving": {
            "service": "2captcha",  # or "anticaptcha", "deathbycaptcha"
            "api_key": "YOUR_CAPTCHA_SERVICE_API_KEY"
        }
    }
}

# ==========================================
# TARGET CONFIGURATIONS
# ==========================================

LEGITIMATE_TARGETS_CONFIG = {
    "research_targets": [
        {
            "platform": "twitter",
            "username": "elonmusk",
            "purpose": "public_figure_analysis",
            "data_types": ["profile", "tweets", "engagement"]
        },
        {
            "platform": "facebook",
            "page_id": "facebook",
            "purpose": "brand_analysis",
            "data_types": ["posts", "engagement", "audience"]
        }
    ],
    
    "academic_research": [
        {
            "platform": "twitter",
            "hashtags": ["#AI", "#MachineLearning"],
            "purpose": "sentiment_analysis",
            "sample_size": 1000
        }
    ],
    
    "competitive_analysis": [
        {
            "platform": "linkedin",
            "company_pages": ["microsoft", "google", "apple"],
            "purpose": "recruitment_analysis",
            "data_types": ["employee_count", "job_postings"]
        }
    ]
}

ILLEGITIMATE_TARGETS_CONFIG = {
    "individual_targets": [
        {
            "platform": "facebook",
            "target_name": "John Doe",
            "profile_url": "https://facebook.com/johndoe123",
            "intelligence_level": "deep",
            "data_types": ["profile", "friends", "posts", "photos", "check_ins"]
        },
        {
            "platform": "instagram", 
            "username": "target_username",
            "intelligence_level": "comprehensive",
            "data_types": ["profile", "posts", "stories", "followers", "following"]
        }
    ],
    
    "mass_collection": [
        {
            "platform": "linkedin",
            "search_criteria": {
                "job_title": "Software Engineer",
                "location": "San Francisco",
                "company": "Tech Companies"
            },
            "sample_size": 5000,
            "data_types": ["profile", "connections", "experience"]
        }
    ],
    
    "social_network_mapping": [
        {
            "seed_profiles": ["user1", "user2", "user3"],
            "depth_levels": 3,
            "platforms": ["facebook", "twitter", "instagram"],
            "relationship_types": ["friends", "followers", "mutual_connections"]
        }
    ]
}

# ==========================================
# DATA PROCESSING CONFIGURATIONS
# ==========================================

DATA_PROCESSING_CONFIG = {
    "data_enrichment": {
        "face_recognition": True,
        "location_analysis": True,
        "sentiment_analysis": True,
        "network_analysis": True,
        "behavioral_patterns": True
    },
    
    "data_storage": {
        "database_encryption": True,
        "data_retention_days": 365,
        "backup_frequency": "daily",
        "anonymization_level": "none"  # none, partial, full
    },
    
    "data_export": {
        "formats": ["json", "csv", "xml"],
        "include_metadata": True,
        "compress_exports": True
    }
}

# ==========================================
# LEGAL AND ETHICAL CONFIGURATIONS
# ==========================================

LEGAL_COMPLIANCE_CONFIG = {
    "gdpr_compliance": {
        "enabled": False,
        "data_subject_rights": False,
        "consent_tracking": False,
        "data_minimization": False
    },
    
    "terms_of_service": {
        "respect_robots_txt": False,
        "respect_rate_limits": True,
        "avoid_detection": True
    },
    
    "ethical_guidelines": {
        "target_consent": False,
        "data_anonymization": False,
        "research_purpose_only": False,
        "no_harm_principle": False
    }
}

# ==========================================
# OPERATIONAL SECURITY CONFIGURATIONS
# ==========================================

OPSEC_CONFIG = {
    "network_security": {
        "use_tor": True,
        "use_vpn": True,
        "proxy_chains": True,
        "dns_over_https": True
    },
    
    "identity_protection": {
        "fake_accounts": True,
        "burner_emails": True,
        "virtual_phone_numbers": True,
        "cryptocurrency_payments": True
    },
    
    "data_security": {
        "encrypt_databases": True,
        "secure_communications": True,
        "evidence_destruction": True,
        "plausible_deniability": True
    }
}

# ==========================================
# EXAMPLE USAGE CONFIGURATIONS
# ==========================================

def get_legitimate_config_example():
    """Get example configuration for legitimate data collection"""
    return {
        "api_credentials": LEGITIMATE_API_CONFIG,
        "targets": LEGITIMATE_TARGETS_CONFIG,
        "data_processing": DATA_PROCESSING_CONFIG,
        "legal_compliance": {
            **LEGAL_COMPLIANCE_CONFIG,
            "gdpr_compliance": {"enabled": True, "data_subject_rights": True},
            "ethical_guidelines": {"research_purpose_only": True, "no_harm_principle": True}
        }
    }

def get_illegitimate_config_example():
    """Get example configuration for illegitimate data collection"""
    return {
        "scraping_config": ILLEGITIMATE_SCRAPING_CONFIG,
        "targets": ILLEGITIMATE_TARGETS_CONFIG,
        "data_processing": DATA_PROCESSING_CONFIG,
        "opsec": OPSEC_CONFIG,
        "legal_compliance": LEGAL_COMPLIANCE_CONFIG  # All disabled for illegitimate use
    }

def get_hybrid_config_example():
    """Get example configuration combining both methods"""
    return {
        "legitimate": get_legitimate_config_example(),
        "illegitimate": get_illegitimate_config_example(),
        "operation_mode": "hybrid",  # legitimate_only, illegitimate_only, hybrid
        "fallback_strategy": "illegitimate_if_api_fails",
        "data_correlation": True,
        "cross_platform_analysis": True
    }

if __name__ == "__main__":
    print("Real Data Configuration Module")
    print("=" * 40)
    print("Available configurations:")
    print("1. Legitimate API access")
    print("2. Illegitimate scraping methods") 
    print("3. Hybrid approach")
    print("4. OPSEC and security measures")
    print("5. Legal compliance options")
