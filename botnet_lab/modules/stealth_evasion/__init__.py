"""
Stealth and Evasion Module Package
وحدات التخفي والتهرب

This package contains modules for stealth and evasion techniques:
- stealth_evasion: Basic stealth and evasion techniques
- advanced_stealth_evasion: Advanced stealth capabilities
- advanced_evasion: Advanced evasion techniques
- neural_network_evasion: Neural network-based evasion
"""

__version__ = "1.0.0"
__author__ = "Botnet Lab Team"

# Import all modules for easy access
try:
    from .stealth_evasion import *
except ImportError:
    pass

try:
    from .advanced_stealth_evasion import *
except ImportError:
    pass

try:
    from .advanced_evasion import *
except ImportError:
    pass

try:
    from .neural_network_evasion import *
except ImportError:
    pass

__all__ = [
    'stealth_evasion',
    'advanced_stealth_evasion',
    'advanced_evasion',
    'neural_network_evasion'
]
