#!/usr/bin/env python3
"""
Quick Info-Stealer Demo - Educational Information Stealing Framework
Based on RedLine, AZORult, and Emotet techniques
FOR EDUCATIONAL PURPOSES ONLY
"""

import os
import sys
import time
import json
from pathlib import Path

def print_banner():
    """Print Info-Stealer demo banner"""
    print("🔍 INFO-STEALER EDUCATIONAL DEMO")
    print("=" * 60)
    print("Information Stealing Learning Framework")
    print("Based on RedLine, AZORult, and Emotet techniques")
    print("FOR EDUCATIONAL AND AUTHORIZED TESTING ONLY")
    print("=" * 60)

def check_environment():
    """Check if environment is ready"""
    print("\n🔍 Checking Environment...")
    
    # Check if we're in the right directory
    if not os.path.exists("modules/info_stealer_educational.py"):
        print("❌ Info-Stealer module not found. Please run from botnet_lab directory.")
        return False
    
    # Check if virtual environment is active
    if not os.environ.get('VIRTUAL_ENV'):
        print("⚠️  Virtual environment not detected.")
        print("💡 Recommended: source botnet_env/bin/activate")
    
    # Check dependencies
    try:
        import sqlite3
        print("✅ sqlite3 library available")
    except ImportError:
        print("❌ sqlite3 library missing")
        return False
    
    try:
        import psutil
        print("✅ psutil library available")
    except ImportError:
        print("⚠️  psutil library missing (optional)")
    
    return True

def show_stealer_techniques():
    """Show Info-Stealer techniques overview"""
    print("\n🔍 Info-Stealer Techniques Overview:")
    print("-" * 50)
    
    techniques = {
        "RedLine Stealer": {
            "specialty": "Browser data & Cryptocurrency wallets",
            "targets": ["Chrome passwords", "Firefox cookies", "MetaMask wallets", "Discord tokens"],
            "distribution": "Dark web marketplaces",
            "popularity": "Very High"
        },
        "AZORult": {
            "specialty": "Comprehensive data theft & Gaming accounts",
            "targets": ["FTP clients", "Steam accounts", "Installed software", "Email clients"],
            "distribution": "Fake games and applications",
            "popularity": "High"
        },
        "Emotet": {
            "specialty": "Email harvesting & Lateral movement",
            "targets": ["Email contacts", "Network reconnaissance", "System persistence"],
            "distribution": "Email campaigns (historically)",
            "popularity": "Historical (shutdown 2021)"
        }
    }
    
    for stealer, info in techniques.items():
        print(f"\n🔸 {stealer}:")
        print(f"   Specialty: {info['specialty']}")
        print(f"   Targets: {', '.join(info['targets'][:2])}...")
        print(f"   Distribution: {info['distribution']}")
        print(f"   Popularity: {info['popularity']}")

def show_data_types():
    """Show types of data targeted by Info-Stealers"""
    print("\n📊 Data Types Targeted by Info-Stealers:")
    print("-" * 50)
    
    data_types = {
        "Browser Data": {
            "items": ["Saved passwords", "Session cookies", "Credit card info", "Autofill data"],
            "risk": "Critical",
            "impact": "Account takeover, Financial theft"
        },
        "Cryptocurrency": {
            "items": ["Wallet files", "Seed phrases", "Private keys", "Exchange sessions"],
            "risk": "Critical", 
            "impact": "Direct financial loss"
        },
        "Gaming Accounts": {
            "items": ["Steam library", "Epic Games", "Battle.net", "Game currencies"],
            "risk": "Medium",
            "impact": "Account theft, Virtual asset loss"
        },
        "Social Media": {
            "items": ["Facebook tokens", "Instagram sessions", "Discord tokens", "Telegram data"],
            "risk": "High",
            "impact": "Identity theft, Social engineering"
        },
        "Email Clients": {
            "items": ["Contact lists", "Email history", "Server settings", "Stored credentials"],
            "risk": "High",
            "impact": "Lateral attacks, Information disclosure"
        },
        "System Information": {
            "items": ["OS details", "Installed software", "Network config", "Hardware info"],
            "risk": "Medium",
            "impact": "Targeted attacks, Vulnerability exploitation"
        }
    }
    
    for category, info in data_types.items():
        print(f"\n📁 {category}:")
        print(f"   Items: {', '.join(info['items'][:2])}...")
        print(f"   Risk Level: {info['risk']}")
        print(f"   Impact: {info['impact']}")

def show_integration_examples():
    """Show integration with other modules"""
    print("\n🔗 Integration with Other Botnet Lab Modules:")
    print("-" * 50)
    
    integrations = {
        "Social Media Accounts": {
            "module": "modules/social_media_accounts.py",
            "integration": "Use stolen tokens for account access",
            "example": "Extract Facebook tokens → Access social media accounts"
        },
        "Password Cracking": {
            "module": "modules/password_cracking.py",
            "integration": "Decrypt stolen browser passwords",
            "example": "Extract encrypted passwords → Crack encryption → Access accounts"
        },
        "XSS Module": {
            "module": "modules/web_exploitation_xss.py",
            "integration": "Deploy Info-Stealer via XSS",
            "example": "XSS payload → Download Info-Stealer → Steal data"
        },
        "Social Engineering": {
            "module": "modules/social_engineering.py",
            "integration": "Use stolen data for targeted phishing",
            "example": "Steal contact lists → Create targeted phishing campaigns"
        },
        "RAT Module": {
            "module": "rat_module/",
            "integration": "Deploy Info-Stealer via RAT",
            "example": "RAT access → Install Info-Stealer → Continuous data theft"
        }
    }
    
    for name, info in integrations.items():
        print(f"\n🧩 {name}:")
        print(f"   Module: {info['module']}")
        print(f"   Integration: {info['integration']}")
        print(f"   Example: {info['example']}")

def show_defense_techniques():
    """Show defense techniques against Info-Stealers"""
    print("\n🛡️ Defense Techniques Against Info-Stealers:")
    print("-" * 50)
    
    defenses = {
        "Detection": [
            "Monitor file access to browser profiles",
            "Watch for cryptocurrency wallet access",
            "Detect unusual network connections",
            "Monitor process behavior patterns"
        ],
        "Prevention": [
            "Use browser master passwords",
            "Enable two-factor authentication",
            "Keep software updated",
            "Use endpoint protection solutions"
        ],
        "Response": [
            "Isolate infected systems immediately",
            "Change all compromised passwords",
            "Review account activities",
            "Restore from clean backups"
        ],
        "Monitoring": [
            "Implement file integrity monitoring",
            "Use network traffic analysis",
            "Deploy behavioral analytics",
            "Monitor for IOCs (Indicators of Compromise)"
        ]
    }
    
    for category, techniques in defenses.items():
        print(f"\n🔹 {category}:")
        for technique in techniques:
            print(f"   • {technique}")

def show_safety_reminders():
    """Show safety and legal reminders"""
    print("\n⚠️  SAFETY AND LEGAL REMINDERS:")
    print("-" * 50)
    print("✅ Only test on systems you own or have explicit permission")
    print("✅ Use isolated environments for learning")
    print("✅ Follow all applicable laws and regulations")
    print("✅ Respect privacy and data protection laws")
    print("✅ Use knowledge for defensive purposes only")
    print("❌ Never test on unauthorized systems")
    print("❌ Never use for malicious purposes")
    print("❌ Never access others' data without permission")
    print("❌ Never distribute or sell stolen information")

def run_info_stealer_module():
    """Run the Info-Stealer educational module"""
    print("\n🚀 Starting Info-Stealer Educational Module...")
    
    try:
        # Import and run the Info-Stealer module
        sys.path.append('modules')
        from modules.security_exploitation.info_stealer_educational import educational_info_stealer_demo
        
        print("✅ Info-Stealer module loaded successfully")
        print("🔍 Starting educational Info-Stealer environment...")
        
        # Run the demo
        educational_info_stealer_demo()
        
    except ImportError as e:
        print(f"❌ Error importing Info-Stealer module: {e}")
        print("💡 Make sure you're in the botnet_lab directory")
        return False
    except Exception as e:
        print(f"❌ Error running Info-Stealer module: {e}")
        return False
    
    return True

def show_learning_path():
    """Show recommended learning path"""
    print("\n🎓 Recommended Learning Path:")
    print("-" * 50)
    
    steps = [
        "1. Understand Info-Stealer basics - What they steal and how",
        "2. Study RedLine techniques - Browser data extraction",
        "3. Learn AZORult methods - Gaming and FTP client targeting",
        "4. Explore Emotet behavior - Email harvesting and lateral movement",
        "5. Practice social media token extraction",
        "6. Understand cryptocurrency wallet targeting",
        "7. Learn system fingerprinting techniques",
        "8. Study network reconnaissance methods",
        "9. Understand persistence mechanisms",
        "10. Learn defense and detection techniques"
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print(f"\n📚 Additional Resources:")
    print("   • MITRE ATT&CK - Credential Access techniques")
    print("   • SANS InfoSec Reading Room")
    print("   • Malware analysis sandboxes")
    print("   • Threat intelligence reports")

def main():
    """Main demo function"""
    print_banner()
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed. Please fix issues and try again.")
        return
    
    print("\n✅ Environment check passed!")
    
    # Show safety reminders
    show_safety_reminders()
    
    # Get user confirmation
    print(f"\n🤔 Do you understand and agree to use this for educational purposes only?")
    response = input("Type 'yes' to continue: ").strip().lower()
    
    if response not in ['yes', 'y']:
        print("❌ Demo cancelled. Please read the safety guidelines carefully.")
        return
    
    # Show menu
    while True:
        print(f"\n📋 Info-Stealer Educational Demo Menu:")
        print("1. Show Info-Stealer Techniques Overview")
        print("2. Show Data Types Targeted")
        print("3. Show Module Integration Examples")
        print("4. Show Defense Techniques")
        print("5. Show Learning Path")
        print("6. Run Info-Stealer Educational Module")
        print("7. Open Info-Stealer Guide")
        print("8. Exit")
        
        choice = input("\nSelect option (1-8): ").strip()
        
        if choice == '1':
            show_stealer_techniques()
        elif choice == '2':
            show_data_types()
        elif choice == '3':
            show_integration_examples()
        elif choice == '4':
            show_defense_techniques()
        elif choice == '5':
            show_learning_path()
        elif choice == '6':
            if run_info_stealer_module():
                print("\n✅ Info-Stealer module completed successfully")
            else:
                print("\n❌ Info-Stealer module failed to run")
        elif choice == '7':
            guide_path = "docs/INFO_STEALER_EDUCATIONAL_GUIDE.md"
            if os.path.exists(guide_path):
                print(f"\n📖 Opening Info-Stealer guide: {guide_path}")
                try:
                    if sys.platform.startswith('linux'):
                        os.system(f"xdg-open {guide_path}")
                    elif sys.platform.startswith('darwin'):
                        os.system(f"open {guide_path}")
                    elif sys.platform.startswith('win'):
                        os.system(f"start {guide_path}")
                    else:
                        print(f"Please manually open: {guide_path}")
                except:
                    print(f"Please manually open: {guide_path}")
            else:
                print(f"❌ Guide not found: {guide_path}")
        elif choice == '8':
            print("\n👋 Thank you for using Info-Stealer Educational Demo!")
            print("🛡️ Remember: Use this knowledge responsibly and ethically!")
            break
        else:
            print("❌ Invalid option. Please select 1-8.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n[!] Demo interrupted by user")
        print("🛡️ Remember: Use Info-Stealer knowledge responsibly!")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        print("🛡️ Remember: Use Info-Stealer knowledge responsibly!")
