#!/usr/bin/env python3
"""
إعداد CI/CD Pipeline لمشروع Botnet Lab
"""

import os
from pathlib import Path

class CICDSetup:
    def __init__(self, project_path):
        self.project_path = Path(project_path)
        
    def create_github_actions(self):
        """إنشاء GitHub Actions workflow"""
        github_dir = self.project_path / ".github" / "workflows"
        github_dir.mkdir(parents=True, exist_ok=True)
        
        # ملف CI/CD الرئيسي
        ci_workflow = """name: Botnet Lab CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, '3.10', '3.11']

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r scripts/requirements_unified.txt
        pip install pytest pytest-cov flake8 black
    
    - name: Lint with flake8
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Format check with black
      run: |
        black --check .
    
    - name: Test Core Components
      run: |
        python -m pytest tests/ -v --cov=core --cov-report=xml
    
    - name: Test Standalone Modules
      run: |
        cd standalone && python run_all_tests.py
    
    - name: Test RAT Module
      run: |
        cd rat && python tests/test_rat_framework.py
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Run Bandit Security Scan
      run: |
        pip install bandit
        bandit -r . -f json -o bandit-report.json || true
    
    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      with:
        name: security-scan-results
        path: bandit-report.json

  build-documentation:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Python
      uses: actions/setup-python@v3
      with:
        python-version: '3.10'
    
    - name: Install documentation dependencies
      run: |
        pip install mkdocs mkdocs-material
    
    - name: Build documentation
      run: |
        mkdocs build
    
    - name: Deploy to GitHub Pages
      if: github.ref == 'refs/heads/main'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./site
"""
        
        (github_dir / "ci.yml").write_text(ci_workflow, encoding='utf-8')
        print("✅ تم إنشاء GitHub Actions workflow")
    
    def create_pre_commit_hooks(self):
        """إنشاء pre-commit hooks"""
        pre_commit_config = """repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
  
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        language_version: python3
  
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=127]
  
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, ., -f, json, -o, bandit-report.json]
        pass_filenames: false
  
  - repo: local
    hooks:
      - id: pytest
        name: pytest
        entry: pytest
        language: python
        pass_filenames: false
        always_run: true
        args: [tests/, -v]
"""
        
        (self.project_path / ".pre-commit-config.yaml").write_text(pre_commit_config, encoding='utf-8')
        print("✅ تم إنشاء pre-commit configuration")
    
    def create_makefile(self):
        """إنشاء Makefile للمهام الشائعة"""
        makefile_content = """# Botnet Lab Makefile

.PHONY: help install test lint format clean setup-dev

help:
	@echo "Available commands:"
	@echo "  install     - Install dependencies"
	@echo "  test        - Run all tests"
	@echo "  lint        - Run code linting"
	@echo "  format      - Format code with black"
	@echo "  clean       - Clean temporary files"
	@echo "  setup-dev   - Setup development environment"

install:
	pip install -r scripts/requirements_unified.txt

test:
	python -m pytest tests/ -v
	cd standalone && python run_all_tests.py
	cd rat && python tests/test_rat_framework.py

lint:
	flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
	flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

format:
	black .

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type f -name "*.log" -delete
	find . -type f -name "*.db" -delete

setup-dev:
	python -m venv botnet_env
	source botnet_env/bin/activate && pip install -r scripts/requirements_unified.txt
	source botnet_env/bin/activate && pip install pre-commit pytest black flake8
	pre-commit install

security-scan:
	bandit -r . -f json -o bandit-report.json

docs:
	mkdocs serve

build-docs:
	mkdocs build
"""
        
        (self.project_path / "Makefile").write_text(makefile_content, encoding='utf-8')
        print("✅ تم إنشاء Makefile")
    
    def create_docker_setup(self):
        """إنشاء ملفات Docker"""
        # Dockerfile
        dockerfile_content = """FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    make \\
    libffi-dev \\
    libssl-dev \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY scripts/requirements_unified.txt .
RUN pip install --no-cache-dir -r requirements_unified.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 botnet && chown -R botnet:botnet /app
USER botnet

# Expose ports
EXPOSE 8080 8443 4444

# Default command
CMD ["python", "core/c2_server.py"]
"""
        
        (self.project_path / "Dockerfile").write_text(dockerfile_content, encoding='utf-8')
        
        # Docker Compose
        docker_compose_content = """version: '3.8'

services:
  c2-server:
    build: .
    ports:
      - "8080:8080"
      - "8443:8443"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
    command: python core/c2_server.py --host 0.0.0.0

  advanced-c2:
    build: .
    ports:
      - "8081:8080"
      - "8444:8443"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
    command: python core/advanced_c2_server.py --host 0.0.0.0

  rat-server:
    build: .
    ports:
      - "4444:4444"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
    command: python rat/core/rat_server.py --host 0.0.0.0

  dashboard:
    build: .
    ports:
      - "8082:8080"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
    command: python rat/tools/dashboard.py --host 0.0.0.0

volumes:
  data:
  logs:
"""
        
        (self.project_path / "docker-compose.yml").write_text(docker_compose_content, encoding='utf-8')
        print("✅ تم إنشاء ملفات Docker")
    
    def create_gitignore(self):
        """إنشاء ملف .gitignore شامل"""
        gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
botnet_env/
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
logs/

# Databases
*.db
*.sqlite3
data/

# OS
.DS_Store
Thumbs.db

# Security
*.key
*.pem
*.crt
bandit-report.json

# Testing
.coverage
.pytest_cache/
htmlcov/

# Documentation
site/

# Temporary files
*.tmp
*.temp
temp/

# Backup files
backup_*/
*.bak

# Node modules (for any JS dependencies)
node_modules/

# Environment variables
.env
.env.local

# Screenshots and media
screenshots/
*.png
*.jpg
*.jpeg
*.gif
*.mp4

# Cache
cache/
.cache/

# Results and reports
results/
reports/
"""
        
        (self.project_path / ".gitignore").write_text(gitignore_content, encoding='utf-8')
        print("✅ تم إنشاء ملف .gitignore")
    
    def setup_cicd(self):
        """إعداد CI/CD كامل"""
        print("🔄 إعداد CI/CD Pipeline...")
        
        self.create_github_actions()
        self.create_pre_commit_hooks()
        self.create_makefile()
        self.create_docker_setup()
        self.create_gitignore()
        
        print("\n🎉 تم إعداد CI/CD Pipeline بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. git init (إذا لم يكن مستودع git)")
        print("2. git add .")
        print("3. git commit -m 'Initial commit with CI/CD setup'")
        print("4. git remote add origin <repository-url>")
        print("5. git push -u origin main")
        print("\n🔧 لتفعيل pre-commit hooks:")
        print("pip install pre-commit && pre-commit install")

def main():
    project_path = "/home/<USER>/Desktop/Year3/botnet/botnet_lab"
    
    cicd_setup = CICDSetup(project_path)
    cicd_setup.setup_cicd()

if __name__ == "__main__":
    main()
