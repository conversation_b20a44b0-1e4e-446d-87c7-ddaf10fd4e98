#!/usr/bin/env python3
# Password Cracking Test Suite

import sys
import time
import json
import hashlib
from datetime import datetime

# Import the standalone module
from password_cracking_standalone import StandalonePasswordCracking

class PasswordCrackingTester:
    def __init__(self):
        self.password_cracking = StandalonePasswordCracking()
        self.test_results = []
        
    def run_all_tests(self):
        """Run comprehensive test suite"""
        print("🔐 PASSWORD CRACKING TEST SUITE")
        print("=" * 60)
        
        # Test 1: System Initialization
        print("\n🔧 Test 1: System Initialization")
        self.test_system_initialization()
        
        # Test 2: Wordlist Loading
        print("\n📚 Test 2: Wordlist Loading")
        self.test_wordlist_loading()
        
        # Test 3: Dictionary Attack
        print("\n📖 Test 3: Dictionary Attack")
        self.test_dictionary_attack()
        
        # Test 4: Brute Force Attack
        print("\n💪 Test 4: Brute Force Attack")
        self.test_brute_force_attack()
        
        # Test 5: Hash Cracking
        print("\n🔐 Test 5: Hash Cracking")
        self.test_hash_cracking()
        
        # Test 6: Credential Stuffing
        print("\n🎯 Test 6: Credential Stuffing")
        self.test_credential_stuffing()
        
        # Test 7: Spear Phishing
        print("\n🎣 Test 7: Spear Phishing")
        self.test_spear_phishing()
        
        # Test 8: Database Operations
        print("\n💾 Test 8: Database Operations")
        self.test_database_operations()
        
        # Test 9: System Performance
        print("\n⚡ Test 9: System Performance")
        self.test_system_performance()
        
        # Generate test report
        self.generate_test_report()
    
    def test_system_initialization(self):
        """Test system initialization"""
        try:
            # Start system
            success = self.password_cracking.start_password_cracking_system()
            
            if success:
                print("[✓] System started successfully")
                
                # Check capabilities
                status = self.password_cracking.get_system_status()
                capabilities = status['capabilities']
                engines = status['engines']
                
                print(f"[*] Capabilities enabled: {sum(capabilities.values())}/{len(capabilities)}")
                for capability, enabled in capabilities.items():
                    status_icon = "✓" if enabled else "✗"
                    print(f"    [{status_icon}] {capability}")
                
                print(f"[*] Attack engines: {len(engines)}")
                for engine in engines:
                    print(f"    [✓] {engine}")
                
                self.test_results.append({
                    'test': 'system_initialization',
                    'status': 'passed',
                    'details': f"Capabilities: {sum(capabilities.values())}/{len(capabilities)}, Engines: {len(engines)}"
                })
            else:
                print("[✗] System failed to start")
                self.test_results.append({
                    'test': 'system_initialization',
                    'status': 'failed',
                    'details': 'System startup failed'
                })
                
        except Exception as e:
            print(f"[✗] Initialization test error: {e}")
            self.test_results.append({
                'test': 'system_initialization',
                'status': 'error',
                'details': str(e)
            })
    
    def test_wordlist_loading(self):
        """Test wordlist loading functionality"""
        try:
            status = self.password_cracking.get_system_status()
            wordlists = status['wordlists']
            
            print(f"[*] Loaded wordlists: {len(wordlists)}")
            
            total_words = 0
            for name, count in wordlists.items():
                print(f"    - {name}: {count:,} words")
                total_words += count
            
            print(f"[*] Total words available: {total_words:,}")
            
            # Check required wordlists
            required_wordlists = ['common', 'numeric', 'dates', 'names']
            missing_wordlists = [wl for wl in required_wordlists if wl not in wordlists]
            
            if not missing_wordlists and total_words > 1000:
                print("[✓] Wordlist loading test passed")
                self.test_results.append({
                    'test': 'wordlist_loading',
                    'status': 'passed',
                    'details': f"Loaded {len(wordlists)} wordlists with {total_words:,} total words"
                })
            else:
                print(f"[✗] Wordlist loading test failed - Missing: {missing_wordlists}")
                self.test_results.append({
                    'test': 'wordlist_loading',
                    'status': 'failed',
                    'details': f"Missing wordlists: {missing_wordlists}"
                })
                
        except Exception as e:
            print(f"[✗] Wordlist loading test error: {e}")
            self.test_results.append({
                'test': 'wordlist_loading',
                'status': 'error',
                'details': str(e)
            })
    
    def test_dictionary_attack(self):
        """Test dictionary attack functionality"""
        try:
            print("[*] Testing dictionary attack...")
            
            # Test configuration
            config = {
                'target': 'test_server',
                'wordlist': 'common',
                'transformations': True
            }
            
            print(f"[*] Target: {config['target']}")
            print(f"[*] Wordlist: {config['wordlist']}")
            print(f"[*] Transformations: {config['transformations']}")
            
            # Execute attack
            operation_id = self.password_cracking.execute_dictionary_attack(config)
            
            if operation_id:
                print(f"[*] Operation ID: {operation_id}")
                
                # Check statistics
                status = self.password_cracking.get_system_status()
                stats = status['statistics']
                
                print(f"[*] Passwords tested: {stats['passwords_tested']}")
                print(f"[*] Campaigns launched: {stats['campaigns_launched']}")
                
                if stats['passwords_tested'] > 0:
                    print("[✓] Dictionary attack test passed")
                    self.test_results.append({
                        'test': 'dictionary_attack',
                        'status': 'passed',
                        'details': f"Operation: {operation_id}, Passwords tested: {stats['passwords_tested']}"
                    })
                else:
                    print("[✗] Dictionary attack test failed - No passwords tested")
                    self.test_results.append({
                        'test': 'dictionary_attack',
                        'status': 'failed',
                        'details': 'No passwords were tested'
                    })
            else:
                print("[✗] Dictionary attack failed to start")
                self.test_results.append({
                    'test': 'dictionary_attack',
                    'status': 'failed',
                    'details': 'Attack failed to start'
                })
                
        except Exception as e:
            print(f"[✗] Dictionary attack test error: {e}")
            self.test_results.append({
                'test': 'dictionary_attack',
                'status': 'error',
                'details': str(e)
            })
    
    def test_brute_force_attack(self):
        """Test brute force attack functionality"""
        try:
            print("[*] Testing brute force attack...")
            
            # Test configuration
            config = {
                'target': 'test_server',
                'charset': 'abc123',
                'min_length': 1,
                'max_length': 3
            }
            
            print(f"[*] Target: {config['target']}")
            print(f"[*] Charset: {config['charset']}")
            print(f"[*] Length range: {config['min_length']}-{config['max_length']}")
            
            # Execute attack
            operation_id = self.password_cracking.execute_brute_force_attack(config)
            
            if operation_id:
                print(f"[*] Operation ID: {operation_id}")
                
                # Check statistics
                status = self.password_cracking.get_system_status()
                stats = status['statistics']
                
                print(f"[*] Total passwords tested: {stats['passwords_tested']}")
                
                if stats['passwords_tested'] > 0:
                    print("[✓] Brute force attack test passed")
                    self.test_results.append({
                        'test': 'brute_force_attack',
                        'status': 'passed',
                        'details': f"Operation: {operation_id}, Passwords tested: {stats['passwords_tested']}"
                    })
                else:
                    print("[✗] Brute force attack test failed - No passwords tested")
                    self.test_results.append({
                        'test': 'brute_force_attack',
                        'status': 'failed',
                        'details': 'No passwords were tested'
                    })
            else:
                print("[✗] Brute force attack failed to start")
                self.test_results.append({
                    'test': 'brute_force_attack',
                    'status': 'failed',
                    'details': 'Attack failed to start'
                })
                
        except Exception as e:
            print(f"[✗] Brute force attack test error: {e}")
            self.test_results.append({
                'test': 'brute_force_attack',
                'status': 'error',
                'details': str(e)
            })
    
    def test_hash_cracking(self):
        """Test hash cracking functionality"""
        try:
            print("[*] Testing hash cracking...")
            
            # Create test hash
            test_password = "password"
            test_hash = hashlib.md5(test_password.encode()).hexdigest()
            
            # Test configuration
            config = {
                'hash_value': test_hash,
                'hash_type': 'md5',
                'attack_mode': 'dictionary'
            }
            
            print(f"[*] Hash: {test_hash[:20]}...")
            print(f"[*] Hash type: {config['hash_type']}")
            print(f"[*] Attack mode: {config['attack_mode']}")
            
            # Execute attack
            operation_id = self.password_cracking.execute_hash_cracking(config)
            
            if operation_id:
                print(f"[*] Operation ID: {operation_id}")
                
                # Check statistics
                status = self.password_cracking.get_system_status()
                stats = status['statistics']
                
                print(f"[*] Total passwords tested: {stats['passwords_tested']}")
                
                if stats['passwords_tested'] > 0:
                    print("[✓] Hash cracking test passed")
                    self.test_results.append({
                        'test': 'hash_cracking',
                        'status': 'passed',
                        'details': f"Operation: {operation_id}, Hash type: {config['hash_type']}"
                    })
                else:
                    print("[✗] Hash cracking test failed - No hashes computed")
                    self.test_results.append({
                        'test': 'hash_cracking',
                        'status': 'failed',
                        'details': 'No hashes were computed'
                    })
            else:
                print("[✗] Hash cracking failed to start")
                self.test_results.append({
                    'test': 'hash_cracking',
                    'status': 'failed',
                    'details': 'Hash cracking failed to start'
                })
                
        except Exception as e:
            print(f"[✗] Hash cracking test error: {e}")
            self.test_results.append({
                'test': 'hash_cracking',
                'status': 'error',
                'details': str(e)
            })
    
    def test_credential_stuffing(self):
        """Test credential stuffing functionality"""
        try:
            print("[*] Testing credential stuffing...")
            
            # Test configuration
            config = {
                'platform': 'test_platform',
                'credential_source': 'breach_2024',
                'proxy_enabled': True
            }
            
            print(f"[*] Platform: {config['platform']}")
            print(f"[*] Credential source: {config['credential_source']}")
            print(f"[*] Proxy enabled: {config['proxy_enabled']}")
            
            # Execute attack
            operation_id = self.password_cracking.execute_credential_stuffing(config)
            
            if operation_id:
                print(f"[*] Operation ID: {operation_id}")
                
                # Check statistics
                status = self.password_cracking.get_system_status()
                stats = status['statistics']
                
                print(f"[*] Total passwords tested: {stats['passwords_tested']}")
                
                if stats['passwords_tested'] > 0:
                    print("[✓] Credential stuffing test passed")
                    self.test_results.append({
                        'test': 'credential_stuffing',
                        'status': 'passed',
                        'details': f"Operation: {operation_id}, Platform: {config['platform']}"
                    })
                else:
                    print("[✗] Credential stuffing test failed - No credentials tested")
                    self.test_results.append({
                        'test': 'credential_stuffing',
                        'status': 'failed',
                        'details': 'No credentials were tested'
                    })
            else:
                print("[✗] Credential stuffing failed to start")
                self.test_results.append({
                    'test': 'credential_stuffing',
                    'status': 'failed',
                    'details': 'Credential stuffing failed to start'
                })
                
        except Exception as e:
            print(f"[✗] Credential stuffing test error: {e}")
            self.test_results.append({
                'test': 'credential_stuffing',
                'status': 'error',
                'details': str(e)
            })
    
    def test_spear_phishing(self):
        """Test spear phishing functionality"""
        try:
            print("[*] Testing spear phishing...")
            
            # Test configuration
            config = {
                'target_email': '<EMAIL>',
                'campaign_type': 'credential_harvesting',
                'personalization_level': 'medium'
            }
            
            print(f"[*] Target email: {config['target_email']}")
            print(f"[*] Campaign type: {config['campaign_type']}")
            print(f"[*] Personalization: {config['personalization_level']}")
            
            # Execute campaign
            operation_id = self.password_cracking.execute_spear_phishing_campaign(config)
            
            if operation_id:
                print(f"[*] Operation ID: {operation_id}")
                
                # Check statistics
                status = self.password_cracking.get_system_status()
                stats = status['statistics']
                
                print(f"[*] Total campaigns launched: {stats['campaigns_launched']}")
                
                if stats['campaigns_launched'] > 0:
                    print("[✓] Spear phishing test passed")
                    self.test_results.append({
                        'test': 'spear_phishing',
                        'status': 'passed',
                        'details': f"Operation: {operation_id}, Campaign type: {config['campaign_type']}"
                    })
                else:
                    print("[✗] Spear phishing test failed - No campaigns launched")
                    self.test_results.append({
                        'test': 'spear_phishing',
                        'status': 'failed',
                        'details': 'No campaigns were launched'
                    })
            else:
                print("[✗] Spear phishing failed to start")
                self.test_results.append({
                    'test': 'spear_phishing',
                    'status': 'failed',
                    'details': 'Spear phishing failed to start'
                })
                
        except Exception as e:
            print(f"[✗] Spear phishing test error: {e}")
            self.test_results.append({
                'test': 'spear_phishing',
                'status': 'error',
                'details': str(e)
            })
    
    def test_database_operations(self):
        """Test database operations"""
        try:
            print("[*] Testing database operations...")
            
            # Test operation storage
            test_operation_id = 'test_operation_123'
            test_config = {'test': 'config'}
            test_results = {'success': True, 'passwords_tested': 100}
            
            self.password_cracking.store_cracking_operation(
                test_operation_id, 'test_attack', test_config, test_results
            )
            print("[✓] Operation storage test passed")
            
            # Test custom wordlist generation
            target_info = {
                'name': 'john doe',
                'company': 'test corp',
                'location': 'new york',
                'birth_year': '1990',
                'interests': ['technology', 'sports']
            }
            
            custom_wordlist = self.password_cracking.generate_custom_wordlist(target_info)
            print(f"[*] Generated {len(custom_wordlist)} custom passwords")
            
            if len(custom_wordlist) > 0:
                print("[✓] Custom wordlist generation test passed")
            else:
                print("[✗] Custom wordlist generation test failed")
            
            self.test_results.append({
                'test': 'database_operations',
                'status': 'passed',
                'details': f'Custom wordlist: {len(custom_wordlist)} passwords'
            })
            
        except Exception as e:
            print(f"[✗] Database operations test error: {e}")
            self.test_results.append({
                'test': 'database_operations',
                'status': 'error',
                'details': str(e)
            })
    
    def test_system_performance(self):
        """Test system performance"""
        try:
            print("[*] Testing system performance...")
            
            # Test multiple operations
            start_time = time.time()
            
            # Quick dictionary attack
            dict_config = {
                'target': 'perf_test',
                'wordlist': 'common',
                'transformations': False
            }
            
            operation_id = self.password_cracking.execute_dictionary_attack(dict_config)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"[*] Dictionary attack completed in {execution_time:.2f} seconds")
            
            # Check system status
            status = self.password_cracking.get_system_status()
            stats = status['statistics']
            
            print(f"[*] Total operations: {stats['campaigns_launched']}")
            print(f"[*] Total passwords tested: {stats['passwords_tested']}")
            print(f"[*] Success rate: {stats['success_rate']:.2%}")
            print(f"[*] Hash rate: {stats['hash_rate']:,.0f} hashes/sec")
            
            if execution_time < 10 and operation_id:  # Should complete within 10 seconds
                print("[✓] System performance test passed")
                self.test_results.append({
                    'test': 'system_performance',
                    'status': 'passed',
                    'details': f"Execution time: {execution_time:.2f}s"
                })
            else:
                print("[!] System performance slower than expected")
                self.test_results.append({
                    'test': 'system_performance',
                    'status': 'warning',
                    'details': f"Slow execution time: {execution_time:.2f}s"
                })
                
        except Exception as e:
            print(f"[✗] System performance test error: {e}")
            self.test_results.append({
                'test': 'system_performance',
                'status': 'error',
                'details': str(e)
            })
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📊 PASSWORD CRACKING TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['status'] == 'passed')
        failed_tests = sum(1 for result in self.test_results if result['status'] == 'failed')
        error_tests = sum(1 for result in self.test_results if result['status'] == 'error')
        warning_tests = sum(1 for result in self.test_results if result['status'] == 'warning')
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        print(f"Errors: {error_tests} ({error_tests/total_tests*100:.1f}%)")
        print(f"Warnings: {warning_tests} ({warning_tests/total_tests*100:.1f}%)")
        
        print(f"\nSuccess Rate: {passed_tests/total_tests*100:.1f}%")
        
        print("\nDetailed Results:")
        for result in self.test_results:
            status_icon = {
                'passed': '✓',
                'failed': '✗',
                'error': '⚠',
                'warning': '!'
            }.get(result['status'], '?')
            
            print(f"  [{status_icon}] {result['test']}: {result['details']}")
        
        # Overall assessment
        if passed_tests / total_tests >= 0.8:
            print(f"\n🎉 OVERALL ASSESSMENT: EXCELLENT")
            print("The password cracking module is working very well!")
        elif passed_tests / total_tests >= 0.6:
            print(f"\n👍 OVERALL ASSESSMENT: GOOD")
            print("The password cracking module is working adequately.")
        else:
            print(f"\n⚠️ OVERALL ASSESSMENT: NEEDS IMPROVEMENT")
            print("The password cracking module needs attention.")
        
        print("\n" + "=" * 60)

def main():
    """Main test function"""
    print("🧪 Starting Password Cracking Test Suite...")
    
    try:
        tester = PasswordCrackingTester()
        tester.run_all_tests()
        
    except KeyboardInterrupt:
        print("\n[!] Tests interrupted by user")
    except Exception as e:
        print(f"\n[✗] Test suite error: {e}")
    finally:
        print("\n[*] Test suite completed")

if __name__ == "__main__":
    main()
