#!/usr/bin/env python3
# Standalone Phone Number Targeting Module
# Advanced phone intelligence and targeting capabilities

import os
import sys
import time
import json
import threading
import sqlite3
import random
import string
import hashlib
import uuid
import re
from datetime import datetime, timedelta
from collections import defaultdict, deque
import pickle

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("[!] Requests not available - some features disabled")

try:
    import phonenumbers
    from phonenumbers import geocoder, carrier, timezone
    PHONENUMBERS_AVAILABLE = True
except ImportError:
    PHONENUMBERS_AVAILABLE = False
    print("[!] Phonenumbers library not available - install with: pip install phonenumbers")

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    print("[!] NumPy not available - AI features disabled")

class StandalonePhoneTargeting:
    def __init__(self):
        self.active = False
        self.database_path = "phone_targeting.db"
        
        # Phone targeting capabilities
        self.capabilities = {
            'osint_gathering': REQUESTS_AVAILABLE,
            'phone_validation': PHONENUMBERS_AVAILABLE,
            'sms_attacks': True,
            'sim_swapping': True,
            'phone_profiling': True,
            'ai_analysis': NUMPY_AVAILABLE
        }
        
        # Phone intelligence data
        self.phone_intelligence = {}
        self.target_profiles = {}
        self.attack_campaigns = {}
        
        # Statistics
        self.stats = {
            'phones_analyzed': 0,
            'profiles_created': 0,
            'attacks_launched': 0,
            'success_rate': 0.0
        }
        
        print("[+] Standalone Phone Targeting Module initialized")
        print(f"[*] Capabilities: {sum(self.capabilities.values())}/{len(self.capabilities)} enabled")
        
        self.init_database()
    
    def init_database(self):
        """Initialize phone targeting database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # Phone intelligence table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS phone_intelligence (
                    id INTEGER PRIMARY KEY,
                    phone_number TEXT UNIQUE,
                    country_code TEXT,
                    carrier TEXT,
                    location TEXT,
                    number_type TEXT,
                    validation_status TEXT,
                    osint_data TEXT,
                    risk_score REAL,
                    last_updated TEXT
                )
            ''')
            
            # Target profiles table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS target_profiles (
                    id INTEGER PRIMARY KEY,
                    profile_id TEXT UNIQUE,
                    phone_number TEXT,
                    personal_info TEXT,
                    social_media TEXT,
                    vulnerabilities TEXT,
                    attack_vectors TEXT,
                    profile_score REAL,
                    creation_date TEXT
                )
            ''')
            
            # Attack campaigns table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS attack_campaigns (
                    id INTEGER PRIMARY KEY,
                    campaign_id TEXT UNIQUE,
                    campaign_type TEXT,
                    target_phone TEXT,
                    attack_config TEXT,
                    start_time TEXT,
                    end_time TEXT,
                    status TEXT,
                    success_rate REAL,
                    results TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("[+] Phone targeting database initialized")
            
        except Exception as e:
            print(f"[-] Database initialization error: {e}")
    
    def start_phone_targeting(self):
        """Start phone targeting system"""
        print("[*] Starting phone targeting system...")
        
        try:
            self.active = True
            
            # Start monitoring threads
            monitoring_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
            monitoring_thread.start()
            
            print("[+] Phone targeting system started successfully")
            return True
            
        except Exception as e:
            print(f"[-] Phone targeting start error: {e}")
            return False
    
    def analyze_phone_number(self, phone_number, country_code=None):
        """Analyze phone number and gather intelligence"""
        try:
            print(f"[*] Analyzing phone number: {phone_number}")
            
            analysis_result = {
                'phone_number': phone_number,
                'timestamp': datetime.now().isoformat(),
                'analysis_id': str(uuid.uuid4())
            }
            
            # Phone number validation and parsing
            if PHONENUMBERS_AVAILABLE:
                validation_result = self.validate_phone_number(phone_number, country_code)
                analysis_result.update(validation_result)
            
            # OSINT gathering
            if REQUESTS_AVAILABLE:
                osint_result = self.gather_phone_osint(phone_number)
                analysis_result['osint_data'] = osint_result
            
            # Risk assessment
            risk_score = self.calculate_risk_score(analysis_result)
            analysis_result['risk_score'] = risk_score
            
            # Store in database
            self.store_phone_intelligence(analysis_result)
            
            # Update statistics
            self.stats['phones_analyzed'] += 1
            
            print(f"[+] Phone analysis completed: {analysis_result['analysis_id']}")
            return analysis_result
            
        except Exception as e:
            print(f"[-] Phone analysis error: {e}")
            return None
    
    def validate_phone_number(self, phone_number, country_code):
        """Validate and parse phone number"""
        try:
            # Parse phone number
            if country_code:
                parsed_number = phonenumbers.parse(phone_number, country_code)
            else:
                parsed_number = phonenumbers.parse(phone_number, None)
            
            # Validation
            is_valid = phonenumbers.is_valid_number(parsed_number)
            is_possible = phonenumbers.is_possible_number(parsed_number)
            
            # Get information
            country = geocoder.description_for_number(parsed_number, "en")
            carrier_name = carrier.name_for_number(parsed_number, "en")
            number_type = phonenumbers.number_type(parsed_number)
            timezones = timezone.time_zones_for_number(parsed_number)
            
            # Format number
            international_format = phonenumbers.format_number(parsed_number, phonenumbers.PhoneNumberFormat.INTERNATIONAL)
            national_format = phonenumbers.format_number(parsed_number, phonenumbers.PhoneNumberFormat.NATIONAL)
            e164_format = phonenumbers.format_number(parsed_number, phonenumbers.PhoneNumberFormat.E164)
            
            return {
                'validation_status': 'valid' if is_valid else 'invalid',
                'is_possible': is_possible,
                'country_code': parsed_number.country_code,
                'national_number': parsed_number.national_number,
                'country': country,
                'carrier': carrier_name,
                'number_type': self.get_number_type_string(number_type),
                'timezones': list(timezones),
                'international_format': international_format,
                'national_format': national_format,
                'e164_format': e164_format
            }
            
        except Exception as e:
            return {
                'validation_status': 'error',
                'error': str(e)
            }
    
    def get_number_type_string(self, number_type):
        """Convert number type enum to string"""
        type_mapping = {
            0: 'FIXED_LINE',
            1: 'MOBILE',
            2: 'FIXED_LINE_OR_MOBILE',
            3: 'TOLL_FREE',
            4: 'PREMIUM_RATE',
            5: 'SHARED_COST',
            6: 'VOIP',
            7: 'PERSONAL_NUMBER',
            8: 'PAGER',
            9: 'UAN',
            10: 'VOICEMAIL',
            99: 'UNKNOWN'
        }
        return type_mapping.get(number_type, 'UNKNOWN')
    
    def gather_phone_osint(self, phone_number):
        """Gather OSINT data for phone number"""
        try:
            osint_data = {
                'search_engines': self.search_engine_lookup(phone_number),
                'social_media': self.social_media_lookup(phone_number),
                'data_breaches': self.data_breach_lookup(phone_number),
                'public_records': self.public_records_lookup(phone_number),
                'reverse_lookup': self.reverse_phone_lookup(phone_number)
            }
            
            return osint_data
            
        except Exception as e:
            return {'error': str(e)}
    
    def search_engine_lookup(self, phone_number):
        """Search for phone number in search engines"""
        try:
            # Simulate search engine results
            results = {
                'google_results': random.randint(0, 100),
                'bing_results': random.randint(0, 50),
                'social_mentions': random.randint(0, 20),
                'business_listings': random.randint(0, 10),
                'spam_reports': random.randint(0, 5)
            }
            
            return results
            
        except Exception as e:
            return {'error': str(e)}
    
    def social_media_lookup(self, phone_number):
        """Look up phone number on social media platforms"""
        try:
            # Simulate social media lookup
            platforms = ['facebook', 'twitter', 'instagram', 'linkedin', 'whatsapp', 'telegram']
            results = {}
            
            for platform in platforms:
                results[platform] = {
                    'found': random.random() < 0.3,  # 30% chance
                    'profile_url': f"https://{platform}.com/user/{random.randint(1000, 9999)}" if random.random() < 0.3 else None,
                    'username': f"user{random.randint(1000, 9999)}" if random.random() < 0.3 else None,
                    'last_activity': datetime.now().isoformat() if random.random() < 0.2 else None
                }
            
            return results
            
        except Exception as e:
            return {'error': str(e)}
    
    def data_breach_lookup(self, phone_number):
        """Check if phone number appears in data breaches"""
        try:
            # Simulate data breach lookup
            breaches = [
                'Facebook 2019', 'LinkedIn 2021', 'Twitter 2022', 
                'WhatsApp 2019', 'Clubhouse 2021', 'T-Mobile 2021'
            ]
            
            found_breaches = []
            for breach in breaches:
                if random.random() < 0.15:  # 15% chance per breach
                    found_breaches.append({
                        'breach_name': breach,
                        'date_discovered': datetime.now().isoformat(),
                        'data_types': random.sample(['phone', 'email', 'name', 'address'], random.randint(1, 3))
                    })
            
            return {
                'total_breaches': len(found_breaches),
                'breaches': found_breaches,
                'risk_level': 'high' if len(found_breaches) > 2 else 'medium' if len(found_breaches) > 0 else 'low'
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def public_records_lookup(self, phone_number):
        """Look up phone number in public records"""
        try:
            # Simulate public records lookup
            return {
                'business_registrations': random.randint(0, 3),
                'court_records': random.randint(0, 1),
                'property_records': random.randint(0, 2),
                'voter_records': random.random() < 0.4,
                'professional_licenses': random.randint(0, 2)
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def reverse_phone_lookup(self, phone_number):
        """Perform reverse phone lookup"""
        try:
            # Simulate reverse lookup
            if random.random() < 0.6:  # 60% chance of finding info
                return {
                    'name': f"John Doe {random.randint(1, 999)}",
                    'address': f"{random.randint(100, 9999)} Main St, City, State",
                    'age_range': f"{random.randint(20, 70)}-{random.randint(20, 70)}",
                    'associated_numbers': [f"+1{random.randint(**********, **********)}" for _ in range(random.randint(0, 3))],
                    'relatives': [f"Jane Doe {random.randint(1, 999)}" for _ in range(random.randint(0, 4))],
                    'confidence': random.uniform(0.6, 0.95)
                }
            else:
                return {'found': False}
                
        except Exception as e:
            return {'error': str(e)}
    
    def calculate_risk_score(self, analysis_data):
        """Calculate risk score for phone number"""
        try:
            risk_score = 0.0
            
            # Validation status
            if analysis_data.get('validation_status') == 'valid':
                risk_score += 0.2
            
            # Number type
            number_type = analysis_data.get('number_type', '')
            if number_type == 'MOBILE':
                risk_score += 0.3
            elif number_type == 'VOIP':
                risk_score += 0.1
            
            # OSINT data
            osint_data = analysis_data.get('osint_data', {})
            
            # Social media presence
            social_media = osint_data.get('social_media', {})
            social_found = sum(1 for platform in social_media.values() if platform.get('found', False))
            risk_score += min(social_found * 0.1, 0.3)
            
            # Data breaches
            breaches = osint_data.get('data_breaches', {})
            breach_count = breaches.get('total_breaches', 0)
            risk_score += min(breach_count * 0.15, 0.4)
            
            # Search engine results
            search_results = osint_data.get('search_engines', {})
            if search_results.get('google_results', 0) > 10:
                risk_score += 0.2
            
            # Spam reports
            if search_results.get('spam_reports', 0) > 0:
                risk_score -= 0.3  # Lower score for spam numbers
            
            return min(max(risk_score, 0.0), 1.0)  # Clamp between 0 and 1
            
        except Exception as e:
            return 0.5  # Default medium risk
    
    def create_target_profile(self, phone_number):
        """Create comprehensive target profile"""
        try:
            print(f"[*] Creating target profile for: {phone_number}")
            
            # Get phone intelligence
            phone_intel = self.analyze_phone_number(phone_number)
            
            if not phone_intel:
                return None
            
            # Generate profile
            profile = {
                'profile_id': str(uuid.uuid4()),
                'phone_number': phone_number,
                'creation_date': datetime.now().isoformat(),
                'phone_intelligence': phone_intel,
                'personal_info': self.extract_personal_info(phone_intel),
                'social_media_presence': self.analyze_social_presence(phone_intel),
                'vulnerabilities': self.identify_vulnerabilities(phone_intel),
                'attack_vectors': self.suggest_attack_vectors(phone_intel),
                'profile_score': self.calculate_profile_score(phone_intel)
            }
            
            # Store profile
            self.store_target_profile(profile)
            
            # Update statistics
            self.stats['profiles_created'] += 1
            
            print(f"[+] Target profile created: {profile['profile_id']}")
            return profile
            
        except Exception as e:
            print(f"[-] Profile creation error: {e}")
            return None
    
    def extract_personal_info(self, phone_intel):
        """Extract personal information from phone intelligence"""
        try:
            personal_info = {}
            
            # From reverse lookup
            osint_data = phone_intel.get('osint_data', {})
            reverse_lookup = osint_data.get('reverse_lookup', {})
            
            if reverse_lookup.get('found', True):
                personal_info.update({
                    'name': reverse_lookup.get('name'),
                    'address': reverse_lookup.get('address'),
                    'age_range': reverse_lookup.get('age_range'),
                    'relatives': reverse_lookup.get('relatives', []),
                    'confidence': reverse_lookup.get('confidence', 0.5)
                })
            
            # From social media
            social_media = osint_data.get('social_media', {})
            social_profiles = []
            for platform, data in social_media.items():
                if data.get('found', False):
                    social_profiles.append({
                        'platform': platform,
                        'username': data.get('username'),
                        'profile_url': data.get('profile_url'),
                        'last_activity': data.get('last_activity')
                    })
            
            personal_info['social_profiles'] = social_profiles
            
            return personal_info
            
        except Exception as e:
            return {}
    
    def analyze_social_presence(self, phone_intel):
        """Analyze social media presence"""
        try:
            osint_data = phone_intel.get('osint_data', {})
            social_media = osint_data.get('social_media', {})
            
            analysis = {
                'total_platforms': len([p for p in social_media.values() if p.get('found', False)]),
                'platforms_found': [platform for platform, data in social_media.items() if data.get('found', False)],
                'activity_level': 'high' if len([p for p in social_media.values() if p.get('last_activity')]) > 2 else 'medium',
                'privacy_score': random.uniform(0.3, 0.9),  # Simulated privacy analysis
                'engagement_level': random.choice(['low', 'medium', 'high'])
            }
            
            return analysis
            
        except Exception as e:
            return {}
    
    def identify_vulnerabilities(self, phone_intel):
        """Identify potential vulnerabilities"""
        try:
            vulnerabilities = []
            
            # Check validation status
            if phone_intel.get('validation_status') != 'valid':
                vulnerabilities.append({
                    'type': 'invalid_number',
                    'severity': 'low',
                    'description': 'Phone number validation failed'
                })
            
            # Check for data breaches
            osint_data = phone_intel.get('osint_data', {})
            breaches = osint_data.get('data_breaches', {})
            if breaches.get('total_breaches', 0) > 0:
                vulnerabilities.append({
                    'type': 'data_breach_exposure',
                    'severity': 'high' if breaches.get('total_breaches', 0) > 2 else 'medium',
                    'description': f"Found in {breaches.get('total_breaches', 0)} data breaches",
                    'breaches': breaches.get('breaches', [])
                })
            
            # Check social media exposure
            social_media = osint_data.get('social_media', {})
            exposed_platforms = [p for p, data in social_media.items() if data.get('found', False)]
            if len(exposed_platforms) > 3:
                vulnerabilities.append({
                    'type': 'high_social_exposure',
                    'severity': 'medium',
                    'description': f"Phone number found on {len(exposed_platforms)} social platforms",
                    'platforms': exposed_platforms
                })
            
            # Check for VOIP numbers (easier to spoof)
            if phone_intel.get('number_type') == 'VOIP':
                vulnerabilities.append({
                    'type': 'voip_number',
                    'severity': 'medium',
                    'description': 'VOIP numbers are easier to spoof and manipulate'
                })
            
            return vulnerabilities
            
        except Exception as e:
            return []
    
    def suggest_attack_vectors(self, phone_intel):
        """Suggest potential attack vectors"""
        try:
            attack_vectors = []
            
            # SMS-based attacks
            if phone_intel.get('number_type') in ['MOBILE', 'FIXED_LINE_OR_MOBILE']:
                attack_vectors.append({
                    'type': 'sms_phishing',
                    'effectiveness': 'high',
                    'description': 'SMS phishing campaigns targeting mobile number',
                    'techniques': ['fake_bank_alerts', 'delivery_notifications', 'security_warnings']
                })
                
                attack_vectors.append({
                    'type': 'sim_swapping',
                    'effectiveness': 'medium',
                    'description': 'SIM swapping attack to take control of phone number',
                    'requirements': ['carrier_social_engineering', 'personal_information']
                })
            
            # Social engineering
            osint_data = phone_intel.get('osint_data', {})
            if osint_data.get('reverse_lookup', {}).get('found', True):
                attack_vectors.append({
                    'type': 'voice_social_engineering',
                    'effectiveness': 'high',
                    'description': 'Voice calls using gathered personal information',
                    'techniques': ['impersonation', 'pretexting', 'authority_manipulation']
                })
            
            # Data breach exploitation
            breaches = osint_data.get('data_breaches', {})
            if breaches.get('total_breaches', 0) > 0:
                attack_vectors.append({
                    'type': 'credential_stuffing',
                    'effectiveness': 'medium',
                    'description': 'Use leaked credentials for account takeover',
                    'data_sources': [breach['breach_name'] for breach in breaches.get('breaches', [])]
                })
            
            return attack_vectors
            
        except Exception as e:
            return []
    
    def calculate_profile_score(self, phone_intel):
        """Calculate overall profile score"""
        try:
            score = 0.0
            
            # Base score from risk assessment
            score += phone_intel.get('risk_score', 0.5) * 0.4
            
            # OSINT data availability
            osint_data = phone_intel.get('osint_data', {})
            
            # Social media presence
            social_media = osint_data.get('social_media', {})
            social_found = sum(1 for platform in social_media.values() if platform.get('found', False))
            score += min(social_found * 0.05, 0.2)
            
            # Personal information availability
            reverse_lookup = osint_data.get('reverse_lookup', {})
            if reverse_lookup.get('found', True):
                score += 0.2
            
            # Data breach exposure
            breaches = osint_data.get('data_breaches', {})
            score += min(breaches.get('total_breaches', 0) * 0.1, 0.3)
            
            return min(score, 1.0)
            
        except Exception as e:
            return 0.5
    
    def store_phone_intelligence(self, intel_data):
        """Store phone intelligence in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO phone_intelligence 
                (phone_number, country_code, carrier, location, number_type, 
                 validation_status, osint_data, risk_score, last_updated)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                intel_data['phone_number'],
                intel_data.get('country_code', ''),
                intel_data.get('carrier', ''),
                intel_data.get('country', ''),
                intel_data.get('number_type', ''),
                intel_data.get('validation_status', ''),
                json.dumps(intel_data.get('osint_data', {})),
                intel_data.get('risk_score', 0.0),
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"[-] Phone intelligence storage error: {e}")
    
    def store_target_profile(self, profile_data):
        """Store target profile in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO target_profiles 
                (profile_id, phone_number, personal_info, social_media, 
                 vulnerabilities, attack_vectors, profile_score, creation_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                profile_data['profile_id'],
                profile_data['phone_number'],
                json.dumps(profile_data.get('personal_info', {})),
                json.dumps(profile_data.get('social_media_presence', {})),
                json.dumps(profile_data.get('vulnerabilities', [])),
                json.dumps(profile_data.get('attack_vectors', [])),
                profile_data.get('profile_score', 0.0),
                profile_data['creation_date']
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"[-] Target profile storage error: {e}")
    
    def monitoring_loop(self):
        """Monitoring loop for background tasks"""
        try:
            while self.active:
                # Update statistics
                self.update_statistics()
                
                # Cleanup old data
                self.cleanup_old_data()
                
                time.sleep(60)  # Run every minute
                
        except Exception as e:
            print(f"[-] Monitoring loop error: {e}")
    
    def update_statistics(self):
        """Update system statistics"""
        try:
            # Calculate success rate
            if self.stats['attacks_launched'] > 0:
                # Simulate success rate calculation
                self.stats['success_rate'] = random.uniform(0.6, 0.9)
            
        except Exception as e:
            print(f"[-] Statistics update error: {e}")
    
    def cleanup_old_data(self):
        """Clean up old data from database"""
        try:
            # Remove data older than 30 days
            cutoff_date = (datetime.now() - timedelta(days=30)).isoformat()
            
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM phone_intelligence WHERE last_updated < ?', (cutoff_date,))
            cursor.execute('DELETE FROM target_profiles WHERE creation_date < ?', (cutoff_date,))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"[-] Data cleanup error: {e}")
    
    def get_system_status(self):
        """Get current system status"""
        return {
            'active': self.active,
            'capabilities': self.capabilities,
            'statistics': self.stats,
            'database_path': self.database_path,
            'libraries_available': {
                'requests': REQUESTS_AVAILABLE,
                'phonenumbers': PHONENUMBERS_AVAILABLE,
                'numpy': NUMPY_AVAILABLE
            }
        }
    
    def stop_phone_targeting(self):
        """Stop phone targeting system"""
        try:
            self.active = False
            print("[+] Phone targeting system stopped")
            return True
            
        except Exception as e:
            print(f"[-] Stop error: {e}")
            return False

def main():
    """Main function for standalone testing"""
    print("🔐 Standalone Phone Number Targeting Module")
    print("=" * 50)
    
    # Initialize system
    phone_targeting = StandalonePhoneTargeting()
    
    # Start system
    if phone_targeting.start_phone_targeting():
        print("[+] System started successfully")
        
        # Example usage
        test_numbers = [
            "+1234567890",
            "+447700900123",
            "+33123456789"
        ]
        
        for number in test_numbers:
            print(f"\n[*] Testing phone number: {number}")
            
            # Analyze phone number
            analysis = phone_targeting.analyze_phone_number(number)
            if analysis:
                print(f"    - Validation: {analysis.get('validation_status', 'unknown')}")
                print(f"    - Country: {analysis.get('country', 'unknown')}")
                print(f"    - Carrier: {analysis.get('carrier', 'unknown')}")
                print(f"    - Risk Score: {analysis.get('risk_score', 0.0):.2f}")
            
            # Create target profile
            profile = phone_targeting.create_target_profile(number)
            if profile:
                print(f"    - Profile Score: {profile.get('profile_score', 0.0):.2f}")
                print(f"    - Vulnerabilities: {len(profile.get('vulnerabilities', []))}")
                print(f"    - Attack Vectors: {len(profile.get('attack_vectors', []))}")
        
        # Show system status
        print(f"\n[*] System Status:")
        status = phone_targeting.get_system_status()
        print(f"    - Phones Analyzed: {status['statistics']['phones_analyzed']}")
        print(f"    - Profiles Created: {status['statistics']['profiles_created']}")
        print(f"    - Success Rate: {status['statistics']['success_rate']:.2f}")
        
        # Keep running for a bit
        print("\n[*] System running... Press Ctrl+C to stop")
        try:
            time.sleep(30)
        except KeyboardInterrupt:
            pass
        
        # Stop system
        phone_targeting.stop_phone_targeting()
    else:
        print("[-] Failed to start system")

if __name__ == "__main__":
    main()
