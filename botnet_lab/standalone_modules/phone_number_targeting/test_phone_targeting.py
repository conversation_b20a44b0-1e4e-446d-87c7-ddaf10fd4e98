#!/usr/bin/env python3
# Phone Number Targeting Test Suite

import sys
import time
import json
from datetime import datetime

# Import the standalone module
from phone_targeting_standalone import StandalonePhoneTargeting

class PhoneTargetingTester:
    def __init__(self):
        self.phone_targeting = StandalonePhoneTargeting()
        self.test_results = []
        
    def run_all_tests(self):
        """Run comprehensive test suite"""
        print("📞 PHONE NUMBER TARGETING TEST SUITE")
        print("=" * 60)
        
        # Test 1: System Initialization
        print("\n🔧 Test 1: System Initialization")
        self.test_system_initialization()
        
        # Test 2: Phone Number Analysis
        print("\n📊 Test 2: Phone Number Analysis")
        self.test_phone_analysis()
        
        # Test 3: OSINT Gathering
        print("\n🕵️ Test 3: OSINT Intelligence Gathering")
        self.test_osint_gathering()
        
        # Test 4: Target Profile Creation
        print("\n👤 Test 4: Target Profile Creation")
        self.test_profile_creation()
        
        # Test 5: Vulnerability Assessment
        print("\n🔍 Test 5: Vulnerability Assessment")
        self.test_vulnerability_assessment()
        
        # Test 6: Attack Vector Analysis
        print("\n⚔️ Test 6: Attack Vector Analysis")
        self.test_attack_vectors()
        
        # Test 7: Database Operations
        print("\n💾 Test 7: Database Operations")
        self.test_database_operations()
        
        # Test 8: System Performance
        print("\n⚡ Test 8: System Performance")
        self.test_system_performance()
        
        # Generate test report
        self.generate_test_report()
    
    def test_system_initialization(self):
        """Test system initialization"""
        try:
            # Start system
            success = self.phone_targeting.start_phone_targeting()
            
            if success:
                print("[✓] System started successfully")
                
                # Check capabilities
                status = self.phone_targeting.get_system_status()
                capabilities = status['capabilities']
                
                print(f"[*] Capabilities enabled: {sum(capabilities.values())}/{len(capabilities)}")
                for capability, enabled in capabilities.items():
                    status_icon = "✓" if enabled else "✗"
                    print(f"    [{status_icon}] {capability}")
                
                self.test_results.append({
                    'test': 'system_initialization',
                    'status': 'passed',
                    'details': f"Capabilities: {sum(capabilities.values())}/{len(capabilities)}"
                })
            else:
                print("[✗] System failed to start")
                self.test_results.append({
                    'test': 'system_initialization',
                    'status': 'failed',
                    'details': 'System startup failed'
                })
                
        except Exception as e:
            print(f"[✗] Initialization test error: {e}")
            self.test_results.append({
                'test': 'system_initialization',
                'status': 'error',
                'details': str(e)
            })
    
    def test_phone_analysis(self):
        """Test phone number analysis"""
        try:
            test_numbers = [
                "+1234567890",      # US number
                "+447700900123",    # UK number
                "+33123456789",     # French number
                "+81312345678",     # Japanese number
                "+49301234567",     # German number
                "invalid_number",   # Invalid number
                "+999999999999"     # Non-existent country code
            ]
            
            successful_analyses = 0
            
            for number in test_numbers:
                print(f"[*] Analyzing: {number}")
                
                analysis = self.phone_targeting.analyze_phone_number(number)
                
                if analysis:
                    successful_analyses += 1
                    validation_status = analysis.get('validation_status', 'unknown')
                    country = analysis.get('country', 'unknown')
                    carrier = analysis.get('carrier', 'unknown')
                    risk_score = analysis.get('risk_score', 0.0)
                    
                    print(f"    - Status: {validation_status}")
                    print(f"    - Country: {country}")
                    print(f"    - Carrier: {carrier}")
                    print(f"    - Risk Score: {risk_score:.2f}")
                else:
                    print(f"    - Analysis failed")
            
            success_rate = successful_analyses / len(test_numbers)
            print(f"[*] Analysis success rate: {success_rate:.2%}")
            
            if success_rate >= 0.7:  # 70% success rate threshold
                print("[✓] Phone analysis test passed")
                self.test_results.append({
                    'test': 'phone_analysis',
                    'status': 'passed',
                    'details': f"Success rate: {success_rate:.2%}"
                })
            else:
                print("[✗] Phone analysis test failed")
                self.test_results.append({
                    'test': 'phone_analysis',
                    'status': 'failed',
                    'details': f"Low success rate: {success_rate:.2%}"
                })
                
        except Exception as e:
            print(f"[✗] Phone analysis test error: {e}")
            self.test_results.append({
                'test': 'phone_analysis',
                'status': 'error',
                'details': str(e)
            })
    
    def test_osint_gathering(self):
        """Test OSINT gathering capabilities"""
        try:
            test_number = "+1234567890"
            
            print(f"[*] Gathering OSINT for: {test_number}")
            
            # Analyze number to trigger OSINT gathering
            analysis = self.phone_targeting.analyze_phone_number(test_number)
            
            if analysis and 'osint_data' in analysis:
                osint_data = analysis['osint_data']
                
                # Check OSINT components
                components = ['search_engines', 'social_media', 'data_breaches', 'public_records', 'reverse_lookup']
                found_components = 0
                
                for component in components:
                    if component in osint_data and not osint_data[component].get('error'):
                        found_components += 1
                        print(f"    [✓] {component}")
                    else:
                        print(f"    [✗] {component}")
                
                # Check social media results
                social_media = osint_data.get('social_media', {})
                platforms_found = sum(1 for platform in social_media.values() if platform.get('found', False))
                print(f"    [*] Social media platforms found: {platforms_found}")
                
                # Check data breaches
                breaches = osint_data.get('data_breaches', {})
                breach_count = breaches.get('total_breaches', 0)
                print(f"    [*] Data breaches found: {breach_count}")
                
                if found_components >= 3:  # At least 3 components working
                    print("[✓] OSINT gathering test passed")
                    self.test_results.append({
                        'test': 'osint_gathering',
                        'status': 'passed',
                        'details': f"Components working: {found_components}/{len(components)}"
                    })
                else:
                    print("[✗] OSINT gathering test failed")
                    self.test_results.append({
                        'test': 'osint_gathering',
                        'status': 'failed',
                        'details': f"Insufficient components: {found_components}/{len(components)}"
                    })
            else:
                print("[✗] No OSINT data gathered")
                self.test_results.append({
                    'test': 'osint_gathering',
                    'status': 'failed',
                    'details': 'No OSINT data in analysis'
                })
                
        except Exception as e:
            print(f"[✗] OSINT gathering test error: {e}")
            self.test_results.append({
                'test': 'osint_gathering',
                'status': 'error',
                'details': str(e)
            })
    
    def test_profile_creation(self):
        """Test target profile creation"""
        try:
            test_numbers = ["+1234567890", "+447700900123", "+33123456789"]
            successful_profiles = 0
            
            for number in test_numbers:
                print(f"[*] Creating profile for: {number}")
                
                profile = self.phone_targeting.create_target_profile(number)
                
                if profile:
                    successful_profiles += 1
                    profile_score = profile.get('profile_score', 0.0)
                    vulnerabilities = len(profile.get('vulnerabilities', []))
                    attack_vectors = len(profile.get('attack_vectors', []))
                    
                    print(f"    - Profile Score: {profile_score:.2f}")
                    print(f"    - Vulnerabilities: {vulnerabilities}")
                    print(f"    - Attack Vectors: {attack_vectors}")
                    
                    # Check profile completeness
                    required_fields = ['profile_id', 'phone_number', 'personal_info', 'social_media_presence']
                    complete = all(field in profile for field in required_fields)
                    
                    if complete:
                        print(f"    [✓] Profile complete")
                    else:
                        print(f"    [!] Profile incomplete")
                else:
                    print(f"    [✗] Profile creation failed")
            
            success_rate = successful_profiles / len(test_numbers)
            print(f"[*] Profile creation success rate: {success_rate:.2%}")
            
            if success_rate >= 0.8:  # 80% success rate threshold
                print("[✓] Profile creation test passed")
                self.test_results.append({
                    'test': 'profile_creation',
                    'status': 'passed',
                    'details': f"Success rate: {success_rate:.2%}"
                })
            else:
                print("[✗] Profile creation test failed")
                self.test_results.append({
                    'test': 'profile_creation',
                    'status': 'failed',
                    'details': f"Low success rate: {success_rate:.2%}"
                })
                
        except Exception as e:
            print(f"[✗] Profile creation test error: {e}")
            self.test_results.append({
                'test': 'profile_creation',
                'status': 'error',
                'details': str(e)
            })
    
    def test_vulnerability_assessment(self):
        """Test vulnerability assessment"""
        try:
            test_number = "+1234567890"
            
            print(f"[*] Assessing vulnerabilities for: {test_number}")
            
            profile = self.phone_targeting.create_target_profile(test_number)
            
            if profile and 'vulnerabilities' in profile:
                vulnerabilities = profile['vulnerabilities']
                
                print(f"[*] Found {len(vulnerabilities)} vulnerabilities:")
                
                severity_counts = {'low': 0, 'medium': 0, 'high': 0, 'critical': 0}
                
                for vuln in vulnerabilities:
                    vuln_type = vuln.get('type', 'unknown')
                    severity = vuln.get('severity', 'unknown')
                    description = vuln.get('description', 'No description')
                    
                    print(f"    - {vuln_type} ({severity}): {description}")
                    
                    if severity in severity_counts:
                        severity_counts[severity] += 1
                
                print(f"[*] Severity breakdown: {severity_counts}")
                
                # Check if assessment is reasonable
                total_vulns = len(vulnerabilities)
                if 0 <= total_vulns <= 10:  # Reasonable number of vulnerabilities
                    print("[✓] Vulnerability assessment test passed")
                    self.test_results.append({
                        'test': 'vulnerability_assessment',
                        'status': 'passed',
                        'details': f"Found {total_vulns} vulnerabilities"
                    })
                else:
                    print("[!] Unusual vulnerability count")
                    self.test_results.append({
                        'test': 'vulnerability_assessment',
                        'status': 'warning',
                        'details': f"Unusual count: {total_vulns} vulnerabilities"
                    })
            else:
                print("[✗] No vulnerability data found")
                self.test_results.append({
                    'test': 'vulnerability_assessment',
                    'status': 'failed',
                    'details': 'No vulnerability data in profile'
                })
                
        except Exception as e:
            print(f"[✗] Vulnerability assessment test error: {e}")
            self.test_results.append({
                'test': 'vulnerability_assessment',
                'status': 'error',
                'details': str(e)
            })
    
    def test_attack_vectors(self):
        """Test attack vector analysis"""
        try:
            test_number = "+1234567890"
            
            print(f"[*] Analyzing attack vectors for: {test_number}")
            
            profile = self.phone_targeting.create_target_profile(test_number)
            
            if profile and 'attack_vectors' in profile:
                attack_vectors = profile['attack_vectors']
                
                print(f"[*] Found {len(attack_vectors)} attack vectors:")
                
                effectiveness_counts = {'low': 0, 'medium': 0, 'high': 0}
                
                for vector in attack_vectors:
                    vector_type = vector.get('type', 'unknown')
                    effectiveness = vector.get('effectiveness', 'unknown')
                    description = vector.get('description', 'No description')
                    
                    print(f"    - {vector_type} ({effectiveness}): {description}")
                    
                    if effectiveness in effectiveness_counts:
                        effectiveness_counts[effectiveness] += 1
                
                print(f"[*] Effectiveness breakdown: {effectiveness_counts}")
                
                # Check for common attack vectors
                expected_vectors = ['sms_phishing', 'voice_social_engineering']
                found_expected = sum(1 for vector in attack_vectors if vector.get('type') in expected_vectors)
                
                if found_expected >= 1:  # At least one expected vector
                    print("[✓] Attack vector analysis test passed")
                    self.test_results.append({
                        'test': 'attack_vectors',
                        'status': 'passed',
                        'details': f"Found {len(attack_vectors)} vectors, {found_expected} expected"
                    })
                else:
                    print("[!] Missing expected attack vectors")
                    self.test_results.append({
                        'test': 'attack_vectors',
                        'status': 'warning',
                        'details': f"Found {len(attack_vectors)} vectors, but missing expected ones"
                    })
            else:
                print("[✗] No attack vector data found")
                self.test_results.append({
                    'test': 'attack_vectors',
                    'status': 'failed',
                    'details': 'No attack vector data in profile'
                })
                
        except Exception as e:
            print(f"[✗] Attack vector analysis test error: {e}")
            self.test_results.append({
                'test': 'attack_vectors',
                'status': 'error',
                'details': str(e)
            })
    
    def test_database_operations(self):
        """Test database operations"""
        try:
            print("[*] Testing database operations...")
            
            # Test phone intelligence storage
            test_intel = {
                'phone_number': '+1234567890',
                'country_code': '1',
                'carrier': 'Test Carrier',
                'country': 'United States',
                'number_type': 'MOBILE',
                'validation_status': 'valid',
                'osint_data': {'test': 'data'},
                'risk_score': 0.75
            }
            
            self.phone_targeting.store_phone_intelligence(test_intel)
            print("[✓] Phone intelligence storage test passed")
            
            # Test profile storage
            test_profile = {
                'profile_id': 'test_profile_123',
                'phone_number': '+1234567890',
                'personal_info': {'name': 'Test User'},
                'social_media_presence': {'platforms': 2},
                'vulnerabilities': [{'type': 'test_vuln'}],
                'attack_vectors': [{'type': 'test_vector'}],
                'profile_score': 0.8,
                'creation_date': datetime.now().isoformat()
            }
            
            self.phone_targeting.store_target_profile(test_profile)
            print("[✓] Target profile storage test passed")
            
            self.test_results.append({
                'test': 'database_operations',
                'status': 'passed',
                'details': 'All database operations successful'
            })
            
        except Exception as e:
            print(f"[✗] Database operations test error: {e}")
            self.test_results.append({
                'test': 'database_operations',
                'status': 'error',
                'details': str(e)
            })
    
    def test_system_performance(self):
        """Test system performance"""
        try:
            print("[*] Testing system performance...")
            
            # Test multiple operations
            start_time = time.time()
            
            test_numbers = [f"+123456789{i}" for i in range(5)]
            
            for number in test_numbers:
                analysis = self.phone_targeting.analyze_phone_number(number)
                if analysis:
                    profile = self.phone_targeting.create_target_profile(number)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"[*] Processed {len(test_numbers)} numbers in {execution_time:.2f} seconds")
            print(f"[*] Average time per number: {execution_time/len(test_numbers):.2f} seconds")
            
            # Check system status
            status = self.phone_targeting.get_system_status()
            stats = status['statistics']
            
            print(f"[*] Total phones analyzed: {stats['phones_analyzed']}")
            print(f"[*] Total profiles created: {stats['profiles_created']}")
            
            if execution_time < 30:  # Should complete within 30 seconds
                print("[✓] System performance test passed")
                self.test_results.append({
                    'test': 'system_performance',
                    'status': 'passed',
                    'details': f"Execution time: {execution_time:.2f}s"
                })
            else:
                print("[!] System performance slower than expected")
                self.test_results.append({
                    'test': 'system_performance',
                    'status': 'warning',
                    'details': f"Slow execution time: {execution_time:.2f}s"
                })
                
        except Exception as e:
            print(f"[✗] System performance test error: {e}")
            self.test_results.append({
                'test': 'system_performance',
                'status': 'error',
                'details': str(e)
            })
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📊 PHONE NUMBER TARGETING TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['status'] == 'passed')
        failed_tests = sum(1 for result in self.test_results if result['status'] == 'failed')
        error_tests = sum(1 for result in self.test_results if result['status'] == 'error')
        warning_tests = sum(1 for result in self.test_results if result['status'] == 'warning')
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        print(f"Errors: {error_tests} ({error_tests/total_tests*100:.1f}%)")
        print(f"Warnings: {warning_tests} ({warning_tests/total_tests*100:.1f}%)")
        
        print(f"\nSuccess Rate: {passed_tests/total_tests*100:.1f}%")
        
        print("\nDetailed Results:")
        for result in self.test_results:
            status_icon = {
                'passed': '✓',
                'failed': '✗',
                'error': '⚠',
                'warning': '!'
            }.get(result['status'], '?')
            
            print(f"  [{status_icon}] {result['test']}: {result['details']}")
        
        # Overall assessment
        if passed_tests / total_tests >= 0.8:
            print(f"\n🎉 OVERALL ASSESSMENT: EXCELLENT")
            print("The phone targeting module is working very well!")
        elif passed_tests / total_tests >= 0.6:
            print(f"\n👍 OVERALL ASSESSMENT: GOOD")
            print("The phone targeting module is working adequately.")
        else:
            print(f"\n⚠️ OVERALL ASSESSMENT: NEEDS IMPROVEMENT")
            print("The phone targeting module needs attention.")
        
        print("\n" + "=" * 60)

def main():
    """Main test function"""
    print("🧪 Starting Phone Number Targeting Test Suite...")
    
    try:
        tester = PhoneTargetingTester()
        tester.run_all_tests()
        
    except KeyboardInterrupt:
        print("\n[!] Tests interrupted by user")
    except Exception as e:
        print(f"\n[✗] Test suite error: {e}")
    finally:
        print("\n[*] Test suite completed")

if __name__ == "__main__":
    main()
