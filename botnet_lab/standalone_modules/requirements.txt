# Standalone Modules Requirements
# Dependencies for the last 4 developed modules

# Core Python libraries
requests>=2.31.0
beautifulsoup4>=4.12.0
numpy>=1.24.0
sqlite3 (built-in)
json (built-in)
threading (built-in)
datetime (built-in)
random (built-in)
time (built-in)
hashlib (built-in)
itertools (built-in)
re (built-in)
uuid (built-in)
base64 (built-in)
pickle (built-in)
os (built-in)
sys (built-in)
platform (built-in)
subprocess (built-in)

# Phone Number Targeting dependencies
phonenumbers>=8.13.0
pycountry>=22.3.0

# Social Media Accounts dependencies
selenium>=4.15.0
Pillow>=10.0.0

# Password Cracking dependencies
# (uses built-in libraries primarily)

# Social Media Blocking dependencies
# (uses built-in libraries primarily)

# Optional advanced dependencies
# Uncomment if needed for enhanced functionality

# Machine Learning (optional)
# scikit-learn>=1.3.0
# tensorflow>=2.13.0
# torch>=2.0.0

# Advanced networking (optional)
# scapy>=2.5.0
# paramiko>=3.3.0

# Image processing (optional)
# opencv-python>=4.8.0

# Web automation (optional)
# playwright>=1.37.0

# Cryptography (optional)
# cryptography>=41.0.0
# pycryptodome>=3.18.0

# Data analysis (optional)
# pandas>=2.0.0
# matplotlib>=3.7.0

# Network analysis (optional)
# networkx>=3.1.0

# Text processing (optional)
# nltk>=3.8.0
# spacy>=3.6.0

# Installation instructions:
# pip install -r requirements.txt

# For development and testing:
# pip install pytest>=7.4.0
# pip install pytest-cov>=4.1.0
