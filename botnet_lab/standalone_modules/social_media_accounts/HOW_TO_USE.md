# 🎯 كيفية الاستخدام - نظام الاستخبارات الاجتماعية

## 🚀 البدء السريع (30 ثانية)

```bash
# 1. تفعيل البيئة الافتراضية
source venv/bin/activate

# 2. تشغيل النظام
python run.py
```

## 📋 جدول المحتويات

| الملف | الوصف | متى تستخدمه |
|-------|--------|-------------|
| `run.py` | 🎯 القائمة السريعة | **للمبتدئين** - أسهل طريقة للبدء |
| `cli_interface.py` | 🎨 واجهة CLI متقدمة | **للمستخدمين المتقدمين** - واجهة جميلة |
| `examples.py` | 📚 أمثلة عملية | **للتعلم** - أمثلة مع شرح |
| `demo.py` | 🎬 عرض تجريبي | **للاستكشاف** - عرض سريع للميزات |
| `social_accounts_standalone.py` | 🔧 النظام الرئيسي | **للمطورين** - تشغيل مباشر |

## 🎯 اختر طريقتك المفضلة

### 🥇 للمبتدئين: القائمة السريعة
```bash
python run.py
```
**الميزات:**
- ✅ قائمة بسيطة وواضحة
- ✅ خيارات محدودة لتجنب التعقيد
- ✅ مناسبة للاستخدام الأول

**مثال سريع:**
```bash
python run.py
# اختر: 2 - Quick Profile Analysis
# Platform: instagram
# Username: example_user
```

### 🥈 للمستخدمين المتقدمين: CLI المتقدم
```bash
python cli_interface.py
```
**الميزات:**
- ✅ واجهة ملونة وجميلة
- ✅ تقارير مفصلة
- ✅ تحكم كامل في الخيارات

**مثال متقدم:**
```bash
python cli_interface.py
# اختر: 1 - Analyze Profile (Enhanced)
# Platform: instagram
# Username: example_user
# Use real scraping: Yes
# Include OSINT: Yes
# Include AI analysis: Yes
```

### 🥉 للتعلم: الأمثلة العملية
```bash
python examples.py
```
**الميزات:**
- ✅ أمثلة مع شرح مفصل
- ✅ كود جاهز للتعديل
- ✅ تعلم كيفية البرمجة

**مثال تعليمي:**
```bash
python examples.py
# اختر: 1 - تحليل ملف شخصي شامل
# سيعرض الكود والنتائج مع الشرح
```

### 🎬 للاستكشاف: العرض التجريبي
```bash
python demo.py
```
**الميزات:**
- ✅ عرض سريع لجميع الميزات
- ✅ محاكاة آمنة
- ✅ لا يحتاج إعدادات

## 📊 أمثلة عملية مفصلة

### 🔍 مثال 1: تحليل ملف شخصي

#### الطريقة السهلة:
```bash
python run.py
# اختر: 2 - Quick Profile Analysis
# Platform: instagram
# Username: example_user
```

#### الطريقة المتقدمة:
```bash
python cli_interface.py
# اختر: 1 - Analyze Profile (Enhanced)
# أجب على الأسئلة التفاعلية
```

#### الطريقة البرمجية:
```python
import asyncio
from social_accounts_standalone import EnhancedSocialMediaIntelligence

async def analyze_profile():
    intelligence = EnhancedSocialMediaIntelligence()
    intelligence.start_social_media_system()
    
    result = await intelligence.analyze_profile(
        platform="instagram",
        username_or_url="example_user",
        use_real_scraping=True,
        include_osint=True,
        include_ai_analysis=True
    )
    
    print(f"Risk Score: {result.get('risk_score', 0.0)}")
    intelligence.stop_social_media_system()

asyncio.run(analyze_profile())
```

### 🕵️ مثال 2: تحقيق OSINT

#### الطريقة السهلة:
```bash
python run.py
# اختر: 3 - OSINT Investigation
# Type: email
# Email: <EMAIL>
```

#### الطريقة البرمجية:
```python
import asyncio
from osint_collector import RealOSINTCollector

async def investigate_email():
    async with RealOSINTCollector() as osint:
        result = await osint.comprehensive_email_osint("<EMAIL>")
        print(f"Breaches: {result.get('breaches', {}).get('total_breaches', 0)}")

asyncio.run(investigate_email())
```

### 🤖 مثال 3: تحليل المحتوى

#### الطريقة السهلة:
```bash
python run.py
# اختر: 4 - AI Content Analysis Demo
# Text: "أحب هذا المنتج الرائع!"
```

#### الطريقة البرمجية:
```python
from ai_analyzer import AIContentAnalyzer

analyzer = AIContentAnalyzer()
text = "أحب هذا المنتج الرائع!"
result = analyzer.analyze_sentiment_textblob(text)
print(f"Sentiment: {result.get('sentiment')}")
```

## ⚙️ إعداد مفاتيح API

### 1. إنشاء ملف الإعدادات
```bash
cp .env.example .env
nano .env
```

### 2. إضافة المفاتيح المطلوبة
```bash
# في ملف .env
HUNTER_IO_API_KEY=your_hunter_io_key
HIBP_API_KEY=your_haveibeenpwned_key
TWOCAPTCHA_API_KEY=your_2captcha_key
```

### 3. مفاتيح API المجانية
- **Hunter.io**: 25 طلب/شهر مجاناً
- **EmailRep.io**: مجاني بدون مفتاح
- **Numverify**: 1000 طلب/شهر مجاناً

## 🛠️ حل المشاكل الشائعة

### ❌ مشكلة: "Module not found"
```bash
# الحل:
source venv/bin/activate
pip install -r requirements.txt
```

### ❌ مشكلة: "ChromeDriver not found"
```bash
# الحل:
sudo apt-get install chromium-browser
# أو
pip install --upgrade webdriver-manager
```

### ❌ مشكلة: "API key not configured"
```bash
# الحل:
cp .env.example .env
# ثم أضف مفاتيح API في ملف .env
```

### ❌ مشكلة: "Permission denied"
```bash
# الحل:
chmod +x *.py
```

## 📊 فهم النتائج

### 🚦 درجات المخاطر
- **🟢 0.0-0.3**: مخاطر منخفضة (آمن)
- **🟡 0.3-0.7**: مخاطر متوسطة (حذر)
- **🔴 0.7-1.0**: مخاطر عالية (خطر)

### 🤖 درجات الأصالة
- **🟢 0.8-1.0**: أصيل جداً
- **🟡 0.6-0.8**: أصيل إلى حد كبير
- **🟠 0.4-0.6**: مشكوك فيه
- **🔴 0.0-0.4**: مشبوه جداً

### 😊 تحليل المشاعر
- **😊 Positive**: إيجابي (0.1 إلى 1.0)
- **😐 Neutral**: محايد (-0.1 إلى 0.1)
- **😞 Negative**: سلبي (-1.0 إلى -0.1)

## 🔒 نصائح الأمان

### ✅ افعل دائماً
- استخدم الوضع التعليمي للاختبار
- احصل على إذن قبل تحليل حسابات حقيقية
- احترم معدلات الطلبات للمنصات
- اقرأ التحذيرات القانونية

### ❌ لا تفعل أبداً
- لا تستخدم للأغراض الضارة
- لا تنتهك شروط خدمة المنصات
- لا تشارك مفاتيح API مع الآخرين
- لا تفرط في الطلبات

## 🆘 الحصول على المساعدة

### 1. اختبار النظام
```bash
python -m pytest test_enhanced_system.py -v
```

### 2. عرض حالة النظام
```bash
python run.py
# اختر: 6 - System Status & Capabilities
```

### 3. قراءة التوثيق
- `README.md` - التوثيق الكامل
- `USAGE_GUIDE.md` - دليل الاستخدام المفصل
- `QUICK_START.md` - دليل البدء السريع

### 4. تشغيل الأمثلة
```bash
python examples.py
# جرب الأمثلة المختلفة
```

## 🎯 نصائح للنجاح

### للمبتدئين
1. ابدأ بـ `python run.py`
2. استخدم الوضع التعليمي دائماً
3. اقرأ `QUICK_START.md`

### للمستخدمين المتقدمين
1. أضف مفاتيح API حقيقية
2. استخدم `python cli_interface.py`
3. اقرأ `USAGE_GUIDE.md`

### للمطورين
1. ادرس `examples.py`
2. اقرأ الكود المصدري
3. شغل `test_enhanced_system.py`

## ⚖️ التذكير القانوني

**🎓 للتعليم والبحث فقط**

### ✅ الاستخدامات المسموحة
- البحث الأكاديمي مع موافقة مؤسسية
- اختبار الأمان المصرح به
- العروض التعليمية والتدريب

### ❌ الاستخدامات المحظورة
- الوصول غير المصرح به
- انتهاك الخصوصية
- الأنشطة الضارة أو غير القانونية

---

## 🚀 ابدأ الآن!

```bash
# الطريقة الأسهل
python run.py

# أو العرض التجريبي
python demo.py

# أو الأمثلة التعليمية
python examples.py
```

**🎯 نصيحة أخيرة:** ابدأ دائماً بالوضع التعليمي والمحاكاة قبل استخدام الميزات الحقيقية!
