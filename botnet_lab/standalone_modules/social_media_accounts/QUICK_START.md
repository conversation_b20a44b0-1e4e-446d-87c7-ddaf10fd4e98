# ⚡ دليل البدء السريع - نظام الاستخبارات الاجتماعية

## 🚀 البدء في 5 دقائق

### 1️⃣ تفعيل البيئة الافتراضية
```bash
source venv/bin/activate
```

### 2️⃣ اختبار النظام
```bash
python -c "print('✅ النظام جاهز!')"
```

### 3️⃣ تشغيل القائمة السريعة
```bash
python run.py
```

## 🎯 الطرق السريعة للاستخدام

### 🖥️ الطريقة الأولى: القائمة التفاعلية
```bash
python run.py
```
**الأسهل للمبتدئين** - قائمة بسيطة مع خيارات واضحة

### 🎨 الطريقة الثانية: CLI المتقدم
```bash
python cli_interface.py
```
**الأجمل والأكثر تفصيلاً** - واجهة ملونة مع تقارير مفصلة

### 📚 الطريقة الثالثة: الأمثلة العملية
```bash
python examples.py
```
**للتعلم والفهم** - أمثلة عملية مع شرح مفصل

### 🔧 الطريقة الرابعة: النظام الرئيسي
```bash
python social_accounts_standalone.py
```
**للمطورين** - تشغيل مباشر للنظام الكامل

## 📋 أمثلة سريعة

### 🔍 تحليل ملف شخصي
```bash
python run.py
# اختر: 2 - Quick Profile Analysis
# Platform: instagram
# Username: example_user
```

### 🕵️ تحقيق OSINT
```bash
python run.py
# اختر: 3 - OSINT Investigation
# Type: email
# Email: <EMAIL>
```

### 🤖 تحليل المحتوى
```bash
python run.py
# اختر: 4 - AI Content Analysis Demo
# Text: "أحب هذا المنتج الرائع!"
```

### 🎓 إنشاء حساب تعليمي
```bash
python run.py
# اختر: 5 - Educational Account Creation
# Platform: instagram
# (اقرأ التحذيرات واوافق)
```

## ⚙️ إعداد سريع لمفاتيح API

### إنشاء ملف الإعدادات
```bash
cp .env.example .env
nano .env
```

### إضافة مفاتيح API الأساسية
```bash
# في ملف .env
HUNTER_IO_API_KEY=your_key_here
HIBP_API_KEY=your_key_here
TWOCAPTCHA_API_KEY=your_key_here
```

### مفاتيح API المجانية
- **Hunter.io**: 25 طلب مجاني/شهر
- **EmailRep.io**: مجاني بدون مفتاح
- **Numverify**: 1000 طلب مجاني/شهر

## 🛠️ حل المشاكل السريع

### مشكلة: "Module not found"
```bash
source venv/bin/activate
pip install -r requirements.txt
```

### مشكلة: "ChromeDriver not found"
```bash
sudo apt-get install chromium-browser
```

### مشكلة: "Permission denied"
```bash
chmod +x *.py
chmod +x install.sh
```

## 📊 فهم النتائج السريع

### 🚦 درجات المخاطر
- **🟢 0.0-0.3**: آمن
- **🟡 0.3-0.7**: متوسط
- **🔴 0.7-1.0**: خطر

### 🤖 درجات الأصالة
- **🟢 0.8-1.0**: أصيل
- **🟡 0.6-0.8**: مشكوك
- **🔴 0.0-0.6**: مزيف

### 😊 تحليل المشاعر
- **😊 Positive**: إيجابي
- **😐 Neutral**: محايد  
- **😞 Negative**: سلبي

## 🔒 نصائح الأمان السريعة

### ✅ افعل
- استخدم الوضع التعليمي دائماً
- احصل على إذن قبل التحليل
- احترم معدلات الطلبات

### ❌ لا تفعل
- لا تستخدم للأغراض الضارة
- لا تنتهك شروط الخدمة
- لا تشارك مفاتيح API

## 🆘 المساعدة السريعة

### اختبار النظام
```bash
python -m pytest test_enhanced_system.py -v
```

### عرض الحالة
```bash
python run.py
# اختر: 6 - System Status
```

### عرض المساعدة
```bash
python run.py
# اختر: 9 - Help & Documentation
```

## 📞 الحصول على الدعم

### 1. تحقق من الملفات
- `README.md` - التوثيق الكامل
- `USAGE_GUIDE.md` - دليل الاستخدام المفصل
- `examples.py` - أمثلة عملية

### 2. تشغيل الاختبارات
```bash
python examples.py
# اختر مثال للاختبار
```

### 3. فحص السجلات
```bash
tail -f logs/social_intelligence.log
```

## 🎯 نصائح للنجاح

### للمبتدئين
1. ابدأ بـ `python run.py`
2. استخدم الوضع التعليمي
3. اقرأ التحذيرات القانونية

### للمتقدمين
1. أضف مفاتيح API حقيقية
2. استخدم `python cli_interface.py`
3. اقرأ `USAGE_GUIDE.md`

### للمطورين
1. ادرس `examples.py`
2. اقرأ الكود المصدري
3. شغل الاختبارات

## ⚖️ تذكير قانوني سريع

**🎓 للتعليم والبحث فقط**
- ✅ البحث الأكاديمي
- ✅ اختبار الأمان
- ❌ الأنشطة الضارة
- ❌ انتهاك الخصوصية

---

**🚀 ابدأ الآن:** `python run.py`
