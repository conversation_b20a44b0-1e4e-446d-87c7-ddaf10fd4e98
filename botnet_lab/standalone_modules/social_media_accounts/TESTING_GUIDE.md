# 🧪 دليل اختبار النظام على حسابات حقيقية

## ⚖️ **تحذير قانوني مهم**

### 🔴 **قبل البدء - اقرأ هذا بعناية:**

**هذا النظام للأغراض التعليمية والبحثية فقط**

#### ✅ **مسموح:**
- اختبار حسابك الشخصي فقط
- حسابات عامة مع إذن صريح من المالك
- البحث الأكاديمي المصرح به رسمياً
- اختبار الأمان المصرح به

#### ❌ **ممنوع تماماً:**
- تحليل حسابات الآخرين بدون إذن
- انتهاك شروط خدمة المنصات
- جمع معلومات لأغراض ضارة
- انتهاك قوانين الخصوصية

---

## 🚀 **طرق الاختبار الآمنة**

### 1️⃣ **الاختبار الآمن (محاكاة) - الأفضل للبداية**

```bash
# تفعيل البيئة
source venv/bin/activate

# تشغيل الاختبار الآمن
python safe_test.py
# اختر: 1 - اختبار آمن (محاكاة)
```

**الميزات:**
- ✅ لا يصل للإنترنت
- ✅ بيانات محاكاة آمنة
- ✅ اختبار جميع الوظائف
- ✅ لا يخالف أي قوانين

### 2️⃣ **اختبار حساب عام - للمتقدمين**

```bash
python safe_test.py
# اختر: 2 - اختبار حساب عام
```

**الشروط:**
- ✅ حساب عام ومتاح للجميع
- ✅ لا ينتهك شروط الخدمة
- ✅ مناسب للاختبار التعليمي
- ⚠️ احصل على إذن إذا أمكن

### 3️⃣ **اختبار حساب شخصي - للخبراء فقط**

```bash
python safe_test.py
# اختر: 3 - اختبار حساب شخصي
```

**الشروط:**
- ✅ حسابك الشخصي فقط
- ✅ تملك الحساب بالكامل
- ✅ موافق على التحليل
- ⚠️ مسؤولية كاملة عليك

---

## 📋 **خطوات الاختبار التفصيلية**

### 🔧 **الإعداد الأولي**

#### 1. تفعيل البيئة الافتراضية
```bash
source venv/bin/activate
```

#### 2. التحقق من النظام
```bash
python -c "
try:
    from social_accounts_standalone import EnhancedSocialMediaIntelligence
    print('✅ النظام جاهز للاختبار')
except Exception as e:
    print(f'❌ خطأ: {e}')
"
```

#### 3. إعداد مفاتيح API (اختياري)
```bash
# نسخ ملف الإعدادات
cp .env.example .env

# تحرير الملف (اختياري للاختبار الأساسي)
nano .env
```

### 🧪 **تشغيل الاختبارات**

#### الطريقة الأولى: الاختبار الآمن المرشد
```bash
python safe_test.py
```

#### الطريقة الثانية: الاختبار المباشر
```bash
python run.py
# اختر: 2 - Quick Profile Analysis
```

#### الطريقة الثالثة: الاختبار المتقدم
```bash
python cli_interface.py
# اختر: 1 - Analyze Profile (Enhanced)
```

---

## 📊 **أمثلة عملية للاختبار**

### 🟢 **مثال 1: اختبار آمن (محاكاة)**

```bash
python safe_test.py
```

**الخطوات:**
1. اقرأ التحذير القانوني
2. وافق على الشروط
3. اختر: `1 - اختبار آمن`
4. شاهد النتائج المحاكاة

**النتائج المتوقعة:**
```
✅ تم التحليل بنجاح!
📊 درجة المخاطر: 0.35
🔧 الطرق المستخدمة: simulation, ai_analysis
👥 المتابعون (محاكاة): 15,420
✅ محقق (محاكاة): False
```

### 🟡 **مثال 2: اختبار حساب عام**

```bash
python safe_test.py
```

**الخطوات:**
1. اختر: `2 - اختبار حساب عام`
2. أدخل المنصة: `instagram`
3. أدخل اسم مستخدم عام: `nasa` (مثال)
4. أكد الاختبار

**ملاحظة:** استخدم حسابات عامة مشهورة مثل:
- `nasa` على Instagram
- `microsoft` على Twitter
- `google` على LinkedIn

### 🔴 **مثال 3: اختبار حساب شخصي**

```bash
python safe_test.py
```

**الخطوات:**
1. اختر: `3 - اختبار حساب شخصي`
2. أدخل المنصة
3. أدخل اسم المستخدم لحسابك
4. أكد الملكية
5. اختر خيارات التحليل

---

## 🔧 **إعداد مفاتيح API للاختبار المتقدم**

### مفاتيح API المجانية (اختيارية)

#### 1. Hunter.io (فحص الإيميلات)
```bash
# في ملف .env
HUNTER_IO_API_KEY=your_key_here
```
- 🌐 الموقع: https://hunter.io/api
- 🆓 مجاني: 25 طلب/شهر
- 📧 الوظيفة: فحص الإيميلات

#### 2. EmailRep.io (سمعة الإيميل)
```bash
# مجاني بدون مفتاح
# يعمل تلقائياً
```
- 🌐 الموقع: https://emailrep.io/
- 🆓 مجاني: بدون حدود
- 🔍 الوظيفة: فحص سمعة الإيميل

#### 3. Numverify (أرقام الهاتف)
```bash
# في ملف .env
NUMVERIFY_API_KEY=your_key_here
```
- 🌐 الموقع: https://numverify.com/
- 🆓 مجاني: 1000 طلب/شهر
- 📞 الوظيفة: التحقق من أرقام الهاتف

---

## 🛠️ **حل مشاكل الاختبار**

### ❌ **مشكلة: "Module not found"**
```bash
# الحل:
source venv/bin/activate
pip install -r requirements.txt
```

### ❌ **مشكلة: "ChromeDriver not found"**
```bash
# الحل:
sudo apt-get update
sudo apt-get install chromium-browser
```

### ❌ **مشكلة: "Connection timeout"**
```bash
# الحل:
# تحقق من الاتصال بالإنترنت
ping google.com

# أو استخدم الوضع الآمن
python safe_test.py
# اختر: 1 - اختبار آمن
```

### ❌ **مشكلة: "Rate limit exceeded"**
```bash
# الحل:
# انتظر قليلاً ثم أعد المحاولة
# أو قلل من عدد الطلبات
```

---

## 📊 **فهم نتائج الاختبار**

### 🚦 **درجات المخاطر**
- **🟢 0.0-0.3**: مخاطر منخفضة (آمن)
- **🟡 0.3-0.7**: مخاطر متوسطة (حذر)
- **🔴 0.7-1.0**: مخاطر عالية (خطر)

### 🤖 **درجات الأصالة (AI)**
- **🟢 0.8-1.0**: أصيل جداً
- **🟡 0.6-0.8**: أصيل إلى حد كبير
- **🟠 0.4-0.6**: مشكوك فيه
- **🔴 0.0-0.4**: مشبوه جداً

### 📈 **مؤشرات الجودة**
- **المتابعون/المتابَعون**: نسبة طبيعية
- **معدل التفاعل**: نشاط حقيقي
- **تاريخ الحساب**: عمر الحساب
- **التحقق**: حالة التحقق الرسمي

---

## 🔒 **نصائح الأمان أثناء الاختبار**

### ✅ **افعل دائماً:**
- ابدأ بالاختبار الآمن (محاكاة)
- اقرأ التحذيرات القانونية
- احصل على إذن قبل تحليل أي حساب
- احترم معدلات الطلبات
- استخدم VPN إذا لزم الأمر

### ❌ **لا تفعل أبداً:**
- لا تختبر حسابات الآخرين بدون إذن
- لا تفرط في الطلبات
- لا تحفظ بيانات حساسة
- لا تشارك النتائج مع الآخرين
- لا تستخدم للأغراض الضارة

---

## 📞 **الحصول على المساعدة**

### 1. اختبار النظام
```bash
python -m pytest test_enhanced_system.py -v
```

### 2. عرض حالة النظام
```bash
python run.py
# اختر: 6 - System Status
```

### 3. تشغيل العرض التجريبي
```bash
python demo.py
```

### 4. قراءة الأمثلة
```bash
python examples.py
```

---

## ⚖️ **التذكير القانوني النهائي**

### 📝 **بتشغيل أي اختبار، أنت توافق على:**
- استخدام النظام للأغراض التعليمية فقط
- احترام قوانين الخصوصية وحقوق الآخرين
- عدم انتهاك شروط خدمة المنصات
- تحمل المسؤولية الكاملة عن الاستخدام

### 🚨 **تحذير أخير:**
**أي استخدام غير قانوني أو ضار للنظام هو مسؤولية المستخدم بالكامل**

---

## 🚀 **ابدأ الاختبار الآن**

```bash
# الطريقة الآمنة (الأفضل للبداية)
python safe_test.py

# أو الطريقة السريعة
python run.py

# أو العرض التجريبي
python demo.py
```

**🎯 نصيحة:** ابدأ دائماً بالاختبار الآمن قبل الانتقال للاختبار الحقيقي!
