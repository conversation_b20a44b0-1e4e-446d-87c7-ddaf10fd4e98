#!/usr/bin/env python3
"""
AI-Powered Content and Behavior Analysis Module
Uses transformers and NLP for real sentiment and behavioral analysis
"""

import re
import json
import logging
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from collections import Counter, defaultdict

try:
    from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

try:
    from textblob import TextBlob
    import nltk
    TEXTBLOB_AVAILABLE = True
except ImportError:
    TEXTBLOB_AVAILABLE = False

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

from config import AI_CONFIG

class AIContentAnalyzer:
    """AI-powered content and behavioral analysis"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.sentiment_analyzer = None
        self.emotion_analyzer = None
        self.language_detector = None

        if not TRANSFORMERS_AVAILABLE:
            self.logger.warning("Transformers not available - AI features disabled")

        self.setup_models()

    def setup_models(self):
        """Setup AI models for analysis"""
        try:
            if TRANSFORMERS_AVAILABLE:
                # Sentiment analysis model
                self.sentiment_analyzer = pipeline(
                    "sentiment-analysis",
                    model=AI_CONFIG['sentiment_model'],
                    device=0 if torch.cuda.is_available() and AI_CONFIG['device'] == 'cuda' else -1
                )

                # Emotion analysis model
                self.emotion_analyzer = pipeline(
                    "text-classification",
                    model="j-hartmann/emotion-english-distilroberta-base",
                    device=0 if torch.cuda.is_available() and AI_CONFIG['device'] == 'cuda' else -1
                )

                self.logger.info("AI models loaded successfully")

            # Download NLTK data if using TextBlob
            if TEXTBLOB_AVAILABLE:
                try:
                    nltk.download('punkt', quiet=True)
                    nltk.download('vader_lexicon', quiet=True)
                    nltk.download('brown', quiet=True)
                except:
                    pass

        except Exception as e:
            self.logger.error(f"Model setup error: {e}")

    def preprocess_text(self, text: str) -> str:
        """Preprocess text for analysis"""
        if not text:
            return ""

        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)

        # Remove mentions and hashtags for sentiment analysis
        text = re.sub(r'@\w+', '', text)
        text = re.sub(r'#\w+', '', text)

        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def analyze_sentiment_transformers(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment using transformers"""
        try:
            if not self.sentiment_analyzer:
                return {'error': 'Sentiment analyzer not available'}

            processed_text = self.preprocess_text(text)
            if not processed_text:
                return {'sentiment': 'neutral', 'confidence': 0.0}

            # Truncate text if too long
            if len(processed_text) > AI_CONFIG['max_length']:
                processed_text = processed_text[:AI_CONFIG['max_length']]

            result = self.sentiment_analyzer(processed_text)[0]

            return {
                'sentiment': result['label'].lower(),
                'confidence': result['score'],
                'model': 'transformers'
            }

        except Exception as e:
            self.logger.error(f"Transformers sentiment analysis error: {e}")
            return {'error': str(e)}

    def analyze_sentiment_textblob(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment using TextBlob"""
        try:
            if not TEXTBLOB_AVAILABLE:
                return {'error': 'TextBlob not available'}

            processed_text = self.preprocess_text(text)
            if not processed_text:
                return {'sentiment': 'neutral', 'polarity': 0.0, 'subjectivity': 0.0}

            blob = TextBlob(processed_text)
            polarity = blob.sentiment.polarity
            subjectivity = blob.sentiment.subjectivity

            # Convert polarity to sentiment label
            if polarity > 0.1:
                sentiment = 'positive'
            elif polarity < -0.1:
                sentiment = 'negative'
            else:
                sentiment = 'neutral'

            return {
                'sentiment': sentiment,
                'polarity': polarity,
                'subjectivity': subjectivity,
                'confidence': abs(polarity),
                'model': 'textblob'
            }

        except Exception as e:
            self.logger.error(f"TextBlob sentiment analysis error: {e}")
            return {'error': str(e)}

    def analyze_emotions(self, text: str) -> Dict[str, Any]:
        """Analyze emotions in text"""
        try:
            if not self.emotion_analyzer:
                return {'error': 'Emotion analyzer not available'}

            processed_text = self.preprocess_text(text)
            if not processed_text:
                return {'emotions': {}, 'dominant_emotion': 'neutral'}

            # Truncate text if too long
            if len(processed_text) > AI_CONFIG['max_length']:
                processed_text = processed_text[:AI_CONFIG['max_length']]

            results = self.emotion_analyzer(processed_text)

            emotions = {}
            for result in results:
                emotions[result['label'].lower()] = result['score']

            dominant_emotion = max(emotions.items(), key=lambda x: x[1])[0] if emotions else 'neutral'

            return {
                'emotions': emotions,
                'dominant_emotion': dominant_emotion,
                'model': 'transformers'
            }

        except Exception as e:
            self.logger.error(f"Emotion analysis error: {e}")
            return {'error': str(e)}

    def analyze_text_comprehensive(self, text: str) -> Dict[str, Any]:
        """Comprehensive text analysis"""
        analysis = {
            'text_length': len(text),
            'word_count': len(text.split()) if text else 0,
            'timestamp': datetime.now().isoformat()
        }

        # Sentiment analysis (try both methods)
        sentiment_transformers = self.analyze_sentiment_transformers(text)
        sentiment_textblob = self.analyze_sentiment_textblob(text)

        analysis['sentiment'] = {
            'transformers': sentiment_transformers,
            'textblob': sentiment_textblob
        }

        # Emotion analysis
        emotions = self.analyze_emotions(text)
        analysis['emotions'] = emotions

        # Text statistics
        analysis['statistics'] = self.extract_text_statistics(text)

        # Language patterns
        analysis['patterns'] = self.analyze_language_patterns(text)

        return analysis

    def extract_text_statistics(self, text: str) -> Dict[str, Any]:
        """Extract statistical features from text"""
        if not text:
            return {}

        words = text.split()
        sentences = text.split('.')

        stats = {
            'character_count': len(text),
            'word_count': len(words),
            'sentence_count': len([s for s in sentences if s.strip()]),
            'avg_word_length': np.mean([len(word) for word in words]) if words else 0,
            'avg_sentence_length': len(words) / len(sentences) if sentences else 0,
            'uppercase_ratio': sum(1 for c in text if c.isupper()) / len(text) if text else 0,
            'punctuation_count': sum(1 for c in text if c in '.,!?;:'),
            'exclamation_count': text.count('!'),
            'question_count': text.count('?'),
            'hashtag_count': len(re.findall(r'#\w+', text)),
            'mention_count': len(re.findall(r'@\w+', text)),
            'url_count': len(re.findall(r'http[s]?://\S+', text)),
            'emoji_count': len(re.findall(r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]', text))
        }

        return stats

    def analyze_language_patterns(self, text: str) -> Dict[str, Any]:
        """Analyze language patterns and style"""
        if not text:
            return {}

        patterns = {
            'writing_style': self.detect_writing_style(text),
            'formality_level': self.assess_formality(text),
            'emotional_intensity': self.measure_emotional_intensity(text),
            'topic_indicators': self.extract_topic_indicators(text)
        }

        return patterns

    def detect_writing_style(self, text: str) -> str:
        """Detect writing style"""
        text_lower = text.lower()

        # Casual indicators
        casual_indicators = ['lol', 'omg', 'btw', 'tbh', 'imo', 'gonna', 'wanna', 'gotta']
        casual_score = sum(1 for indicator in casual_indicators if indicator in text_lower)

        # Formal indicators
        formal_indicators = ['therefore', 'furthermore', 'consequently', 'nevertheless', 'moreover']
        formal_score = sum(1 for indicator in formal_indicators if indicator in text_lower)

        # Abbreviation ratio
        words = text.split()
        abbrev_count = sum(1 for word in words if word.isupper() and len(word) > 1)
        abbrev_ratio = abbrev_count / len(words) if words else 0

        if formal_score > casual_score and abbrev_ratio < 0.1:
            return 'formal'
        elif casual_score > formal_score or abbrev_ratio > 0.2:
            return 'casual'
        else:
            return 'neutral'

    def assess_formality(self, text: str) -> float:
        """Assess formality level (0-1 scale)"""
        if not text:
            return 0.5

        formal_score = 0.0

        # Sentence structure
        sentences = [s.strip() for s in text.split('.') if s.strip()]
        avg_sentence_length = np.mean([len(s.split()) for s in sentences]) if sentences else 0

        if avg_sentence_length > 15:
            formal_score += 0.2

        # Punctuation usage
        punctuation_ratio = sum(1 for c in text if c in '.,;:') / len(text) if text else 0
        formal_score += min(punctuation_ratio * 2, 0.3)

        # Capitalization
        proper_caps = sum(1 for word in text.split() if word and word[0].isupper())
        caps_ratio = proper_caps / len(text.split()) if text.split() else 0
        formal_score += min(caps_ratio, 0.3)

        # Contractions (reduce formality)
        contractions = ["n't", "'re", "'ve", "'ll", "'d"]
        contraction_count = sum(text.lower().count(c) for c in contractions)
        formal_score -= min(contraction_count * 0.1, 0.2)

        return max(0.0, min(1.0, formal_score))

    def measure_emotional_intensity(self, text: str) -> float:
        """Measure emotional intensity (0-1 scale)"""
        if not text:
            return 0.0

        intensity = 0.0

        # Exclamation marks
        intensity += min(text.count('!') * 0.1, 0.3)

        # All caps words
        words = text.split()
        caps_words = sum(1 for word in words if word.isupper() and len(word) > 2)
        intensity += min(caps_words * 0.05, 0.2)

        # Emotional words
        emotional_words = ['amazing', 'terrible', 'awesome', 'horrible', 'fantastic', 'awful', 'incredible', 'disgusting']
        emotional_count = sum(1 for word in emotional_words if word.lower() in text.lower())
        intensity += min(emotional_count * 0.1, 0.3)

        # Repeated characters
        repeated_chars = len(re.findall(r'(.)\1{2,}', text))
        intensity += min(repeated_chars * 0.05, 0.2)

        return min(1.0, intensity)

    def extract_topic_indicators(self, text: str) -> List[str]:
        """Extract topic indicators from text"""
        if not text:
            return []

        # Common topic keywords
        topic_keywords = {
            'technology': ['tech', 'software', 'app', 'digital', 'ai', 'machine learning', 'coding', 'programming'],
            'business': ['business', 'company', 'startup', 'entrepreneur', 'marketing', 'sales', 'revenue'],
            'health': ['health', 'fitness', 'medical', 'doctor', 'hospital', 'medicine', 'wellness'],
            'travel': ['travel', 'trip', 'vacation', 'flight', 'hotel', 'destination', 'tourism'],
            'food': ['food', 'restaurant', 'cooking', 'recipe', 'meal', 'cuisine', 'chef'],
            'sports': ['sports', 'game', 'team', 'player', 'match', 'championship', 'training'],
            'entertainment': ['movie', 'music', 'show', 'concert', 'actor', 'singer', 'entertainment'],
            'politics': ['politics', 'government', 'election', 'policy', 'politician', 'vote', 'democracy'],
            'education': ['education', 'school', 'university', 'student', 'teacher', 'learning', 'study']
        }

        text_lower = text.lower()
        detected_topics = []

        for topic, keywords in topic_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                detected_topics.append(topic)

        return detected_topics

    def analyze_posting_behavior(self, posts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze posting behavior patterns"""
        if not posts:
            return {'error': 'No posts provided'}

        behavior_analysis = {
            'total_posts': len(posts),
            'analysis_date': datetime.now().isoformat()
        }

        # Temporal patterns
        behavior_analysis['temporal_patterns'] = self.analyze_temporal_patterns(posts)

        # Content patterns
        behavior_analysis['content_patterns'] = self.analyze_content_patterns(posts)

        # Engagement patterns
        behavior_analysis['engagement_patterns'] = self.analyze_engagement_patterns(posts)

        # Sentiment evolution
        behavior_analysis['sentiment_evolution'] = self.analyze_sentiment_evolution(posts)

        # Behavioral score
        behavior_analysis['behavioral_score'] = self.calculate_behavioral_score(behavior_analysis)

        return behavior_analysis

    def analyze_temporal_patterns(self, posts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze temporal posting patterns"""
        if not posts:
            return {}

        # Extract timestamps
        timestamps = []
        for post in posts:
            if 'timestamp' in post:
                try:
                    timestamps.append(datetime.fromisoformat(post['timestamp'].replace('Z', '+00:00')))
                except:
                    continue

        if not timestamps:
            return {'error': 'No valid timestamps found'}

        # Hour analysis
        hours = [ts.hour for ts in timestamps]
        hour_distribution = Counter(hours)
        most_active_hour = hour_distribution.most_common(1)[0][0] if hour_distribution else 12

        # Day of week analysis
        weekdays = [ts.weekday() for ts in timestamps]
        weekday_distribution = Counter(weekdays)
        most_active_day = weekday_distribution.most_common(1)[0][0] if weekday_distribution else 0

        # Posting frequency
        if len(timestamps) > 1:
            time_diffs = [(timestamps[i] - timestamps[i-1]).total_seconds() / 3600 for i in range(1, len(timestamps))]
            avg_posting_interval = np.mean(time_diffs) if time_diffs else 0
        else:
            avg_posting_interval = 0

        return {
            'most_active_hour': most_active_hour,
            'most_active_day': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'][most_active_day],
            'hour_distribution': dict(hour_distribution),
            'weekday_distribution': dict(weekday_distribution),
            'avg_posting_interval_hours': avg_posting_interval,
            'posting_consistency': self.calculate_posting_consistency(timestamps)
        }

    def analyze_content_patterns(self, posts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze content patterns in posts"""
        if not posts:
            return {}

        # Extract text content
        texts = [post.get('text', '') for post in posts if post.get('text')]

        if not texts:
            return {'error': 'No text content found'}

        # Content statistics
        total_chars = sum(len(text) for text in texts)
        total_words = sum(len(text.split()) for text in texts)

        # Topic analysis
        all_topics = []
        for text in texts:
            topics = self.extract_topic_indicators(text)
            all_topics.extend(topics)

        topic_distribution = Counter(all_topics)

        # Language style consistency
        styles = [self.detect_writing_style(text) for text in texts]
        style_distribution = Counter(styles)
        dominant_style = style_distribution.most_common(1)[0][0] if style_distribution else 'neutral'

        # Hashtag and mention usage
        hashtag_counts = [len(re.findall(r'#\w+', text)) for text in texts]
        mention_counts = [len(re.findall(r'@\w+', text)) for text in texts]

        return {
            'avg_post_length': total_chars / len(texts),
            'avg_words_per_post': total_words / len(texts),
            'topic_distribution': dict(topic_distribution.most_common(10)),
            'dominant_writing_style': dominant_style,
            'style_consistency': style_distribution.most_common(1)[0][1] / len(styles) if styles else 0,
            'avg_hashtags_per_post': np.mean(hashtag_counts) if hashtag_counts else 0,
            'avg_mentions_per_post': np.mean(mention_counts) if mention_counts else 0,
            'content_diversity': len(set(texts)) / len(texts) if texts else 0
        }

    def analyze_engagement_patterns(self, posts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze engagement patterns"""
        if not posts:
            return {}

        # Extract engagement metrics
        likes = [post.get('likes', 0) for post in posts]
        comments = [post.get('comments', 0) for post in posts]
        shares = [post.get('shares', 0) for post in posts]

        engagement_data = {
            'avg_likes': np.mean(likes) if likes else 0,
            'avg_comments': np.mean(comments) if comments else 0,
            'avg_shares': np.mean(shares) if shares else 0,
            'max_likes': max(likes) if likes else 0,
            'max_comments': max(comments) if comments else 0,
            'max_shares': max(shares) if shares else 0,
            'engagement_variance': {
                'likes_std': np.std(likes) if likes else 0,
                'comments_std': np.std(comments) if comments else 0,
                'shares_std': np.std(shares) if shares else 0
            }
        }

        # Calculate engagement rate (if follower count available)
        if posts and 'follower_count' in posts[0]:
            follower_count = posts[0]['follower_count']
            if follower_count > 0:
                total_engagement = [likes[i] + comments[i] + shares[i] for i in range(len(posts))]
                engagement_rates = [eng / follower_count for eng in total_engagement]
                engagement_data['avg_engagement_rate'] = np.mean(engagement_rates)

        return engagement_data

    def analyze_sentiment_evolution(self, posts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze sentiment evolution over time"""
        if not posts:
            return {}

        # Sort posts by timestamp
        timestamped_posts = []
        for post in posts:
            if 'timestamp' in post and 'text' in post:
                try:
                    ts = datetime.fromisoformat(post['timestamp'].replace('Z', '+00:00'))
                    timestamped_posts.append((ts, post['text']))
                except:
                    continue

        timestamped_posts.sort(key=lambda x: x[0])

        if len(timestamped_posts) < 2:
            return {'error': 'Insufficient timestamped posts for evolution analysis'}

        # Analyze sentiment for each post
        sentiments = []
        for timestamp, text in timestamped_posts:
            sentiment_result = self.analyze_sentiment_textblob(text)
            if 'polarity' in sentiment_result:
                sentiments.append({
                    'timestamp': timestamp.isoformat(),
                    'polarity': sentiment_result['polarity'],
                    'sentiment': sentiment_result['sentiment']
                })

        if not sentiments:
            return {'error': 'No sentiment data available'}

        # Calculate trends
        polarities = [s['polarity'] for s in sentiments]

        # Simple trend calculation
        if len(polarities) > 1:
            trend = np.polyfit(range(len(polarities)), polarities, 1)[0]
        else:
            trend = 0

        return {
            'sentiment_timeline': sentiments,
            'avg_sentiment': np.mean(polarities),
            'sentiment_trend': 'improving' if trend > 0.01 else 'declining' if trend < -0.01 else 'stable',
            'sentiment_volatility': np.std(polarities),
            'most_positive_period': max(sentiments, key=lambda x: x['polarity'])['timestamp'] if sentiments else None,
            'most_negative_period': min(sentiments, key=lambda x: x['polarity'])['timestamp'] if sentiments else None
        }

    def calculate_posting_consistency(self, timestamps: List[datetime]) -> float:
        """Calculate posting consistency score (0-1)"""
        if len(timestamps) < 3:
            return 0.0

        # Calculate intervals between posts
        intervals = [(timestamps[i] - timestamps[i-1]).total_seconds() for i in range(1, len(timestamps))]

        # Consistency is inverse of coefficient of variation
        mean_interval = np.mean(intervals)
        std_interval = np.std(intervals)

        if mean_interval == 0:
            return 0.0

        cv = std_interval / mean_interval
        consistency = max(0.0, 1.0 - min(cv, 1.0))

        return consistency

    def calculate_behavioral_score(self, behavior_analysis: Dict[str, Any]) -> Dict[str, float]:
        """Calculate overall behavioral scores"""
        scores = {
            'authenticity': 0.5,
            'engagement_quality': 0.5,
            'content_quality': 0.5,
            'consistency': 0.5
        }

        # Authenticity score
        temporal = behavior_analysis.get('temporal_patterns', {})
        if temporal.get('posting_consistency', 0) > 0.7:
            scores['authenticity'] += 0.2
        if temporal.get('avg_posting_interval_hours', 0) > 1:  # Not too frequent
            scores['authenticity'] += 0.1

        # Engagement quality
        engagement = behavior_analysis.get('engagement_patterns', {})
        if engagement.get('avg_engagement_rate', 0) > 0.02:  # Good engagement rate
            scores['engagement_quality'] += 0.3
        if engagement.get('engagement_variance', {}).get('likes_std', 0) < 100:  # Consistent engagement
            scores['engagement_quality'] += 0.2

        # Content quality
        content = behavior_analysis.get('content_patterns', {})
        if content.get('content_diversity', 0) > 0.8:  # Diverse content
            scores['content_quality'] += 0.2
        if content.get('avg_words_per_post', 0) > 10:  # Substantial posts
            scores['content_quality'] += 0.2
        if content.get('style_consistency', 0) > 0.7:  # Consistent style
            scores['content_quality'] += 0.1

        # Consistency score
        if temporal.get('posting_consistency', 0) > 0.6:
            scores['consistency'] += 0.3
        if content.get('style_consistency', 0) > 0.6:
            scores['consistency'] += 0.2

        # Normalize scores
        for key in scores:
            scores[key] = min(1.0, max(0.0, scores[key]))

        return scores
