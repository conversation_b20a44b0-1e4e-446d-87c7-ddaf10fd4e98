#!/usr/bin/env python3
"""
Configuration file for Social Media Intelligence System
"""

import os
from pathlib import Path

# Base configuration
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
LOGS_DIR = BASE_DIR / "logs"
CACHE_DIR = BASE_DIR / "cache"
PROFILES_DIR = BASE_DIR / "profiles"

# Create directories if they don't exist
for directory in [DATA_DIR, LOGS_DIR, CACHE_DIR, PROFILES_DIR]:
    directory.mkdir(exist_ok=True)

# Database configuration
DATABASE_CONFIG = {
    'path': DATA_DIR / "social_intelligence.db",
    'backup_path': DATA_DIR / "backups",
    'encryption_key_file': DATA_DIR / ".encryption_key"
}

# API Keys (to be set via environment variables)
API_KEYS = {
    'hunter_io': os.getenv('HUNTER_IO_API_KEY'),
    'haveibeenpwned': os.getenv('HIBP_API_KEY'),
    'emailrep': os.getenv('EMAILREP_API_KEY'),
    'numverify': os.getenv('NUMVERIFY_API_KEY'),
    'twocaptcha': os.getenv('TWOCAPTCHA_API_KEY'),
    'tempmail': os.getenv('TEMPMAIL_API_KEY')
}

# Selenium configuration
SELENIUM_CONFIG = {
    'headless': True,
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'window_size': (1920, 1080),
    'page_load_timeout': 30,
    'implicit_wait': 10,
    'download_dir': str(CACHE_DIR),
    'proxy': None  # Format: 'ip:port' or None
}

# Platform-specific configurations
PLATFORM_CONFIGS = {
    'facebook': {
        'base_url': 'https://www.facebook.com',
        'login_url': 'https://www.facebook.com/login',
        'rate_limit': 2,  # seconds between requests
        'max_retries': 3,
        'selectors': {
            'username': 'input[name="email"]',
            'password': 'input[name="pass"]',
            'login_button': 'button[name="login"]',
            'profile_name': 'h1',
            'followers': '[data-overviewsection="followers"]',
            'posts': '[role="article"]'
        }
    },
    'instagram': {
        'base_url': 'https://www.instagram.com',
        'login_url': 'https://www.instagram.com/accounts/login/',
        'rate_limit': 3,
        'max_retries': 3,
        'selectors': {
            'username': 'input[name="username"]',
            'password': 'input[name="password"]',
            'login_button': 'button[type="submit"]',
            'profile_name': 'h2',
            'followers': 'a[href*="/followers/"] span',
            'posts': 'article'
        }
    },
    'twitter': {
        'base_url': 'https://twitter.com',
        'login_url': 'https://twitter.com/i/flow/login',
        'rate_limit': 1,
        'max_retries': 3,
        'selectors': {
            'username': 'input[name="text"]',
            'password': 'input[name="password"]',
            'login_button': '[data-testid="LoginForm_Login_Button"]',
            'profile_name': '[data-testid="UserName"]',
            'followers': '[data-testid="UserFollowers-stat"]',
            'posts': '[data-testid="tweet"]'
        }
    },
    'linkedin': {
        'base_url': 'https://www.linkedin.com',
        'login_url': 'https://www.linkedin.com/login',
        'rate_limit': 5,
        'max_retries': 3,
        'selectors': {
            'username': 'input[name="session_key"]',
            'password': 'input[name="session_password"]',
            'login_button': 'button[type="submit"]',
            'profile_name': 'h1',
            'connections': '.pv-top-card--list-bullet li',
            'posts': '.feed-shared-update-v2'
        }
    }
}

# OSINT configuration
OSINT_CONFIG = {
    'hunter_io': {
        'base_url': 'https://api.hunter.io/v2',
        'rate_limit': 1,
        'timeout': 30
    },
    'haveibeenpwned': {
        'base_url': 'https://haveibeenpwned.com/api/v3',
        'rate_limit': 1.5,
        'timeout': 30
    },
    'emailrep': {
        'base_url': 'https://emailrep.io',
        'rate_limit': 1,
        'timeout': 30
    },
    'numverify': {
        'base_url': 'http://apilayer.net/api',
        'rate_limit': 1,
        'timeout': 30
    }
}

# AI/ML configuration
AI_CONFIG = {
    'sentiment_model': 'cardiffnlp/twitter-roberta-base-sentiment-latest',
    'language_model': 'distilbert-base-uncased',
    'device': 'cpu',  # or 'cuda' if available
    'max_length': 512,
    'batch_size': 16
}

# Security configuration
SECURITY_CONFIG = {
    'encryption_algorithm': 'AES',
    'key_length': 256,
    'hash_algorithm': 'SHA256',
    'max_login_attempts': 3,
    'session_timeout': 3600,  # seconds
    'audit_log_retention': 90  # days
}

# Logging configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_path': LOGS_DIR / 'social_intelligence.log',
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# Legal and compliance
LEGAL_CONFIG = {
    'terms_accepted': False,
    'educational_mode': True,
    'data_retention_days': 30,
    'anonymize_data': True,
    'require_consent': True
}

# Performance settings
PERFORMANCE_CONFIG = {
    'max_concurrent_requests': 5,
    'request_timeout': 30,
    'cache_ttl': 3600,  # seconds
    'max_cache_size': 1000,  # number of items
    'cleanup_interval': 3600  # seconds
}
