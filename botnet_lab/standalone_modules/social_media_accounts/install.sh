#!/bin/bash
# Enhanced Social Media Intelligence System - Installation Script

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to detect OS
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if command_exists apt-get; then
            echo "ubuntu"
        elif command_exists yum; then
            echo "centos"
        elif command_exists pacman; then
            echo "arch"
        else
            echo "linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        echo "windows"
    else
        echo "unknown"
    fi
}

# Banner
echo -e "${BLUE}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════════════════════╗
║                    SOCIAL MEDIA INTELLIGENCE SYSTEM                         ║
║                           Installation Script                               ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  🔍 Real OSINT Collection    🤖 AI-Powered Analysis    🔒 Security Features  ║
║  🌐 Multi-Platform Support  📊 Comprehensive Reports  ⚖️  Legal Compliance  ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

print_status "Starting installation process..."

# Detect OS
OS=$(detect_os)
print_status "Detected OS: $OS"

# Check Python version
if command_exists python3; then
    PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
    print_status "Python version: $PYTHON_VERSION"
    
    # Check if Python version is >= 3.8
    if python3 -c 'import sys; exit(0 if sys.version_info >= (3, 8) else 1)'; then
        print_success "Python version is compatible"
    else
        print_error "Python 3.8+ is required. Current version: $PYTHON_VERSION"
        exit 1
    fi
else
    print_error "Python 3 is not installed"
    exit 1
fi

# Install system dependencies based on OS
install_system_deps() {
    print_status "Installing system dependencies..."
    
    case $OS in
        "ubuntu")
            sudo apt-get update
            sudo apt-get install -y \
                chromium-browser \
                python3-pip \
                python3-venv \
                python3-dev \
                build-essential \
                libssl-dev \
                libffi-dev \
                libjpeg-dev \
                zlib1g-dev
            ;;
        "centos")
            sudo yum update -y
            sudo yum install -y \
                chromium \
                python3-pip \
                python3-devel \
                gcc \
                openssl-devel \
                libffi-devel \
                libjpeg-devel \
                zlib-devel
            ;;
        "arch")
            sudo pacman -Syu --noconfirm
            sudo pacman -S --noconfirm \
                chromium \
                python-pip \
                base-devel \
                openssl \
                libffi \
                libjpeg \
                zlib
            ;;
        "macos")
            if command_exists brew; then
                brew install chromium python@3.11
            else
                print_warning "Homebrew not found. Please install Chromium manually."
            fi
            ;;
        *)
            print_warning "Unknown OS. Please install Chromium/Chrome manually."
            ;;
    esac
}

# Create virtual environment
create_venv() {
    print_status "Creating virtual environment..."
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_success "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    print_status "Virtual environment activated"
    
    # Upgrade pip
    pip install --upgrade pip
}

# Install Python dependencies
install_python_deps() {
    print_status "Installing Python dependencies..."
    
    # Install requirements
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        print_success "Python dependencies installed"
    else
        print_error "requirements.txt not found"
        exit 1
    fi
    
    # Install development dependencies (optional)
    read -p "Install development dependencies? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        pip install pytest pytest-asyncio black flake8 mypy
        print_success "Development dependencies installed"
    fi
}

# Download NLTK data
setup_nltk() {
    print_status "Setting up NLTK data..."
    
    python3 -c "
import nltk
try:
    nltk.download('punkt', quiet=True)
    nltk.download('vader_lexicon', quiet=True)
    nltk.download('brown', quiet=True)
    print('NLTK data downloaded successfully')
except Exception as e:
    print(f'NLTK setup warning: {e}')
"
}

# Create configuration files
setup_config() {
    print_status "Setting up configuration..."
    
    # Create .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        cat > .env << EOF
# OSINT APIs
HUNTER_IO_API_KEY=your_hunter_io_key_here
HIBP_API_KEY=your_haveibeenpwned_key_here
EMAILREP_API_KEY=your_emailrep_key_here
NUMVERIFY_API_KEY=your_numverify_key_here

# CAPTCHA Solving
TWOCAPTCHA_API_KEY=your_2captcha_key_here

# Temporary Email Services
TEMPMAIL_API_KEY=your_tempmail_key_here

# Security Settings
ENCRYPTION_ENABLED=true
EDUCATIONAL_MODE=true
AUDIT_LOGGING=true
EOF
        print_success "Configuration file created: .env"
        print_warning "Please edit .env file with your API keys"
    else
        print_warning ".env file already exists"
    fi
    
    # Create data directories
    mkdir -p data logs cache profiles
    print_success "Data directories created"
}

# Run tests
run_tests() {
    print_status "Running system tests..."
    
    # Run basic tests
    if python3 -m pytest test_enhanced_system.py -v --tb=short; then
        print_success "All tests passed"
    else
        print_warning "Some tests failed - system may still work"
    fi
}

# Main installation process
main() {
    # Check if running as root (not recommended)
    if [ "$EUID" -eq 0 ]; then
        print_warning "Running as root is not recommended"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Install system dependencies
    read -p "Install system dependencies? (Y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        install_system_deps
    fi
    
    # Create virtual environment and install Python deps
    create_venv
    install_python_deps
    
    # Setup NLTK
    setup_nltk
    
    # Setup configuration
    setup_config
    
    # Run tests
    read -p "Run system tests? (Y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        run_tests
    fi
    
    # Installation complete
    print_success "Installation completed successfully!"
    
    echo
    print_status "Next steps:"
    echo "1. Edit .env file with your API keys"
    echo "2. Activate virtual environment: source venv/bin/activate"
    echo "3. Run the CLI: python cli_interface.py"
    echo "4. Or run the main module: python social_accounts_standalone.py"
    echo
    print_warning "Remember: Use this tool responsibly and ethically!"
    print_warning "Only use for authorized research and educational purposes!"
}

# Run main function
main "$@"
