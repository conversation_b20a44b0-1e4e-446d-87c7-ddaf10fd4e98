#!/usr/bin/env python3
"""
اختبار آمن لنظام الاستخبارات الاجتماعية
Safe Testing Script for Social Media Intelligence System
"""

import asyncio
import sys
import os
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_legal_warning():
    """عرض التحذير القانوني"""
    warning = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                           ⚖️ تحذير قانوني مهم ⚖️                           ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║  🔴 هذا النظام للأغراض التعليمية والبحثية فقط                              ║
║                                                                              ║
║  ✅ مسموح:                                                                  ║
║     • اختبار حسابك الشخصي فقط                                              ║
║     • حسابات عامة مع إذن صريح                                              ║
║     • البحث الأكاديمي المصرح به                                            ║
║                                                                              ║
║  ❌ ممنوع:                                                                  ║
║     • تحليل حسابات الآخرين بدون إذن                                        ║
║     • انتهاك شروط خدمة المنصات                                             ║
║     • جمع معلومات لأغراض ضارة                                              ║
║                                                                              ║
║  📝 بالمتابعة، أنت توافق على:                                               ║
║     • استخدام النظام بمسؤولية                                              ║
║     • احترام قوانين الخصوصية                                               ║
║     • عدم انتهاك حقوق الآخرين                                              ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(warning)

def get_user_consent():
    """الحصول على موافقة المستخدم"""
    print("\n🔍 أنواع الاختبارات المتاحة:")
    print("1. 🟢 اختبار آمن (محاكاة) - لا يصل للإنترنت")
    print("2. 🟡 اختبار حساب عام - حسابات عامة فقط")
    print("3. 🔴 اختبار حساب شخصي - حسابك الشخصي فقط")
    print("0. ❌ إلغاء")
    
    while True:
        choice = input("\nاختر نوع الاختبار (0-3): ").strip()
        if choice in ['0', '1', '2', '3']:
            return choice
        print("❌ اختيار غير صحيح، حاول مرة أخرى")

def get_legal_confirmation():
    """تأكيد الموافقة القانونية"""
    print("\n📋 تأكيد الموافقة:")
    print("أؤكد أنني:")
    print("✅ قرأت وفهمت التحذير القانوني")
    print("✅ سأستخدم النظام للأغراض التعليمية فقط")
    print("✅ لن أنتهك حقوق الآخرين أو قوانين الخصوصية")
    print("✅ أتحمل المسؤولية الكاملة عن استخدامي")
    
    while True:
        confirm = input("\nهل توافق على الشروط أعلاه؟ (نعم/لا): ").strip().lower()
        if confirm in ['نعم', 'yes', 'y', 'ن']:
            return True
        elif confirm in ['لا', 'no', 'n', 'ل']:
            return False
        print("❌ يرجى الإجابة بـ 'نعم' أو 'لا'")

async def safe_simulation_test():
    """اختبار آمن بالمحاكاة"""
    print("\n🟢 تشغيل الاختبار الآمن (محاكاة)")
    print("=" * 50)
    
    try:
        from social_accounts_standalone import EnhancedSocialMediaIntelligence
        
        print("🔄 إنشاء نظام الاستخبارات...")
        intelligence = EnhancedSocialMediaIntelligence()
        intelligence.start_social_media_system()
        
        print("🔄 تحليل ملف شخصي تجريبي...")
        
        # تحليل آمن (محاكاة فقط)
        result = await intelligence.analyze_profile(
            platform="instagram",
            username_or_url="test_user_simulation",
            use_real_scraping=False,  # محاكاة آمنة
            include_osint=False,      # بدون OSINT حقيقي
            include_ai_analysis=True  # تحليل AI آمن
        )
        
        if result and 'error' not in result:
            print("✅ تم التحليل بنجاح!")
            print(f"📊 درجة المخاطر: {result.get('risk_score', 0.0):.2f}")
            print(f"🔧 الطرق المستخدمة: {', '.join(result.get('methods_used', []))}")
            print(f"🎓 وضع المحاكاة: نعم")
            
            # عرض بيانات المحاكاة
            profile_data = result.get('profile_data', {})
            if profile_data:
                print(f"👥 المتابعون (محاكاة): {profile_data.get('follower_count', 'غير متوفر')}")
                print(f"✅ محقق (محاكاة): {profile_data.get('verified', False)}")
        else:
            print(f"❌ فشل التحليل: {result.get('error', 'خطأ غير معروف')}")
        
        intelligence.stop_social_media_system()
        print("\n✅ انتهى الاختبار الآمن بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

async def public_account_test():
    """اختبار حساب عام"""
    print("\n🟡 اختبار حساب عام")
    print("=" * 50)
    
    print("⚠️ تحذير: سيتم الوصول لحساب عام حقيقي")
    print("تأكد من أن الحساب:")
    print("• عام ومتاح للجميع")
    print("• لا ينتهك شروط الخدمة")
    print("• مناسب للاختبار التعليمي")
    
    platform = input("\nأدخل المنصة (instagram/facebook/twitter): ").strip().lower()
    if platform not in ['instagram', 'facebook', 'twitter']:
        print("❌ منصة غير مدعومة")
        return
    
    username = input(f"أدخل اسم المستخدم على {platform}: ").strip()
    if not username:
        print("❌ اسم المستخدم مطلوب")
        return
    
    # تأكيد إضافي
    confirm = input(f"\nتأكيد تحليل @{username} على {platform}؟ (نعم/لا): ").strip().lower()
    if confirm not in ['نعم', 'yes', 'y', 'ن']:
        print("❌ تم إلغاء الاختبار")
        return
    
    try:
        from social_accounts_standalone import EnhancedSocialMediaIntelligence
        
        print("🔄 إنشاء نظام الاستخبارات...")
        intelligence = EnhancedSocialMediaIntelligence()
        intelligence.start_social_media_system()
        
        print(f"🔄 تحليل @{username} على {platform}...")
        
        # تحليل حقيقي لكن آمن
        result = await intelligence.analyze_profile(
            platform=platform,
            username_or_url=username,
            use_real_scraping=True,   # استخراج حقيقي
            include_osint=False,      # بدون OSINT للأمان
            include_ai_analysis=True  # تحليل AI
        )
        
        if result and 'error' not in result:
            print("✅ تم التحليل بنجاح!")
            print(f"📊 درجة المخاطر: {result.get('risk_score', 0.0):.2f}")
            print(f"🔧 الطرق المستخدمة: {', '.join(result.get('methods_used', []))}")
            
            # عرض النتائج الأساسية
            profile_data = result.get('profile_data', {})
            if profile_data:
                print(f"👥 المتابعون: {profile_data.get('follower_count', 'غير متوفر')}")
                print(f"✅ محقق: {profile_data.get('verified', False)}")
                print(f"📝 النبذة: {profile_data.get('bio', 'غير متوفر')[:100]}...")
            
            # تحليل AI
            ai_analysis = result.get('ai_analysis', {})
            if ai_analysis:
                authenticity = ai_analysis.get('authenticity_assessment', {})
                if authenticity:
                    print(f"🤖 درجة الأصالة: {authenticity.get('overall_authenticity', 0.0):.2f}")
        else:
            print(f"❌ فشل التحليل: {result.get('error', 'خطأ غير معروف')}")
        
        intelligence.stop_social_media_system()
        print("\n✅ انتهى اختبار الحساب العام")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

async def personal_account_test():
    """اختبار حساب شخصي"""
    print("\n🔴 اختبار حساب شخصي")
    print("=" * 50)
    
    print("⚠️ تحذير مهم:")
    print("• استخدم حسابك الشخصي فقط")
    print("• تأكد من أنك تملك الحساب")
    print("• لا تستخدم حسابات الآخرين")
    
    print("\n📝 معلومات إضافية:")
    print("• سيتم تحليل الحساب بشكل شامل")
    print("• قد يتضمن فحص OSINT للإيميل")
    print("• جميع البيانات ستبقى محلية")
    
    platform = input("\nأدخل المنصة (instagram/facebook/twitter): ").strip().lower()
    if platform not in ['instagram', 'facebook', 'twitter']:
        print("❌ منصة غير مدعومة")
        return
    
    username = input(f"أدخل اسم المستخدم لحسابك على {platform}: ").strip()
    if not username:
        print("❌ اسم المستخدم مطلوب")
        return
    
    # تأكيد الملكية
    ownership_confirm = input(f"\nأؤكد أن @{username} هو حسابي الشخصي (نعم/لا): ").strip().lower()
    if ownership_confirm not in ['نعم', 'yes', 'y', 'ن']:
        print("❌ يجب تأكيد ملكية الحساب")
        return
    
    # خيارات التحليل
    print("\n🔧 خيارات التحليل:")
    include_osint = input("تضمين فحص OSINT؟ (نعم/لا): ").strip().lower() in ['نعم', 'yes', 'y', 'ن']
    
    # تأكيد نهائي
    final_confirm = input(f"\nتأكيد نهائي لتحليل حسابك @{username}؟ (نعم/لا): ").strip().lower()
    if final_confirm not in ['نعم', 'yes', 'y', 'ن']:
        print("❌ تم إلغاء الاختبار")
        return
    
    try:
        from social_accounts_standalone import EnhancedSocialMediaIntelligence
        
        print("🔄 إنشاء نظام الاستخبارات...")
        intelligence = EnhancedSocialMediaIntelligence()
        intelligence.start_social_media_system()
        
        print(f"🔄 تحليل حسابك @{username} على {platform}...")
        
        # تحليل شامل
        result = await intelligence.analyze_profile(
            platform=platform,
            username_or_url=username,
            use_real_scraping=True,      # استخراج حقيقي
            include_osint=include_osint, # حسب اختيار المستخدم
            include_ai_analysis=True     # تحليل AI
        )
        
        if result and 'error' not in result:
            print("✅ تم التحليل بنجاح!")
            print(f"📊 درجة المخاطر: {result.get('risk_score', 0.0):.2f}")
            print(f"🔧 الطرق المستخدمة: {', '.join(result.get('methods_used', []))}")
            
            # عرض النتائج التفصيلية
            profile_data = result.get('profile_data', {})
            if profile_data:
                print(f"\n📱 بيانات الملف الشخصي:")
                print(f"👥 المتابعون: {profile_data.get('follower_count', 'غير متوفر')}")
                print(f"👤 المتابَعون: {profile_data.get('following_count', 'غير متوفر')}")
                print(f"📸 المنشورات: {profile_data.get('post_count', 'غير متوفر')}")
                print(f"✅ محقق: {profile_data.get('verified', False)}")
                print(f"📝 النبذة: {profile_data.get('bio', 'غير متوفر')[:100]}...")
            
            # تحليل AI
            ai_analysis = result.get('ai_analysis', {})
            if ai_analysis:
                print(f"\n🤖 تحليل الذكاء الاصطناعي:")
                authenticity = ai_analysis.get('authenticity_assessment', {})
                if authenticity:
                    print(f"🎯 درجة الأصالة الإجمالية: {authenticity.get('overall_authenticity', 0.0):.2f}")
                    print(f"📝 أصالة المحتوى: {authenticity.get('content_authenticity', 0.0):.2f}")
                    print(f"🔄 الاتساق السلوكي: {authenticity.get('behavioral_consistency', 0.0):.2f}")
            
            # بيانات OSINT (إذا تم تفعيلها)
            if include_osint:
                osint_data = result.get('osint_data', {})
                if osint_data:
                    print(f"\n🕵️ تقرير OSINT:")
                    print(f"📊 المصادر المستخدمة: {', '.join(osint_data.get('sources', []))}")
                    
                    comprehensive_report = osint_data.get('comprehensive_report', {})
                    if comprehensive_report:
                        print(f"⚠️ المخاطر الإجمالية: {comprehensive_report.get('overall_risk', 'غير معروف')}")
        else:
            print(f"❌ فشل التحليل: {result.get('error', 'خطأ غير معروف')}")
        
        intelligence.stop_social_media_system()
        print("\n✅ انتهى اختبار الحساب الشخصي")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

async def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نظام الاستخبارات الاجتماعية")
    print("=" * 60)
    
    # عرض التحذير القانوني
    print_legal_warning()
    
    # الحصول على الموافقة القانونية
    if not get_legal_confirmation():
        print("\n❌ لا يمكن المتابعة بدون الموافقة على الشروط")
        print("👋 شكراً لك")
        return
    
    # اختيار نوع الاختبار
    test_type = get_user_consent()
    
    if test_type == '0':
        print("\n❌ تم إلغاء الاختبار")
        return
    elif test_type == '1':
        await safe_simulation_test()
    elif test_type == '2':
        await public_account_test()
    elif test_type == '3':
        await personal_account_test()
    
    print("\n" + "=" * 60)
    print("🎉 انتهى الاختبار")
    print("📝 تذكر: استخدم النظام بمسؤولية دائماً")
    print("⚖️ احترم قوانين الخصوصية وحقوق الآخرين")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        print("💡 جرب: python run.py للاستخدام العادي")
