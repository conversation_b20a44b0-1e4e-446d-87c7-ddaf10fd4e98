#!/usr/bin/env python3
"""
Security and Encryption Manager
Handles data encryption, audit logging, and legal compliance
"""

import os
import json
import hashlib
import logging
import sqlite3
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from pathlib import Path

try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    import base64
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False

from config import DATABASE_CONFIG, SECURITY_CONFIG, LEGAL_CONFIG, LOGS_DIR

class SecurityManager:
    """Comprehensive security and compliance manager"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.encryption_key = None
        self.audit_db_path = LOGS_DIR / "audit.db"
        self.legal_consent = False
        
        if not CRYPTOGRAPHY_AVAILABLE:
            self.logger.warning("Cryptography library not available - encryption disabled")
        
        self.setup_security()
        self.setup_audit_logging()
        self.check_legal_compliance()
    
    def setup_security(self):
        """Setup encryption and security measures"""
        try:
            if CRYPTOGRAPHY_AVAILABLE:
                # Generate or load encryption key
                key_file = DATABASE_CONFIG['encryption_key_file']
                
                if os.path.exists(key_file):
                    with open(key_file, 'rb') as f:
                        self.encryption_key = f.read()
                else:
                    # Generate new key
                    self.encryption_key = Fernet.generate_key()
                    
                    # Save key securely
                    os.makedirs(os.path.dirname(key_file), exist_ok=True)
                    with open(key_file, 'wb') as f:
                        f.write(self.encryption_key)
                    
                    # Set restrictive permissions
                    os.chmod(key_file, 0o600)
                
                self.logger.info("Encryption system initialized")
            
        except Exception as e:
            self.logger.error(f"Security setup error: {e}")
    
    def setup_audit_logging(self):
        """Setup audit logging database"""
        try:
            conn = sqlite3.connect(self.audit_db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audit_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    user_id TEXT,
                    action TEXT NOT NULL,
                    resource TEXT,
                    details TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    success BOOLEAN,
                    risk_level TEXT,
                    session_id TEXT
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS security_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    event_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    description TEXT,
                    source_ip TEXT,
                    details TEXT,
                    resolved BOOLEAN DEFAULT FALSE
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS legal_compliance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    user_id TEXT,
                    consent_type TEXT NOT NULL,
                    consent_given BOOLEAN,
                    terms_version TEXT,
                    ip_address TEXT,
                    details TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
            self.logger.info("Audit logging system initialized")
            
        except Exception as e:
            self.logger.error(f"Audit logging setup error: {e}")
    
    def check_legal_compliance(self):
        """Check and enforce legal compliance"""
        try:
            if LEGAL_CONFIG['require_consent'] and not LEGAL_CONFIG['terms_accepted']:
                self.show_legal_notice()
            
            if LEGAL_CONFIG['educational_mode']:
                self.show_educational_notice()
            
        except Exception as e:
            self.logger.error(f"Legal compliance check error: {e}")
    
    def show_legal_notice(self):
        """Display legal notice and get consent"""
        legal_notice = """
        ╔══════════════════════════════════════════════════════════════════════════════╗
        ║                              LEGAL NOTICE                                    ║
        ╠══════════════════════════════════════════════════════════════════════════════╣
        ║                                                                              ║
        ║  SOCIAL MEDIA INTELLIGENCE SYSTEM - TERMS OF USE                            ║
        ║                                                                              ║
        ║  This software is provided for EDUCATIONAL and RESEARCH purposes only.      ║
        ║                                                                              ║
        ║  PERMITTED USES:                                                             ║
        ║  • Academic research with proper institutional approval                      ║
        ║  • Security research and vulnerability assessment                            ║
        ║  • Authorized penetration testing                                           ║
        ║  • Educational demonstrations and training                                   ║
        ║                                                                              ║
        ║  PROHIBITED USES:                                                            ║
        ║  • Unauthorized access to accounts or systems                               ║
        ║  • Identity theft or impersonation                                          ║
        ║  • Harassment, stalking, or malicious activities                            ║
        ║  • Violation of platform terms of service                                   ║
        ║  • Any illegal activities under applicable law                              ║
        ║                                                                              ║
        ║  DATA PROTECTION:                                                            ║
        ║  • All collected data is encrypted and stored securely                      ║
        ║  • Data retention period: 30 days (configurable)                           ║
        ║  • Data is anonymized when possible                                         ║
        ║  • Users can request data deletion at any time                              ║
        ║                                                                              ║
        ║  COMPLIANCE:                                                                 ║
        ║  • GDPR compliant data handling                                             ║
        ║  • Audit logging of all activities                                          ║
        ║  • Regular security assessments                                             ║
        ║                                                                              ║
        ║  By using this software, you acknowledge that you:                          ║
        ║  • Have read and understood these terms                                     ║
        ║  • Have proper authorization for your intended use                          ║
        ║  • Will comply with all applicable laws and regulations                     ║
        ║  • Understand the ethical implications of your actions                      ║
        ║                                                                              ║
        ║  The developers and distributors of this software are not responsible       ║
        ║  for any misuse or illegal activities conducted with this tool.             ║
        ║                                                                              ║
        ╚══════════════════════════════════════════════════════════════════════════════╝
        """
        
        print(legal_notice)
        
        # Get user consent
        while True:
            consent = input("\nDo you accept these terms and conditions? (yes/no): ").lower().strip()
            
            if consent in ['yes', 'y']:
                self.legal_consent = True
                self.log_legal_consent(True, "Terms and conditions accepted")
                break
            elif consent in ['no', 'n']:
                self.legal_consent = False
                self.log_legal_consent(False, "Terms and conditions rejected")
                print("\nTerms rejected. Exiting application.")
                exit(1)
            else:
                print("Please enter 'yes' or 'no'")
    
    def show_educational_notice(self):
        """Display educational mode notice"""
        educational_notice = """
        🎓 EDUCATIONAL MODE ENABLED
        
        This system is running in educational mode with the following restrictions:
        • Real account creation is disabled
        • Simulated data is used for demonstrations
        • All activities are logged for educational review
        • Enhanced safety measures are active
        
        This mode is designed for learning about social media intelligence
        techniques in a safe and controlled environment.
        """
        
        print(educational_notice)
        self.log_audit_event("system", "educational_mode_enabled", "Educational mode activated")
    
    def encrypt_data(self, data: Union[str, Dict, List]) -> Optional[str]:
        """Encrypt sensitive data"""
        try:
            if not CRYPTOGRAPHY_AVAILABLE or not self.encryption_key:
                self.logger.warning("Encryption not available - storing data in plain text")
                return json.dumps(data) if not isinstance(data, str) else data
            
            # Convert data to JSON string if needed
            if not isinstance(data, str):
                data = json.dumps(data)
            
            # Encrypt data
            fernet = Fernet(self.encryption_key)
            encrypted_data = fernet.encrypt(data.encode())
            
            return base64.b64encode(encrypted_data).decode()
            
        except Exception as e:
            self.logger.error(f"Encryption error: {e}")
            return None
    
    def decrypt_data(self, encrypted_data: str) -> Optional[Union[str, Dict, List]]:
        """Decrypt sensitive data"""
        try:
            if not CRYPTOGRAPHY_AVAILABLE or not self.encryption_key:
                # Try to parse as JSON, fallback to string
                try:
                    return json.loads(encrypted_data)
                except:
                    return encrypted_data
            
            # Decrypt data
            fernet = Fernet(self.encryption_key)
            decoded_data = base64.b64decode(encrypted_data.encode())
            decrypted_data = fernet.decrypt(decoded_data).decode()
            
            # Try to parse as JSON
            try:
                return json.loads(decrypted_data)
            except:
                return decrypted_data
                
        except Exception as e:
            self.logger.error(f"Decryption error: {e}")
            return None
    
    def hash_sensitive_data(self, data: str) -> str:
        """Create hash of sensitive data for indexing"""
        return hashlib.sha256(data.encode()).hexdigest()
    
    def log_audit_event(self, user_id: str, action: str, resource: str = None, 
                       details: Dict = None, success: bool = True, 
                       risk_level: str = "low", ip_address: str = None,
                       user_agent: str = None, session_id: str = None):
        """Log audit event"""
        try:
            conn = sqlite3.connect(self.audit_db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO audit_log 
                (timestamp, user_id, action, resource, details, ip_address, 
                 user_agent, success, risk_level, session_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                user_id,
                action,
                resource,
                json.dumps(details) if details else None,
                ip_address,
                user_agent,
                success,
                risk_level,
                session_id
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Audit logging error: {e}")
    
    def log_security_event(self, event_type: str, severity: str, description: str,
                          source_ip: str = None, details: Dict = None):
        """Log security event"""
        try:
            conn = sqlite3.connect(self.audit_db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO security_events 
                (timestamp, event_type, severity, description, source_ip, details)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                event_type,
                severity,
                description,
                source_ip,
                json.dumps(details) if details else None
            ))
            
            conn.commit()
            conn.close()
            
            # Log high severity events to main logger
            if severity in ['high', 'critical']:
                self.logger.warning(f"Security event: {event_type} - {description}")
            
        except Exception as e:
            self.logger.error(f"Security event logging error: {e}")
    
    def log_legal_consent(self, consent_given: bool, details: str = None,
                         user_id: str = "anonymous", ip_address: str = None):
        """Log legal consent"""
        try:
            conn = sqlite3.connect(self.audit_db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO legal_compliance 
                (timestamp, user_id, consent_type, consent_given, terms_version, 
                 ip_address, details)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                user_id,
                "terms_and_conditions",
                consent_given,
                "1.0",
                ip_address,
                details
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Legal consent logging error: {e}")
    
    def get_audit_report(self, start_date: str = None, end_date: str = None,
                        user_id: str = None, action: str = None) -> List[Dict]:
        """Generate audit report"""
        try:
            conn = sqlite3.connect(self.audit_db_path)
            cursor = conn.cursor()
            
            query = "SELECT * FROM audit_log WHERE 1=1"
            params = []
            
            if start_date:
                query += " AND timestamp >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND timestamp <= ?"
                params.append(end_date)
            
            if user_id:
                query += " AND user_id = ?"
                params.append(user_id)
            
            if action:
                query += " AND action = ?"
                params.append(action)
            
            query += " ORDER BY timestamp DESC"
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            columns = [description[0] for description in cursor.description]
            
            conn.close()
            
            return [dict(zip(columns, row)) for row in rows]
            
        except Exception as e:
            self.logger.error(f"Audit report generation error: {e}")
            return []
    
    def cleanup_old_data(self, retention_days: int = None):
        """Clean up old audit data"""
        try:
            if retention_days is None:
                retention_days = SECURITY_CONFIG['audit_log_retention']
            
            cutoff_date = (datetime.now() - timedelta(days=retention_days)).isoformat()
            
            conn = sqlite3.connect(self.audit_db_path)
            cursor = conn.cursor()
            
            # Clean up old audit logs
            cursor.execute("DELETE FROM audit_log WHERE timestamp < ?", (cutoff_date,))
            deleted_audit = cursor.rowcount
            
            # Clean up old security events (keep resolved ones longer)
            cursor.execute("""
                DELETE FROM security_events 
                WHERE timestamp < ? AND resolved = TRUE
            """, (cutoff_date,))
            deleted_security = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"Cleaned up {deleted_audit} audit logs and {deleted_security} security events")
            
        except Exception as e:
            self.logger.error(f"Data cleanup error: {e}")
    
    def validate_user_permissions(self, user_id: str, action: str, resource: str = None) -> bool:
        """Validate user permissions for action"""
        # Basic permission validation
        # In a real system, this would check against a proper RBAC system
        
        if not self.legal_consent:
            self.log_security_event("unauthorized_access", "high", 
                                   f"User {user_id} attempted {action} without legal consent")
            return False
        
        # Log the permission check
        self.log_audit_event(user_id, f"permission_check_{action}", resource, 
                           {"result": "granted"}, True, "low")
        
        return True
    
    def get_security_status(self) -> Dict[str, Any]:
        """Get current security status"""
        return {
            'encryption_enabled': CRYPTOGRAPHY_AVAILABLE and self.encryption_key is not None,
            'audit_logging_enabled': os.path.exists(self.audit_db_path),
            'legal_consent': self.legal_consent,
            'educational_mode': LEGAL_CONFIG['educational_mode'],
            'data_retention_days': SECURITY_CONFIG['audit_log_retention'],
            'last_cleanup': datetime.now().isoformat()
        }
