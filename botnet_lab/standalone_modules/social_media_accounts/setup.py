#!/usr/bin/env python3
"""
Setup script for Enhanced Social Media Intelligence System
"""

from setuptools import setup, find_packages
import os

# Read README file
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="social-media-intelligence",
    version="2.0.0",
    author="Social Intelligence Research Team",
    author_email="<EMAIL>",
    description="Enhanced Social Media Intelligence System with Real OSINT and AI Analysis",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/example/social-media-intelligence",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Information Analysis",
        "Topic :: Security",
        "Topic :: Internet :: WWW/HTTP :: Indexing/Search",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.9.0",
            "flake8>=6.1.0",
            "mypy>=1.6.0",
        ],
        "gui": [
            "streamlit>=1.28.0",
            "flask>=3.0.0",
            "gradio>=4.0.0",
        ],
        "full": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.9.0",
            "flake8>=6.1.0",
            "mypy>=1.6.0",
            "streamlit>=1.28.0",
            "flask>=3.0.0",
            "gradio>=4.0.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "social-intelligence=cli_interface:main",
            "social-intel=social_accounts_standalone:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.yml", "*.yaml"],
    },
    project_urls={
        "Bug Reports": "https://github.com/example/social-media-intelligence/issues",
        "Source": "https://github.com/example/social-media-intelligence",
        "Documentation": "https://social-intelligence.readthedocs.io/",
    },
)
