#!/usr/bin/env python3
"""
Enhanced Test Suite for Social Media Intelligence System
Tests all new real capabilities and security features
"""

import unittest
import asyncio
import sys
import os
import tempfile
import json
import time
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from datetime import datetime

# Add the module to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from social_accounts_standalone import EnhancedSocialMediaIntelligence
    from web_scraper import Real<PERSON>ebS<PERSON>raper
    from osint_collector import RealOSINTCollector
    from ai_analyzer import AIContentAnalyzer
    from security_manager import SecurityManager
    from fake_account_creator import FakeAccountCreator
    ENHANCED_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Enhanced modules not available: {e}")
    ENHANCED_MODULES_AVAILABLE = False

class TestEnhancedSocialMediaIntelligence(unittest.TestCase):
    """Test cases for enhanced social media intelligence system"""
    
    def setUp(self):
        """Set up test environment"""
        if not ENHANCED_MODULES_AVAILABLE:
            self.skipTest("Enhanced modules not available")
        
        self.intelligence = EnhancedSocialMediaIntelligence()
        self.test_data = {
            'test_username': 'test_user_123',
            'test_email': '<EMAIL>',
            'test_phone': '+**********',
            'test_platform': 'instagram'
        }
    
    def tearDown(self):
        """Clean up after tests"""
        if hasattr(self, 'intelligence'):
            self.intelligence.stop_social_media_system()
    
    def test_system_initialization(self):
        """Test system initialization and capabilities"""
        print("Testing system initialization...")
        
        # Test basic initialization
        self.assertIsNotNone(self.intelligence)
        self.assertIsInstance(self.intelligence.capabilities, dict)
        self.assertIsInstance(self.intelligence.platforms, dict)
        
        # Test enhanced modules initialization
        if ENHANCED_MODULES_AVAILABLE:
            self.assertIsNotNone(self.intelligence.security_manager)
            self.assertTrue(self.intelligence.capabilities['real_profile_scraping'])
            self.assertTrue(self.intelligence.capabilities['security_encryption'])
        
        print("✅ System initialization test passed")
    
    def test_security_manager(self):
        """Test security manager functionality"""
        print("Testing security manager...")
        
        if not self.intelligence.security_manager:
            self.skipTest("Security manager not available")
        
        security = self.intelligence.security_manager
        
        # Test encryption/decryption
        test_data = {"sensitive": "information", "password": "secret123"}
        encrypted = security.encrypt_data(test_data)
        self.assertIsNotNone(encrypted)
        
        decrypted = security.decrypt_data(encrypted)
        self.assertEqual(decrypted, test_data)
        
        # Test audit logging
        security.log_audit_event("test_user", "test_action", "test_resource")
        
        # Test security status
        status = security.get_security_status()
        self.assertIsInstance(status, dict)
        self.assertIn('encryption_enabled', status)
        
        print("✅ Security manager test passed")
    
    @patch('web_scraper.RealWebScraper.scrape_profile')
    async def test_real_web_scraping(self, mock_scrape):
        """Test real web scraping functionality"""
        print("Testing real web scraping...")
        
        # Mock scraping result
        mock_result = {
            'username': self.test_data['test_username'],
            'platform': self.test_data['test_platform'],
            'follower_count': 1000,
            'verified': False,
            'bio': 'Test bio content'
        }
        mock_scrape.return_value = mock_result
        
        if self.intelligence.web_scraper:
            result = self.intelligence.web_scraper.scrape_profile(
                self.test_data['test_platform'], 
                self.test_data['test_username']
            )
            
            self.assertIsInstance(result, dict)
            self.assertEqual(result['username'], self.test_data['test_username'])
            self.assertEqual(result['platform'], self.test_data['test_platform'])
        
        print("✅ Real web scraping test passed")
    
    @patch('osint_collector.RealOSINTCollector.comprehensive_email_osint')
    async def test_osint_collection(self, mock_osint):
        """Test OSINT collection functionality"""
        print("Testing OSINT collection...")
        
        # Mock OSINT result
        mock_result = {
            'email': self.test_data['test_email'],
            'breaches': {'total_breaches': 2, 'risk_level': 'medium'},
            'reputation': {'suspicious': False},
            'sources': ['haveibeenpwned', 'emailrep']
        }
        mock_osint.return_value = mock_result
        
        if self.intelligence.osint_collector:
            result = await self.intelligence.osint_collector.comprehensive_email_osint(
                self.test_data['test_email']
            )
            
            self.assertIsInstance(result, dict)
            self.assertEqual(result['email'], self.test_data['test_email'])
            self.assertIn('sources', result)
        
        print("✅ OSINT collection test passed")
    
    def test_ai_content_analysis(self):
        """Test AI content analysis functionality"""
        print("Testing AI content analysis...")
        
        if not self.intelligence.ai_analyzer:
            self.skipTest("AI analyzer not available")
        
        test_text = "I love this amazing product! It's absolutely fantastic and works perfectly."
        
        # Test sentiment analysis
        sentiment_result = self.intelligence.ai_analyzer.analyze_sentiment_textblob(test_text)
        self.assertIsInstance(sentiment_result, dict)
        self.assertIn('sentiment', sentiment_result)
        self.assertIn('polarity', sentiment_result)
        
        # Test comprehensive text analysis
        comprehensive_result = self.intelligence.ai_analyzer.analyze_text_comprehensive(test_text)
        self.assertIsInstance(comprehensive_result, dict)
        self.assertIn('sentiment', comprehensive_result)
        self.assertIn('statistics', comprehensive_result)
        
        print("✅ AI content analysis test passed")
    
    async def test_enhanced_profile_analysis(self):
        """Test enhanced profile analysis with all features"""
        print("Testing enhanced profile analysis...")
        
        with patch.object(self.intelligence, 'scrape_profile_data') as mock_scrape, \
             patch.object(self.intelligence, 'gather_enhanced_osint') as mock_osint, \
             patch.object(self.intelligence, 'perform_ai_analysis') as mock_ai:
            
            # Mock results
            mock_scrape.return_value = {
                'username': self.test_data['test_username'],
                'follower_count': 1000,
                'bio': 'Test bio'
            }
            
            mock_osint.return_value = {
                'comprehensive_report': {'overall_risk': 'low'}
            }
            
            mock_ai.return_value = {
                'authenticity_assessment': {'overall_authenticity': 0.8}
            }
            
            # Test enhanced analysis
            result = await self.intelligence.analyze_profile(
                self.test_data['test_platform'],
                self.test_data['test_username'],
                use_real_scraping=False,  # Use mocked data
                include_osint=True,
                include_ai_analysis=True
            )
            
            self.assertIsInstance(result, dict)
            self.assertIn('profile_id', result)
            self.assertIn('risk_score', result)
            self.assertIn('methods_used', result)
            self.assertIsInstance(result['methods_used'], list)
        
        print("✅ Enhanced profile analysis test passed")
    
    async def test_fake_account_creation_educational(self):
        """Test educational fake account creation"""
        print("Testing educational fake account creation...")
        
        # Test in educational mode
        result = await self.intelligence.create_fake_account(
            self.test_data['test_platform'],
            account_type='educational',
            educational_mode=True
        )
        
        self.assertIsInstance(result, dict)
        if 'error' not in result:
            self.assertIn('username', result)
            self.assertIn('platform', result)
            self.assertTrue(result.get('educational_mode', False))
            self.assertEqual(result.get('status'), 'simulated')
        
        print("✅ Educational fake account creation test passed")
    
    def test_database_encryption(self):
        """Test encrypted database operations"""
        print("Testing database encryption...")
        
        if not self.intelligence.security_manager:
            self.skipTest("Security manager not available")
        
        # Test data encryption
        test_analysis = {
            'profile_id': 'test_123',
            'platform': 'instagram',
            'username': 'test_user',
            'profile_data': {'sensitive': 'data'},
            'risk_score': 0.5
        }
        
        # This should encrypt and store data
        asyncio.run(self.intelligence.store_profile_intelligence_secure(test_analysis))
        
        print("✅ Database encryption test passed")
    
    def test_performance_metrics(self):
        """Test system performance and statistics"""
        print("Testing performance metrics...")
        
        # Get initial stats
        initial_stats = self.intelligence.stats.copy()
        
        # Simulate some operations
        self.intelligence.stats['profiles_analyzed'] += 1
        self.intelligence.stats['real_profiles_scraped'] += 1
        
        # Check stats updated
        self.assertGreater(
            self.intelligence.stats['profiles_analyzed'],
            initial_stats['profiles_analyzed']
        )
        
        # Test system status
        status = self.intelligence.get_system_status()
        self.assertIsInstance(status, dict)
        self.assertIn('statistics', status)
        
        print("✅ Performance metrics test passed")

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def setUp(self):
        """Set up integration test environment"""
        if not ENHANCED_MODULES_AVAILABLE:
            self.skipTest("Enhanced modules not available")
        
        self.intelligence = EnhancedSocialMediaIntelligence()
        self.intelligence.start_social_media_system()
    
    def tearDown(self):
        """Clean up integration tests"""
        if hasattr(self, 'intelligence'):
            self.intelligence.stop_social_media_system()
    
    async def test_full_workflow(self):
        """Test complete analysis workflow"""
        print("Testing full analysis workflow...")
        
        # Mock all external dependencies
        with patch.object(self.intelligence, 'scrape_profile_data') as mock_scrape, \
             patch.object(self.intelligence, 'gather_enhanced_osint') as mock_osint, \
             patch.object(self.intelligence, 'perform_ai_analysis') as mock_ai:
            
            # Setup mocks
            mock_scrape.return_value = {
                'username': 'integration_test',
                'follower_count': 5000,
                'verified': True,
                'bio': 'Integration test profile'
            }
            
            mock_osint.return_value = {
                'email_intelligence': {'breaches': {'total_breaches': 0}},
                'comprehensive_report': {'overall_risk': 'low'}
            }
            
            mock_ai.return_value = {
                'authenticity_assessment': {'overall_authenticity': 0.9},
                'bio_analysis': {'sentiment': {'sentiment': 'positive'}}
            }
            
            # Run full analysis
            result = await self.intelligence.analyze_profile(
                'instagram',
                'integration_test',
                use_real_scraping=False,
                include_osint=True,
                include_ai_analysis=True
            )
            
            # Verify complete result
            self.assertIsInstance(result, dict)
            self.assertIn('profile_data', result)
            self.assertIn('osint_data', result)
            self.assertIn('ai_analysis', result)
            self.assertIn('risk_score', result)
            
            # Verify risk score calculation
            risk_score = result.get('risk_score', 0)
            self.assertGreaterEqual(risk_score, 0.0)
            self.assertLessEqual(risk_score, 1.0)
        
        print("✅ Full workflow integration test passed")

def run_performance_tests():
    """Run performance benchmarks"""
    print("\n🚀 PERFORMANCE TESTS")
    print("=" * 40)
    
    if not ENHANCED_MODULES_AVAILABLE:
        print("❌ Enhanced modules not available for performance testing")
        return
    
    intelligence = EnhancedSocialMediaIntelligence()
    
    # Test initialization time
    start_time = time.time()
    intelligence.start_social_media_system()
    init_time = time.time() - start_time
    print(f"⏱️  System initialization: {init_time:.2f}s")
    
    # Test analysis performance
    start_time = time.time()
    asyncio.run(intelligence.analyze_profile('instagram', 'test_user', False, False, False))
    analysis_time = time.time() - start_time
    print(f"⏱️  Basic analysis: {analysis_time:.2f}s")
    
    intelligence.stop_social_media_system()
    print("✅ Performance tests completed")

def main():
    """Run all tests"""
    print("🧪 ENHANCED SOCIAL MEDIA INTELLIGENCE TEST SUITE")
    print("=" * 60)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance tests
    run_performance_tests()
    
    print("\n✅ All tests completed!")

if __name__ == '__main__':
    main()
