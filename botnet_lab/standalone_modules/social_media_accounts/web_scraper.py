#!/usr/bin/env python3
"""
Real Web Scraping Module for Social Media Intelligence
Uses Selenium and undetected-chromedriver for realistic scraping
"""

import time
import json
import random
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse

try:
    import undetected_chromedriver as uc
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.common.action_chains import ActionChains
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

try:
    from fake_useragent import UserAgent
    FAKE_USERAGENT_AVAILABLE = True
except ImportError:
    FAKE_USERAGENT_AVAILABLE = False

from config import SELENIUM_CONFIG, PLATFORM_CONFIGS

class RealWebScraper:
    """Real web scraper using Selenium for social media platforms"""
    
    def __init__(self):
        self.driver = None
        self.logger = logging.getLogger(__name__)
        self.session_data = {}
        self.rate_limits = {}
        
        if not SELENIUM_AVAILABLE:
            raise ImportError("Selenium and undetected-chromedriver are required")
    
    def setup_driver(self, headless: bool = True, proxy: str = None) -> bool:
        """Setup Chrome driver with stealth options"""
        try:
            options = uc.ChromeOptions()
            
            # Stealth options
            if headless:
                options.add_argument('--headless')
            
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # User agent
            if FAKE_USERAGENT_AVAILABLE:
                ua = UserAgent()
                options.add_argument(f'--user-agent={ua.random}')
            else:
                options.add_argument(f'--user-agent={SELENIUM_CONFIG["user_agent"]}')
            
            # Window size
            options.add_argument(f'--window-size={SELENIUM_CONFIG["window_size"][0]},{SELENIUM_CONFIG["window_size"][1]}')
            
            # Proxy if provided
            if proxy:
                options.add_argument(f'--proxy-server={proxy}')
            
            # Download preferences
            prefs = {
                "download.default_directory": SELENIUM_CONFIG["download_dir"],
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True
            }
            options.add_experimental_option("prefs", prefs)
            
            # Create driver
            self.driver = uc.Chrome(options=options)
            self.driver.set_page_load_timeout(SELENIUM_CONFIG["page_load_timeout"])
            self.driver.implicitly_wait(SELENIUM_CONFIG["implicit_wait"])
            
            # Execute script to hide automation
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.logger.info("Chrome driver setup successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Driver setup failed: {e}")
            return False
    
    def human_like_delay(self, min_delay: float = 1.0, max_delay: float = 3.0):
        """Add human-like delay between actions"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    def scroll_page(self, scrolls: int = 3, delay: float = 2.0):
        """Scroll page to load dynamic content"""
        for i in range(scrolls):
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(delay)
            
        # Scroll back to top
        self.driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(1)
    
    def wait_for_element(self, selector: str, timeout: int = 10, by: By = By.CSS_SELECTOR):
        """Wait for element to be present"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, selector))
            )
            return element
        except TimeoutException:
            return None
    
    def safe_find_element(self, selector: str, by: By = By.CSS_SELECTOR):
        """Safely find element without throwing exception"""
        try:
            return self.driver.find_element(by, selector)
        except NoSuchElementException:
            return None
    
    def safe_get_text(self, element) -> str:
        """Safely get text from element"""
        try:
            return element.text.strip() if element else ""
        except:
            return ""
    
    def safe_get_attribute(self, element, attribute: str) -> str:
        """Safely get attribute from element"""
        try:
            return element.get_attribute(attribute) if element else ""
        except:
            return ""
    
    def extract_number_from_text(self, text: str) -> int:
        """Extract number from text (e.g., '1.2K followers' -> 1200)"""
        if not text:
            return 0
            
        text = text.lower().replace(',', '').replace(' ', '')
        
        # Handle K, M, B suffixes
        multipliers = {'k': 1000, 'm': 1000000, 'b': 1000000000}
        
        for suffix, multiplier in multipliers.items():
            if suffix in text:
                try:
                    number = float(text.replace(suffix, ''))
                    return int(number * multiplier)
                except:
                    pass
        
        # Extract plain number
        import re
        numbers = re.findall(r'\d+', text)
        if numbers:
            try:
                return int(numbers[0])
            except:
                pass
        
        return 0
    
    def scrape_facebook_profile(self, username: str) -> Dict[str, Any]:
        """Scrape Facebook profile data"""
        try:
            config = PLATFORM_CONFIGS['facebook']
            profile_url = f"{config['base_url']}/{username}"
            
            self.logger.info(f"Scraping Facebook profile: {username}")
            self.driver.get(profile_url)
            self.human_like_delay(3, 5)
            
            # Scroll to load content
            self.scroll_page(2, 2)
            
            profile_data = {
                'username': username,
                'platform': 'facebook',
                'url': profile_url,
                'scraped_at': datetime.now().isoformat()
            }
            
            # Profile name
            name_element = self.safe_find_element('h1')
            profile_data['display_name'] = self.safe_get_text(name_element)
            
            # Profile picture
            img_element = self.safe_find_element('image')
            profile_data['profile_picture'] = self.safe_get_attribute(img_element, 'src')
            
            # Bio/About
            bio_element = self.safe_find_element('[data-overviewsection="about"]')
            profile_data['bio'] = self.safe_get_text(bio_element)
            
            # Friends count (Facebook doesn't show exact follower count publicly)
            friends_element = self.safe_find_element('[data-overviewsection="friends"]')
            friends_text = self.safe_get_text(friends_element)
            profile_data['friends_count'] = self.extract_number_from_text(friends_text)
            
            # Posts (recent posts visible)
            posts_elements = self.driver.find_elements(By.CSS_SELECTOR, '[role="article"]')
            profile_data['visible_posts_count'] = len(posts_elements)
            
            # Check if profile is verified (blue checkmark)
            verified_element = self.safe_find_element('[aria-label*="Verified"]')
            profile_data['verified'] = verified_element is not None
            
            # Location (if visible)
            location_element = self.safe_find_element('[data-overviewsection="places"]')
            profile_data['location'] = self.safe_get_text(location_element)
            
            # Work/Education (if visible)
            work_element = self.safe_find_element('[data-overviewsection="work"]')
            profile_data['work'] = self.safe_get_text(work_element)
            
            self.logger.info(f"Successfully scraped Facebook profile: {username}")
            return profile_data
            
        except Exception as e:
            self.logger.error(f"Facebook scraping error for {username}: {e}")
            return {'error': str(e), 'username': username, 'platform': 'facebook'}
    
    def scrape_instagram_profile(self, username: str) -> Dict[str, Any]:
        """Scrape Instagram profile data"""
        try:
            config = PLATFORM_CONFIGS['instagram']
            profile_url = f"{config['base_url']}/{username}/"
            
            self.logger.info(f"Scraping Instagram profile: {username}")
            self.driver.get(profile_url)
            self.human_like_delay(3, 5)
            
            profile_data = {
                'username': username,
                'platform': 'instagram',
                'url': profile_url,
                'scraped_at': datetime.now().isoformat()
            }
            
            # Profile name
            name_element = self.safe_find_element('h2')
            profile_data['display_name'] = self.safe_get_text(name_element)
            
            # Profile stats (posts, followers, following)
            stats_elements = self.driver.find_elements(By.CSS_SELECTOR, 'ul li')
            if len(stats_elements) >= 3:
                profile_data['posts_count'] = self.extract_number_from_text(self.safe_get_text(stats_elements[0]))
                profile_data['followers_count'] = self.extract_number_from_text(self.safe_get_text(stats_elements[1]))
                profile_data['following_count'] = self.extract_number_from_text(self.safe_get_text(stats_elements[2]))
            
            # Bio
            bio_element = self.safe_find_element('div.-vDIg span')
            profile_data['bio'] = self.safe_get_text(bio_element)
            
            # Profile picture
            img_element = self.safe_find_element('img[alt*="profile picture"]')
            profile_data['profile_picture'] = self.safe_get_attribute(img_element, 'src')
            
            # Verified status
            verified_element = self.safe_find_element('[title="Verified"]')
            profile_data['verified'] = verified_element is not None
            
            # Private account check
            private_element = self.safe_find_element('h2:contains("This Account is Private")')
            profile_data['private_account'] = private_element is not None
            
            # Website link
            website_element = self.safe_find_element('a[href*="http"]')
            profile_data['website'] = self.safe_get_attribute(website_element, 'href')
            
            self.logger.info(f"Successfully scraped Instagram profile: {username}")
            return profile_data
            
        except Exception as e:
            self.logger.error(f"Instagram scraping error for {username}: {e}")
            return {'error': str(e), 'username': username, 'platform': 'instagram'}
    
    def scrape_twitter_profile(self, username: str) -> Dict[str, Any]:
        """Scrape Twitter/X profile data"""
        try:
            config = PLATFORM_CONFIGS['twitter']
            profile_url = f"{config['base_url']}/{username}"
            
            self.logger.info(f"Scraping Twitter profile: {username}")
            self.driver.get(profile_url)
            self.human_like_delay(3, 5)
            
            profile_data = {
                'username': username,
                'platform': 'twitter',
                'url': profile_url,
                'scraped_at': datetime.now().isoformat()
            }
            
            # Profile name
            name_element = self.safe_find_element('[data-testid="UserName"]')
            profile_data['display_name'] = self.safe_get_text(name_element)
            
            # Bio
            bio_element = self.safe_find_element('[data-testid="UserDescription"]')
            profile_data['bio'] = self.safe_get_text(bio_element)
            
            # Followers and following
            followers_element = self.safe_find_element('[data-testid="UserFollowers-stat"]')
            following_element = self.safe_find_element('[data-testid="UserFollowing-stat"]')
            
            profile_data['followers_count'] = self.extract_number_from_text(self.safe_get_text(followers_element))
            profile_data['following_count'] = self.extract_number_from_text(self.safe_get_text(following_element))
            
            # Verified status
            verified_element = self.safe_find_element('[data-testid="icon-verified"]')
            profile_data['verified'] = verified_element is not None
            
            # Location
            location_element = self.safe_find_element('[data-testid="UserLocation"]')
            profile_data['location'] = self.safe_get_text(location_element)
            
            # Website
            website_element = self.safe_find_element('[data-testid="UserUrl"]')
            profile_data['website'] = self.safe_get_text(website_element)
            
            # Join date
            join_element = self.safe_find_element('[data-testid="UserJoinDate"]')
            profile_data['join_date'] = self.safe_get_text(join_element)
            
            # Recent tweets count
            tweets_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-testid="tweet"]')
            profile_data['visible_tweets_count'] = len(tweets_elements)
            
            self.logger.info(f"Successfully scraped Twitter profile: {username}")
            return profile_data
            
        except Exception as e:
            self.logger.error(f"Twitter scraping error for {username}: {e}")
            return {'error': str(e), 'username': username, 'platform': 'twitter'}
    
    def scrape_linkedin_profile(self, username: str) -> Dict[str, Any]:
        """Scrape LinkedIn profile data"""
        try:
            config = PLATFORM_CONFIGS['linkedin']
            profile_url = f"{config['base_url']}/in/{username}"
            
            self.logger.info(f"Scraping LinkedIn profile: {username}")
            self.driver.get(profile_url)
            self.human_like_delay(3, 5)
            
            profile_data = {
                'username': username,
                'platform': 'linkedin',
                'url': profile_url,
                'scraped_at': datetime.now().isoformat()
            }
            
            # Profile name
            name_element = self.safe_find_element('h1')
            profile_data['display_name'] = self.safe_get_text(name_element)
            
            # Job title
            title_element = self.safe_find_element('.text-body-medium')
            profile_data['job_title'] = self.safe_get_text(title_element)
            
            # Location
            location_element = self.safe_find_element('.text-body-small.inline')
            profile_data['location'] = self.safe_get_text(location_element)
            
            # Connections count
            connections_element = self.safe_find_element('.pv-top-card--list-bullet li')
            profile_data['connections_count'] = self.extract_number_from_text(self.safe_get_text(connections_element))
            
            # About section
            about_element = self.safe_find_element('.pv-about__summary-text')
            profile_data['about'] = self.safe_get_text(about_element)
            
            # Experience (first job)
            experience_element = self.safe_find_element('.pv-entity__summary-info h3')
            profile_data['current_position'] = self.safe_get_text(experience_element)
            
            self.logger.info(f"Successfully scraped LinkedIn profile: {username}")
            return profile_data
            
        except Exception as e:
            self.logger.error(f"LinkedIn scraping error for {username}: {e}")
            return {'error': str(e), 'username': username, 'platform': 'linkedin'}
    
    def scrape_profile(self, platform: str, username: str) -> Dict[str, Any]:
        """Main method to scrape profile from any platform"""
        if not self.driver:
            if not self.setup_driver():
                return {'error': 'Failed to setup driver'}
        
        # Rate limiting
        if platform in self.rate_limits:
            time_since_last = time.time() - self.rate_limits[platform]
            required_delay = PLATFORM_CONFIGS[platform]['rate_limit']
            if time_since_last < required_delay:
                time.sleep(required_delay - time_since_last)
        
        self.rate_limits[platform] = time.time()
        
        # Route to appropriate scraper
        scrapers = {
            'facebook': self.scrape_facebook_profile,
            'instagram': self.scrape_instagram_profile,
            'twitter': self.scrape_twitter_profile,
            'linkedin': self.scrape_linkedin_profile
        }
        
        if platform not in scrapers:
            return {'error': f'Platform {platform} not supported'}
        
        try:
            return scrapers[platform](username)
        except Exception as e:
            self.logger.error(f"Scraping error for {platform}/{username}: {e}")
            return {'error': str(e), 'platform': platform, 'username': username}
    
    def close(self):
        """Close the driver"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            self.logger.info("Driver closed")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
