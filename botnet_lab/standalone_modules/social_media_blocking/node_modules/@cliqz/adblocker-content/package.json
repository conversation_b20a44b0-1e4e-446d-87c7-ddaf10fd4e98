{"name": "@cliqz/adblocker-content", "version": "1.34.0", "description": "Ghostery adblocker library (content-scripts helpers)", "author": {"name": "Ghostery"}, "homepage": "https://github.com/ghostery/adblocker#readme", "license": "MPL-2.0", "type": "module", "tshy": {"project": "./tsconfig.json", "exports": {"./package.json": "./package.json", ".": "./src/index.js"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "files": ["LICENSE", "dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+ssh://**************/ghostery/adblocker.git", "directory": "packages/adblocker-content"}, "scripts": {"clean": "rimraf dist .tshy .tshy-build .rollup.cache", "lint": "eslint src", "build": "tshy && rollup --config ./rollup.config.js"}, "bugs": {"url": "https://github.com/ghostery/adblocker/issues"}, "dependencies": {"@cliqz/adblocker-extended-selectors": "^1.34.0"}, "devDependencies": {"@rollup/plugin-terser": "^0.4.4", "@types/node": "^22.0.2", "eslint": "^9.3.0", "rimraf": "^6.0.1", "rollup": "^4.0.2", "tshy": "^3.0.2", "typescript": "^5.5.2"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "zhong<PERSON>@cliqz.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Eleni", "email": "<EMAIL>"}, {"name": "ecnmst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "a68f58dfe72eb92ebf8ab1836608aa752f0d13e9"}