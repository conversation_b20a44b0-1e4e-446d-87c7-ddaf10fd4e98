{"name": "@cliqz/adblocker-puppeteer", "version": "1.23.8", "description": "Cliqz adblocker Puppeteer wrapper", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "homepage": "https://github.com/cliqz-oss/adblocker#readme", "license": "MPL-2.0", "main": "dist/cjs/adblocker.js", "module": "dist/es6/adblocker.js", "types": "dist/types/adblocker.d.ts", "files": ["LICENSE", "dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+ssh://**************/cliqz-oss/adblocker.git", "directory": "packages/adblocker-puppeteer"}, "scripts": {"clean": "rimraf dist coverage", "lint": "tslint --config ../../tslint.json --project ./tsconfig.json", "build": "tsc --build ./tsconfig.json", "bundle": "tsc --build ./tsconfig.bundle.json", "prepack": "yarn run bundle", "test": "nyc mocha --config ../../.mocharc.js"}, "bugs": {"url": "https://github.com/cliqz-oss/adblocker/issues"}, "peerDependencies": {"puppeteer": ">5"}, "dependencies": {"@cliqz/adblocker": "^1.23.8", "@cliqz/adblocker-content": "^1.23.8", "tldts-experimental": "^5.6.21"}, "devDependencies": {"@types/chai": "^4.2.18", "@types/mocha": "^9.0.0", "chai": "^4.2.0", "mocha": "^9.0.0", "nyc": "^15.0.0", "puppeteer": "14.1.0", "rimraf": "^3.0.0", "ts-node": "^10.0.0", "tslint": "^6.0.0", "tslint-config-prettier": "^1.18.0", "tslint-no-unused-expression-chai": "^0.1.4", "typescript": "^4.4.3"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "zhong<PERSON>@cliqz.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Eleni", "email": "<EMAIL>"}, {"name": "ecnmst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "f73036232c94b8579edf8d991fabe233abe373a7"}