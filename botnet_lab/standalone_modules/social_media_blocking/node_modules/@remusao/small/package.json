{"name": "@remusao/small", "version": "1.3.0", "description": "Smalles files for different MIME types", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/remusao/mono#readme", "license": "MPL-2.0", "type": "module", "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.js"}}, "files": ["dist"], "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/remusao/mono.git", "directory": "packages/small"}, "scripts": {"clean": "rimraf dist coverage", "lint": "tslint --config ../../tslint.json --project ./tsconfig.json", "build": "tshy", "test": "nyc mocha"}, "bugs": {"url": "https://github.com/remusao/mono/issues"}, "devDependencies": {"@types/chai": "^4.2.8", "@types/mocha": "^10.0.7", "@types/node": "^20.14.10", "chai": "^4.2.0", "mocha": "^10.6.0", "nyc": "^15.0.0", "rimraf": "^3.0.0", "tshy": "^3.0.2", "tslint": "^6.0.0", "tslint-config-prettier": "^1.18.0", "typescript": "5.5.2"}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "gitHead": "275e1e6498e21d8fd0bda4299577bcd2af28a7ee"}