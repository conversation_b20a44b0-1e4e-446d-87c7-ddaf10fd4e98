{"name": "@types/filewriter", "version": "0.0.33", "description": "TypeScript definitions for filewriter", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/filewriter", "license": "MIT", "contributors": [{"name": "Kon", "url": "http://phyzkit.net/"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/filewriter"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "ca0960debc53ce698593f3cd6015ab0ac96d87e8cf6646fda5b70b1fe1434b12", "typeScriptVersion": "4.6", "nonNpm": true}