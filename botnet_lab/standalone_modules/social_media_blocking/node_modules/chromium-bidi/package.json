{"name": "chromium-bidi", "version": "0.5.8", "description": "An implementation of the WebDriver BiDi protocol for Chromium implemented as a JavaScript layer translating between BiDi and CDP, running inside a Chrome tab.", "scripts": {"bidi-types": "node tools/generate-bidi-types.mjs", "build": "wireit", "clean": "rimraf lib .eslintcache .wireit", "e2e-headful": "wireit", "e2e-headless": "wireit", "e2e": "npm run e2e-headless --", "flake8": "flake8 examples/ tests/", "format": "npm run pre-commit --", "format:eslint": "eslint --ext js --ext mjs --ext ts --fix .", "format:prettier": "prettier --write .", "pre-commit": "pre-commit run --hook-stage manual --all-files", "prepare": "wireit", "rollup": "wireit", "server": "npm run server-headless --", "server-headful": "wireit", "server-headless": "wireit", "test": "wireit", "tsc": "wireit", "unit": "wireit", "wpt": "wireit", "wpt-all": "wireit", "yapf": "yapf -i --parallel --recursive --exclude=wpt examples/ tests/"}, "nyc": {"exclude": ["**/*.spec.ts"]}, "wireit": {"build": {"dependencies": ["rollup", "tsc"]}, "e2e-headful": {"command": "pipenv run python -m pytest --ignore=tests/input", "dependencies": ["server-headful"], "files": ["pytest.ini", "tests/**/*.py"]}, "e2e-headless": {"command": "pipenv run python -m pytest", "dependencies": ["server-headless"], "files": ["pytest.ini", "tests/**/*.py"]}, "prepare": {"dependencies": ["build"]}, "rollup": {"command": "rollup -c", "dependencies": ["tsc"], "files": ["lib/cjs/bidiMapper/index.js", "rollup.config.mjs"], "output": ["lib/iife/mapperTab.js"]}, "server-headful": {"command": "npm run server-headless -- --headless=false", "service": {"readyWhen": {"lineMatches": "BiDi server is listening on port \\d+"}}, "dependencies": ["rollup"]}, "server-headless": {"command": "tools/run-bidi-server.mjs --headless=true", "files": ["tools/run-bidi-server.mjs"], "service": {"readyWhen": {"lineMatches": "BiDi server is listening on port \\d+"}}, "dependencies": ["rollup"]}, "test": {"dependencies": ["unit", "e2e", "wpt"]}, "tsc": {"command": "tsc --build src/tsconfig.json --pretty", "clean": "if-file-deleted", "files": ["**/tsconfig*.json", "src/**/*.ts"], "output": ["lib/cjs/**"]}, "unit": {"command": "mocha", "dependencies": ["tsc"]}, "wpt": {"command": "tools/run-wpt.mjs", "files": ["tools/run-wpt.mjs", "wpt/tools/webdriver/**/*.py", "wpt/webdriver/tests/**/*.py", "wpt-metadata/**/*.ini"], "dependencies": ["rollup"]}, "wpt-all": {"command": "tools/run-wpt-all.mjs", "files": ["tools/run-wpt.mjs", "tools/run-wpt-all.mjs", "wpt/tools/webdriver/**/*.py", "wpt/webdriver/tests/**/*.py", "wpt-metadata/**/*.ini"], "dependencies": ["rollup"]}}, "files": ["lib", "!lib/**/*.spec.*", "!*.tsbuil<PERSON><PERSON>", ".browser"], "repository": {"type": "git", "url": "https://github.com/GoogleChromeLabs/chromium-bidi.git"}, "author": "The Chromium Authors", "license": "Apache-2.0", "peerDependencies": {"devtools-protocol": "*"}, "devDependencies": {"@actions/core": "1.10.1", "@puppeteer/browsers": "1.9.1", "@rollup/plugin-commonjs": "25.0.7", "@rollup/plugin-node-resolve": "15.2.3", "@rollup/plugin-terser": "0.4.4", "@rollup/wasm-node": "4.9.6", "@types/argparse": "2.0.14", "@types/chai": "4.3.11", "@types/chai-as-promised": "7.1.8", "@types/debug": "4.1.12", "@types/mocha": "10.0.6", "@types/node": "20.11.6", "@types/sinon": "17.0.3", "@types/websocket": "1.0.10", "@types/ws": "8.5.10", "@types/yargs": "17.0.32", "@typescript-eslint/eslint-plugin": "6.19.1", "@typescript-eslint/parser": "6.19.1", "argparse": "2.0.1", "chai": "4.4.1", "chai-as-promised": "7.1.1", "debug": "4.3.4", "devtools-protocol": "0.0.1245094", "eslint": "8.56.0", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-import": "2.29.1", "eslint-plugin-mocha": "10.2.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-promise": "6.1.1", "gts": "5.2.0", "mocha": "10.2.0", "nyc": "15.1.0", "pkg-dir": "8.0.0", "prettier": "3.2.4", "rimraf": "5.0.5", "rollup": "4.9.6", "selenium-webdriver": "4.17.0", "sinon": "17.0.1", "source-map-support": "0.5.21", "terser": "5.27.0", "tslib": "2.6.2", "typescript": "5.3.3", "webdriverio": "8.29.1", "websocket": "1.0.34", "wireit": "0.14.3", "ws": "8.16.0", "yargs": "17.7.2", "zod": "3.22.4"}, "dependencies": {"mitt": "3.0.1", "urlpattern-polyfill": "10.0.0"}}