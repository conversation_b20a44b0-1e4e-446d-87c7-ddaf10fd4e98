{"name": "cross-fetch", "version": "4.0.0", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "homepage": "https://github.com/lquixada/cross-fetch", "main": "dist/node-ponyfill.js", "browser": "dist/browser-ponyfill.js", "react-native": "dist/react-native-ponyfill.js", "types": "index.d.ts", "scripts": {"commit": "cz", "prepare": "husky install", "prepublishOnly": "rimraf dist && make dist"}, "lint-staged": {"*.js": ["standard --fix"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "standard": {"env": ["browser", "mocha", "serviceworker"], "globals": ["expect", "assert", "chai", "<PERSON><PERSON>"], "ignore": ["/dist/", "api.spec.js", "bundle.js", "test.js", "*.bundle.js", "*.ts"]}, "mocha": {"require": ["chai/register-expect.js", "chai/register-assert.js"], "check-leaks": true}, "nyc": {"temp-dir": ".reports/.coverage"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"type": "git", "url": "https://github.com/lquixada/cross-fetch.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dependencies": {"node-fetch": "^2.6.12"}, "devDependencies": {"@commitlint/cli": "17.6.6", "@commitlint/config-conventional": "17.6.6", "@rollup/plugin-terser": "0.4.3", "@types/chai": "4.3.5", "@types/mocha": "10.0.1", "@types/node": "18.15.13", "body-parser": "1.20.2", "chai": "4.3.7", "codecov": "3.8.3", "commitizen": "4.3.0", "cz-conventional-changelog": "3.3.0", "express": "4.18.2", "husky": "8.0.3", "lint-staged": "13.2.3", "mocha": "10.2.0", "mocha-headless-chrome": "4.0.0", "nock": "13.3.1", "nyc": "15.1.0", "rimraf": "5.0.1", "rollup": "3.26.0", "rollup-plugin-copy": "3.4.0", "semver": "7.5.3", "serve-index": "1.9.1", "standard": "17.1.0", "standard-version": "9.5.0", "typescript": "5.1.6", "webpack": "5.88.1", "webpack-cli": "5.1.4", "whatwg-fetch": "3.6.2", "yargs": "17.7.2"}, "files": ["dist", "polyfill", "index.d.ts"], "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"]}