{"version": 3, "file": "Browser.d.ts", "sourceRoot": "", "sources": ["../../../../src/api/Browser.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,eAAe,CAAC;AAEhD,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAShD,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EAAC,YAAY,EAAE,KAAK,SAAS,EAAC,MAAM,2BAA2B,CAAC;AAEvE,OAAO,EAAC,kBAAkB,EAAE,aAAa,EAAC,MAAM,uBAAuB,CAAC;AAExE,OAAO,KAAK,EAAC,cAAc,EAAC,MAAM,qBAAqB,CAAC;AACxD,OAAO,KAAK,EAAC,IAAI,EAAC,MAAM,WAAW,CAAC;AACpC,OAAO,KAAK,EAAC,MAAM,EAAC,MAAM,aAAa,CAAC;AACxC;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACpC;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;CAC5B;AAED;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAE9D;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK,OAAO,CAAC;AAE/D;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK,OAAO,CAAC;AAE/D;;GAEG;AACH,eAAO,MAAM,qCAAqC,kDAyBhD,CAAC;AAEH;;GAEG;AACH,MAAM,MAAM,UAAU,GAClB,aAAa,GACb,MAAM,GACN,eAAe,GACf,QAAQ,GACR,YAAY,GACZ,iBAAiB,GACjB,sBAAsB,GACtB,eAAe,GACf,WAAW,GACX,cAAc,GACd,sBAAsB,GACtB,gBAAgB,GAChB,iBAAiB,GACjB,2BAA2B,GAC3B,iBAAiB,GACjB,oBAAoB,GACpB,gBAAgB,GAChB,YAAY,CAAC;AAEjB;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACnC;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED;;;;GAIG;AACH,0BAAkB,YAAY;IAC5B;;;;;;OAMG;IACH,YAAY,iBAAiB;IAC7B;;;;;;OAMG;IACH,aAAa,kBAAkB;IAC/B;;;;;;;;;OASG;IACH,aAAa,kBAAkB;IAC/B;;;;;;OAMG;IACH,eAAe,oBAAoB;IACnC;;OAEG;IACH,gBAAgB,qBAAqB;CACtC;AAED,OAAO;AACL;;GAEG;AACH,YAAY,IAAI,oBAAoB,GACrC,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,aAAc,SAAQ,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC;IAC/D,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,SAAS,CAAC;IACvC,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC;IACrC,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE,MAAM,CAAC;IACvC,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC;IACrC;;OAEG;IACH,CAAC,YAAY,CAAC,gBAAgB,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;CAC7D;AAED;;;GAGG;AACH,MAAM,WAAW,SAAS;IACxB,qBAAqB,EAAE,KAAK,EAAE,CAAC;CAChC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsCG;AACH,8BAAsB,OAAQ,SAAQ,YAAY,CAAC,aAAa,CAAC;IAC/D;;OAEG;;IAKH;;;;;;OAMG;IACH,QAAQ,CAAC,OAAO,IAAI,YAAY,GAAG,IAAI;IAEvC;;;;;;;;;;;;;;;;;;OAkBG;IACH,QAAQ,CAAC,6BAA6B,CACpC,OAAO,CAAC,EAAE,qBAAqB,GAC9B,OAAO,CAAC,cAAc,CAAC;IAE1B;;;;;OAKG;IACH,QAAQ,CAAC,eAAe,IAAI,cAAc,EAAE;IAE5C;;;;;OAKG;IACH,QAAQ,CAAC,qBAAqB,IAAI,cAAc;IAEhD;;;;;;;;;;;;;OAaG;IACH,QAAQ,CAAC,UAAU,IAAI,MAAM;IAE7B;;;OAGG;IACH,QAAQ,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAEjC;;;;;;OAMG;IACH,QAAQ,CAAC,OAAO,IAAI,MAAM,EAAE;IAE5B;;;OAGG;IACH,QAAQ,CAAC,MAAM,IAAI,MAAM;IAEzB;;;;;;;;;;;;;;OAcG;IACG,aAAa,CACjB,SAAS,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,EACpD,OAAO,GAAE,oBAAyB,GACjC,OAAO,CAAC,MAAM,CAAC;IAWlB;;;;;;;;;OASG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;IAY9B;;;;;;;;;;OAUG;IACH,QAAQ,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;IAEnC;;;;;;OAMG;IACH,QAAQ,CAAC,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;IAErC;;;OAGG;IACH,QAAQ,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAE/B;;;OAGG;IACH,QAAQ,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAEpC;;;;OAIG;IACH,WAAW,IAAI,OAAO;IAItB;;OAEG;IACH,QAAQ,KAAK,SAAS,IAAI,OAAO,CAAC;IAElC,gBAAgB;IAChB,CAAC,aAAa,CAAC,IAAI,IAAI;IAIvB,gBAAgB;IAChB,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC;IAIrC;;OAEG;IACH,QAAQ,KAAK,QAAQ,IAAI,YAAY,CAAC;IAEtC;;;;;;;;;OASG;IACH,QAAQ,KAAK,SAAS,IAAI,SAAS,CAAC;CACrC"}