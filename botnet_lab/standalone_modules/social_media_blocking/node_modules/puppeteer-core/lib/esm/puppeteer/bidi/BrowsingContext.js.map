{"version": 3, "file": "BrowsingContext.js", "sourceRoot": "", "sources": ["../../../../src/bidi/BrowsingContext.ts"], "names": [], "mappings": "AAGA,OAAO,EAAC,UAAU,EAAC,MAAM,sBAAsB,CAAC;AAEhD,OAAO,EAAC,gBAAgB,EAAE,oBAAoB,EAAC,MAAM,qBAAqB,CAAC;AAE3E,OAAO,EAAC,UAAU,EAAC,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAC;AAG7C,OAAO,EAAC,SAAS,EAAC,MAAM,YAAY,CAAC;AAErC;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,IAAI,GAAG,EAA6B,CAAC;AAEhE;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,UAAU;IAC/C,QAAQ,CAAkB;IAC1B,UAAU,GAAG,QAAQ,CAAC,MAAM,EAAU,CAAC;IACvC,SAAS,GAAG,KAAK,CAAC;IAElB,YAAY,OAAwB,EAAE,SAAkB;QACtD,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;YACjC,OAAO;QACT,CAAC;QACD,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACnC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,UAAU;iBACf,IAAI,CAAC,gBAAgB,EAAE;gBACtB,OAAO,EAAE,OAAO,CAAC,EAAE;aACpB,CAAC;iBACD,IAAI,CAAC,OAAO,CAAC,EAAE;gBACd,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,OAAQ,CAAC,CAAC;gBACjD,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,OAAQ,EAAE,IAAI,CAAC,CAAC;YACjD,CAAC,CAAC;iBACD,KAAK,CAAC,GAAG,CAAC,EAAE;gBACX,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;QACP,CAAC;IACH,CAAC;IAEQ,UAAU;QACjB,OAAO,SAAS,CAAC;IACnB,CAAC;IAEQ,KAAK,CAAC,IAAI,CACjB,MAAS,EACT,GAAG,SAAoD;QAEvD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;YACjC,MAAM,IAAI,oBAAoB,CAC5B,qFAAqF,CACtF,CAAC;QACJ,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,gBAAgB,CACxB,mBAAmB,MAAM,0DAA0D,CACpF,CAAC;QACJ,CAAC;QACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;QACrD,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACtE,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;YACpB,OAAO;SACR,CAAC,CAAC;QACH,OAAO,MAAM,CAAC,MAAM,CAAC;IACvB,CAAC;IAEQ,KAAK,CAAC,MAAM;QACnB,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;YACnD,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBAC7D,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;aACrB,CAAC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACxB,CAAC;IAEQ,EAAE;QACT,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACpC,OAAO,GAAG,YAAY,KAAK,IAAI,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IAC9D,CAAC;CACF;AAED;;;;GAIG;AACH,2DAA2D;AAC3D,MAAM,KAAW,oBAAoB,CAUpC;AAVD,WAAiB,oBAAoB;IACnC;;OAEG;IACU,4BAAO,GAAG,MAAM,CAAC,yBAAyB,CAAC,CAAC;IACzD;;;OAGG;IACU,8BAAS,GAAG,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAC/D,CAAC,EAVgB,oBAAoB,KAApB,oBAAoB,QAUpC;AAUD;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,SAAS;IAC5C,GAAG,CAAS;IACZ,IAAI,CAAS;IACb,WAAW,CAAa;IACxB,OAAO,CAAiB;IACxB,YAAY,GAAG,EAAE,CAAC;IAElB,YACE,UAA0B,EAC1B,IAA+B,EAC/B,WAAmB;QAEnB,KAAK,CAAC,UAAU,CAAC,CAAC;QAClB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,WAAW,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAE1D,IAAI,CAAC,EAAE,CAAC,kCAAkC,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACxE,IAAI,CAAC,EAAE,CAAC,mCAAmC,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,EAAE,CAAC,sBAAsB,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED,WAAW;QACT,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC9D,CAAC;IAED,UAAU,CAAC,IAAyC;QAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC;IACvB,CAAC;IAED,qBAAqB;QACnB,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IAED,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,MAAS,EACT,GAAG,SAAoD;QAEvD,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC;IAC3D,CAAC;IAED,OAAO;QACL,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,0BAA0B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrD,KAAK,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACnD,CAAC;CACF"}