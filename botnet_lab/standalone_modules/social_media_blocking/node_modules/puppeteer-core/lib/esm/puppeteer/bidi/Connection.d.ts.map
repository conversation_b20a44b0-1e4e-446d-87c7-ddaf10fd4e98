{"version": 3, "file": "Connection.d.ts", "sourceRoot": "", "sources": ["../../../../src/bidi/Connection.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,KAAK,IAAI,MAAM,4CAA4C,CAAC;AAGxE,OAAO,KAAK,EAAC,mBAAmB,EAAC,MAAM,kCAAkC,CAAC;AAE1E,OAAO,KAAK,EAAC,kBAAkB,EAAC,MAAM,2BAA2B,CAAC;AAClE,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AAIvD,OAAO,EAAc,KAAK,eAAe,EAAC,MAAM,sBAAsB,CAAC;AACvE,OAAO,KAAK,EACV,UAAU,EACV,QAAQ,IAAI,YAAY,EACxB,UAAU,EACX,MAAM,sBAAsB,CAAC;AAK9B;;GAEG;AACH,MAAM,WAAW,QAAS,SAAQ,YAAY;IAC5C,iBAAiB,EAAE;QACjB,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC;QACvC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC;KACxC,CAAC;IACF,gBAAgB,EAAE;QAChB,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC;QACtC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;KACvC,CAAC;CACH;AAED;;GAEG;AACH,qBAAa,cACX,SAAQ,YAAY,CAAC,UAAU,CAC/B,YAAW,UAAU;;gBAYnB,GAAG,EAAE,MAAM,EACX,SAAS,EAAE,mBAAmB,EAC9B,KAAK,SAAI,EACT,OAAO,CAAC,EAAE,MAAM;IAYlB,IAAI,MAAM,IAAI,OAAO,CAEpB;IAED,IAAI,GAAG,IAAI,MAAM,CAEhB;IAED,MAAM,CAAC,MAAM,SAAS,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI;IAI7D,IAAI,CAAC,GAAG,SAAS,MAAM,kBAAkB,CAAC,UAAU,CAAC,EAC5D,IAAI,EAAE,GAAG,EACT,KAAK,EAAE,kBAAkB,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,GACzC,OAAO;IAOV,IAAI,CAAC,CAAC,SAAS,MAAM,QAAQ,EAC3B,MAAM,EAAE,CAAC,EACT,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAC5B,OAAO,CAAC;QAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAA;KAAC,CAAC;IAc/C;;OAEG;cACa,SAAS,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAkEzD,wBAAwB,CAAC,OAAO,EAAE,eAAe,GAAG,IAAI;IAIxD,kBAAkB,CAAC,SAAS,EAAE,MAAM,GAAG,eAAe;IAQtD,kBAAkB,CAAC,SAAS,EAAE,MAAM,GAAG,eAAe;IAetD,0BAA0B,CAAC,EAAE,EAAE,MAAM,GAAG,IAAI;IAI5C;;;;OAIG;IACH,MAAM,IAAI,IAAI;IAad;;OAEG;IACH,OAAO,IAAI,IAAI;IAKf,wBAAwB,IAAI,KAAK,EAAE;CAGpC"}