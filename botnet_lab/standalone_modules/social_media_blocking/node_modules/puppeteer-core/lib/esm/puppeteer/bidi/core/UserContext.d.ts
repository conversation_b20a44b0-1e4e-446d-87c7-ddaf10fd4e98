/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
import type * as Bid<PERSON> from 'chromium-bidi/lib/cjs/protocol/protocol.js';
import { EventEmitter } from '../../common/EventEmitter.js';
import { disposeSymbol } from '../../util/disposable.js';
import type { <PERSON><PERSON><PERSON> } from './Browser.js';
import { BrowsingContext } from './BrowsingContext.js';
/**
 * @internal
 */
export type CreateBrowsingContextOptions = Omit<Bidi.BrowsingContext.CreateParameters, 'type' | 'referenceContext'> & {
    referenceContext?: BrowsingContext;
};
/**
 * @internal
 */
export declare class UserContext extends EventEmitter<{
    /**
     * Emitted when a new browsing context is created.
     */
    browsingcontext: {
        /** The new browsing context. */
        browsingContext: BrowsingContext;
    };
    /**
     * Emitted when the user context is closed.
     */
    closed: {
        /** The reason the user context was closed. */
        reason: string;
    };
}> {
    #private;
    static DEFAULT: string;
    static create(browser: Browser, id: string): UserContext;
    readonly browser: Browser;
    private constructor();
    get browsingContexts(): Iterable<BrowsingContext>;
    get closed(): boolean;
    get disposed(): boolean;
    get id(): string;
    private dispose;
    createBrowsingContext(type: Bidi.BrowsingContext.CreateType, options?: CreateBrowsingContextOptions): Promise<BrowsingContext>;
    remove(): Promise<void>;
    [disposeSymbol](): void;
}
//# sourceMappingURL=UserContext.d.ts.map