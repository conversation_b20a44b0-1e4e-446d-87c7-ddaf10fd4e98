{"version": 3, "file": "ChromeTargetManager.js", "sourceRoot": "", "sources": ["../../../../src/cdp/ChromeTargetManager.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAKH,OAAO,EAAC,UAAU,EAAE,eAAe,EAAC,MAAM,sBAAsB,CAAC;AACjE,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAC,UAAU,EAAC,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAC;AAI7C,OAAO,EAAC,SAAS,EAAE,oBAAoB,EAAC,MAAM,aAAa,CAAC;AAQ5D,SAAS,2BAA2B,CAClC,MAAiB,EACjB,aAAyC;IAEzC,OAAO,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AAC9D,CAAC;AAED;;;;;;GAMG;AACH,MAAM,OAAO,mBACX,SAAQ,YAAiC;IAGzC,WAAW,CAAa;IACxB;;;;;;;;;OASG;IACH,4BAA4B,GAAG,IAAI,GAAG,EAAsC,CAAC;IAC7E;;;OAGG;IACH,0BAA0B,GAAG,IAAI,GAAG,EAAqB,CAAC;IAC1D;;OAEG;IACH,2BAA2B,GAAG,IAAI,GAAG,EAAqB,CAAC;IAC3D;;;OAGG;IACH,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;IACpC,qBAAqB,CAAmC;IACxD,cAAc,CAAgB;IAE9B,mCAAmC,GAAG,IAAI,OAAO,EAG9C,CAAC;IACJ,qCAAqC,GAAG,IAAI,OAAO,EAGhD,CAAC;IAEJ,mBAAmB,GAAG,QAAQ,CAAC,MAAM,EAAQ,CAAC;IAC9C,kBAAkB,GAAG,IAAI,GAAG,EAAU,CAAC;IACvC,kCAAkC,GAAG,IAAI,CAAC;IAE1C,gBAAgB,GAAkC,CAAC,EAAE,CAAC,CAAC;IAEvD,YACE,UAAsB,EACtB,aAA4B,EAC5B,oBAA2C,EAC3C,iCAAiC,GAAG,IAAI;QAExC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAClD,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,kCAAkC,GAAG,iCAAiC,CAAC;QAE5E,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,sBAAsB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACnE,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,wBAAwB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACvE,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,0BAA0B,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC3E,IAAI,CAAC,WAAW,CAAC,EAAE,CACjB,eAAe,CAAC,eAAe,EAC/B,IAAI,CAAC,kBAAkB,CACxB,CAAC;QACF,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACnD,CAAC;IAED,4BAA4B,GAAG,GAAG,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC,kCAAkC,EAAE,CAAC;YAC7C,OAAO;QACT,CAAC;QACD,KAAK,MAAM,CACT,QAAQ,EACR,UAAU,EACX,IAAI,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,EAAE,CAAC;YACjD,MAAM,eAAe,GAAG,IAAI,SAAS,CACnC,UAAU,EACV,SAAS,EACT,SAAS,EACT,IAAI,EACJ,SAAS,CACV,CAAC;YACF,IACE,CAAC,CAAC,IAAI,CAAC,qBAAqB;gBAC1B,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;gBAC9C,UAAU,CAAC,IAAI,KAAK,SAAS,EAC7B,CAAC;gBACD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,2BAA2B,EAAE;YACvD,QAAQ,EAAE,IAAI;YACd,MAAM,EAAE,IAAI,CAAC,gBAAgB;SAC9B,CAAC,CAAC;QAEH,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAEpC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAClD,sBAAsB,EAAE,IAAI;YAC5B,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,IAAI;YAChB,MAAM,EAAE;gBACN;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,IAAI;iBACd;gBACD,GAAG,IAAI,CAAC,gBAAgB;aACzB;SACF,CAAC,CAAC;QACH,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,CAAC;IAChD,CAAC;IAED,OAAO;QACL,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACxE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC5E,IAAI,CAAC,WAAW,CAAC,GAAG,CAClB,eAAe,CAAC,eAAe,EAC/B,IAAI,CAAC,kBAAkB,CACxB,CAAC;QAEF,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACpD,CAAC;IAED,mBAAmB;QACjB,OAAO,IAAI,CAAC,0BAA0B,CAAC;IACzC,CAAC;IAED,yBAAyB,CAAC,OAAgC;QACxD,MAAM,QAAQ,GAAG,CAAC,KAA4C,EAAE,EAAE;YAChE,KAAK,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC;QACF,MAAM,CAAC,CAAC,IAAI,CAAC,mCAAmC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,mCAAmC,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAChE,OAAO,CAAC,EAAE,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC;QAEhD,MAAM,gBAAgB,GAAG,CACvB,KAA8C,EAC9C,EAAE;YACF,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC;QACF,MAAM,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;QACjE,IAAI,CAAC,qCAAqC,CAAC,GAAG,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAC1E,OAAO,CAAC,EAAE,CAAC,2BAA2B,EAAE,gBAAgB,CAAC,CAAC;IAC5D,CAAC;IAED,0BAA0B,CAAC,OAAgC;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,mCAAmC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvE,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC;YACjD,IAAI,CAAC,mCAAmC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,IAAI,CAAC,qCAAqC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5D,OAAO,CAAC,GAAG,CACT,2BAA2B,EAC3B,IAAI,CAAC,qCAAqC,CAAC,GAAG,CAAC,OAAO,CAAE,CACzD,CAAC;YACF,IAAI,CAAC,qCAAqC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED,kBAAkB,GAAG,CAAC,OAAmB,EAAE,EAAE;QAC3C,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;IAC3C,CAAC,CAAC;IAEF,gBAAgB,GAAG,KAAK,EAAE,KAAyC,EAAE,EAAE;QACrE,IAAI,CAAC,4BAA4B,CAAC,GAAG,CACnC,KAAK,CAAC,UAAU,CAAC,QAAQ,EACzB,KAAK,CAAC,UAAU,CACjB,CAAC;QAEF,IAAI,CAAC,IAAI,+DAAsC,KAAK,CAAC,UAAU,CAAC,CAAC;QAEjE,uEAAuE;QACvE,uEAAuE;QACvE,QAAQ;QACR,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YACrE,IAAI,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACnE,OAAO;YACT,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAChE,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACzE,CAAC;IACH,CAAC,CAAC;IAEF,kBAAkB,GAAG,CAAC,KAA2C,EAAE,EAAE;QACnE,MAAM,UAAU,GAAG,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACzE,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAClD,IACE,UAAU,EAAE,IAAI,KAAK,gBAAgB;YACrC,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EACnD,CAAC;YACD,iEAAiE;YACjE,2BAA2B;YAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACnE,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,CAAC,IAAI,mDAAgC,MAAM,CAAC,CAAC;gBACjD,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF,oBAAoB,GAAG,CAAC,KAA6C,EAAE,EAAE;QACvE,IAAI,CAAC,4BAA4B,CAAC,GAAG,CACnC,KAAK,CAAC,UAAU,CAAC,QAAQ,EACzB,KAAK,CAAC,UAAU,CACjB,CAAC;QAEF,IACE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;YACnD,CAAC,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;YAC/D,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAC1B,CAAC;YACD,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAChD,KAAK,CAAC,UAAU,CAAC,QAAQ,CAC1B,CAAC;QACF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;QACT,CAAC;QACD,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;QACjC,MAAM,cAAc,GAClB,MAAM,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,oBAAoB,CAAC,OAAO,CAAC;QAEvE,IAAI,2BAA2B,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1D,MAAM,OAAO,GAAG,MAAM,EAAE,QAAQ,EAAE,CAAC;YACnC,MAAM,CACJ,OAAO,EACP,yDAAyD,CAC1D,CAAC;YACF,OAAO,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAE5C,IAAI,cAAc,IAAI,WAAW,KAAK,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC;YACnD,IAAI,CAAC,IAAI,yDAAmC;gBAC1C,MAAM;gBACN,cAAc;gBACd,WAAW;aACZ,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;IAEF,mBAAmB,GAAG,KAAK,EACzB,aAAsC,EACtC,KAA4C,EAC5C,EAAE;QACF,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC1D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,WAAW,KAAK,CAAC,SAAS,mBAAmB,CAAC,CAAC;QACjE,CAAC;QAED,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;YAC9B,MAAM,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACxE,0EAA0E;YAC1E,gDAAgD;YAChD,MAAM,aAAa;iBAChB,IAAI,CAAC,yBAAyB,EAAE;gBAC/B,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE;aACxB,CAAC;iBACD,KAAK,CAAC,UAAU,CAAC,CAAC;QACvB,CAAC,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1D,OAAO;QACT,CAAC;QAED,2EAA2E;QAC3E,wEAAwE;QACxE,sEAAsE;QACtE,6CAA6C;QAC7C,4EAA4E;QAC5E,wEAAwE;QACxE,OAAO;QACP,IAAI,UAAU,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;YACzC,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACvD,MAAM,YAAY,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7D,OAAO;YACT,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACjE,IAAI,CAAC,IAAI,6DAAqC,MAAM,CAAC,CAAC;YACtD,OAAO;QACT,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAC1D,UAAU,CAAC,QAAQ,CACpB,CAAC;QAEF,MAAM,MAAM,GAAG,gBAAgB;YAC7B,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAE;YAC3D,CAAC,CAAC,IAAI,CAAC,cAAc,CACjB,UAAU,EACV,OAAO,EACP,aAAa,YAAY,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAChE,CAAC;QAEN,IAAI,IAAI,CAAC,qBAAqB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC;YACtE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC9C,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACvD,MAAM,YAAY,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QAExC,IAAI,gBAAgB,EAAE,CAAC;YACpB,OAAyB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAC9C,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAClC,OAAO,CAAC,EAAE,EAAE,EACZ,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAE,CAC1D,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACjE,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;QAC7D,CAAC;QAED,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAEnD,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,IAAI,CAAC,IAAI,6DAAqC,MAAM,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAEpC,wEAAwE;QACxE,SAAS;QACT,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBACnC,sBAAsB,EAAE,IAAI;gBAC5B,OAAO,EAAE,IAAI;gBACb,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,IAAI,CAAC,gBAAgB;aAC9B,CAAC;YACF,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC;SAChD,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACvB,CAAC,CAAC;IAEF,4BAA4B,CAAC,QAAiB;QAC5C,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACnE,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;QACrC,CAAC;IACH,CAAC;IAED,qBAAqB,GAAG,CACtB,cAAuC,EACvC,KAA8C,EAC9C,EAAE;QACF,MAAM,MAAM,GAAG,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAErE,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEzD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;QACT,CAAC;QAED,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,IAAI,mDAAgC,MAAM,CAAC,CAAC;IACnD,CAAC,CAAC;CACH"}