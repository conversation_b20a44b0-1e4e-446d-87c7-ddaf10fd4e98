{"version": 3, "file": "DeviceRequestPrompt.js", "sourceRoot": "", "sources": ["../../../../src/cdp/DeviceRequestPrompt.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAOH,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAC;AAE7C;;;;GAIG;AACH,MAAM,OAAO,yBAAyB;IACpC;;OAEG;IACH,EAAE,CAAS;IAEX;;OAEG;IACH,IAAI,CAAS;IAEb;;OAEG;IACH,YAAY,EAAU,EAAE,IAAY;QAClC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;CACF;AAED;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,OAAO,mBAAmB;IAC9B,OAAO,CAAoB;IAC3B,gBAAgB,CAAkB;IAClC,GAAG,CAAS;IACZ,QAAQ,GAAG,KAAK,CAAC;IACjB,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtD,sBAAsB,GAAG,IAAI,GAAG,EAG5B,CAAC;IAEL;;OAEG;IACH,OAAO,GAAgC,EAAE,CAAC;IAE1C;;OAEG;IACH,YACE,MAAkB,EAClB,eAAgC,EAChC,UAA4D;QAE5D,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,GAAG,GAAG,UAAU,CAAC,EAAE,CAAC;QAEzB,IAAI,CAAC,OAAO,CAAC,EAAE,CACb,oCAAoC,EACpC,IAAI,CAAC,oBAAoB,CAC1B,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YAChD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IAED,cAAc,CAAC,KAAuD;QACpE,IAAI,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;YAC1B,OAAO;QACT,CAAC;QAED,KAAK,MAAM,SAAS,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACtC,IACE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBACzB,OAAO,MAAM,CAAC,EAAE,KAAK,SAAS,CAAC,EAAE,CAAC;YACpC,CAAC,CAAC,EACF,CAAC;gBACD,SAAS;YACX,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,yBAAyB,CAC7C,SAAS,CAAC,EAAE,EACZ,SAAS,CAAC,IAAI,CACf,CAAC;YACF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE7B,KAAK,MAAM,oBAAoB,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC/D,IAAI,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC3C,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACjB,MAAsD,EACtD,UAA8B,EAAE;QAEhC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClC,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnB,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QAED,MAAM,EAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAC,GAAG,OAAO,CAAC;QAC5D,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAA4B;YAC1D,OAAO,EAAE,qDAAqD,OAAO,aAAa;YAClF,OAAO;SACR,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,EAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAC,CAAC;QAC3C,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC;YACH,OAAO,MAAM,QAAQ,CAAC,YAAY,EAAE,CAAC;QACvC,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,MAAiC;QAC5C,MAAM,CACJ,IAAI,CAAC,OAAO,KAAK,IAAI,EACrB,gDAAgD,CACjD,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,+BAA+B,CAAC,CAAC;QACvE,MAAM,CACJ,CAAC,IAAI,CAAC,QAAQ,EACd,6DAA6D,CAC9D,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,GAAG,CACd,oCAAoC,EACpC,IAAI,CAAC,oBAAoB,CAC1B,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,2BAA2B,EAAE;YAC1D,EAAE,EAAE,IAAI,CAAC,GAAG;YACZ,QAAQ,EAAE,MAAM,CAAC,EAAE;SACpB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM;QACV,MAAM,CACJ,IAAI,CAAC,OAAO,KAAK,IAAI,EACrB,gDAAgD,CACjD,CAAC;QACF,MAAM,CACJ,CAAC,IAAI,CAAC,QAAQ,EACd,6DAA6D,CAC9D,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,GAAG,CACd,oCAAoC,EACpC,IAAI,CAAC,oBAAoB,CAC1B,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAC,EAAE,EAAE,IAAI,CAAC,GAAG,EAAC,CAAC,CAAC;IAC9E,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,0BAA0B;IACrC,OAAO,CAAoB;IAC3B,gBAAgB,CAAkB;IAClC,4BAA4B,GAAG,IAAI,GAAG,EAAiC,CAAC;IAExE;;OAEG;IACH,YAAY,MAAkB,EAAE,eAAgC;QAC9D,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QAExC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,oCAAoC,EAAE,KAAK,CAAC,EAAE;YAC5D,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YAChD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB,CACvB,UAA8B,EAAE;QAEhC,MAAM,CACJ,IAAI,CAAC,OAAO,KAAK,IAAI,EACrB,yDAAyD,CAC1D,CAAC;QACF,MAAM,WAAW,GAAG,IAAI,CAAC,4BAA4B,CAAC,IAAI,KAAK,CAAC,CAAC;QACjE,IAAI,aAAwC,CAAC;QAC7C,IAAI,WAAW,EAAE,CAAC;YAChB,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,EAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAC,GAAG,OAAO,CAAC;QAC5D,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAsB;YACpD,OAAO,EAAE,+CAA+C,OAAO,aAAa;YAC5E,OAAO;SACR,CAAC,CAAC;QACH,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACjC,QAAQ,CAAC,YAAY,EAAE;gBACvB,aAAa;aACd,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,wBAAwB,CACtB,KAAuD;QAEvD,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO;QACT,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,CAAC;QAC9B,MAAM,YAAY,GAAG,IAAI,mBAAmB,CAC1C,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,gBAAgB,EACrB,KAAK,CACN,CAAC;QACF,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACxD,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAChC,CAAC;QACD,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;IAC5C,CAAC;CACF"}