{"version": 3, "file": "HTTPRequest.js", "sourceRoot": "", "sources": ["../../../../src/cdp/HTTPRequest.ts"], "names": [], "mappings": "AASA,OAAO,EAGL,YAAY,EACZ,WAAW,EACX,yBAAyB,EAIzB,YAAY,GACb,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAAC,UAAU,EAAE,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AACvD,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AAIzC;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,WAAW;IAI7C,OAAO,CAAa;IACpB,oBAAoB,CAAU;IAC9B,kBAAkB,CAAU;IAC5B,oBAAoB,GAAG,KAAK,CAAC;IAC7B,IAAI,CAAS;IACb,aAAa,CAAe;IAE5B,OAAO,CAAS;IAChB,YAAY,GAAG,KAAK,CAAC;IACrB,SAAS,CAAU;IACnB,QAAQ,GAA2B,EAAE,CAAC;IACtC,MAAM,CAAe;IACrB,yBAAyB,CAA2B;IACpD,mBAAmB,GAAuC,IAAI,CAAC;IAC/D,iBAAiB,GAAwC,IAAI,CAAC;IAC9D,yBAAyB,GAA6B;QACpD,MAAM,EAAE,yBAAyB,CAAC,IAAI;KACvC,CAAC;IACF,kBAAkB,CAAuC;IACzD,UAAU,CAA8B;IAExC,IAAa,MAAM;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,YACE,MAAkB,EAClB,KAAmB,EACnB,cAAkC,EAClC,iBAA0B,EAC1B,IAyBC,EACD,aAA+B;QAE/B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,IAAI,CAAC,oBAAoB;YACvB,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC;QAC/D,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,WAAW,EAAkB,CAAC;QAC1E,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,KAAK,CAAC;QACtD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,yBAAyB,GAAG,EAAE,CAAC;QACpC,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YAChE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC;QAC3C,CAAC;IACH,CAAC;IAEQ,GAAG;QACV,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAEQ,wBAAwB;QAC/B,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,sCAAsC,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC,yBAAyB,CAAC;IACxC,CAAC;IAEQ,kBAAkB;QACzB,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,sCAAsC,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAEQ,gBAAgB;QACvB,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,sCAAsC,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAChC,CAAC;IAEQ,wBAAwB;QAC/B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,OAAO,EAAC,MAAM,EAAE,yBAAyB,CAAC,QAAQ,EAAC,CAAC;QACtD,CAAC;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,OAAO,EAAC,MAAM,EAAE,yBAAyB,CAAC,cAAc,EAAC,CAAC;QAC5D,CAAC;QACD,OAAO,EAAC,GAAG,IAAI,CAAC,yBAAyB,EAAC,CAAC;IAC7C,CAAC;IAEQ,4BAA4B;QACnC,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IAED,sBAAsB,CACpB,cAAiD;QAEjD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC/C,CAAC;IAEQ,KAAK,CAAC,qBAAqB;QAClC,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,EAAE;YACrE,OAAO,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC5C,CAAC,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACtB,MAAM,EAAC,MAAM,EAAC,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACjD,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,OAAO;gBACV,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACnD,KAAK,SAAS;gBACZ,IAAI,IAAI,CAAC,mBAAmB,KAAK,IAAI,EAAE,CAAC;oBACtC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;gBAC9D,CAAC;gBACD,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACvD,KAAK,UAAU;gBACb,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAEQ,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAEQ,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEQ,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEQ,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAEQ,KAAK,CAAC,aAAa;QAC1B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACnE,SAAS,EAAE,IAAI,CAAC,UAAU;aAC3B,CAAC,CAAC;YACH,OAAO,MAAM,CAAC,QAAQ,CAAC;QACzB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,UAAU,CAAC,GAAG,CAAC,CAAC;YAChB,OAAO;QACT,CAAC;IACH,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEQ,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEQ,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEQ,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IAEQ,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAEQ,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IACrC,CAAC;IAEQ,OAAO;QACd,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,YAAY;SAC7B,CAAC;IACJ,CAAC;IAEQ,KAAK,CAAC,QAAQ,CACrB,YAAsC,EAAE,EACxC,QAAiB;QAEjB,wDAAwD;QACxD,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,sCAAsC,CAAC,CAAC;QACxE,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,6BAA6B,CAAC,CAAC;QAClE,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,yBAAyB,GAAG,SAAS,CAAC;QAC3C,IACE,IAAI,CAAC,yBAAyB,CAAC,QAAQ,KAAK,SAAS;YACrD,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAClD,CAAC;YACD,IAAI,CAAC,yBAAyB,GAAG;gBAC/B,MAAM,EAAE,yBAAyB,CAAC,QAAQ;gBAC1C,QAAQ;aACT,CAAC;YACF,OAAO;QACT,CAAC;QACD,IAAI,QAAQ,KAAK,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,CAAC;YACzD,IACE,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,OAAO;gBACjD,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,SAAS,EACnD,CAAC;gBACD,OAAO;YACT,CAAC;YACD,IAAI,CAAC,yBAAyB,CAAC,MAAM;gBACnC,yBAAyB,CAAC,QAAQ,CAAC;QACvC,CAAC;QACD,OAAO;IACT,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,YAAsC,EAAE;QACtD,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAC,GAAG,SAAS,CAAC;QACnD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAEjC,MAAM,oBAAoB,GAAG,QAAQ;YACnC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC1C,CAAC,CAAC,SAAS,CAAC;QAEd,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CACb,yEAAyE,CAC1E,CAAC;QACJ,CAAC;QACD,MAAM,IAAI,CAAC,OAAO;aACf,IAAI,CAAC,uBAAuB,EAAE;YAC7B,SAAS,EAAE,IAAI,CAAC,eAAe;YAC/B,GAAG;YACH,MAAM;YACN,QAAQ,EAAE,oBAAoB;YAC9B,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;SACrD,CAAC;aACD,KAAK,CAAC,KAAK,CAAC,EAAE;YACb,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;YAClC,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACP,CAAC;IAEQ,KAAK,CAAC,OAAO,CACpB,QAAqC,EACrC,QAAiB;QAEjB,qEAAqE;QACrE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,sCAAsC,CAAC,CAAC;QACxE,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,6BAA6B,CAAC,CAAC;QAClE,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;QACpC,IACE,IAAI,CAAC,yBAAyB,CAAC,QAAQ,KAAK,SAAS;YACrD,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAClD,CAAC;YACD,IAAI,CAAC,yBAAyB,GAAG;gBAC/B,MAAM,EAAE,yBAAyB,CAAC,OAAO;gBACzC,QAAQ;aACT,CAAC;YACF,OAAO;QACT,CAAC;QACD,IAAI,QAAQ,KAAK,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,CAAC;YACzD,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;gBACtD,OAAO;YACT,CAAC;YACD,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,yBAAyB,CAAC,OAAO,CAAC;QAC5E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,QAAqC;QAClD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAEjC,MAAM,YAAY,GAChB,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;YACtC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YAC5B,CAAC,CAAE,QAAQ,CAAC,IAAe,IAAI,IAAI,CAAC;QAExC,MAAM,eAAe,GAAsC,EAAE,CAAC;QAC9D,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACnD,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAEvC,eAAe,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;oBAC1D,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBACf,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;oBACtB,CAAC,CAAC;oBACJ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;QACD,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YACzB,eAAe,CAAC,cAAc,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC;QACzD,CAAC;QACD,IAAI,YAAY,IAAI,CAAC,CAAC,gBAAgB,IAAI,eAAe,CAAC,EAAE,CAAC;YAC3D,eAAe,CAAC,gBAAgB,CAAC,GAAG,MAAM,CACxC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAChC,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,GAAG,CAAC;QACtC,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CACb,wEAAwE,CACzE,CAAC;QACJ,CAAC;QACD,MAAM,IAAI,CAAC,OAAO;aACf,IAAI,CAAC,sBAAsB,EAAE;YAC5B,SAAS,EAAE,IAAI,CAAC,eAAe;YAC/B,YAAY,EAAE,MAAM;YACpB,cAAc,EAAE,YAAY,CAAC,MAAM,CAAC;YACpC,eAAe,EAAE,YAAY,CAAC,eAAe,CAAC;YAC9C,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS;SACjE,CAAC;aACD,KAAK,CAAC,KAAK,CAAC,EAAE;YACb,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;YAClC,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACP,CAAC;IAEQ,KAAK,CAAC,KAAK,CAClB,YAAuB,QAAQ,EAC/B,QAAiB;QAEjB,wDAAwD;QACxD,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;QACD,MAAM,WAAW,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;QAC5C,MAAM,CAAC,WAAW,EAAE,sBAAsB,GAAG,SAAS,CAAC,CAAC;QACxD,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,sCAAsC,CAAC,CAAC;QACxE,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,6BAA6B,CAAC,CAAC;QAClE,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;QACrC,IACE,IAAI,CAAC,yBAAyB,CAAC,QAAQ,KAAK,SAAS;YACrD,QAAQ,IAAI,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACnD,CAAC;YACD,IAAI,CAAC,yBAAyB,GAAG;gBAC/B,MAAM,EAAE,yBAAyB,CAAC,KAAK;gBACvC,QAAQ;aACT,CAAC;YACF,OAAO;QACT,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CACV,WAAgD;QAEhD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CACb,qEAAqE,CACtE,CAAC;QACJ,CAAC;QACD,MAAM,IAAI,CAAC,OAAO;aACf,IAAI,CAAC,mBAAmB,EAAE;YACzB,SAAS,EAAE,IAAI,CAAC,eAAe;YAC/B,WAAW,EAAE,WAAW,IAAI,QAAQ;SACrC,CAAC;aACD,KAAK,CAAC,WAAW,CAAC,CAAC;IACxB,CAAC;CACF;AAED,MAAM,YAAY,GAAoD;IACpE,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,cAAc;IAC5B,kBAAkB,EAAE,oBAAoB;IACxC,eAAe,EAAE,iBAAiB;IAClC,iBAAiB,EAAE,mBAAmB;IACtC,iBAAiB,EAAE,mBAAmB;IACtC,gBAAgB,EAAE,kBAAkB;IACpC,gBAAgB,EAAE,kBAAkB;IACpC,iBAAiB,EAAE,mBAAmB;IACtC,eAAe,EAAE,iBAAiB;IAClC,oBAAoB,EAAE,sBAAsB;IAC5C,eAAe,EAAE,iBAAiB;IAClC,QAAQ,EAAE,UAAU;IACpB,MAAM,EAAE,QAAQ;CACR,CAAC;AAEX,KAAK,UAAU,WAAW,CAAC,KAAoB;IAC7C,IAAI,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;IACD,kEAAkE;IAClE,oEAAoE;IACpE,UAAU;IACV,UAAU,CAAC,KAAK,CAAC,CAAC;AACpB,CAAC"}