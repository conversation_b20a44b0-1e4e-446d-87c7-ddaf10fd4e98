{"version": 3, "file": "index.cjs.js", "sources": ["../src/index.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport os from 'os'\nimport path from 'path'\n\nimport { PuppeteerBlocker } from '@cliqz/adblocker-puppeteer'\nimport fetch from 'node-fetch'\nimport { PuppeteerExtraPlugin } from 'puppeteer-extra-plugin'\n\nconst pkg = require('../package.json')\nconst engineCacheFilename = `${pkg.name}-${pkg.version}-engine.bin`\n\n/** Available plugin options */\nexport interface PluginOptions {\n  /** Whether or not to block trackers (in addition to ads). Default: false */\n  blockTrackers: boolean\n  /** Whether or not to block trackers and other annoyances, including cookie\n      notices. Default: false */\n  blockTrackersAndAnnoyances: boolean\n  /** Persist adblocker engine cache to disk for speedup. Default: true */\n  useCache: boolean\n  /** Optional custom directory for adblocker cache files. Default: undefined */\n  cacheDir?: string\n  /** Optional custom priority for interception resolution. Default: undefined */\n  interceptResolutionPriority?: number\n}\n\n/**\n * A puppeteer-extra plugin to automatically block ads and trackers.\n */\nexport class PuppeteerExtraPluginAdblocker extends PuppeteerExtraPlugin {\n  private blocker: PuppeteerBlocker | undefined\n\n  constructor(opts: Partial<PluginOptions>) {\n    super(opts)\n    this.debug('Initialized', this.opts)\n  }\n\n  get name() {\n    return 'adblocker'\n  }\n\n  get defaults(): PluginOptions {\n    return {\n      blockTrackers: false,\n      blockTrackersAndAnnoyances: false,\n      useCache: true,\n      cacheDir: undefined,\n      interceptResolutionPriority: undefined\n    }\n  }\n\n  get engineCacheFile() {\n    const cacheDir = this.opts.cacheDir || os.tmpdir()\n    return path.join(cacheDir, engineCacheFilename)\n  }\n\n  /**\n   * Cache an instance of `PuppeteerBlocker` to disk if 'cacheDir' option was\n   * specified for the plugin. It can then be used the next time this plugin is\n   * used to load the adblocker faster.\n   */\n  private async persistToCache(blocker: PuppeteerBlocker): Promise<void> {\n    if (!this.opts.useCache) {\n      return\n    }\n    this.debug('persist to cache', this.engineCacheFile)\n    await fs.writeFile(this.engineCacheFile, blocker.serialize())\n  }\n\n  /**\n   * Initialize instance of `PuppeteerBlocker` from cache if possible.\n   * Otherwise, it throws and we will try to initialize it from remote instead.\n   */\n  private async loadFromCache(): Promise<PuppeteerBlocker> {\n    if (!this.opts.useCache) {\n      throw new Error('caching disabled')\n    }\n    this.debug('load from cache', this.engineCacheFile)\n    return PuppeteerBlocker.deserialize(\n      new Uint8Array(await fs.readFile(this.engineCacheFile))\n    )\n  }\n\n  /**\n   * Initialize instance of `PuppeteerBlocker` from remote (either by fetching\n   * a serialized version of the engine when available, or by downloading raw\n   * lists for filters such as EasyList then parsing them to initialize\n   * blocker).\n   */\n  private async loadFromRemote(): Promise<PuppeteerBlocker> {\n    this.debug('load from remote', {\n      blockTrackers: this.opts.blockTrackers,\n      blockTrackersAndAnnoyances: this.opts.blockTrackersAndAnnoyances\n    })\n    if (this.opts.blockTrackersAndAnnoyances === true) {\n      return PuppeteerBlocker.fromPrebuiltFull(fetch)\n    } else if (this.opts.blockTrackers === true) {\n      return PuppeteerBlocker.fromPrebuiltAdsAndTracking(fetch)\n    } else {\n      return PuppeteerBlocker.fromPrebuiltAdsOnly(fetch)\n    }\n  }\n\n  /**\n   * Return instance of `PuppeteerBlocker`. It will take care of initializing\n   * it if necessary (first time it is called), or return the existing instance\n   * if it already exists.\n   */\n  async getBlocker(): Promise<PuppeteerBlocker> {\n    this.debug('getBlocker', { hasBlocker: !!this.blocker })\n    if (this.blocker === undefined) {\n      try {\n        this.blocker = await this.loadFromCache()\n        this.setRequestInterceptionPriority()\n      } catch (ex) {\n        this.blocker = await this.loadFromRemote()\n        this.setRequestInterceptionPriority()\n        await this.persistToCache(this.blocker)\n      }\n    }\n    return this.blocker\n  }\n\n  /**\n   * Sets the request interception priority on the `PuppeteerBlocker` instance.\n   */\n  private setRequestInterceptionPriority(): void {\n    this.blocker?.setRequestInterceptionPriority(this.opts.interceptResolutionPriority)\n  }\n\n  /**\n   * Hook into this blocking event to make sure the cache is initialized before navigation.\n   */\n  async beforeLaunch() {\n    this.debug('beforeLaunch')\n    await this.getBlocker()\n  }\n\n  /**\n   * Hook into this blocking event to make sure the cache is initialized before navigation.\n   */\n  async beforeConnect() {\n    this.debug('beforeConnect')\n    await this.getBlocker()\n  }\n\n  /**\n   * Enable adblocking in `page`.\n   */\n  async onPageCreated(page: any) {\n    this.debug('onPageCreated')\n    ;(await this.getBlocker()).enableBlockingInPage(page)\n  }\n}\n\nexport default (options: Partial<PluginOptions> = {}) => {\n  return new PuppeteerExtraPluginAdblocker(options)\n}\n"], "names": ["PuppeteerExtraPlugin", "fs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;AAQA,MAAM,GAAG,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAA;AACtC,MAAM,mBAAmB,GAAG,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,OAAO,aAAa,CAAA;AAiBnE;;;MAGa,6BAA8B,SAAQA,yCAAoB;IAGrE,YAAY,IAA4B;QACtC,KAAK,CAAC,IAAI,CAAC,CAAA;QACX,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;KACrC;IAED,IAAI,IAAI;QACN,OAAO,WAAW,CAAA;KACnB;IAED,IAAI,QAAQ;QACV,OAAO;YACL,aAAa,EAAE,KAAK;YACpB,0BAA0B,EAAE,KAAK;YACjC,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,SAAS;YACnB,2BAA2B,EAAE,SAAS;SACvC,CAAA;KACF;IAED,IAAI,eAAe;QACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC,MAAM,EAAE,CAAA;QAClD,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAA;KAChD;;;;;;IAOO,MAAM,cAAc,CAAC,OAAyB;QACpD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACvB,OAAM;SACP;QACD,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAA;QACpD,MAAMC,WAAE,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAA;KAC9D;;;;;IAMO,MAAM,aAAa;QACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAA;SACpC;QACD,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAA;QACnD,OAAOC,mCAAgB,CAAC,WAAW,CACjC,IAAI,UAAU,CAAC,MAAMD,WAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CACxD,CAAA;KACF;;;;;;;IAQO,MAAM,cAAc;QAC1B,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;YAC7B,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa;YACtC,0BAA0B,EAAE,IAAI,CAAC,IAAI,CAAC,0BAA0B;SACjE,CAAC,CAAA;QACF,IAAI,IAAI,CAAC,IAAI,CAAC,0BAA0B,KAAK,IAAI,EAAE;YACjD,OAAOC,mCAAgB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;SAChD;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;YAC3C,OAAOA,mCAAgB,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAA;SAC1D;aAAM;YACL,OAAOA,mCAAgB,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;SACnD;KACF;;;;;;IAOD,MAAM,UAAU;QACd,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;QACxD,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;YAC9B,IAAI;gBACF,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAA;gBACzC,IAAI,CAAC,8BAA8B,EAAE,CAAA;aACtC;YAAC,OAAO,EAAE,EAAE;gBACX,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAA;gBAC1C,IAAI,CAAC,8BAA8B,EAAE,CAAA;gBACrC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;aACxC;SACF;QACD,OAAO,IAAI,CAAC,OAAO,CAAA;KACpB;;;;IAKO,8BAA8B;;QACpC,MAAA,IAAI,CAAC,OAAO,0CAAE,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAA;KACpF;;;;IAKD,MAAM,YAAY;QAChB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;QAC1B,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;KACxB;;;;IAKD,MAAM,aAAa;QACjB,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;QAC3B,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;KACxB;;;;IAKD,MAAM,aAAa,CAAC,IAAS;QAC3B,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAC1B;QAAA,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAA;KACtD;CACF;AAED,YAAe,CAAC,UAAkC,EAAE;IAClD,OAAO,IAAI,6BAA6B,CAAC,OAAO,CAAC,CAAA;AACnD,CAAC;;;;;;;;;;;;"}