{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;AAAA,2BAAmC;AACnC,4CAAmB;AACnB,gDAAuB;AAEvB,oEAA6D;AAC7D,4DAA8B;AAC9B,mEAA6D;AAE7D,MAAM,GAAG,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAA;AACtC,MAAM,mBAAmB,GAAG,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,OAAO,aAAa,CAAA;AAiBnE;;GAEG;AACH,MAAa,6BAA8B,SAAQ,6CAAoB;IAGrE,YAAY,IAA4B;QACtC,KAAK,CAAC,IAAI,CAAC,CAAA;QACX,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;IACtC,CAAC;IAED,IAAI,IAAI;QACN,OAAO,WAAW,CAAA;IACpB,CAAC;IAED,IAAI,QAAQ;QACV,OAAO;YACL,aAAa,EAAE,KAAK;YACpB,0BAA0B,EAAE,KAAK;YACjC,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,SAAS;YACnB,2BAA2B,EAAE,SAAS;SACvC,CAAA;IACH,CAAC;IAED,IAAI,eAAe;QACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,YAAE,CAAC,MAAM,EAAE,CAAA;QAClD,OAAO,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAA;IACjD,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,cAAc,CAAC,OAAyB;QACpD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACvB,OAAM;SACP;QACD,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAA;QACpD,MAAM,aAAE,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAA;IAC/D,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,aAAa;QACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAA;SACpC;QACD,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAA;QACnD,OAAO,sCAAgB,CAAC,WAAW,CACjC,IAAI,UAAU,CAAC,MAAM,aAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CACxD,CAAA;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;YAC7B,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa;YACtC,0BAA0B,EAAE,IAAI,CAAC,IAAI,CAAC,0BAA0B;SACjE,CAAC,CAAA;QACF,IAAI,IAAI,CAAC,IAAI,CAAC,0BAA0B,KAAK,IAAI,EAAE;YACjD,OAAO,sCAAgB,CAAC,gBAAgB,CAAC,oBAAK,CAAC,CAAA;SAChD;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;YAC3C,OAAO,sCAAgB,CAAC,0BAA0B,CAAC,oBAAK,CAAC,CAAA;SAC1D;aAAM;YACL,OAAO,sCAAgB,CAAC,mBAAmB,CAAC,oBAAK,CAAC,CAAA;SACnD;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;QACxD,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;YAC9B,IAAI;gBACF,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAA;gBACzC,IAAI,CAAC,8BAA8B,EAAE,CAAA;aACtC;YAAC,OAAO,EAAE,EAAE;gBACX,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAA;gBAC1C,IAAI,CAAC,8BAA8B,EAAE,CAAA;gBACrC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;aACxC;SACF;QACD,OAAO,IAAI,CAAC,OAAO,CAAA;IACrB,CAAC;IAED;;OAEG;IACK,8BAA8B;;QACpC,MAAA,IAAI,CAAC,OAAO,0CAAE,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAA;IACrF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;QAC1B,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;QAC3B,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,IAAS;QAC3B,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAC1B;QAAA,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;IACvD,CAAC;CACF;AA5HD,sEA4HC;AAED,kBAAe,CAAC,UAAkC,EAAE,EAAE,EAAE;IACtD,OAAO,IAAI,6BAA6B,CAAC,OAAO,CAAC,CAAA;AACnD,CAAC,CAAA"}