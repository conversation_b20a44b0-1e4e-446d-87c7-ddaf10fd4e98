{"name": "puppeteer-extra-plugin-stealth", "version": "2.11.2", "description": "Stealth mode: Applies various techniques to make detection of headless puppeteer harder.", "main": "index.js", "typings": "index.d.ts", "repository": "berstend/puppeteer-extra", "homepage": "https://github.com/berstend/puppeteer-extra/tree/master/packages/puppeteer-extra-plugin-stealth#readme", "author": "be<PERSON><PERSON>", "license": "MIT", "scripts": {"docs": "run-s docs-for-plugin postdocs-for-plugin docs-for-evasions postdocs-for-evasions types", "docs-for-plugin": "documentation readme --quiet --shallow --github --markdown-theme transitivebs --readme-file readme.md --section API index.js", "postdocs-for-plugin": "npx prettier --write readme.md", "docs-for-evasions": "cd ./evasions && loop \"documentation readme --quiet --shallow --github --markdown-theme transitivebs --readme-file readme.md --section API index.js\"", "postdocs-for-evasions": "cd ./evasions && loop \"npx prettier --write readme.md\"", "lint": "eslint --ext .js .", "test:js": "ava --concurrency 2 -v", "test": "run-p test:js", "test-ci": "run-s test:js", "types": "npx --package typescript@3.7 tsc --emitDeclarationOnly --declaration --allowJs index.js"}, "engines": {"node": ">=8"}, "keywords": ["puppeteer", "puppeteer-extra", "puppeteer-extra-plugin", "stealth", "stealth-mode", "detection-evasion", "crawler", "chrome", "headless", "pupeteer"], "ava": {"files": ["!test/util.js", "!test/fixtures/sw.js"]}, "devDependencies": {"ava": "2.4.0", "documentation-markdown-themes": "^12.1.5", "fpcollect": "^1.0.4", "fpscanner": "^0.1.5", "loop": "^3.0.6", "npm-run-all": "^4.1.5", "puppeteer": "9"}, "dependencies": {"debug": "^4.1.1", "puppeteer-extra-plugin": "^3.2.3", "puppeteer-extra-plugin-user-preferences": "^2.4.1"}, "peerDependencies": {"playwright-extra": "*", "puppeteer-extra": "*"}, "peerDependenciesMeta": {"puppeteer-extra": {"optional": true}, "playwright-extra": {"optional": true}}, "gitHead": "2f4a357f233b35a7a20f16ce007f5ef3f62765b9"}