{"version": 3, "file": "index.cjs.js", "sources": ["../src/index.ts"], "sourcesContent": ["/// <reference path=\"./puppeteer-legacy.d.ts\" />\nimport { P<PERSON>peteerN<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer'\n\nimport Debug from 'debug'\nconst debug = Debug('puppeteer-extra')\n\nimport merge from 'deepmerge'\n\n/**\n * Original Puppeteer API\n * @private\n */\nexport interface VanillaPuppeteer\n  extends Pick<\n    PuppeteerNode,\n    | 'connect'\n    | 'defaultArgs'\n    | 'executablePath'\n    | 'launch'\n    | 'createBrowserFetcher'\n  > {}\n\n/**\n * Minimal plugin interface\n * @private\n */\nexport interface PuppeteerExtraPlugin {\n  _isPuppeteerExtraPlugin: boolean\n  [propName: string]: any\n}\n\n/**\n * We need to hook into non-public APIs in rare occasions to fix puppeteer bugs. :(\n * @private\n */\ninterface BrowserInternals extends Browser {\n  _createPageInContext(contextId?: string): Promise<Page>\n}\n\n/**\n * Modular plugin framework to teach `puppeteer` new tricks.\n *\n * This module acts as a drop-in replacement for `puppeteer`.\n *\n * Allows PuppeteerExtraPlugin's to register themselves and\n * to extend puppeteer with additional functionality.\n *\n * @class PuppeteerExtra\n * @implements {VanillaPuppeteer}\n *\n * @example\n * const puppeteer = require('puppeteer-extra')\n * puppeteer.use(require('puppeteer-extra-plugin-anonymize-ua')())\n * puppeteer.use(require('puppeteer-extra-plugin-font-size')({defaultFontSize: 18}))\n *\n * ;(async () => {\n *   const browser = await puppeteer.launch({headless: false})\n *   const page = await browser.newPage()\n *   await page.goto('http://example.com', {waitUntil: 'domcontentloaded'})\n *   await browser.close()\n * })()\n */\nexport class PuppeteerExtra implements VanillaPuppeteer {\n  private _plugins: PuppeteerExtraPlugin[] = []\n\n  constructor(\n    private _pptr?: VanillaPuppeteer,\n    private _requireError?: Error\n  ) {}\n\n  /**\n   * The **main interface** to register `puppeteer-extra` plugins.\n   *\n   * @example\n   * puppeteer.use(plugin1).use(plugin2)\n   *\n   * @see [PuppeteerExtraPlugin]\n   *\n   * @return The same `PuppeteerExtra` instance (for optional chaining)\n   */\n  use(plugin: PuppeteerExtraPlugin): this {\n    if (typeof plugin !== 'object' || !plugin._isPuppeteerExtraPlugin) {\n      console.error(\n        `Warning: Plugin is not derived from PuppeteerExtraPlugin, ignoring.`,\n        plugin\n      )\n      return this\n    }\n    if (!plugin.name) {\n      console.error(\n        `Warning: Plugin with no name registering, ignoring.`,\n        plugin\n      )\n      return this\n    }\n    if (plugin.requirements.has('dataFromPlugins')) {\n      plugin.getDataFromPlugins = this.getPluginData.bind(this)\n    }\n    plugin._register(Object.getPrototypeOf(plugin))\n    this._plugins.push(plugin)\n    debug('plugin registered', plugin.name)\n    return this\n  }\n\n  /**\n   * To stay backwards compatible with puppeteer's (and our) default export after adding `addExtra`\n   * we need to defer the check if we have a puppeteer instance to work with.\n   * Otherwise we would throw even if the user intends to use their non-standard puppeteer implementation.\n   *\n   * @private\n   */\n  get pptr(): VanillaPuppeteer {\n    if (this._pptr) {\n      return this._pptr\n    }\n\n    // Whoopsie\n    console.warn(`\n    Puppeteer is missing. :-)\n\n    Note: puppeteer is a peer dependency of puppeteer-extra,\n    which means you can install your own preferred version.\n\n    - To get the latest stable version run: 'yarn add puppeteer' or 'npm i puppeteer'\n\n    Alternatively:\n    - To get puppeteer without the bundled Chromium browser install 'puppeteer-core'\n    `)\n    throw this._requireError || new Error('No puppeteer instance provided.')\n  }\n\n  /**\n   * The method launches a browser instance with given arguments. The browser will be closed when the parent node.js process is closed.\n   *\n   * Augments the original `puppeteer.launch` method with plugin lifecycle methods.\n   *\n   * All registered plugins that have a `beforeLaunch` method will be called\n   * in sequence to potentially update the `options` Object before launching the browser.\n   *\n   * @example\n   * const browser = await puppeteer.launch({\n   *   headless: false,\n   *   defaultViewport: null\n   * })\n   *\n   * @param options - See [puppeteer docs](https://github.com/puppeteer/puppeteer/blob/master/docs/api.md#puppeteerlaunchoptions).\n   */\n  async launch(\n    options?: Parameters<VanillaPuppeteer['launch']>[0]\n  ): ReturnType<VanillaPuppeteer['launch']> {\n    // Ensure there are certain properties (e.g. the `options.args` array)\n    const defaultLaunchOptions = { args: [] }\n    options = merge(defaultLaunchOptions, options || {})\n    this.resolvePluginDependencies()\n    this.orderPlugins()\n\n    // Give plugins the chance to modify the options before launch\n    options = await this.callPluginsWithValue('beforeLaunch', options)\n\n    const opts = {\n      context: 'launch',\n      options,\n      defaultArgs: this.defaultArgs\n    }\n\n    // Let's check requirements after plugin had the chance to modify the options\n    this.checkPluginRequirements(opts)\n\n    const browser = await this.pptr.launch(options)\n    this._patchPageCreationMethods(browser as BrowserInternals)\n\n    await this.callPlugins('_bindBrowserEvents', browser, opts)\n    return browser\n  }\n\n  /**\n   * Attach Puppeteer to an existing Chromium instance.\n   *\n   * Augments the original `puppeteer.connect` method with plugin lifecycle methods.\n   *\n   * All registered plugins that have a `beforeConnect` method will be called\n   * in sequence to potentially update the `options` Object before launching the browser.\n   *\n   * @param options - See [puppeteer docs](https://github.com/puppeteer/puppeteer/blob/master/docs/api.md#puppeteerconnectoptions).\n   */\n  async connect(\n    options: Parameters<VanillaPuppeteer['connect']>[0]\n  ): ReturnType<VanillaPuppeteer['connect']> {\n    this.resolvePluginDependencies()\n    this.orderPlugins()\n\n    // Give plugins the chance to modify the options before connect\n    options = await this.callPluginsWithValue('beforeConnect', options)\n\n    const opts = { context: 'connect', options }\n\n    // Let's check requirements after plugin had the chance to modify the options\n    this.checkPluginRequirements(opts)\n\n    const browser = await this.pptr.connect(options)\n    this._patchPageCreationMethods(browser as BrowserInternals)\n\n    await this.callPlugins('_bindBrowserEvents', browser, opts)\n    return browser\n  }\n\n  /**\n   * The default flags that Chromium will be launched with.\n   *\n   * @param options - See [puppeteer docs](https://github.com/puppeteer/puppeteer/blob/master/docs/api.md#puppeteerdefaultargsoptions).\n   */\n  defaultArgs(\n    options?: Parameters<VanillaPuppeteer['defaultArgs']>[0]\n  ): ReturnType<VanillaPuppeteer['defaultArgs']> {\n    return this.pptr.defaultArgs(options)\n  }\n\n  /** Path where Puppeteer expects to find bundled Chromium. */\n  executablePath(): string {\n    return this.pptr.executablePath()\n  }\n\n  /**\n   * This methods attaches Puppeteer to an existing Chromium instance.\n   *\n   * @param options - See [puppeteer docs](https://github.com/puppeteer/puppeteer/blob/master/docs/api.md#puppeteercreatebrowserfetcheroptions).\n   */\n  createBrowserFetcher(\n    options: Parameters<VanillaPuppeteer['createBrowserFetcher']>[0]\n  ): ReturnType<VanillaPuppeteer['createBrowserFetcher']> {\n    return this.pptr.createBrowserFetcher(options)\n  }\n\n  /**\n   * Patch page creation methods (both regular and incognito contexts).\n   *\n   * Unfortunately it's possible that the `targetcreated` events are not triggered\n   * early enough for listeners (e.g. plugins using `onPageCreated`) to be able to\n   * modify the page instance (e.g. user-agent) before the browser request occurs.\n   *\n   * This only affects the first request of a newly created page target.\n   *\n   * As a workaround I've noticed that navigating to `about:blank` (again),\n   * right after a page has been created reliably fixes this issue and adds\n   * no noticable delay or side-effects.\n   *\n   * This problem is not specific to `puppeteer-extra` but default Puppeteer behaviour.\n   *\n   * Note: This patch only fixes explicitly created pages, implicitly created ones\n   * (e.g. through `window.open`) are still subject to this issue. I didn't find a\n   * reliable mitigation for implicitly created pages yet.\n   *\n   * Puppeteer issues:\n   * https://github.com/GoogleChrome/puppeteer/issues/2669\n   * https://github.com/puppeteer/puppeteer/issues/3667\n   * https://github.com/GoogleChrome/puppeteer/issues/386#issuecomment-343059315\n   * https://github.com/GoogleChrome/puppeteer/issues/1378#issue-273733905\n   *\n   * @private\n   */\n  private _patchPageCreationMethods(browser: BrowserInternals) {\n    if (!browser._createPageInContext) {\n      debug(\n        'warning: _patchPageCreationMethods failed (no browser._createPageInContext)'\n      )\n      return\n    }\n    browser._createPageInContext = (function(originalMethod, context) {\n      return async function() {\n        const page = await originalMethod.apply(context, arguments as any)\n        await page.goto('about:blank')\n        return page\n      }\n    })(browser._createPageInContext, browser)\n  }\n\n  /**\n   * Get a list of all registered plugins.\n   *\n   * @member {Array<PuppeteerExtraPlugin>}\n   */\n  get plugins() {\n    return this._plugins\n  }\n\n  /**\n   * Get the names of all registered plugins.\n   *\n   * @member {Array<string>}\n   * @private\n   */\n  get pluginNames() {\n    return this._plugins.map(p => p.name)\n  }\n\n  /**\n   * Collects the exposed `data` property of all registered plugins.\n   * Will be reduced/flattened to a single array.\n   *\n   * Can be accessed by plugins that listed the `dataFromPlugins` requirement.\n   *\n   * Implemented mainly for plugins that need data from other plugins (e.g. `user-preferences`).\n   *\n   * @see [PuppeteerExtraPlugin]/data\n   * @param name - Filter data by optional plugin name\n   *\n   * @private\n   */\n  public getPluginData(name?: string) {\n    const data = this._plugins\n      .map(p => (Array.isArray(p.data) ? p.data : [p.data]))\n      .reduce((acc, arr) => [...acc, ...arr], [])\n    return name ? data.filter((d: any) => d.name === name) : data\n  }\n\n  /**\n   * Get all plugins that feature a given property/class method.\n   *\n   * @private\n   */\n  private getPluginsByProp(prop: string): PuppeteerExtraPlugin[] {\n    return this._plugins.filter(plugin => prop in plugin)\n  }\n\n  /**\n   * Lightweight plugin dependency management to require plugins and code mods on demand.\n   *\n   * This uses the `dependencies` stanza (a `Set`) exposed by `puppeteer-extra` plugins.\n   *\n   * @todo Allow objects as depdencies that contains opts for the requested plugin.\n   *\n   * @private\n   */\n  private resolvePluginDependencies() {\n    // Request missing dependencies from all plugins and flatten to a single Set\n    const missingPlugins = this._plugins\n      .map(p => p._getMissingDependencies(this._plugins))\n      .reduce((combined, list) => {\n        return new Set([...combined, ...list])\n      }, new Set())\n    if (!missingPlugins.size) {\n      debug('no dependencies are missing')\n      return\n    }\n    debug('dependencies missing', missingPlugins)\n    // Loop through all dependencies declared missing by plugins\n    for (let name of [...missingPlugins]) {\n      // Check if the dependency hasn't been registered as plugin already.\n      // This might happen when multiple plugins have nested dependencies.\n      if (this.pluginNames.includes(name)) {\n        debug(`ignoring dependency '${name}', which has been required already.`)\n        continue\n      }\n      // We follow a plugin naming convention, but let's rather enforce it <3\n      name = name.startsWith('puppeteer-extra-plugin')\n        ? name\n        : `puppeteer-extra-plugin-${name}`\n      // In case a module sub resource is requested print out the main package name\n      // e.g. puppeteer-extra-plugin-stealth/evasions/console.debug => puppeteer-extra-plugin-stealth\n      const packageName = name.split('/')[0]\n      let dep = null\n      try {\n        // Try to require and instantiate the stated dependency\n        dep = require(name)()\n        // Register it with `puppeteer-extra` as plugin\n        this.use(dep)\n      } catch (err) {\n        console.warn(`\n          A plugin listed '${name}' as dependency,\n          which is currently missing. Please install it:\n\n          yarn add ${packageName}\n\n          Note: You don't need to require the plugin yourself,\n          unless you want to modify it's default settings.\n          `)\n        throw err\n      }\n      // Handle nested dependencies :D\n      if (dep.dependencies.size) {\n        this.resolvePluginDependencies()\n      }\n    }\n  }\n\n  /**\n   * Order plugins that have expressed a special placement requirement.\n   *\n   * This is useful/necessary for e.g. plugins that depend on the data from other plugins.\n   *\n   * @todo Support more than 'runLast'.\n   * @todo If there are multiple plugins defining 'runLast', sort them depending on who depends on whom. :D\n   *\n   * @private\n   */\n  private orderPlugins() {\n    debug('orderPlugins:before', this.pluginNames)\n    const runLast = this._plugins\n      .filter(p => p.requirements.has('runLast'))\n      .map(p => p.name)\n    for (const name of runLast) {\n      const index = this._plugins.findIndex(p => p.name === name)\n      this._plugins.push(this._plugins.splice(index, 1)[0])\n    }\n    debug('orderPlugins:after', this.pluginNames)\n  }\n\n  /**\n   * Lightweight plugin requirement checking.\n   *\n   * The main intent is to notify the user when a plugin won't work as expected.\n   *\n   * @todo This could be improved, e.g. be evaluated by the plugin base class.\n   *\n   * @private\n   */\n  private checkPluginRequirements(opts = {} as any) {\n    for (const plugin of this._plugins) {\n      for (const requirement of plugin.requirements) {\n        if (\n          opts.context === 'launch' &&\n          requirement === 'headful' &&\n          opts.options.headless\n        ) {\n          console.warn(\n            `Warning: Plugin '${plugin.name}' is not supported in headless mode.`\n          )\n        }\n        if (opts.context === 'connect' && requirement === 'launch') {\n          console.warn(\n            `Warning: Plugin '${plugin.name}' doesn't support puppeteer.connect().`\n          )\n        }\n      }\n    }\n  }\n\n  /**\n   * Call plugins sequentially with the same values.\n   * Plugins that expose the supplied property will be called.\n   *\n   * @param prop - The plugin property to call\n   * @param values - Any number of values\n   * @private\n   */\n  private async callPlugins(prop: string, ...values: any[]) {\n    for (const plugin of this.getPluginsByProp(prop)) {\n      await plugin[prop].apply(plugin, values)\n    }\n  }\n\n  /**\n   * Call plugins sequentially and pass on a value (waterfall style).\n   * Plugins that expose the supplied property will be called.\n   *\n   * The plugins can either modify the value or return an updated one.\n   * Will return the latest, updated value which ran through all plugins.\n   *\n   * @param prop - The plugin property to call\n   * @param value - Any value\n   * @return The new updated value\n   * @private\n   */\n  private async callPluginsWithValue(prop: string, value: any) {\n    for (const plugin of this.getPluginsByProp(prop)) {\n      const newValue = await plugin[prop](value)\n      if (newValue) {\n        value = newValue\n      }\n    }\n    return value\n  }\n}\n\n/**\n * The **default export** will behave exactly the same as the regular puppeteer\n * (just with extra plugin functionality) and can be used as a drop-in replacement.\n *\n * Behind the scenes it will try to require either `puppeteer`\n * or [`puppeteer-core`](https://github.com/puppeteer/puppeteer/blob/master/docs/api.md#puppeteer-vs-puppeteer-core)\n * from the installed dependencies.\n *\n * @example\n * // javascript import\n * const puppeteer = require('puppeteer-extra')\n *\n * // typescript/es6 module import\n * import puppeteer from 'puppeteer-extra'\n *\n * // Add plugins\n * puppeteer.use(...)\n */\nconst defaultExport: PuppeteerExtra = (() => {\n  return new PuppeteerExtra(...requireVanillaPuppeteer())\n})()\n\nexport default defaultExport\n\n/**\n * An **alternative way** to use `puppeteer-extra`: Augments the provided puppeteer with extra plugin functionality.\n *\n * This is useful in case you need multiple puppeteer instances with different plugins or to add plugins to a non-standard puppeteer package.\n *\n * @example\n * // js import\n * const { addExtra } = require('puppeteer-extra')\n *\n * // ts/es6 import\n * import { addExtra } from 'puppeteer-extra'\n *\n * // Patch e.g. puppeteer-firefox and add plugins\n * const puppeteer = addExtra(require('puppeteer-firefox'))\n * puppeteer.use(...)\n *\n * @param puppeteer Any puppeteer API-compatible puppeteer implementation or version.\n * @return A fresh PuppeteerExtra instance using the provided puppeteer\n */\nexport const addExtra = (puppeteer: VanillaPuppeteer): PuppeteerExtra =>\n  new PuppeteerExtra(puppeteer)\n\n/**\n * Attempt to require puppeteer or puppeteer-core from dependencies.\n * To stay backwards compatible with the existing default export we have to do some gymnastics here.\n *\n * @return Either a Puppeteer instance or an Error, which we'll throw later if need be.\n * @private\n */\nfunction requireVanillaPuppeteer(): [VanillaPuppeteer?, Error?] {\n  try {\n    return [require('puppeteer'), undefined]\n  } catch (_) {\n    // noop\n  }\n  try {\n    return [require('puppeteer-core'), undefined]\n  } catch (err) {\n    return [undefined, err as Error]\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAIA,MAAM,KAAK,GAAG,KAAK,CAAC,iBAAiB,CAAC,CAAA;AAmCtC;;;;;;;;;;;;;;;;;;;;;;;MAuBa,cAAc;IAGzB,YACU,KAAwB,EACxB,aAAqB;QADrB,UAAK,GAAL,KAAK,CAAmB;QACxB,kBAAa,GAAb,aAAa,CAAQ;QAJvB,aAAQ,GAA2B,EAAE,CAAA;KAKzC;;;;;;;;;;;IAYJ,GAAG,CAAC,MAA4B;QAC9B,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE;YACjE,OAAO,CAAC,KAAK,CACX,qEAAqE,EACrE,MAAM,CACP,CAAA;YACD,OAAO,IAAI,CAAA;SACZ;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YAChB,OAAO,CAAC,KAAK,CACX,qDAAqD,EACrD,MAAM,CACP,CAAA;YACD,OAAO,IAAI,CAAA;SACZ;QACD,IAAI,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;YAC9C,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAC1D;QACD,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAA;QAC/C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC1B,KAAK,CAAC,mBAAmB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA;QACvC,OAAO,IAAI,CAAA;KACZ;;;;;;;;IASD,IAAI,IAAI;QACN,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,KAAK,CAAA;SAClB;;QAGD,OAAO,CAAC,IAAI,CAAC;;;;;;;;;;KAUZ,CAAC,CAAA;QACF,MAAM,IAAI,CAAC,aAAa,IAAI,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAA;KACzE;;;;;;;;;;;;;;;;;IAkBD,MAAM,MAAM,CACV,OAAmD;;QAGnD,MAAM,oBAAoB,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,CAAA;QACzC,OAAO,GAAG,KAAK,CAAC,oBAAoB,EAAE,OAAO,IAAI,EAAE,CAAC,CAAA;QACpD,IAAI,CAAC,yBAAyB,EAAE,CAAA;QAChC,IAAI,CAAC,YAAY,EAAE,CAAA;;QAGnB,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAA;QAElE,MAAM,IAAI,GAAG;YACX,OAAO,EAAE,QAAQ;YACjB,OAAO;YACP,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAA;;QAGD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAA;QAElC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QAC/C,IAAI,CAAC,yBAAyB,CAAC,OAA2B,CAAC,CAAA;QAE3D,MAAM,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;QAC3D,OAAO,OAAO,CAAA;KACf;;;;;;;;;;;IAYD,MAAM,OAAO,CACX,OAAmD;QAEnD,IAAI,CAAC,yBAAyB,EAAE,CAAA;QAChC,IAAI,CAAC,YAAY,EAAE,CAAA;;QAGnB,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAA;QAEnE,MAAM,IAAI,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,CAAA;;QAG5C,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAA;QAElC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAChD,IAAI,CAAC,yBAAyB,CAAC,OAA2B,CAAC,CAAA;QAE3D,MAAM,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;QAC3D,OAAO,OAAO,CAAA;KACf;;;;;;IAOD,WAAW,CACT,OAAwD;QAExD,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;KACtC;;IAGD,cAAc;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAA;KAClC;;;;;;IAOD,oBAAoB,CAClB,OAAgE;QAEhE,OAAO,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAA;KAC/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6BO,yBAAyB,CAAC,OAAyB;QACzD,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;YACjC,KAAK,CACH,6EAA6E,CAC9E,CAAA;YACD,OAAM;SACP;QACD,OAAO,CAAC,oBAAoB,GAAG,CAAC,UAAS,cAAc,EAAE,OAAO;YAC9D,OAAO;gBACL,MAAM,IAAI,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,SAAgB,CAAC,CAAA;gBAClE,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;gBAC9B,OAAO,IAAI,CAAA;aACZ,CAAA;SACF,EAAE,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAA;KAC1C;;;;;;IAOD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAA;KACrB;;;;;;;IAQD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAA;KACtC;;;;;;;;;;;;;;IAeM,aAAa,CAAC,IAAa;QAChC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ;aACvB,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;aACrD,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAA;QAC7C,OAAO,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAM,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,CAAA;KAC9D;;;;;;IAOO,gBAAgB,CAAC,IAAY;QACnC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,IAAI,MAAM,CAAC,CAAA;KACtD;;;;;;;;;;IAWO,yBAAyB;;QAE/B,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ;aACjC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAClD,MAAM,CAAC,CAAC,QAAQ,EAAE,IAAI;YACrB,OAAO,IAAI,GAAG,CAAC,CAAC,GAAG,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC,CAAA;SACvC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAA;QACf,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;YACxB,KAAK,CAAC,6BAA6B,CAAC,CAAA;YACpC,OAAM;SACP;QACD,KAAK,CAAC,sBAAsB,EAAE,cAAc,CAAC,CAAA;;QAE7C,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,cAAc,CAAC,EAAE;;;YAGpC,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACnC,KAAK,CAAC,wBAAwB,IAAI,qCAAqC,CAAC,CAAA;gBACxE,SAAQ;aACT;;YAED,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC;kBAC5C,IAAI;kBACJ,0BAA0B,IAAI,EAAE,CAAA;;;YAGpC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YACtC,IAAI,GAAG,GAAG,IAAI,CAAA;YACd,IAAI;;gBAEF,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAA;;gBAErB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;aACd;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,CAAC,IAAI,CAAC;6BACQ,IAAI;;;qBAGZ,WAAW;;;;WAIrB,CAAC,CAAA;gBACJ,MAAM,GAAG,CAAA;aACV;;YAED,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;gBACzB,IAAI,CAAC,yBAAyB,EAAE,CAAA;aACjC;SACF;KACF;;;;;;;;;;;IAYO,YAAY;QAClB,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ;aAC1B,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC1C,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAA;QACnB,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAA;YAC3D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SACtD;QACD,KAAK,CAAC,oBAAoB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;KAC9C;;;;;;;;;;IAWO,uBAAuB,CAAC,OAAO,EAAS;QAC9C,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClC,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,YAAY,EAAE;gBAC7C,IACE,IAAI,CAAC,OAAO,KAAK,QAAQ;oBACzB,WAAW,KAAK,SAAS;oBACzB,IAAI,CAAC,OAAO,CAAC,QAAQ,EACrB;oBACA,OAAO,CAAC,IAAI,CACV,oBAAoB,MAAM,CAAC,IAAI,sCAAsC,CACtE,CAAA;iBACF;gBACD,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,WAAW,KAAK,QAAQ,EAAE;oBAC1D,OAAO,CAAC,IAAI,CACV,oBAAoB,MAAM,CAAC,IAAI,wCAAwC,CACxE,CAAA;iBACF;aACF;SACF;KACF;;;;;;;;;IAUO,MAAM,WAAW,CAAC,IAAY,EAAE,GAAG,MAAa;QACtD,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAChD,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;SACzC;KACF;;;;;;;;;;;;;IAcO,MAAM,oBAAoB,CAAC,IAAY,EAAE,KAAU;QACzD,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAChD,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAA;YAC1C,IAAI,QAAQ,EAAE;gBACZ,KAAK,GAAG,QAAQ,CAAA;aACjB;SACF;QACD,OAAO,KAAK,CAAA;KACb;CACF;AAED;;;;;;;;;;;;;;;;;;AAkBA,MAAM,aAAa,GAAmB,CAAC;IACrC,OAAO,IAAI,cAAc,CAAC,GAAG,uBAAuB,EAAE,CAAC,CAAA;AACzD,CAAC,GAAG,CAAA;AAIJ;;;;;;;;;;;;;;;;;;;MAmBa,QAAQ,GAAG,CAAC,SAA2B,KAClD,IAAI,cAAc,CAAC,SAAS,EAAC;AAE/B;;;;;;;AAOA,SAAS,uBAAuB;IAC9B,IAAI;QACF,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,CAAA;KACzC;IAAC,OAAO,CAAC,EAAE;;KAEX;IACD,IAAI;QACF,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,SAAS,CAAC,CAAA;KAC9C;IAAC,OAAO,GAAG,EAAE;QACZ,OAAO,CAAC,SAAS,EAAE,GAAY,CAAC,CAAA;KACjC;AACH;;;;;;;;;;;;;"}