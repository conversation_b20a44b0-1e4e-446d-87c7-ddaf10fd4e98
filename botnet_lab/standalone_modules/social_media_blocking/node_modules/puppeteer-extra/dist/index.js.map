{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;AAGA,kDAAyB;AACzB,MAAM,KAAK,GAAG,IAAA,eAAK,EAAC,iBAAiB,CAAC,CAAA;AAEtC,0DAA6B;AAiC7B;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAa,cAAc;IAGzB,YACU,KAAwB,EACxB,aAAqB;QADrB,UAAK,GAAL,KAAK,CAAmB;QACxB,kBAAa,GAAb,aAAa,CAAQ;QAJvB,aAAQ,GAA2B,EAAE,CAAA;IAK1C,CAAC;IAEJ;;;;;;;;;OASG;IACH,GAAG,CAAC,MAA4B;QAC9B,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE;YACjE,OAAO,CAAC,KAAK,CACX,qEAAqE,EACrE,MAAM,CACP,CAAA;YACD,OAAO,IAAI,CAAA;SACZ;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YAChB,OAAO,CAAC,KAAK,CACX,qDAAqD,EACrD,MAAM,CACP,CAAA;YACD,OAAO,IAAI,CAAA;SACZ;QACD,IAAI,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;YAC9C,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAC1D;QACD,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAA;QAC/C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC1B,KAAK,CAAC,mBAAmB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA;QACvC,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,IAAI,IAAI;QACN,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,KAAK,CAAA;SAClB;QAED,WAAW;QACX,OAAO,CAAC,IAAI,CAAC;;;;;;;;;;KAUZ,CAAC,CAAA;QACF,MAAM,IAAI,CAAC,aAAa,IAAI,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAA;IAC1E,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,KAAK,CAAC,MAAM,CACV,OAAmD;QAEnD,sEAAsE;QACtE,MAAM,oBAAoB,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,CAAA;QACzC,OAAO,GAAG,IAAA,mBAAK,EAAC,oBAAoB,EAAE,OAAO,IAAI,EAAE,CAAC,CAAA;QACpD,IAAI,CAAC,yBAAyB,EAAE,CAAA;QAChC,IAAI,CAAC,YAAY,EAAE,CAAA;QAEnB,8DAA8D;QAC9D,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAA;QAElE,MAAM,IAAI,GAAG;YACX,OAAO,EAAE,QAAQ;YACjB,OAAO;YACP,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAA;QAED,6EAA6E;QAC7E,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAA;QAElC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QAC/C,IAAI,CAAC,yBAAyB,CAAC,OAA2B,CAAC,CAAA;QAE3D,MAAM,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;QAC3D,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,OAAO,CACX,OAAmD;QAEnD,IAAI,CAAC,yBAAyB,EAAE,CAAA;QAChC,IAAI,CAAC,YAAY,EAAE,CAAA;QAEnB,+DAA+D;QAC/D,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAA;QAEnE,MAAM,IAAI,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,CAAA;QAE5C,6EAA6E;QAC7E,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAA;QAElC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAChD,IAAI,CAAC,yBAAyB,CAAC,OAA2B,CAAC,CAAA;QAE3D,MAAM,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;QAC3D,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;;;OAIG;IACH,WAAW,CACT,OAAwD;QAExD,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;IACvC,CAAC;IAED,6DAA6D;IAC7D,cAAc;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAA;IACnC,CAAC;IAED;;;;OAIG;IACH,oBAAoB,CAClB,OAAgE;QAEhE,OAAO,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAA;IAChD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACK,yBAAyB,CAAC,OAAyB;QACzD,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;YACjC,KAAK,CACH,6EAA6E,CAC9E,CAAA;YACD,OAAM;SACP;QACD,OAAO,CAAC,oBAAoB,GAAG,CAAC,UAAS,cAAc,EAAE,OAAO;YAC9D,OAAO,KAAK;gBACV,MAAM,IAAI,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,SAAgB,CAAC,CAAA;gBAClE,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;gBAC9B,OAAO,IAAI,CAAA;YACb,CAAC,CAAA;QACH,CAAC,CAAC,CAAC,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAA;IAC3C,CAAC;IAED;;;;OAIG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAA;IACtB,CAAC;IAED;;;;;OAKG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IACvC,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,aAAa,CAAC,IAAa;QAChC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ;aACvB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;aACrD,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAA;QAC7C,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAC/D,CAAC;IAED;;;;OAIG;IACK,gBAAgB,CAAC,IAAY;QACnC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI,MAAM,CAAC,CAAA;IACvD,CAAC;IAED;;;;;;;;OAQG;IACK,yBAAyB;QAC/B,4EAA4E;QAC5E,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ;aACjC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAClD,MAAM,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE;YACzB,OAAO,IAAI,GAAG,CAAC,CAAC,GAAG,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC,CAAA;QACxC,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAA;QACf,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;YACxB,KAAK,CAAC,6BAA6B,CAAC,CAAA;YACpC,OAAM;SACP;QACD,KAAK,CAAC,sBAAsB,EAAE,cAAc,CAAC,CAAA;QAC7C,4DAA4D;QAC5D,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,cAAc,CAAC,EAAE;YACpC,oEAAoE;YACpE,oEAAoE;YACpE,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACnC,KAAK,CAAC,wBAAwB,IAAI,qCAAqC,CAAC,CAAA;gBACxE,SAAQ;aACT;YACD,uEAAuE;YACvE,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC;gBAC9C,CAAC,CAAC,IAAI;gBACN,CAAC,CAAC,0BAA0B,IAAI,EAAE,CAAA;YACpC,6EAA6E;YAC7E,+FAA+F;YAC/F,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YACtC,IAAI,GAAG,GAAG,IAAI,CAAA;YACd,IAAI;gBACF,uDAAuD;gBACvD,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAA;gBACrB,+CAA+C;gBAC/C,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;aACd;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,CAAC,IAAI,CAAC;6BACQ,IAAI;;;qBAGZ,WAAW;;;;WAIrB,CAAC,CAAA;gBACJ,MAAM,GAAG,CAAA;aACV;YACD,gCAAgC;YAChC,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;gBACzB,IAAI,CAAC,yBAAyB,EAAE,CAAA;aACjC;SACF;IACH,CAAC;IAED;;;;;;;;;OASG;IACK,YAAY;QAClB,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ;aAC1B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC1C,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;QACnB,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAA;YAC3D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SACtD;QACD,KAAK,CAAC,oBAAoB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;;;;;;;OAQG;IACK,uBAAuB,CAAC,OAAO,EAAS;QAC9C,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClC,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,YAAY,EAAE;gBAC7C,IACE,IAAI,CAAC,OAAO,KAAK,QAAQ;oBACzB,WAAW,KAAK,SAAS;oBACzB,IAAI,CAAC,OAAO,CAAC,QAAQ,EACrB;oBACA,OAAO,CAAC,IAAI,CACV,oBAAoB,MAAM,CAAC,IAAI,sCAAsC,CACtE,CAAA;iBACF;gBACD,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,WAAW,KAAK,QAAQ,EAAE;oBAC1D,OAAO,CAAC,IAAI,CACV,oBAAoB,MAAM,CAAC,IAAI,wCAAwC,CACxE,CAAA;iBACF;aACF;SACF;IACH,CAAC;IAED;;;;;;;OAOG;IACK,KAAK,CAAC,WAAW,CAAC,IAAY,EAAE,GAAG,MAAa;QACtD,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAChD,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;SACzC;IACH,CAAC;IAED;;;;;;;;;;;OAWG;IACK,KAAK,CAAC,oBAAoB,CAAC,IAAY,EAAE,KAAU;QACzD,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAChD,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAA;YAC1C,IAAI,QAAQ,EAAE;gBACZ,KAAK,GAAG,QAAQ,CAAA;aACjB;SACF;QACD,OAAO,KAAK,CAAA;IACd,CAAC;CACF;AA1ZD,wCA0ZC;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,aAAa,GAAmB,CAAC,GAAG,EAAE;IAC1C,OAAO,IAAI,cAAc,CAAC,GAAG,uBAAuB,EAAE,CAAC,CAAA;AACzD,CAAC,CAAC,EAAE,CAAA;AAEJ,kBAAe,aAAa,CAAA;AAE5B;;;;;;;;;;;;;;;;;;GAkBG;AACI,MAAM,QAAQ,GAAG,CAAC,SAA2B,EAAkB,EAAE,CACtE,IAAI,cAAc,CAAC,SAAS,CAAC,CAAA;AADlB,QAAA,QAAQ,YACU;AAE/B;;;;;;GAMG;AACH,SAAS,uBAAuB;IAC9B,IAAI;QACF,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,CAAA;KACzC;IAAC,OAAO,CAAC,EAAE;QACV,OAAO;KACR;IACD,IAAI;QACF,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,SAAS,CAAC,CAAA;KAC9C;IAAC,OAAO,GAAG,EAAE;QACZ,OAAO,CAAC,SAAS,EAAE,GAAY,CAAC,CAAA;KACjC;AACH,CAAC"}