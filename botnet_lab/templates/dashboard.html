<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C2 Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            line-height: 1.6;
        }
        
        .header {
            background-color: #2d2d2d;
            padding: 1rem 2rem;
            border-bottom: 2px solid #444;
        }
        
        .header h1 {
            color: #00ff00;
            font-size: 2rem;
            text-shadow: 0 0 10px #00ff00;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background-color: #2d2d2d;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #444;
            text-align: center;
        }
        
        .stat-card h3 {
            color: #00ff00;
            margin-bottom: 0.5rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #ffffff;
        }
        
        .clients-section {
            background-color: #2d2d2d;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #444;
            margin-bottom: 2rem;
        }
        
        .section-title {
            color: #00ff00;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid #444;
            padding-bottom: 0.5rem;
        }
        
        .client-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .client-table th,
        .client-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        
        .client-table th {
            background-color: #1a1a1a;
            color: #00ff00;
            font-weight: bold;
        }
        
        .client-table tr:hover {
            background-color: #3d3d3d;
        }
        
        .status-online {
            color: #00ff00;
            font-weight: bold;
        }
        
        .status-offline {
            color: #ff4444;
            font-weight: bold;
        }
        
        .command-section {
            background-color: #2d2d2d;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #444;
        }
        
        .command-form {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .command-input {
            flex: 1;
            padding: 0.75rem;
            background-color: #1a1a1a;
            border: 1px solid #444;
            border-radius: 4px;
            color: #ffffff;
            font-family: 'Courier New', monospace;
        }
        
        .command-button {
            padding: 0.75rem 1.5rem;
            background-color: #00ff00;
            color: #000000;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        
        .command-button:hover {
            background-color: #00cc00;
        }
        
        .log-section {
            background-color: #2d2d2d;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #444;
            margin-top: 2rem;
        }
        
        .log-output {
            background-color: #1a1a1a;
            padding: 1rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            height: 200px;
            overflow-y: auto;
            border: 1px solid #444;
        }
        
        .refresh-button {
            background-color: #444;
            color: #ffffff;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 1rem;
        }
        
        .refresh-button:hover {
            background-color: #555;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔴 C2 Command & Control Dashboard</h1>
    </div>
    
    <div class="container">
        <!-- Statistics Section -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3>Active Bots</h3>
                <div class="stat-number" id="active-bots">0</div>
            </div>
            <div class="stat-card">
                <h3>Total Connections</h3>
                <div class="stat-number" id="total-connections">0</div>
            </div>
            <div class="stat-card">
                <h3>Commands Sent</h3>
                <div class="stat-number" id="commands-sent">0</div>
            </div>
            <div class="stat-card">
                <h3>Server Uptime</h3>
                <div class="stat-number" id="uptime">00:00:00</div>
            </div>
        </div>
        
        <!-- Connected Clients Section -->
        <div class="clients-section">
            <h2 class="section-title">Connected Clients</h2>
            <button class="refresh-button" onclick="refreshClients()">Refresh</button>
            <table class="client-table">
                <thead>
                    <tr>
                        <th>Client ID</th>
                        <th>IP Address</th>
                        <th>Status</th>
                        <th>Connected At</th>
                        <th>Last Seen</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="clients-tbody">
                    <tr>
                        <td colspan="6" style="text-align: center; color: #888;">No clients connected</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Command Section -->
        <div class="command-section">
            <h2 class="section-title">Send Command</h2>
            <div class="command-form">
                <select id="client-select" class="command-input">
                    <option value="">Select Client</option>
                </select>
                <input type="text" id="command-input" class="command-input" placeholder="Enter command...">
                <button class="command-button" onclick="sendCommand()">Send</button>
            </div>
        </div>
        
        <!-- Log Section -->
        <div class="log-section">
            <h2 class="section-title">Activity Log</h2>
            <div class="log-output" id="log-output">
                <div>[INFO] C2 Dashboard initialized</div>
                <div>[INFO] Waiting for client connections...</div>
            </div>
        </div>
    </div>
    
    <script>
        // Dashboard JavaScript functionality
        let startTime = new Date();
        let commandCount = 0;
        
        function updateUptime() {
            const now = new Date();
            const diff = now - startTime;
            const hours = Math.floor(diff / 3600000);
            const minutes = Math.floor((diff % 3600000) / 60000);
            const seconds = Math.floor((diff % 60000) / 1000);
            
            document.getElementById('uptime').textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        
        function refreshClients() {
            // Simulate client data - in real implementation, this would fetch from server
            const tbody = document.getElementById('clients-tbody');
            tbody.innerHTML = `
                <tr>
                    <td>bot_001</td>
                    <td>*************</td>
                    <td><span class="status-online">ONLINE</span></td>
                    <td>2024-01-15 10:30:15</td>
                    <td>2024-01-15 10:35:22</td>
                    <td><button onclick="selectClient('bot_001')">Select</button></td>
                </tr>
            `;
            
            document.getElementById('active-bots').textContent = '1';
            document.getElementById('total-connections').textContent = '1';
        }
        
        function selectClient(clientId) {
            document.getElementById('client-select').value = clientId;
        }
        
        function sendCommand() {
            const client = document.getElementById('client-select').value;
            const command = document.getElementById('command-input').value;
            
            if (!client || !command) {
                alert('Please select a client and enter a command');
                return;
            }
            
            commandCount++;
            document.getElementById('commands-sent').textContent = commandCount;
            
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `<div>[${timestamp}] Command sent to ${client}: ${command}</div>`;
            logOutput.scrollTop = logOutput.scrollHeight;
            
            document.getElementById('command-input').value = '';
        }
        
        // Update uptime every second
        setInterval(updateUptime, 1000);
        
        // Initialize dashboard
        refreshClients();
    </script>
</body>
</html>
