#!/usr/bin/env python3
# Performance Testing Script for Enhanced C2 Server

import socket
import threading
import time
import json
import statistics
from datetime import datetime

class PerformanceTester:
    def __init__(self, server_host='localhost', server_port=8080):
        self.server_host = server_host
        self.server_port = server_port
        self.results = {
            'connection_times': [],
            'response_times': [],
            'successful_connections': 0,
            'failed_connections': 0,
            'total_messages_sent': 0,
            'total_messages_received': 0
        }
        
    def test_single_connection(self, test_id):
        """Test a single connection performance"""
        start_time = time.time()
        
        try:
            # Create connection
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            
            connect_start = time.time()
            sock.connect((self.server_host, self.server_port))
            connect_time = time.time() - connect_start
            
            self.results['connection_times'].append(connect_time)
            self.results['successful_connections'] += 1
            
            # Send test message
            test_message = {
                'type': 'heartbeat',
                'test_id': test_id,
                'timestamp': datetime.now().isoformat()
            }
            
            message_start = time.time()
            sock.send(json.dumps(test_message).encode('utf-8'))
            self.results['total_messages_sent'] += 1
            
            # Receive response
            response = sock.recv(1024).decode('utf-8')
            response_time = time.time() - message_start
            
            if response:
                self.results['response_times'].append(response_time)
                self.results['total_messages_received'] += 1
            
            sock.close()
            
        except Exception as e:
            self.results['failed_connections'] += 1
            print(f"Connection {test_id} failed: {e}")
    
    def test_concurrent_connections(self, num_connections=100):
        """Test multiple concurrent connections"""
        print(f"Testing {num_connections} concurrent connections...")
        
        threads = []
        start_time = time.time()
        
        for i in range(num_connections):
            thread = threading.Thread(
                target=self.test_single_connection,
                args=(i,)
            )
            threads.append(thread)
            thread.start()
            
            # Small delay to avoid overwhelming the server
            time.sleep(0.01)
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        print(f"Concurrent test completed in {total_time:.2f} seconds")
        return total_time
    
    def test_sustained_load(self, duration_seconds=60, connections_per_second=10):
        """Test sustained load over time"""
        print(f"Testing sustained load for {duration_seconds} seconds...")
        print(f"Target: {connections_per_second} connections per second")
        
        start_time = time.time()
        connection_count = 0
        
        while time.time() - start_time < duration_seconds:
            batch_start = time.time()
            
            # Create batch of connections
            threads = []
            for i in range(connections_per_second):
                thread = threading.Thread(
                    target=self.test_single_connection,
                    args=(connection_count + i,)
                )
                threads.append(thread)
                thread.start()
            
            connection_count += connections_per_second
            
            # Wait for batch to complete
            for thread in threads:
                thread.join()
            
            # Maintain rate
            batch_time = time.time() - batch_start
            if batch_time < 1.0:
                time.sleep(1.0 - batch_time)
        
        actual_duration = time.time() - start_time
        actual_rate = connection_count / actual_duration
        
        print(f"Sustained test completed:")
        print(f"  Duration: {actual_duration:.2f} seconds")
        print(f"  Actual rate: {actual_rate:.2f} connections/second")
    
    def generate_report(self):
        """Generate performance report"""
        print("\n" + "="*60)
        print("PERFORMANCE TEST REPORT")
        print("="*60)
        
        # Connection Statistics
        print(f"Total Connections Attempted: {self.results['successful_connections'] + self.results['failed_connections']}")
        print(f"Successful Connections: {self.results['successful_connections']}")
        print(f"Failed Connections: {self.results['failed_connections']}")
        
        if self.results['successful_connections'] > 0:
            success_rate = (self.results['successful_connections'] / 
                          (self.results['successful_connections'] + self.results['failed_connections'])) * 100
            print(f"Success Rate: {success_rate:.2f}%")
        
        # Connection Time Statistics
        if self.results['connection_times']:
            conn_times = self.results['connection_times']
            print(f"\nConnection Time Statistics:")
            print(f"  Average: {statistics.mean(conn_times):.4f} seconds")
            print(f"  Median: {statistics.median(conn_times):.4f} seconds")
            print(f"  Min: {min(conn_times):.4f} seconds")
            print(f"  Max: {max(conn_times):.4f} seconds")
            if len(conn_times) > 1:
                print(f"  Std Dev: {statistics.stdev(conn_times):.4f} seconds")
        
        # Response Time Statistics
        if self.results['response_times']:
            resp_times = self.results['response_times']
            print(f"\nResponse Time Statistics:")
            print(f"  Average: {statistics.mean(resp_times):.4f} seconds")
            print(f"  Median: {statistics.median(resp_times):.4f} seconds")
            print(f"  Min: {min(resp_times):.4f} seconds")
            print(f"  Max: {max(resp_times):.4f} seconds")
            if len(resp_times) > 1:
                print(f"  Std Dev: {statistics.stdev(resp_times):.4f} seconds")
        
        # Message Statistics
        print(f"\nMessage Statistics:")
        print(f"  Messages Sent: {self.results['total_messages_sent']}")
        print(f"  Messages Received: {self.results['total_messages_received']}")
        
        if self.results['total_messages_sent'] > 0:
            message_success_rate = (self.results['total_messages_received'] / 
                                  self.results['total_messages_sent']) * 100
            print(f"  Message Success Rate: {message_success_rate:.2f}%")
        
        print("="*60)

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='C2 Server Performance Tester')
    parser.add_argument('--host', default='localhost', help='Server host')
    parser.add_argument('--port', type=int, default=8080, help='Server port')
    parser.add_argument('--concurrent', type=int, default=100, help='Concurrent connections test')
    parser.add_argument('--sustained', type=int, default=60, help='Sustained test duration (seconds)')
    parser.add_argument('--rate', type=int, default=10, help='Connections per second for sustained test')
    
    args = parser.parse_args()
    
    tester = PerformanceTester(args.host, args.port)
    
    print("Starting C2 Server Performance Tests...")
    print(f"Target Server: {args.host}:{args.port}")
    print("-" * 60)
    
    # Test 1: Concurrent Connections
    tester.test_concurrent_connections(args.concurrent)
    
    # Small delay between tests
    time.sleep(2)
    
    # Test 2: Sustained Load
    tester.test_sustained_load(args.sustained, args.rate)
    
    # Generate Report
    tester.generate_report()

if __name__ == "__main__":
    main()
