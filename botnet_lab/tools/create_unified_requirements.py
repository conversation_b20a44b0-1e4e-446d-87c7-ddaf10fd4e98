#!/usr/bin/env python3
"""
إنشاء ملف requirements.txt موحد لجميع وحدات المشروع
"""

import os
import re
from pathlib import Path
from collections import defaultdict

class RequirementsUnifier:
    def __init__(self, project_path):
        self.project_path = Path(project_path)
        self.all_requirements = defaultdict(set)
        self.requirements_files = []
        
    def find_requirements_files(self):
        """البحث عن جميع ملفات requirements في المشروع"""
        print("🔍 البحث عن ملفات requirements...")
        
        # البحث عن ملفات requirements
        patterns = ['requirements*.txt', 'requirements*.in']
        
        for pattern in patterns:
            files = list(self.project_path.rglob(pattern))
            self.requirements_files.extend(files)
        
        # إزالة المكررات
        self.requirements_files = list(set(self.requirements_files))
        
        print(f"📁 تم العثور على {len(self.requirements_files)} ملف requirements:")
        for file in self.requirements_files:
            print(f"  - {file.relative_to(self.project_path)}")
    
    def parse_requirements_file(self, file_path):
        """تحليل ملف requirements واستخراج التبعيات"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            requirements = set()
            
            for line in lines:
                line = line.strip()
                
                # تجاهل التعليقات والأسطر الفارغة
                if not line or line.startswith('#'):
                    continue
                
                # تجاهل خيارات pip
                if line.startswith('-'):
                    continue
                
                # استخراج اسم الحزمة
                # إزالة شروط النظام والإصدارات
                package = re.split(r'[>=<!=]', line)[0].strip()
                package = re.split(r';', package)[0].strip()
                
                if package:
                    requirements.add(line)  # حفظ السطر كاملاً مع الإصدار
            
            return requirements
            
        except Exception as e:
            print(f"❌ خطأ في تحليل {file_path}: {e}")
            return set()
    
    def scan_python_files_for_imports(self):
        """فحص ملفات Python لاستخراج الاستيرادات"""
        print("\n🔍 فحص ملفات Python للبحث عن الاستيرادات...")
        
        python_files = list(self.project_path.rglob("*.py"))
        imports_found = set()
        
        # قاموس تحويل أسماء الاستيرادات إلى أسماء الحزم
        import_to_package = {
            'cv2': 'opencv-python',
            'PIL': 'Pillow',
            'bs4': 'beautifulsoup4',
            'sklearn': 'scikit-learn',
            'yaml': 'PyYAML',
            'jwt': 'PyJWT',
            'serial': 'pyserial',
            'win32api': 'pywin32',
            'wmi': 'WMI',
        }
        
        for file_path in python_files:
            # تجاهل ملفات معينة
            if any(skip in str(file_path) for skip in ['__pycache__', '.git', 'venv', 'env']):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # البحث عن استيرادات
                import_patterns = [
                    r'^import\s+([a-zA-Z_][a-zA-Z0-9_]*)',
                    r'^from\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+import',
                ]
                
                for pattern in import_patterns:
                    matches = re.findall(pattern, content, re.MULTILINE)
                    for match in matches:
                        # تحويل اسم الاستيراد إلى اسم الحزمة
                        package_name = import_to_package.get(match, match)
                        imports_found.add(package_name)
                        
            except Exception as e:
                continue
        
        return imports_found
    
    def merge_requirements(self):
        """دمج جميع التبعيات"""
        print("\n🔄 دمج جميع التبعيات...")
        
        # جمع التبعيات من ملفات requirements
        all_reqs = set()
        
        for req_file in self.requirements_files:
            reqs = self.parse_requirements_file(req_file)
            all_reqs.update(reqs)
            print(f"  📦 {len(reqs)} تبعية من {req_file.name}")
        
        # إضافة التبعيات المكتشفة من الكود
        code_imports = self.scan_python_files_for_imports()
        
        # التبعيات الأساسية المطلوبة
        essential_packages = {
            'psutil>=5.9.0',
            'paramiko>=3.3.0', 
            'beautifulsoup4>=4.12.0',
            'pynput>=1.7.6',
            'requests>=2.31.0',
            'numpy>=1.24.0',
            'cryptography>=41.0.0',
            'websockets>=11.0.0',
            'flask>=2.3.0',
            'Pillow>=10.0.0',
            'flask-socketio>=5.3.0',
            'sqlalchemy>=2.0.0',
            'colorama>=0.4.6',
            'tqdm>=4.65.0',
            'phonenumbers>=8.13.0',
            'pyautogui>=0.9.54',
            'opencv-python>=4.8.0',
            'lxml>=4.9.0',
            'selenium>=4.15.0',
        }
        
        # دمج جميع التبعيات
        final_requirements = all_reqs.union(essential_packages)
        
        return sorted(final_requirements)
    
    def create_unified_requirements(self):
        """إنشاء ملف requirements.txt موحد"""
        requirements = self.merge_requirements()
        
        # إنشاء محتوى الملف
        content = [
            "# Unified Requirements for Botnet Lab Project",
            "# Generated automatically - includes all dependencies",
            "# Educational and Research Purposes Only",
            "",
            "# Core Dependencies",
        ]
        
        # تصنيف التبعيات
        categories = {
            'Core System': ['psutil', 'requests', 'cryptography'],
            'Network & Communication': ['websockets', 'paramiko', 'flask'],
            'Data Processing': ['numpy', 'beautifulsoup4', 'lxml'],
            'GUI & Automation': ['pynput', 'pyautogui', 'Pillow'],
            'Database & Storage': ['sqlalchemy'],
            'Utilities': ['colorama', 'tqdm', 'phonenumbers'],
            'Advanced Features': ['opencv-python', 'selenium'],
        }
        
        # إضافة التبعيات مصنفة
        for category, packages in categories.items():
            content.append(f"\n# {category}")
            for req in requirements:
                for package in packages:
                    if req.startswith(package):
                        content.append(req)
        
        # إضافة باقي التبعيات
        content.append("\n# Other Dependencies")
        added_packages = set()
        for category_packages in categories.values():
            added_packages.update(category_packages)
        
        for req in requirements:
            package_name = re.split(r'[>=<!=]', req)[0].strip()
            if package_name not in added_packages:
                content.append(req)
        
        # إضافة تبعيات خاصة بالنظام
        content.extend([
            "",
            "# Platform-specific dependencies",
            "pywin32>=306; sys_platform == 'win32'",
            "wmi>=1.5.1; sys_platform == 'win32'",
            "",
            "# Development dependencies (optional)",
            "pytest>=7.4.0",
            "black>=23.7.0",
            "flake8>=6.0.0",
        ])
        
        # كتابة الملف
        output_file = self.project_path / "requirements_unified.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(content))
        
        print(f"\n✅ تم إنشاء ملف requirements موحد: {output_file}")
        print(f"📦 إجمالي التبعيات: {len([line for line in content if line and not line.startswith('#')])}")
        
        return output_file

def main():
    project_path = "/home/<USER>/Desktop/Year3/botnet/botnet_lab"
    
    unifier = RequirementsUnifier(project_path)
    
    # البحث عن ملفات requirements
    unifier.find_requirements_files()
    
    # إنشاء ملف موحد
    unified_file = unifier.create_unified_requirements()
    
    print(f"\n🎉 تم إنشاء ملف requirements موحد بنجاح!")
    print(f"📁 الملف: {unified_file}")

if __name__ == "__main__":
    main()
