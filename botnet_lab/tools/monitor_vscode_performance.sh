#!/bin/bash
# VS Code Performance Monitor
# مراقب أداء VS Code

echo "🔍 مراقبة أداء VS Code..."

# فحص عمليات VS Code
echo "📊 عمليات VS Code النشطة:"
ps aux | grep -i "code" | grep -v grep | head -5

echo ""
echo "💾 استهلاك الذاكرة:"
ps aux | grep -i "code" | grep -v grep | awk '{sum+=$6} END {print "إجمالي استهلاك الذاكرة: " sum/1024 " MB"}'

echo ""
echo "🔄 استهلاك المعالج:"
top -b -n1 | grep -i "code" | head -3

echo ""
echo "📁 حجم مجلد المشروع:"
du -sh .

echo ""
echo "🗂️ عدد الملفات المفتوحة:"
lsof | grep -i "code" | wc -l
