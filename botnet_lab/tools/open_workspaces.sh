#!/bin/bash
# Advanced VS Code Workspace Launcher
# مشغل متقدم لـ workspaces VS Code

echo "🚀 مشغل Workspaces المتقدم لمشروع Botnet Lab"
echo "=" * 50

# الانتقال إلى مجلد المشروع
cd "$(dirname "$0")/.."

echo "📁 المجلد الحالي: $(pwd)"
echo ""

# عرض الخيارات المتاحة
echo "📋 اختر Workspace للفتح:"
echo "1. 🏗️  Core Workspace - النواة الأساسية"
echo "2. 🧩 Modules Workspace - الوحدات المتقدمة"
echo "3. 🔧 Standalone Workspace - الوحدات المستقلة"
echo "4. 🎯 فتح جميع Workspaces"
echo "5. 📊 فحص الأداء قبل الفتح"
echo "6. 🧹 تنظيف سريع ثم فتح"
echo "7. ❌ إلغاء"
echo ""

read -p "اختر رقم (1-7): " choice

case $choice in
    1)
        echo "🏗️ فتح Core Workspace..."
        echo "   يحتوي على: النواة الأساسية، الإعدادات، الاختبارات الأساسية"
        code workspaces/core_workspace.code-workspace
        ;;
    2)
        echo "🧩 فتح Modules Workspace..."
        echo "   يحتوي على: جميع الوحدات المتقدمة، اختبارات الوحدات"
        code workspaces/modules_workspace.code-workspace
        ;;
    3)
        echo "🔧 فتح Standalone Workspace..."
        echo "   يحتوي على: الوحدات المستقلة، RAT Module"
        code workspaces/standalone_workspace.code-workspace
        ;;
    4)
        echo "🎯 فتح جميع Workspaces..."
        echo "   ⚠️ تحذير: قد يستهلك موارد أكثر"
        read -p "هل أنت متأكد؟ (y/N): " confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            echo "   🏗️ فتح Core Workspace..."
            code workspaces/core_workspace.code-workspace
            sleep 2
            echo "   🧩 فتح Modules Workspace..."
            code workspaces/modules_workspace.code-workspace
            sleep 2
            echo "   🔧 فتح Standalone Workspace..."
            code workspaces/standalone_workspace.code-workspace
        else
            echo "   ❌ تم الإلغاء"
        fi
        ;;
    5)
        echo "📊 فحص الأداء..."
        echo ""
        echo "🧠 الذاكرة:"
        free -h | grep "Mem:"
        echo ""
        echo "🔄 المعالج:"
        uptime
        echo ""
        echo "📁 حجم المشروع:"
        du -sh .
        echo ""
        echo "🗂️ عدد الملفات:"
        find . -type f | wc -l
        echo ""
        echo "🐍 ملفات Python:"
        find . -name "*.py" | wc -l
        echo ""
        echo "💾 قواعد البيانات:"
        find . -name "*.db" -o -name "*.sqlite*" | wc -l
        echo ""
        echo "📊 عمليات VS Code النشطة:"
        ps aux | grep -i "code" | grep -v grep | wc -l
        echo ""
        read -p "اضغط Enter للمتابعة..."
        ;;
    6)
        echo "🧹 تشغيل التنظيف السريع..."
        ./tools/quick_cleanup.sh
        echo ""
        echo "🚀 اختر Workspace للفتح بعد التنظيف:"
        echo "1. 🏗️ Core"
        echo "2. 🧩 Modules" 
        echo "3. 🔧 Standalone"
        read -p "اختر (1-3): " ws_choice
        case $ws_choice in
            1) code workspaces/core_workspace.code-workspace ;;
            2) code workspaces/modules_workspace.code-workspace ;;
            3) code workspaces/standalone_workspace.code-workspace ;;
            *) echo "❌ خيار غير صحيح" ;;
        esac
        ;;
    7)
        echo "❌ تم الإلغاء"
        exit 0
        ;;
    *)
        echo "❌ خيار غير صحيح"
        exit 1
        ;;
esac

echo ""
echo "✅ تم تنفيذ الأمر"
echo ""
echo "💡 نصائح للأداء الأفضل:"
echo "   • استخدم workspace واحد في كل مرة للأداء الأمثل"
echo "   • أغلق الملفات غير المستخدمة"
echo "   • شغل التنظيف السريع دورياً"
echo "   • راقب استهلاك الذاكرة"
echo ""
echo "📊 لمراقبة الأداء: ./tools/monitor_vscode_performance.sh"
echo "🧹 للتنظيف السريع: ./tools/quick_cleanup.sh"
