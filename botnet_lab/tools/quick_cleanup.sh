#!/bin/bash
# Quick Cleanup Script for VS Code Performance
# سكريبت تنظيف سريع لتحسين أداء VS Code

echo "🧹 بدء التنظيف السريع لتحسين أداء VS Code..."

# الانتقال إلى مجلد المشروع
cd "$(dirname "$0")/.."

echo "📁 المجلد الحالي: $(pwd)"

# 1. تنظيف ملفات Python المؤقتة
echo "🐍 تنظيف ملفات Python المؤقتة..."
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null
find . -name "*.pyc" -delete 2>/dev/null
find . -name "*.pyo" -delete 2>/dev/null
find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null
echo "✅ تم تنظيف ملفات Python المؤقتة"

# 2. تنظيف ملفات السجلات الكبيرة
echo "📝 تنظيف ملفات السجلات الكبيرة..."
find ./logs -name "*.log" -size +10M -delete 2>/dev/null
find . -name "*.log" -size +50M -delete 2>/dev/null
echo "✅ تم تنظيف ملفات السجلات الكبيرة"

# 3. تنظيف ملفات البيانات المؤقتة
echo "💾 تنظيف ملفات البيانات المؤقتة..."
find ./temp -type f -delete 2>/dev/null
find ./tmp -type f -delete 2>/dev/null
find . -name "*.tmp" -delete 2>/dev/null
find . -name "*.temp" -delete 2>/dev/null
echo "✅ تم تنظيف ملفات البيانات المؤقتة"

# 4. تنظيف ملفات النظام
echo "🖥️ تنظيف ملفات النظام..."
find . -name ".DS_Store" -delete 2>/dev/null
find . -name "Thumbs.db" -delete 2>/dev/null
find . -name "desktop.ini" -delete 2>/dev/null
echo "✅ تم تنظيف ملفات النظام"

# 5. ضغط قواعد البيانات
echo "🗄️ ضغط قواعد البيانات..."
for db in $(find . -name "*.db" -size +1M 2>/dev/null); do
    if command -v sqlite3 >/dev/null 2>&1; then
        echo "   ضغط: $db"
        sqlite3 "$db" "VACUUM;" 2>/dev/null
    fi
done
echo "✅ تم ضغط قواعد البيانات"

# 6. فحص حجم المشروع
echo "📊 فحص حجم المشروع..."
PROJECT_SIZE=$(du -sh . 2>/dev/null | cut -f1)
FILE_COUNT=$(find . -type f 2>/dev/null | wc -l)
PYTHON_COUNT=$(find . -name "*.py" 2>/dev/null | wc -l)

echo "   📁 حجم المشروع: $PROJECT_SIZE"
echo "   📄 عدد الملفات: $FILE_COUNT"
echo "   🐍 ملفات Python: $PYTHON_COUNT"

# 7. فحص الذاكرة المتاحة
echo "💻 فحص موارد النظام..."
if command -v free >/dev/null 2>&1; then
    MEMORY_INFO=$(free -h | grep "Mem:" | awk '{print "المستخدمة: " $3 " / المتاحة: " $7}')
    echo "   🧠 الذاكرة: $MEMORY_INFO"
fi

# 8. فحص عمليات VS Code النشطة
echo "🔍 فحص عمليات VS Code..."
CODE_PROCESSES=$(ps aux | grep -i "code" | grep -v grep | wc -l)
if [ $CODE_PROCESSES -gt 0 ]; then
    echo "   ⚠️ يوجد $CODE_PROCESSES عملية VS Code نشطة"
    echo "   💡 يُنصح بإعادة تشغيل VS Code بعد التنظيف"
else
    echo "   ✅ لا توجد عمليات VS Code نشطة"
fi

# 9. إنشاء ملخص التنظيف
echo ""
echo "📋 ملخص التنظيف:"
echo "=================="
echo "✅ تم تنظيف ملفات Python المؤقتة"
echo "✅ تم تنظيف ملفات السجلات الكبيرة"
echo "✅ تم تنظيف ملفات البيانات المؤقتة"
echo "✅ تم تنظيف ملفات النظام"
echo "✅ تم ضغط قواعد البيانات"
echo ""
echo "🎉 تم الانتهاء من التنظيف السريع!"
echo ""
echo "🔄 الخطوات التالية:"
echo "   1. أعد تشغيل VS Code"
echo "   2. افتح المشروع من جديد"
echo "   3. راقب تحسن الأداء"
echo ""
echo "📊 للمراقبة المستمرة، شغل:"
echo "   ./tools/monitor_vscode_performance.sh"
