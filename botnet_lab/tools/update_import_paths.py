#!/usr/bin/env python3
"""
Update Import Paths Script
سكريبت تحديث مسارات الاستيراد بعد إعادة تنظيم الوحدات
"""

import os
import re
import sys
from pathlib import Path

class ImportPathUpdater:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.module_mappings = {
            # Security Exploitation
            'password_cracking': 'modules.security_exploitation.password_cracking',
            'realistic_password_cracking': 'modules.security_exploitation.realistic_password_cracking',
            'web_exploitation_xss': 'modules.security_exploitation.web_exploitation_xss',
            'info_stealer_educational': 'modules.security_exploitation.info_stealer_educational',
            
            # Phone Targeting
            'phone_number_targeting': 'modules.phone_targeting.phone_number_targeting',
            'smart_phone_targeting': 'modules.phone_targeting.smart_phone_targeting',
            'advanced_phone_attacks': 'modules.phone_targeting.advanced_phone_attacks',
            'advanced_phone_osint': 'modules.phone_targeting.advanced_phone_osint',
            'ai_phone_intelligence': 'modules.phone_targeting.ai_phone_intelligence',
            'mobile_capabilities': 'modules.phone_targeting.mobile_capabilities',
            
            # Social Media
            'social_media_accounts': 'modules.social_media.social_media_accounts',
            'social_media_blocking': 'modules.social_media.social_media_blocking',
            'social_engineering': 'modules.social_media.social_engineering',
            
            # Intelligence Gathering
            'intelligence_gathering': 'modules.intelligence_gathering.intelligence_gathering',
            'advanced_intelligence': 'modules.intelligence_gathering.advanced_intelligence',
            'predictive_analytics': 'modules.intelligence_gathering.predictive_analytics',
            
            # Stealth Evasion
            'stealth_evasion': 'modules.stealth_evasion.stealth_evasion',
            'advanced_stealth_evasion': 'modules.stealth_evasion.advanced_stealth_evasion',
            'advanced_evasion': 'modules.stealth_evasion.advanced_evasion',
            'neural_network_evasion': 'modules.stealth_evasion.neural_network_evasion',
            
            # Propagation Persistence
            'advanced_propagation': 'modules.propagation_persistence.advanced_propagation',
            'persistence_survival': 'modules.propagation_persistence.persistence_survival',
            
            # Network Communications
            'network_pivoting': 'modules.network_communications.network_pivoting',
            'satellite_communication': 'modules.network_communications.satellite_communication',
            'distributed_operations': 'modules.network_communications.distributed_operations',
            
            # Financial Exploitation
            'monetization_exploitation': 'modules.financial_exploitation.monetization_exploitation',
            'financial_exploitation': 'modules.financial_exploitation.financial_exploitation',
            
            # System Control
            'system_manipulation': 'modules.system_control.system_manipulation',
            'webcam_microphone': 'modules.system_control.webcam_microphone',
            
            # Advanced Technologies
            'blockchain_integration': 'modules.advanced_technologies.blockchain_integration',
            'deep_fake_technology': 'modules.advanced_technologies.deep_fake_technology'
        }
        
        self.updated_files = []
        self.errors = []
    
    def update_file_imports(self, file_path):
        """Update imports in a single file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            updated = False
            
            # Update import statements
            for old_module, new_module in self.module_mappings.items():
                # Pattern for "from modules.old_module import"
                pattern1 = rf'from modules\.{old_module} import'
                replacement1 = f'from {new_module} import'
                if re.search(pattern1, content):
                    content = re.sub(pattern1, replacement1, content)
                    updated = True
                
                # Pattern for "import modules.old_module"
                pattern2 = rf'import modules\.{old_module}'
                replacement2 = f'import {new_module}'
                if re.search(pattern2, content):
                    content = re.sub(pattern2, replacement2, content)
                    updated = True
                
                # Pattern for "from old_module import" (relative imports)
                pattern3 = rf'from {old_module} import'
                replacement3 = f'from {new_module} import'
                if re.search(pattern3, content):
                    content = re.sub(pattern3, replacement3, content)
                    updated = True
            
            # Write back if updated
            if updated and content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.updated_files.append(str(file_path))
                print(f"✅ Updated: {file_path}")
            
            return updated
            
        except Exception as e:
            error_msg = f"Error updating {file_path}: {e}"
            self.errors.append(error_msg)
            print(f"❌ {error_msg}")
            return False
    
    def update_all_imports(self):
        """Update imports in all relevant files"""
        print("🔄 Updating import paths after module reorganization...")
        
        # Files to update
        files_to_update = []
        
        # Core files
        core_dir = self.project_root / "core"
        if core_dir.exists():
            files_to_update.extend(core_dir.glob("*.py"))
        
        # Test files
        tests_dir = self.project_root / "tests"
        if tests_dir.exists():
            files_to_update.extend(tests_dir.rglob("*.py"))
        
        # Standalone files
        standalone_dir = self.project_root / "standalone"
        if standalone_dir.exists():
            files_to_update.extend(standalone_dir.rglob("*.py"))
        
        # RAT module files
        rat_dir = self.project_root / "rat_module"
        if rat_dir.exists():
            files_to_update.extend(rat_dir.glob("*.py"))
        
        # Scripts
        scripts_dir = self.project_root / "scripts"
        if scripts_dir.exists():
            files_to_update.extend(scripts_dir.glob("*.py"))
        
        # Tools
        tools_dir = self.project_root / "tools"
        if tools_dir.exists():
            files_to_update.extend(tools_dir.glob("*.py"))
        
        # Root level Python files
        files_to_update.extend(self.project_root.glob("*.py"))
        
        # Update each file
        total_files = len(files_to_update)
        updated_count = 0
        
        for file_path in files_to_update:
            if self.update_file_imports(file_path):
                updated_count += 1
        
        print(f"\n📊 Update Summary:")
        print(f"   Total files checked: {total_files}")
        print(f"   Files updated: {updated_count}")
        print(f"   Errors: {len(self.errors)}")
        
        if self.errors:
            print(f"\n❌ Errors encountered:")
            for error in self.errors:
                print(f"   • {error}")
        
        return updated_count > 0
    
    def create_module_index(self):
        """Create a module index file for easy imports"""
        index_content = '''"""
Botnet Lab Module Index
فهرس وحدات مشروع Botnet Lab

This file provides easy access to all reorganized modules.
"""

# Security Exploitation Modules
from modules.security_exploitation import (
    password_cracking,
    realistic_password_cracking,
    web_exploitation_xss,
    info_stealer_educational
)

# Phone Targeting Modules
from modules.phone_targeting import (
    phone_number_targeting,
    smart_phone_targeting,
    advanced_phone_attacks,
    advanced_phone_osint,
    ai_phone_intelligence,
    mobile_capabilities
)

# Social Media Modules
from modules.social_media import (
    social_media_accounts,
    social_media_blocking,
    social_engineering
)

# Intelligence Gathering Modules
from modules.intelligence_gathering.intelligence_gathering import (
    intelligence_gathering,
    advanced_intelligence,
    predictive_analytics
)

# Stealth Evasion Modules
from modules.stealth_evasion.stealth_evasion import (
    stealth_evasion,
    advanced_stealth_evasion,
    advanced_evasion,
    neural_network_evasion
)

# Propagation Persistence Modules
from modules.propagation_persistence import (
    advanced_propagation,
    persistence_survival
)

# Network Communications Modules
from modules.network_communications import (
    network_pivoting,
    satellite_communication,
    distributed_operations
)

# Financial Exploitation Modules
from modules.financial_exploitation.financial_exploitation import (
    monetization_exploitation,
    financial_exploitation
)

# System Control Modules
from modules.system_control import (
    system_manipulation,
    webcam_microphone
)

# Advanced Technologies Modules
from modules.advanced_technologies import (
    blockchain_integration,
    deep_fake_technology
)

__all__ = [
    # Security Exploitation
    'password_cracking', 'realistic_password_cracking', 'web_exploitation_xss', 'info_stealer_educational',
    
    # Phone Targeting
    'phone_number_targeting', 'smart_phone_targeting', 'advanced_phone_attacks', 
    'advanced_phone_osint', 'ai_phone_intelligence', 'mobile_capabilities',
    
    # Social Media
    'social_media_accounts', 'social_media_blocking', 'social_engineering',
    
    # Intelligence Gathering
    'intelligence_gathering', 'advanced_intelligence', 'predictive_analytics',
    
    # Stealth Evasion
    'stealth_evasion', 'advanced_stealth_evasion', 'advanced_evasion', 'neural_network_evasion',
    
    # Propagation Persistence
    'advanced_propagation', 'persistence_survival',
    
    # Network Communications
    'network_pivoting', 'satellite_communication', 'distributed_operations',
    
    # Financial Exploitation
    'monetization_exploitation', 'financial_exploitation',
    
    # System Control
    'system_manipulation', 'webcam_microphone',
    
    # Advanced Technologies
    'blockchain_integration', 'deep_fake_technology'
]
'''
        
        index_file = self.project_root / "modules" / "__init__.py"
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write(index_content)
        
        print(f"✅ Created module index: {index_file}")

def main():
    """Main function"""
    project_root = "/home/<USER>/Desktop/Year3/botnet/botnet_lab"
    
    if not os.path.exists(project_root):
        print(f"❌ Project root not found: {project_root}")
        return
    
    updater = ImportPathUpdater(project_root)
    
    print("🏗️ Starting import path update process...")
    
    # Update all import paths
    success = updater.update_all_imports()
    
    # Create module index
    updater.create_module_index()
    
    if success:
        print("\n🎉 Import path update completed successfully!")
    else:
        print("\n⚠️ Import path update completed with some issues.")
    
    print("\n💡 Next steps:")
    print("   1. Test the reorganized modules")
    print("   2. Update any remaining manual imports")
    print("   3. Run tests to ensure everything works")

if __name__ == "__main__":
    main()
