{"folders": [{"name": "🏗️ Core System", "path": "../core"}, {"name": "⚙️ Config", "path": "../config"}, {"name": "🧪 Core Tests", "path": "../tests/core"}, {"name": "📚 Documentation", "path": "../docs"}, {"name": "🛠️ Tools", "path": "../tools"}], "settings": {"// Core Workspace Settings": "محسن للعمل على النواة الأساسية", "search.exclude": {"**/__pycache__/**": true, "**/*.pyc": true, "**/*.db": true, "**/*.sqlite*": true, "**/logs/**": true, "**/data/**": true}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true}, "python.defaultInterpreterPath": "../botnet_env/bin/python", "python.analysis.autoImportCompletions": false, "python.analysis.indexing": false, "editor.minimap.enabled": false, "editor.codeLens": false, "git.enabled": true, "git.autorefresh": false, "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 2000, "terminal.integrated.cwd": "../", "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}/.."}}, "launch": {"version": "0.2.0", "configurations": [{"name": "🚀 C2 Server", "type": "python", "request": "launch", "program": "${workspaceFolder}/../core/c2_server.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/.."}, {"name": "🔧 Advanced C2 Server", "type": "python", "request": "launch", "program": "${workspaceFolder}/../core/advanced_c2_server.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/.."}]}}