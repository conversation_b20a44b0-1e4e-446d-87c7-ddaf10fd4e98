{"folders": [{"name": "🧩 <PERSON><PERSON><PERSON>", "path": "../modules"}, {"name": "🧪 Module Tests", "path": "../tests/modules"}, {"name": "📚 Module Docs", "path": "../docs"}, {"name": "🛠️ Tools", "path": "../tools"}], "settings": {"// Modules Workspace Settings": "محسن للعمل على الوحدات", "search.exclude": {"**/__pycache__/**": true, "**/*.pyc": true, "**/*.db": true, "**/*.sqlite*": true, "**/logs/**": true, "**/data/**": true}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true}, "python.defaultInterpreterPath": "../botnet_env/bin/python", "python.analysis.autoImportCompletions": false, "python.analysis.indexing": false, "python.analysis.extraPaths": ["../modules", "../core"], "editor.minimap.enabled": false, "editor.codeLens": false, "git.enabled": true, "git.autorefresh": false, "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 2000, "terminal.integrated.cwd": "../", "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}/.."}}, "launch": {"version": "0.2.0", "configurations": [{"name": "🧪 Test Module", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}/.."}, {"name": "🔍 <PERSON><PERSON> Stealer <PERSON>", "type": "python", "request": "launch", "program": "${workspaceFolder}/../modules/info_stealer_educational.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/.."}, {"name": "🌐 XSS Demo", "type": "python", "request": "launch", "program": "${workspaceFolder}/../modules/web_exploitation_xss.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/.."}]}}