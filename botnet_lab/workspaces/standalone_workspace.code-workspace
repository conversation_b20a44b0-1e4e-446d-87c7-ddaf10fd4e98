{"folders": [{"name": "🔧 Standalone Modules", "path": "../standalone"}, {"name": "🐀 RAT Module", "path": "../rat_module"}, {"name": "🧪 Standalone Tests", "path": "../tests/standalone"}, {"name": "📚 Documentation", "path": "../docs"}], "settings": {"// Standalone Workspace Settings": "محسن للعمل على الوحدات المستقلة", "search.exclude": {"**/__pycache__/**": true, "**/*.pyc": true, "**/*.db": true, "**/*.sqlite*": true, "**/logs/**": true, "**/data/**": true}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true}, "python.defaultInterpreterPath": "../botnet_env/bin/python", "python.analysis.autoImportCompletions": false, "python.analysis.indexing": false, "python.analysis.extraPaths": ["../standalone", "../modules", "../core"], "editor.minimap.enabled": false, "editor.codeLens": false, "git.enabled": true, "git.autorefresh": false, "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 2000, "terminal.integrated.cwd": "../", "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}/.."}}, "launch": {"version": "0.2.0", "configurations": [{"name": "🧪 Test Standalone Module", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}/.."}, {"name": "🔧 Run All Standalone Tests", "type": "python", "request": "launch", "program": "${workspaceFolder}/../standalone/run_all_tests.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/.."}, {"name": "🐀 RAT Quick Test", "type": "python", "request": "launch", "program": "${workspaceFolder}/../rat_module/quick_test.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/.."}]}}